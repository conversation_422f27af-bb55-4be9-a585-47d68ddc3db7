{"name": "fleet-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^17.3.10", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/google-maps": "^17.3.1", "@angular/localize": "^18.0.1", "@angular/material": "^17.3.1", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@fortawesome/angular-fontawesome": "^0.14.1", "@fortawesome/fontawesome-free": "^6.5.1", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@ng-bootstrap/ng-bootstrap": "^16.0.0", "@ng-select/ng-select": "^13.8.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@popperjs/core": "^2.11.8", "@swimlane/ngx-charts": "^20.5.0", "ag-grid-angular": "^31.2.0", "angularx-qrcode": "^18.0.1", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.3", "crisp-sdk-web": "^1.0.22", "d3": "^7.9.0", "html2pdf.js": "^0.10.2", "jquery": "^3.7.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "ng-qrcode": "^17.0.0", "ng2-charts": "^6.0.1", "ngx-csv": "^0.3.2", "ngx-mask": "^18.0.0", "ngx-select-dropdown": "^3.3.2", "ngx-toastr": "^18.0.0", "rxjs": "~7.8.0", "sweetalert2": "^11.6.13", "tslib": "^2.6.2", "xlsx": "^0.18.5", "zone.js": "^0.14.4"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.13", "@types/bootstrap": "^5.2.10", "@types/jasmine": "~5.1.0", "@types/leaflet": "^1.9.12", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.4"}}