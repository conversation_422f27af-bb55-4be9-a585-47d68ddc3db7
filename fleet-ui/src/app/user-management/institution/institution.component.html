<div class="modal-container" *ngIf="showForm">
  <div class="modal-content">
    <div>
      <div class="top w-100"> 
        Complete Institution Details</div>
      <div class="card w-100 custom-height-container">
        <form (ngSubmit)="onSubmit()" [formGroup]="institutionForm" method="post">
          <div class="row mt-3">
            <div class="form-group col-md-6">
              <label for="institution">Institution</label>
              <input type="text" id="institution" class="form-control mt-1" formControlName="institution" readonly>
            </div>

            <div class="form-group col-md-6">
              <label for="province">Province</label>
              <select id="province" class="form-control mt-1" formControlName="province" (change)="onProvinceChange($event)">
                <option selected>Choose province</option>
                <option *ngFor="let province of provinces" [value]="province.id">{{ province.name }}</option>
              </select>
            </div>
          </div>

          <div class="row mt-3">
            <div class="form-group col-md-6">
              <label for="district">District</label>
              <select id="district" class="form-control mt-1" formControlName="district" (change)="onDistrictChange($event)">
                <option selected>Choose district</option>
                <option *ngFor="let district of districts" [value]="district.id">{{ district.name }}</option>
              </select>
            </div>
            <div class="form-group col-md-6">
              <label for="sector">Sector</label>
              <select id="sector" class="form-control mt-1" formControlName="sector" (change)="onSectorChange($event)">
                <option selected>Choose sector</option>
                <option *ngFor="let sector of sectors" [value]="sector.id">{{ sector.name }}</option>
              </select>
            </div>
          </div>

          <div class="row mt-3">
            <div class="form-group col-md-6">
              <label for="cell">Cell</label>
              <select id="cell" class="form-control mt-1" formControlName="cell" (change)="onCellChange($event)">
                <option selected>Choose cell</option>
                <option *ngFor="let cell of cells" [value]="cell.id">{{ cell.name }}</option>
              </select>
            </div>
            <div class="form-group col-md-6">
              <label for="village">Village</label>
              <select id="village" class="form-control" formControlName="village">
                <option selected>Choose village</option>
                <option *ngFor="let village of villages" [value]="village.id">{{ village.name }}</option>
              </select>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-md-6">
              <button type="submit" class="btn mt-1 mb-3 w-50">Register Details</button>
              <app-toastr></app-toastr>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
