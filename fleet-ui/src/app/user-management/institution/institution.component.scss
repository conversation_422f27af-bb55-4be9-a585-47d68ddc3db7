.custom-height-container {
    height: 83%;
    border-radius: 0;
  
  }
  .modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(196, 196, 196, 0.5); 
    display: flex;
    justify-content: center; 
    align-items: center; 
  }

  .modal-content {
    background-color: #fff; 
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1); 
    width: 50%; 
  }
  label{
    font-size: 15px;
    color: #757575;
  }
  input{
    font-size: 14px;
    color: #757575;
  }
  h1{
    font-size: 20px;
    padding: 0 0 20px 0;
    color: #28A4E2;
    text-align: left !important;
    }
  .top{
    color: #28A4E2;
    height: 50px;
    padding: 10px;
    font-weight: 600;
    font-size: 18px;
    text-align: center;
    border: none;

}
form{
 padding: 20px;
}
.btn{
  background-color: #28A4E2;
  color: #ffffff;
}
@media screen and (max-width: 991px) {
  .container{
    width: 90%;
    height: 93%;
    right: 0;
    top: 7%;
    z-index: -1;
  }
  label{
    display: none;
  }
  .form-group{
    margin: 10px;
  }
}