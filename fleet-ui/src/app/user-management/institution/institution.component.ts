import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit, ViewChild  } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-institution',
  templateUrl: './institution.component.html',
  styleUrls: ['./institution.component.scss']
})
export class InstitutionComponent implements OnInit {
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  institutionForm: FormGroup;
  institutionId: string = '';
  provinces: any[] = [];
  districts: any[] = [];
  sectors: any[] = [];
  cells: any[] = [];
  villages: any[] = [];
  showForm: boolean = true;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService
  ) {
    this.institutionForm = this.formBuilder.group({
      institution: ['', Validators.required],
      province: ['', Validators.required],
      district: ['', Validators.required],
      sector: ['', Validators.required],
      cell: ['', Validators.required],
      village: ['', Validators.required],
    }); 
  }

  ngOnInit(): void {
    this.fetchProvinces();
    this.getUserInstitution();
  }

  getUserInstitution(): void {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parseObj = JSON.parse(data);
      this.institutionId = parseObj.data.user.institution.id;

      if (!parseObj.data.user.institution.isLocationAvailable) {
        if (this.institutionId) {
          this.showForm = true;
        } else {
          this.updateIsLocationAvailable(false);
        }
      }

      this.institutionForm.patchValue({
        institution: parseObj.data.user.institution.name.toUpperCase()
      });
    }
  }

  fetchProvinces(): void {
    this.http.get<any[]>(`${environment.otherUrl}/user-management/provinces`).subscribe(
      (response) => {
        this.provinces = response;
      },
      (error) => {
        console.error('Error fetching provinces:', error);
      }
    );
  }

  onProvinceChange(event: Event): void {
    const provinceId = (event.target as HTMLSelectElement).value;
    this.http.get<any[]>(`${environment.otherUrl}/user-management/districts`).subscribe(
      (response) => {
        this.districts = response.filter(district => district.province.id === provinceId);
      },
      (error) => {
        console.error('Error fetching districts:', error);
      }
    );
  }
  
  onDistrictChange(event: Event): void {
    const districtId = (event.target as HTMLSelectElement).value;
    this.http.get<any[]>(`${environment.otherUrl}/user-management/sectors`).subscribe(
      (response) => {
        this.sectors = response.filter(sector => sector.district.id === districtId);
      },
      (error) => {
        console.error('Error fetching sectors:', error);
      }
    );
  }
  
  onSectorChange(event: Event): void {
    const sectorId = (event.target as HTMLSelectElement).value;
    this.http.get<any[]>(`${environment.otherUrl}/user-management/cells`).subscribe(
      (response) => {
        this.cells = response.filter(cell => cell.sector.id === sectorId);
      },
      (error) => {
        console.error('Error fetching cells:', error);
      }
    );
  }

  onCellChange(event: any): void {
    const cellId = (event.target as HTMLSelectElement).value;
    this.http.get<any[]>(`${environment.otherUrl}/user-management/villages`).subscribe(
      (response) => {
        this.villages = response.filter(village => village.cell.id === cellId);
      },
      (error) => {
        console.error('Error fetching cells:', error);
      }
    );
  }


  onSubmit(): void {
    if (this.institutionForm.valid) {
      const requestBody = {
        institution: this.institutionId,
        province: this.institutionForm.value.province,
        district: this.institutionForm.value.district,
        sector: this.institutionForm.value.sector,
        cell: this.institutionForm.value.cell,
        village: this.institutionForm.value.village
      };

      this.http.post(`${environment.otherUrl}/user-management/institutionDistrict`, requestBody).subscribe(
        (response) => {
          console.log(response);
          this.updateIsLocationAvailable(true);
          this.toastr.success('Institution details updated successfully');
          this.institutionForm.reset();
          this.showForm = false;
        },
        (error: HttpErrorResponse) => {
          console.error('Error updating Institution details:', error);
          this.toastr.error('Error updating Institution details. Please try again later.');
        }
      );
    } else {
      this.toastr.error('Please fill in all fields properly');
    }
  }

  updateIsLocationAvailable(value: boolean): void {
    const userData = localStorage.getItem('localUserData');
    if (userData) {
      const parsedUserData = JSON.parse(userData);
      parsedUserData.data.user.institution.isLocationAvailable = value;
      localStorage.setItem('localUserData', JSON.stringify(parsedUserData));
    }
  }

  resetForm(): void {
    this.institutionForm.reset();
  }
}
