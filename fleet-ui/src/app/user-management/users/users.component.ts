import { Component } from '@angular/core';
import { User } from '../../models/user.interface';
import { HttpClient } from '@angular/common/http';
import { faUsers, faCar, faInstitution, faUser, IconDefinition ,faCalendar, faClock, faArrowTrendUp, faSearch, faCaretLeft, faCaretRight } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';

interface Item{
  icon: IconDefinition;
  title: string;
  count: number;
}


@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrl: './users.component.scss'
})

export class UsersComponent {
  users: User[] = [];
  usersCount: number = 0;
  adminCount: number = 0;
  items:Item[] = [];
  displayedUsers: User[]=[];
  filteredUsers: User[]=[];


  constructor(private http: HttpClient, private router: Router) { }
  
  ngOnInit() {
    this.fetchUsers();
    this.isAdmin();
    this.updateDisplayedUsers()
  }
  navigateToRegisterUser(){
    this.router.navigateByUrl('auth/register');
  }

  isAdmin():boolean {
    const data = localStorage.getItem('localUserData');
    if(data !=null){
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'ADMIN' 
    }
    return false;
  }

  isLogistics():boolean{
    const data = localStorage.getItem('localUserData');
    if(data !=null){
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }
  getCurrentInstitutionId(): number {
    const data = localStorage.getItem('localUserData');
    if (data) {
        const parseObj = JSON.parse(data);
        return parseObj?.data?.user?.institution.id; // Adjust based on your data structure
    }
    return 0;
}

  fetchUsers() {
    const url = `${environment.baseUrl}/user-management/users`;
    this.http.get<User[]>(url)
      .subscribe(users => {
        console.log(users)
        this.users = users.map(user => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
          status: user.isEmailValid ? 'Active' : 'Inactive' 
        }));

        // Filtering logic based on role
        if (this.isAdmin()) {
          // If Admin, show all users with the same institution ID
          const institutionId = this.getCurrentInstitutionId(); // Implement this method based on your logic
          this.filteredUsers = this.users.filter(user => user.institution.id === institutionId);
        } else {
          // For non-admins, show users without filtering
          this.filteredUsers = this.users;
        }
        this.updateDisplayedUsers()
        this.totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
      
        this.usersCount = this.users.length; 
        this.adminCount = this.users.filter(user => user.role.name === 'ADMIN').length;
        this.items = [
          { icon: faUsers, title: 'Users', count: this.usersCount },
          { icon: faCar, title: 'Vehicles', count: 1893 },
          { icon: faInstitution, title: 'Institutions', count: 185 },
          { icon: faUser, title: 'Admins', count: this.adminCount },
        ];
      });
}




  calendarIcon = faCalendar;
  clockIcon = faClock;
  trendIcon = faArrowTrendUp;
  searchIcon = faSearch;
  caretLeft = faCaretLeft;
  userIcon = faUser;
  caretRight = faCaretRight;

  searchText: string = '';
  sortField: string = 'fullName';
  sortDirection: string = 'asc';

  pageSize = 10;
  currentPage = 1; 
  totalPages: number = 0; 

allPageNumbers(): number[] {
  const pageNumbers = [];
  for (let i = 1; i <= this.totalPages; i++) {
    pageNumbers.push(i);
  }
  return pageNumbers;
}


  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
    this.updateDisplayedUsers();
  }
  
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
    this.updateDisplayedUsers();
  }
  
  updateDisplayedUsers() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedUsers = this.filteredUsers.slice(startIndex, endIndex);
  }

  getPageNumbers(): number[] {
    const pageNumbers = [];
    const totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
    const numPagesToShow = Math.min(totalPages, 5); 
  
    const startIndex = Math.max(Math.min(this.currentPage - Math.floor(numPagesToShow / 2), totalPages - numPagesToShow + 1), 1);
    const endIndex = startIndex + numPagesToShow - 1;
  
    for (let i = startIndex; i <= endIndex; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedUsers();
    }
  }

  getFirstEntryIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }
  
  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.filteredUsers.length);
  }

getVisiblePageNumbers(): (number | string)[] {
  const totalPages = this.totalPages;
  const currentPage = this.currentPage;
  const visiblePages: (number | string)[] = [];

  // For mobile/small screens, show fewer pages
  const isMobile = window.innerWidth < 768;
  const maxVisiblePages = isMobile ? 3 : 5;

  if (totalPages <= maxVisiblePages + 2) {
    // Show all pages if total is small
    for (let i = 1; i <= totalPages; i++) {
      visiblePages.push(i);
    }
  } else {
    // Always show first page
    visiblePages.push(1);

    // Calculate start and end of visible range
    let start = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
    let end = Math.min(totalPages - 1, currentPage + Math.floor(maxVisiblePages / 2));

    // Adjust if we're near the beginning or end
    if (currentPage <= Math.floor(maxVisiblePages / 2) + 1) {
      end = Math.min(totalPages - 1, maxVisiblePages);
    }
    if (currentPage >= totalPages - Math.floor(maxVisiblePages / 2)) {
      start = Math.max(2, totalPages - maxVisiblePages);
    }

    // Add ellipsis if needed
    if (start > 2) {
      visiblePages.push('...');
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      visiblePages.push(i);
    }

    // Add ellipsis if needed
    if (end < totalPages - 1) {
      visiblePages.push('...');
    }

    // Always show last page
    if (totalPages > 1) {
      visiblePages.push(totalPages);
    }
  }

  return visiblePages;
}

// Add method to handle ellipsis clicks
goToPageOrEllipsis(pageNumber: number | string) {
  if (typeof pageNumber === 'number') {
    this.goToPage(pageNumber);
  }
  // For ellipsis, you could implement a dropdown or do nothing
}

// Add method to get pagination info text for mobile
getPaginationInfoMobile(): string {
  return `${this.currentPage} of ${this.totalPages}`;
}

// Update the searchUsers method to fix pagination
searchUsers() {
  const searchTextLower = this.searchText.toLowerCase();
  
  // Apply search to the base filtered users (not displayed users)
  const searchResults = this.filteredUsers.filter(user => {
    return (
      user.identification.toLowerCase().includes(searchTextLower) ||
      user.fullName?.toLowerCase().includes(searchTextLower) || 
      user.role.name.toLowerCase().includes(searchTextLower) ||  
      (user.phoneNumber && user.phoneNumber.toString().includes(searchTextLower)) ||
      user.email.toLowerCase().includes(searchTextLower)
    );
  });

  // Update filtered users with search results
  this.filteredUsers = searchResults;
  
  // Reset to first page and update pagination
  this.currentPage = 1;
  this.totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
  this.updateDisplayedUsers();
}

// Update the sortTable method to work with pagination
sortTable(event: any) {
  const selectedField: string = event.target.value;
  
  if (!selectedField) {
    this.sortField = '';
    this.sortDirection = 'asc';
    // Reset to original filtered users (maintaining institution filter)
    if (this.isAdmin()) {
      const institutionId = this.getCurrentInstitutionId();
      this.filteredUsers = this.users
        .filter(user => user.institution.id === institutionId)
        .map(user => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
          status: user.isEmailValid ? 'Active' : 'Inactive' 
        }));
    } else {
      this.filteredUsers = this.users.map(user => ({
        ...user,
        fullName: user.firstName + ' ' + user.lastName,
        status: user.isEmailValid ? 'Active' : 'Inactive' 
      }));
    }
    this.currentPage = 1;
    this.totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
    this.updateDisplayedUsers();
    return;
  }

  if (this.sortField === selectedField) {
    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
  } else {
    this.sortField = selectedField;
    this.sortDirection = 'asc';
  }
  
  // Sort the filtered users array (not displayed users)
  this.filteredUsers = this.filteredUsers.sort((a, b) => {
    const isAsc = this.sortDirection === 'asc';
    switch (this.sortField) {
      case 'fullName':
        return compare(a.fullName, b.fullName, isAsc);
      case 'role':
        return compare(a.role.name, b.role.name, isAsc);
      case 'status':
        return compare(a.status, b.status, isAsc);
      default:
        return 0; 
    }
  });
  
  // Reset to first page and update displayed users
  this.currentPage = 1;
  this.updateDisplayedUsers();
}
}
function compare (a: string, b: string, isAsc: boolean):number {
  const comparison = a.localeCompare(b);
  return isAsc ? comparison : -comparison;
}
