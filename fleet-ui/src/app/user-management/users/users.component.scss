.container{
    background-color:#D9D9D94D;
    width: 83%;
    height: 90%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
}

.request-btn{
    background-color: #28A4E2;
    color: #F9FBFF;
    font-weight: 500;
    font-size: 14px;
    height: 8%;
    
}
.search-group{
    background-color: #ffffff;
    border-radius: 10px;
    width: 40%;
    height: 35px;
    border: 1px solid #edebf0;
}
.icon{
    background-color: #ffffff;
    color: #28A4E2;
    font-size: 13px;
    border-radius: 10px;
    padding: 9px 0px 7px 9px !important;
    text-align: center;
}
.global-input{
    color: #A098AE;
    background-color: #fff;
    border-radius: 10px;
    font-size: 13px;
}
.sorting-group{
    font-size: 12px;
    background-color: #F9FBFF;
    width: 18%;
    height: 10%;
    border-radius: 10px;
    margin-left: 20px;
}
.select{
    font-size: 12px;
    font-weight: bold;
    width: 60%;
    border: none !important;
    background-color: #F9FBFF;
    outline: none !important;
}
.table-container{
    background-color: #ffffff;
}
.table-title{
    font-size: 22px;
    color: #28A4E2;
}
.card{
    border: none;
}
th{
    font-size: 13px;
    color: #B5B7C0 !important;
    line-height: 21px;
}

.table-container td{
    color: #757575 !important;
    font-size: small;
}
.status-active{
    color: rgb(33, 190, 33);
    font-weight: 500;
    font-size: 13px;
}
.status-inactive{
    color: #DF0404;
    font-weight: 500;
    font-size: 13px;
}
.table-body.empty {
    display: none;
  }
.nav{
    padding: 0px 20px;
}
.nav li .pages{
    height: 30px;
    width: 30px;
    border-radius: 50%;
    font-size: 12px;
    padding: 5px;
}
.nav li{
    margin: 0px 5px;
}
.caret{
    background-color: #ffffff;
    border: none;
    font-size: 20px;
    color: #A098AE;
    padding: 5px 0px;
}
.pagination-info{
    font-size: 14px;
    color: #B5B7C0;
}
.table-only table{
    width: 100%;
}
