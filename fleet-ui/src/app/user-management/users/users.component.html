<div class="users-container d-flex">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="container">
        <div class="header d-flex flex-row justify-content-between">
            <h2 class="table-title">All Users</h2>
            <ng-container *ngIf="isAdmin()">
                <button class="btn request-btn d-flex flex-row justify-content-center align-items-center mb-2" (click)="navigateToRegisterUser()">
                    <fa-icon [icon]="userIcon" class="pe-2"></fa-icon>
                    Register New User
                </button>
            </ng-container>
        </div>
        
        <div class="card table-container d-flex flex-column p-2">
            <div class="table-header d-flex flex-row justify-content-between p-2">
                <div class="search-group d-flex">
                    <fa-icon [icon]="searchIcon" class="icon"></fa-icon>
                    <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchUsers()">
                </div>

                <div class="sorting-group d-flex flex-row p-2">
                    <label for="sort-field" class="text-muted p-1">Sort by:</label>
                    <select id="sort-field" class="select" (change)="sortTable($event)">
                        <option value="">Select Field</option>
                        <option value="fullName">Full Name</option>
                        <option value="role">Role</option>
                        <option value="status">Status</option>
                    </select>
                </div>
            </div>
            
            <div class="table-only">
                <table class="table table-stripped">
                    <thead>
                        <tr>
                            <th>Emp ID</th>
                            <th sortable="fullName">Full Names</th>
                            <th sortable="institution">Role</th>
                            <th>Phone Number</th>
                            <th>Email</th>
                            <th sortable="status">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of displayedUsers">
                            <td>{{ user.identification }}</td>
                            <td>{{ user.fullName }}</td>
                            <td>{{ user.role.name }}</td>
                            <td>{{ user.phoneNumber }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                <p [ngClass]="{
                                    'status-active': user.status === 'Active',
                                    'status-inactive': user.status === 'Inactive'
                                }">{{ user.status }}</p>
                            </td>
                        </tr>
                    </tbody>
                    
                </table>
            </div>
      <!-- Replace your existing pagination nav with this responsive version -->
<nav aria-label="Page navigation" class="nav d-flex flex-column flex-md-row justify-content-between align-items-center">
    <!-- Pagination info - responsive layout -->
    <div class="pagination-info mb-2 mb-md-0 order-2 order-md-1">
      <span class="d-none d-md-inline">
        Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredUsers.length }} entries
      </span>
      <span class="d-md-none">
        Page {{ getPaginationInfoMobile() }} ({{ filteredUsers.length }} total)
      </span>
    </div>
  
    <!-- Pagination controls -->
    <div class="pagination-controls order-1 order-md-2">
      <ul class="pagination justify-content-center mb-0">
        <!-- Previous button -->
        <li class="page-item">
          <button class="page-link caret d-flex align-items-center justify-content-center" 
                  (click)="previousPage()" 
                  [disabled]="currentPage === 1"
                  aria-label="Previous page">
            <fa-icon [icon]="caretLeft"></fa-icon>
          </button>
        </li>
  
        <!-- Page numbers -->
        <li class="page-item" *ngFor="let pageNumber of getVisiblePageNumbers()">
          <button *ngIf="pageNumber !== '...'" 
                  class="page-link pages" 
                  [class.active]="currentPage === pageNumber" 
                  (click)="goToPageOrEllipsis(pageNumber)"
                  [attr.aria-label]="'Go to page ' + pageNumber">
            {{ pageNumber }}
          </button>
          <span *ngIf="pageNumber === '...'" 
                class="page-link ellipsis" 
                aria-hidden="true">
            ...
          </span>
        </li>
  
        <!-- Next button -->
        <li class="page-item">
          <button class="page-link caret d-flex align-items-center justify-content-center" 
                  (click)="nextPage()" 
                  [disabled]="currentPage === totalPages"
                  aria-label="Next page">
            <fa-icon [icon]="caretRight"></fa-icon>
          </button>
        </li>
      </ul>
    </div>
  </nav>
        </div>
    </div>
</div>
