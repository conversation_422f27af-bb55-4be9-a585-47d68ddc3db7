import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UsersComponent } from './users/users.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { InstitutionComponent } from './institution/institution.component';
import { ProfileComponent } from './profile/profile.component';
import { CardsComponent } from './cards/cards.component';
import { ChartsComponent } from './charts/charts.component';

const routes: Routes = [
  { path: 'all-users', component: UsersComponent },
  { path: 'dashboard', component: DashboardComponent},
  { path: 'institution', component: InstitutionComponent},
  {path: 'profile', component: ProfileComponent},
  {path:'cards', component: CardsComponent},
  {path:'charts', component: ChartsComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserManagementRoutingModule { }
