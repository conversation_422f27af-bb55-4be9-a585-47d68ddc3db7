<div class="row">
    <div class="col-xl-4 col-md-6 mb-4" *ngFor="let item of statItems">
        <a [routerLink]="item.path" class="card shadow h-100" style="border-radius: 20px; display: block;">
            <div class="card-body d-flex align-items-center">
                <div class="iconContainer d-flex justify-content-center align-items-center rounded-circle me-3 {{ item.class }}" style="width: 40px; height: 40px;">
                    <fa-icon [icon]="item.icon"></fa-icon>
                </div>
                <div class="text-title" style="font-size: 16px;">
                    <span class="link-text">{{ item.title }}</span>
                </div>
            </div>
        </a>
    </div>
</div>

