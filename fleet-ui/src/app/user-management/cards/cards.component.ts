import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { faUsers, faAdd, faUser, IconDefinition, faInstitution, faPenToSquare, faCar } from '@fortawesome/free-solid-svg-icons';
import { Router } from '@angular/router';
interface StatItem {
  path: string;
  title: string;
  class: string;
  icon: IconDefinition; // Added icon property
}

@Component({
  selector: 'app-cards',
  templateUrl: './cards.component.html',
  styleUrl: './cards.component.scss'
})

export class CardsComponent {
  statItems: StatItem[] = [];

  CarIcon= faAdd;
  role: string = '';
  constructor(private http: HttpClient) { }
  ngOnInit() {
    this.getUserData();
}

getUserData() {
    const data = localStorage.getItem('localUserData');
    if (data !== null) {
        const parseObj = JSON.parse(data);
        this.role = parseObj.data.user.role.name;
        console.log('User role:', this.role);
        this.loadStatistics(this.role);
    }
}
    loadStatistics(role: string) {
      switch (role) {
          case 'SUPER-ADMIN':
              this.statItems = [
                  {
                    path: '/auth/register',  
                    title: 'Add New User',
                      class: 'bg-primary',
                      icon: faAdd,
                  },
                  {
                    path: '/user-management/all-users',  
                    title: 'View Users',
                      class: 'bg-success',
                      icon: faUsers,
                  },
                  {
                    path: '',  
                    title: 'View Institutions',
                      class: 'bg-info',
                      icon: faInstitution,
                  }
              ];
              break;
          case 'ADMIN':
              this.statItems = [
                  {
                    path: '/auth/register',  
                    title: 'Add New User',
                      class: 'bg-primary',
                      icon: faAdd,
                  },
                  {
                    path: '/user-management/all-users',  
                    title: 'View Users',
                      class: 'bg-success',
                      icon: faUsers,
                  },
                  {
                    path: '/user-management/all-users',  
                    title: 'User Roles',
                      class: 'bg-info',
                      icon: faInstitution,
                  }
              ];
              break;
              case 'Institutional logistics':
                this.statItems = [
                    {
                      path: '/vehicle-management/request-vehicle',  
                      title: 'Request Vehicle',
                      class: 'bg-primary',
                      icon: faAdd,
                    },
                    {
                      path: '/vehicle-management/register-existing-vehicle',  
                      title: 'Existing Vehicle Recording',
                      class: 'bg-success',
                      icon: faAdd,
                    },
                    {
                      path: '/vehicle-management/all-disposals',  
                      title: 'Disposal Request',
                      class: 'bg-info',
                      icon: faPenToSquare,
                    }
                ];
                break;
                case 'Institutional CBM':
                this.statItems = [
                    {
                      path: '/vehicle-management/all-vehicles',  
                      title: 'View Acquisitions',
                      class: 'bg-primary',
                      icon: faCar,
                    },
                    {
                      path: '/vehicle-management/all-registered',  
                      title: 'View Registrations',
                      class: 'bg-success',
                      icon: faCar,
                    },
                    {
                      path: '/vehicle-management/all-disposals',  
                      title: 'View Disposals',
                      class: 'bg-info',
                      icon: faPenToSquare,
                    }
                ];
                break;
                case 'Fleet Mgt Senior Engineer':
                  this.statItems = [
                      {
                        path: '/vehicle-management/all-registered',  
                        title: 'View Registrations',
                        class: 'bg-primary',
                        icon: faCar,
                      },
                      {
                        path: '/vehicle-management/all-vehicles',  
                        title: 'View Acquisitions',
                        class: 'bg-success',
                        icon: faCar,
                      },
                      {
                        path: '/vehicle-management/all-disposals',  
                      title: 'View Disposals',
                        class: 'bg-info',
                        icon: faPenToSquare,
                      }
                  ];
                  break;
                 
              
          default:
              
              this.statItems = [
                  {
                    path: '/vehicle-management/all-vehicles',  
                    title: 'View Requests',
                      class: 'bg-primary',
                      icon: faAdd,
                  },
                  {
                    path: '/vehicle-management/all-vehicles',  
                    title: 'View Registrations',
                      class: 'bg-success',
                      icon: faAdd,
                  },
                  {
                    path: '/vehicle-management/all-disposals',  
                      title: 'View Disposals',
                      class: 'bg-info',
                      icon: faAdd,
                  }
              ];
              break;
      }
  }
  
  
}
