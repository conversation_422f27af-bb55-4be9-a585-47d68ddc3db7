<div class="container">
    <h2 class="table-title">Vehicles Overview</h2>
    <div class="d-flex justify-content-start mb-2">
      <button class="btn btn-link" [ngClass]="{'active': activeTab === 'availableVehicles'}" (click)="setActiveTab('availableVehicles')">
        Available Vehicles <span class="badge" [ngClass]="{'bg-secondary': activeTab !== 'availableVehicles', 'bg-primary': activeTab === 'availableVehicles'}"></span>
      </button>
      <button  class="btn btn-link" [ngClass]="{'active': activeTab === 'returnedVehicles'}" (click)="setActiveTab('returnedVehicles')">
        Returned Vehicles <span class="badge" [ngClass]="{'bg-secondary': activeTab !== 'returnedVehicles', 'bg-primary': activeTab === 'returnedVehicles'}"></span>
      </button>
      
      
    </div>
    <div *ngIf="activeTab === 'availableVehicles'">
   <div class="filter">
          <label for="ownershipSelect">Select Ownership Type:</label>
          <select id="ownershipSelect" [(ngModel)]="selectedOwnershipType" (change)="fetchDataFromAPI()">
            <option *ngFor="let ownership of ownerships" [value]="ownership">{{ ownership }}</option>
          </select>
            </div>  
 
      <div class="chart bar-chart w-100"> 
    
        <ngx-charts-bar-vertical
          [results]="chartData"
          [view]="[1200, 400]"
          [gradient]="false"
          [xAxis]="true"
          [yAxis]="true"
          [legend]="true"
          [showXAxisLabel]="true"
          [showYAxisLabel]="true"
          [xAxisLabel]="'Vehicle Type'"
          [yAxisLabel]="'Count'"
          [barPadding]="10">
        </ngx-charts-bar-vertical>
      </div>
  <div class="charts-row mt-4 px-4">  
    <div class="chart bar-chart">
    <div class="filter">
      <label for="yearSelect" style="margin-left: 30px;">Select Year:</label>
      <select id="year-select" [(ngModel)]="selectedYear" (change)="onYearChange()">
        <option *ngFor="let year of years" [value]="year">{{ year }}</option>
      </select>
    </div>
    <ngx-charts-bar-vertical
      [results]="chartData2"
      [view]="[600, 300]"
      [gradient]="false"
      [xAxis]="true"
      [yAxis]="true"
      [legend]="true"
      [showXAxisLabel]="true"
      [showYAxisLabel]="true"
      [xAxisLabel]="'Vehicle Type'"
      [yAxisLabel]="'Count'"
      [barPadding]="10">
    </ngx-charts-bar-vertical>
  </div>
    <div class="chart pie-chart">
      <ngx-charts-pie-chart
        [results]="pieChartData1"
        [view]="[500, 300]"
        [legend]="true"
        [explodeSlices]="false"
        [labels]="true"
        [doughnut]="false"
        [customColors]="customColors"
        >
      </ngx-charts-pie-chart>
    </div>
   
    <!-- <div class="chart pie-chart">
      <div class="vehicleType-filter">
        <label for="vehicleTypeSelect">Select Vehicle Type:</label>
        <select id="vehicleTypeSelect" [(ngModel)]="selectedvehicleType" (change)="updatePieChart2()">
          <option *ngFor="let vehicleType of vehicleTypes" [value]="vehicleType">{{ vehicleType }}</option>
        </select>
      </div>
      <ngx-charts-pie-chart
        [results]="pieChartData2"
        [view]="[400, 300]"
        [legend]="true"
        [explodeSlices]="false"
        [labels]="true"
        [doughnut]="false">
      </ngx-charts-pie-chart>
    </div> -->
   
  </div>
    </div>
    <div *ngIf="activeTab === 'returnedVehicles'">
      <div class="filter">
        <label for="ownershipSelect">Select Ownership Type:</label>
        <select id="ownershipSelect2" [(ngModel)]="selectedOwnershipType2" (change)="fetchReturnedVehicles()">
          <option *ngFor="let ownership of ownerships" [value]="ownership">{{ ownership }}</option>
        </select>
          </div>  
      <div class="chart bar-chart w-100"> 
    
        <ngx-charts-bar-vertical
          [results]="chartDataReturnedVehicles"
          [view]="[1200, 400]"
          [gradient]="false"
          [xAxis]="true"
          [yAxis]="true"
          [legend]="true"
          [showXAxisLabel]="true"
          [showYAxisLabel]="true"
          [xAxisLabel]="'Vehicle Type'"
          [yAxisLabel]="'Count'"
          [barPadding]="10">
        </ngx-charts-bar-vertical>
      </div>
      </div>
  
</div>
