.container {
  display: flex;
  flex-direction: column;
  // align-items: center;
  // justify-content: center;
  padding: 20px;
  max-width: 100%; /* Adjust the maximum width as per your requirement */
  margin: 0 auto; /* Center horizontally */
  background-color: white;
}

.btn-link {
  color: #6c757d;
   text-decoration: none;
   font-size: 15px;
 }
 
 .btn-link.active {
   color: #28A4E2;
   font-weight: 500;
   border-bottom: 3px solid #28A4E2;
};
.filter, .brand-filter {
  margin-bottom: 20px; /* Space below the filter */
  
}
.table-title{
  font-size: 16px;
  color: #28A4E2;
}
.filter select, .brand-filter select {
  padding: 10px 10px; /* Padding inside the select */
  font-size: 15px; /* Font size */
  border: 1px solid #ccc; /* Border color */
  border-radius: 4px; /* Rounded corners */
  background-color: white; /* Background color */
  cursor: pointer; /* Cursor style */
}

.charts-row {
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Adjust alignment as needed */
  width: 100%;
  gap: 15px; /* Space between the charts */
}

.chart {
  border-radius: 8px; /* Rounded corners */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Box shadow for better visual */
  padding: 10px;
  box-sizing: border-box; /* Ensure padding doesn't affect width */
}

// ngx-charts-pie-chart, ngx-charts-bar-vertical {
   
// }
