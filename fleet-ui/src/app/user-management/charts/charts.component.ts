import { Component, OnInit } from '@angular/core';
import { environment } from '../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';

interface CarData {
  vehicleType: string;
  count: number;
}

interface ModelData {
  model: string;
  count: number;
}

@Component({
  selector: 'app-charts',
  templateUrl: './charts.component.html',
  styleUrls: ['./charts.component.scss']
})

export class ChartsComponent implements OnInit {
  institutionId: string = '';
  userRole: string = '';
  institutionName: any;
  selectedOwnershipType: string = 'Government Vehicles';
  selectedOwnershipType2: string = 'Government Vehicles';
  selectedvehicleType: string = 'Motorcyle';
  ownerships = ['Government Vehicles', 'Project Vehicles', 'Co-owned'];
  vehicleTypes = [];
  selectedYear: number = new Date().getFullYear();
  years = Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i);
  chartData2: any[] = [];
  returnedVehicles: any[] = [];
  data: { [key: string]: CarData[] } = {
    
  };
 
  onYearChange(): void {
    console.log(`Year changed to: ${this.selectedYear}`);
    this.fetchDataFromAPI();
  }
  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id; // Store institution ID
      this.userRole = parsedObj.data.user.role.name; // Store user role
      this.institutionName = parsedObj.data.user.institution.name;
    }
  }
  activeTab: string = 'availableVehicles'; 
setActiveTab(tab: string) {
  this.activeTab = tab;
}
  fetchDataFromAPI(): void {
    const url = `${environment.otherUrl}/gettingVehicleByFilter`;
    const requestBody = {
      searchString: "",
      beneficiaryAgency: "",
      reportingInstitution: "",
      ownershipType: this.selectedOwnershipType,
      vehicleType: "",
      vehicleModel: "",
      registrationStatus: "",
      approvalLevel: "",
      vehicleStatus: "",
      isVehicleActive: "",
      vehicleManufacture: "",
      chassisNumber: "",
      pickCardNumber: "",
      plateNumber: "",
      invoiceNumber: "",
      manufacturingYearStart: "",
      manufacturingYearEnd: "",
      acquisitionDateStart: "",
      acquisitionDateEnd: "",
      projectStartYearStart: "",
      projectStartYearEnd: "",
      projectEndYearStart: "",
      projectEndYearEnd: ""
    };

    this.http.post<any>(url, requestBody).pipe(
      map(response => {
        console.log('API Response:', response); // Log the API response

        if (response && Array.isArray(response.vehicles)) {
          // Process the response if it contains a "vehicles" array
          let filteredVehicles = response.vehicles.filter((vehicle: any) => {
            return vehicle.ownershipType && vehicle.ownershipType.name === this.selectedOwnershipType;
            
          });
          filteredVehicles = filteredVehicles.filter((vehicle: { isDisposalRequestSubmitted: boolean; registrationStatus: { id: string; name: string; }; }) => 
            !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c') &&
            vehicle.registrationStatus?.name === 'APPROVED'
          );

          // Count vehicles by active/inactive status
          const activeCount = filteredVehicles.filter((vehicle: any) => vehicle.isVehicleActive).length;
          const inactiveCount = filteredVehicles.filter((vehicle: any) => !vehicle.isVehicleActive).length;

          // Count vehicles by vehicleType
          const vehicleTypeCounts = filteredVehicles.reduce((acc: { [x: string]: number }, curr: { vehicleType: { name: any } }) => {
            const vehicleType = curr.vehicleType?.name;
            if (vehicleType) {
              acc[vehicleType] = acc[vehicleType] ? acc[vehicleType] + 1 : 1;
            }
            return acc;
          }, {});

          const vehicleTypeCountsByYear = filteredVehicles.reduce((acc: { [x: string]: number }, curr: any) => {
            const vehicleType = curr.vehicleType?.name;
            const manufacturingYear = curr.manufacturingYear;
            const projectExtensionDate = curr.projectExtensionDate;

            if (vehicleType) {
              let shouldInclude = false;
              if (this.selectedOwnershipType === 'Government Vehicles' && (new Date(manufacturingYear).getFullYear()) === this.selectedYear - 5) {
                shouldInclude = true;
              } else if (this.selectedOwnershipType === 'Project Vehicles' && new Date(projectExtensionDate).getFullYear() === this.selectedYear) {
                shouldInclude = true;
              }

              if (shouldInclude) {
                acc[vehicleType] = acc[vehicleType] ? acc[vehicleType] + 1 : 1;
              }
            }
            return acc;
          }, {});

          return { vehicles: filteredVehicles, activeCount, inactiveCount, vehicleTypeCounts, vehicleTypeCountsByYear };
        } else {
          // Handle non-array response or missing "vehicles" key
          console.error('Unexpected API response structure:', response);
          return { vehicles: [], activeCount: 0, inactiveCount: 0, vehicleTypeCounts: {}, vehicleTypeCountsByYear: {} };
        }
      })
    ).subscribe(data => {
      console.log('Processed Data:', data); // Log the processed data

      // Update data object with vehicles and counts
      this.data[this.selectedOwnershipType] = data.vehicles;

      // Update pie chart data for active/inactive
      this.updateActiveInactiveChart(data.activeCount, data.inactiveCount);

      // Update data for vehicle type breakdown chart
      this.chartData = Object.keys(data.vehicleTypeCounts).map(key => ({
        name: key,
        value: data.vehicleTypeCounts[key]
      }));
      this.chartData2 = Object.keys(data.vehicleTypeCountsByYear).map(key => ({
        name: key,
        value: data.vehicleTypeCountsByYear[key]
      }));

      console.log('Chart Data:', this.chartData);
      console.log('Chart Data 2:', this.chartData2);

      // Call updateChart method to reflect changes
      this.updateChart();
    });
  }

  //the function to help update the charts one the has to set up
  updateChart(): void {
    if (this.selectedOwnershipType) {
      const ownershipData = this.data[this.selectedOwnershipType as keyof typeof this.data];
      if (ownershipData) {
        this.chartData = ownershipData.map((d: any) => ({
          name: d.vehicleType.name,
          value: d.count[d.vehicleType.name] || 0 
        }));
        
      }else {
        // Handle case where no data is available for the selected ownership type
        this.chartData = [];
      }

      if (this.selectedvehicleType) {
        this.updatePieChart2();
        this.updateChart2();
      }
    }
  }
  updateChart2(): void {
    const ownershipData = this.data[this.selectedOwnershipType];
    if (ownershipData) {
      const vehicleTypeCountsByYear = ownershipData.reduce((acc: { [x: string]: number }, curr: any) => {
        const vehicleType = curr.vehicleType?.name;
        if (vehicleType) {
          let shouldInclude = false;
          if (this.selectedOwnershipType === 'Government Vehicles' && curr.manufacturingYear && (new Date(curr.manufacturingYear).getFullYear() ) === this.selectedYear - 5) {
            shouldInclude = true;
          } else if (this.selectedOwnershipType === 'Project Vehicles' && curr.projectExtensionDate && new Date(curr.projectExtensionDate).getFullYear() === this.selectedYear) {
            shouldInclude = true;
          }

          if (shouldInclude) {
            acc[vehicleType] = acc[vehicleType] ? acc[vehicleType] + 1 : 1;
          }
        }
        return acc;
      }, {});

      this.chartData2 = Object.keys(vehicleTypeCountsByYear).map(key => ({
        name: key,
        value: vehicleTypeCountsByYear[key]
      }));

      console.log('Updated Chart Data 2:', this.chartData2);
    }
  }
  
  updatePieChart2(): void {
    if (this.selectedOwnershipType && this.selectedvehicleType) {
      const vehicleTypeData = this.models[this.selectedvehicleType];
      if (vehicleTypeData && vehicleTypeData[this.selectedOwnershipType]) {
        const totalCars = this.chartData.find(d => d.name === this.selectedvehicleType)?.value || 0;
        this.pieChartData2 = vehicleTypeData[this.selectedOwnershipType].map((d: ModelData) => ({
          name: d.model,
          value: d.count || 0 // Default to 0 if count is undefined or null
        }));
      }
    }
  }
  
  customColors = [
    { name: 'Active', value: '#3BB143' }, // Example color for Active
    { name: 'Inactive', value: '#FF0000' } // Example color for Inactive
  ];
  updateActiveInactiveChart(activeCount: number, inactiveCount: number): void {
    this.pieChartData1 = [
      { name: 'Active', value: activeCount },
      { name: 'Inactive', value: inactiveCount },
    ];
  }
  
  
  models: { [key: string]: { [key: string]: ModelData[] } } = {
    'Toyota': {
      'Government Vehicles': [{ model: 'Corolla', count: 8 }, { model: 'Camry', count: 5 }],
      'Project Vehicles': [{ model: 'Corolla', count: 3 }, { model: 'Camry', count: 7 }]
    },
    'Ford': {
      'Government Vehicles': [{ model: 'Fiesta', count: 10 }, { model: 'Focus', count: 6 }],
      'Project Vehicles': [{ model: 'Fiesta', count: 4 }, { model: 'Focus', count: 6 }]
    },
    'Chevrolet': {
      'Government Vehicles': [{ model: 'Malibu', count: 4 }, { model: 'Impala', count: 4 }],
      'Project Vehicles': [{ model: 'Malibu', count: 3 }, { model: 'Impala', count: 5 }]
    },
    'Nissan': {
      '2020': [{ model: 'Altima', count: 3 }, { model: 'Sentra', count: 3 }],
      '2021': [{ model: 'Altima', count: 2 }, { model: 'Sentra', count: 4 }]
    },
    'Hyundai': {
      '2020': [{ model: 'Elantra', count: 5 }, { model: 'Sonata', count: 4 }],
      '2021': [{ model: 'Elantra', count: 6 }, { model: 'Sonata', count: 3 }]
    },
    'Honda': {
      '2021': [{ model: 'Civic', count: 10 }, { model: 'Accord', count: 5 }]
    },
    'BMW': {
      '2021': [{ model: '3 Series', count: 5 }, { model: '5 Series', count: 5 }]
    },
    'Mercedes': {
      '2021': [{ model: 'C-Class', count: 3 }, { model: 'E-Class', count: 2 }]
    },
    'Audi': {
      '2021': [{ model: 'A4', count: 4 }, { model: 'A6', count: 3 }]
    },
    'Volkswagen': {
      '2021': [{ model: 'Golf', count: 5 }, { model: 'Passat', count: 3 }]
    }
  };
// Method to fetch and filter returned vehicles based on selected ownership type
fetchReturnedVehicles() {
  if (!this.institutionId || !this.userRole) {
    console.error('Institution ID or User Role not found');
    return;
  }

  const url = `${environment.otherUrl}/allReturnedVehicles`;

  this.http.get<any[]>(url).subscribe(
    (response: any[]) => {
      console.log('Returned Vehicles API Response:', response);

      // Filter the returned vehicles by the selected ownership type
      this.returnedVehicles = response.filter(vehicle => 
        vehicle.ownershipType && vehicle.ownershipType.name === this.selectedOwnershipType2 // Updated to use selectedOwnershipType2
      );
      this.updateReturnedVehiclesChart();
    },
    (error) => {
      console.error('Error fetching returned vehicles data:', error);
    }
  );
}

// Method to update the chart data for returned vehicles
updateReturnedVehiclesChart(): void {
  const vehicleTypeCounts = this.returnedVehicles.reduce((acc: { [key: string]: number }, vehicle: any) => {
    const vehicleType = vehicle.vehicleType?.name;
    if (vehicleType) {
      acc[vehicleType] = acc[vehicleType] ? acc[vehicleType] + 1 : 1;
    }
    return acc;
  }, {});

  this.chartDataReturnedVehicles = Object.keys(vehicleTypeCounts).map(key => ({
    name: key,
    value: vehicleTypeCounts[key]
  }));

    console.log('Chart Data for Returned Vehicles:', this.chartDataReturnedVehicles);
  }
  
  chartDataReturnedVehicles: any[] = [];
  
  
  chartData: any[] = [];
 
  pieChartData1: any[] = [];
  pieChartData2: any[] = [];

  constructor(private http:HttpClient){ }

  ngOnInit(): void {
     this.getUserDetails(); 
    this.fetchDataFromAPI();
    this.fetchReturnedVehicles();
  }
}