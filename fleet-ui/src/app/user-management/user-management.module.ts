import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { UsersComponent } from './users/users.component';
import { SharedModule } from '../shared/shared.module';
import { DashboardComponent } from './dashboard/dashboard.component';
import { UserManagementRoutingModule } from './user-management-routing.module';
import { InstitutionComponent } from './institution/institution.component';
import { ProfileComponent } from './profile/profile.component';
import { CardsComponent } from './cards/cards.component';
import { ChartsComponent } from './charts/charts.component';

@NgModule({
  declarations: [UsersComponent, DashboardComponent, InstitutionComponent, ProfileComponent, CardsComponent, ChartsComponent],
  imports: [
    CommonModule,SharedModule, UserManagementRoutingModule,NgxChartsModule
  ]
})
export class UserManagementModule { }

