import { Component, OnInit, ViewChild} from '@angular/core';
import { faUsers, faCar, faInstitution,faArrowRight, faUser, IconDefinition ,faCalendar, faClock, faArrowTrendUp, faSearch, faCaretLeft, faCaretRight, faC } from '@fortawesome/free-solid-svg-icons';
import { User } from '../../models/user.interface';
import { HttpClient } from '@angular/common/http';
import { InstitutionComponent } from '../institution/institution.component';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { LoaderService } from '../../shared/loader.service';
interface Item{
 // icon: IconDefinition;
  title: string;
  count1: number;
  count2: number;
  filter2:string;
  icon: IconDefinition; 

}
interface Item2{
  icon: IconDefinition;
  title: string;
  count: number;
  filter: string;
   
 }
interface TodoItem {
  title: string;
  date: string;

}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  users: User[] = [];
  usersCount: number = 0;
  adminCount: number = 0;
  items:Item[] = [];
  items2:Item2[] = [];
  role:String=' ';
  isAdmin: boolean = false;
  isFirstLogin: boolean = false;
  showPopup: boolean = false;
  requests: any[] = []; 
  filteredAcquisitions: any[] = [];
  searchText: string = '';
  institutionId: string = ''; 
  acquisitions: any[] = [];
  displayedVehicles: any[] = [];
  registeredVehicles: any[] = [];
  filteredRegisteredVehicles: any[] = [];
   filteredUsers: User[] = [];;
  disposals: any[] = []; 
  filteredDisposals: any[] = []; 
  displayedDisposals: any[] = []; 
  calendarIcon = faCalendar;
  clockIcon = faClock;
  trendIcon = faArrowTrendUp;
  searchIcon = faSearch;
  carIcon= faCar;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  faArrowRight=faArrowRight;
  displayedAcquisitions: any[] = []; 
  sortField: string = 'fullName';
  sortDirection: string = 'asc';
  @ViewChild(InstitutionComponent) institutionComponent!: InstitutionComponent;
  beneficiaryAgency: any;
  vehicleCount: any;
  beneficiaryAgencyId: any;
  count1: number = 0; 
  count2: number = 0;
  count3:number = 0;
  count4: number = 0; 
  count5: number = 0;
  count6: number = 0;
  institutionName: any;
  showReportingVehiclesTab: boolean = false;
  reportingVehicles: any[] = [];
  institutionalVehicles: any[]=[];
  maincount1: number = 0;
  maincount2: number = 0;
  maincount3: number = 0;
  maincount4: number = 0;
  userscount1: number=0;
  allusers: number=0;
  userscount2: number=0;
  userscount3: number=0;
  allusers2: number=0;
  userscount4: number=0;
  noNewDisposals: boolean = false;
  displayedUsers: any[] = []; 
  institutions: any[]=[];
  institutionCount:number=0;
  constructor(private http:HttpClient,private router: Router,public loaderService: LoaderService) { }
  
  ngOnInit() {
    this.updateDisplayedData();
    this.getUserData();
    this.fetchUsers(); 
    this.fetchAllVehicles();
    this.fetchAllInstitutions();
    this.fetchAcquisitions();
    this.isLogistics();
    this.isLogisticsOrCBM();
    this.fetchRegisteredVehicles(); 
    this.fetchVehicleCountsByBeneficiaryAgency();
    this.fetchReportingVehiclesCount();
    this.fetchItemsBasedOnRole();
    this.fetchAllDisposals(); 
    this.fetchAllUsersBasedOnInstitution();
}
getUserData(){
  const data = localStorage.getItem('localUserData');
  if(data !== null){
    const parseObj = JSON.parse(data);
    this.institutionId = parseObj.data.user.institution.id;
    this.institutionName=parseObj.data.user.institution.name;
    console.log(this.institutionName)
    this.role = parseObj.data.user.role.name;
   // this.beneficiaryAgencyId = parseObj.data.user.beneficiaryAgency.id;
  }
}
isUserAdmin():boolean {
  const data = localStorage.getItem('localUserData');
  if(data !=null){
    const parseObj = JSON.parse(data);
    const roleName = parseObj?.data?.user?.role?.name;
    return roleName === 'ADMIN' 
  }
  return false;
}

getCurrentInstitutionId(): number {
  const data = localStorage.getItem('localUserData');
  if (data) {
      const parseObj = JSON.parse(data);
      return parseObj?.data?.user?.institution.id; // Adjust based on your data structure
  }
  return 0;
}
fetchUsers() {
  const url = `${environment.baseUrl}/user-management/users`
  this.http.get<User[]>(url)
    .subscribe(users => {
      this.users = users.map(user => ({
        ...user,
        fullName: user.firstName + ' ' + user.lastName,
        status: user.isEmailValid ? 'Active' : 'Inactive' 
      }));
      if (this.isUserAdmin()) {
        // If Admin, show all users with the same institution ID
        const institutionId = this.getCurrentInstitutionId(); // Implement this method based on your logic
        this.filteredUsers = this.users.filter(user => user.institution.id === institutionId);
      } else {
        // For non-admins, show users without filtering
        this.filteredUsers = this.users;
      }
      this.allusers = this.filteredUsers.length;
      const activeUsers = this.filteredUsers.filter(user => user.status === 'Active').length;

      // Filter for 'Inactive' users based on the new status
      const inactiveUsers = this.filteredUsers.filter(user => user.status === 'Inactive').length;

      // Assign the counts to the respective variables
      this.userscount1 = activeUsers;
      this.userscount2 = inactiveUsers;
      this.fetchItemsBasedOnRole();
    
    });
    this.updateDisplayedUsers()
}  
shouldShowUsersTable(): boolean {
  return this.role === 'SUPER-ADMIN' || this.role === 'ADMIN';
}

  searchUsers() {
    this.displayedUsers = this.filteredUsers.filter(user => {
        const searchTextLower = this.searchText.toLowerCase();
        return (
            user.identification.toLowerCase().includes(searchTextLower) ||
            user.fullName?.toLowerCase().includes(searchTextLower) || 
            user.role.name.toLowerCase().includes(searchTextLower) ||  
            (user.phoneNumber && user.phoneNumber.toString().includes(searchTextLower)) ||
            user.email.toLowerCase().includes(searchTextLower)
        );
    });
}
  sortTable(event: any){
    const selectedField: string = event.target.value;
    
    if(!selectedField){
      this.sortField = '';
      this.sortDirection = 'asc';
      this.filteredUsers = this.users;
      return;
    }

    if(this.sortField === selectedField) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = selectedField;
      this.sortDirection = 'asc';
    }
    
    this.filteredUsers = this.users.sort((a, b) => {
      const isAsc = this.sortDirection === 'asc';
      switch (this.sortField) {
        case 'fullName':
          return compare(a.fullName, b.fullName, isAsc);
        case 'role':
          return compare(a.role.name, b.role.name, isAsc);
        case 'status':
          return compare(a.status, b.status, isAsc);
        default:
          return 0; 
      }
    });
  };

  todoList: TodoItem[] = [
    { title: 'New Recent Activity ! Check It out', date: '20/04/2024' },
    { title: 'New Recent Activity ! Check It out', date: '20/04/2024'},
    { title: 'New Recent Activity ! Check It out', date: '20/04/2024' },
    { title: 'New Recent Activity ! Check It out', date: '20/04/2024'},
    { title: 'New Recent Activity ! Check It out', date: '20/04/2024' },
    
  ]
  checkroleAndLocationAvailability(): void {
    const userData = localStorage.getItem('localUserData');
    if (userData) {
      const user = JSON.parse(userData).data.user;
      this.isAdmin = user.role.name === 'ADMIN';
      this.isFirstLogin = this.isAdmin && !user.institution.isLocationAvailable;
      this.showPopup = this.isFirstLogin;
    }
  }
  

  togglePopup(): void {
    if (!this.showPopup) {
        this.checkroleAndLocationAvailability(); 
    } else {
        this.closePopup();
    }
}

  closePopup(): void {
    this.showPopup = false;
    this.institutionComponent.resetForm(); 
  }
  
  fetchReportingVehiclesCount() {
    if (!this.institutionId) {
      console.error('Institution ID not found');
      return;
    }
  
    const url = `${environment.otherUrl}/allVehiclesByRepInstitution/${this.institutionId}`;
    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log(response);
        const filteredVehicles = response.filter(
          vehicle => !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c')
            && vehicle.registrationStatus?.name === 'APPROVED'
        );
        console.log('Filtered Vehicles:', filteredVehicles);
        const governmentVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Government Vehicles').length;
        const projectVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Project Vehicles').length;
        const coOwnedVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Co-owned Vehicles').length;

        this.count4= governmentVehicles;
        this.count5 = projectVehicles;
        this.count6 = coOwnedVehicles;
        this.reportingVehicles = filteredVehicles;
        this.showReportingVehiclesTab = this.reportingVehicles.length > 0 && (this.role === 'Institutional logistics' || this.role === 'Institutional CBM');
        this.updateItemsBasedOnRole();
      },
      (error) => {
        console.error('Error fetching reporting vehicles:', error);
        this.showReportingVehiclesTab = false;
      }
    );
    this.updateItemsBasedOnRole();
  }

  fetchVehicleCountsByBeneficiaryAgency() {
    if (!this.institutionId) {
      console.error('Institution ID not found');
      return;
    }
  
    const url = `${environment.otherUrl}/allVehiclesbybeneficiaryAgencyId/${this.institutionId}`;
    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log(response);
        const filteredVehicles = response.filter(
          vehicle => !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c')
            && vehicle.registrationStatus?.name === 'APPROVED'
        );
        console.log('Filtered Beneficiary agencies Vehicles:', filteredVehicles);
        const governmentVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Government Vehicles').length;
        const projectVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Project Vehicles').length;
        const coOwnedVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Co-owned Vehicles').length;
        this.count1= governmentVehicles;
        this.count2 = projectVehicles;
        this.count3 = coOwnedVehicles;

        this.institutionalVehicles = filteredVehicles;
        this.showReportingVehiclesTab = this.institutionalVehicles.length > 0 && (this.role === 'Institutional logistics' || this.role === 'Institutional CBM');
        this.updateItemsBasedOnRole();
      },
      (error) => {
        console.error('Error fetching institutional vehicles:', error);
        this.showReportingVehiclesTab = false;
      }
    );
    this.updateItemsBasedOnRole();
  }


  updateItemsBasedOnRole() {
    switch (this.role) {
      case 'Institutional logistics':
      case 'Institutional CBM':
        this.items = [
          { title: 'Government Vehicles', count1: this.count1, count2: this.count4, filter2: 'gr'  ,icon:this.carIcon},
          { title: 'Project Vehicles', count1: this.count2, count2: this.count5, filter2: 'gp'  ,icon:this.carIcon},
          { title: 'Co-owned Vehicles', count1: this.count3, count2: this.count6, filter2: 'co-owned',icon:this.carIcon},
        ];
        break;
      default:
        console.error(this.role);
        break;
    }
  }
  fetchAllVehicles() {
    if (!this.institutionId) {
      console.error('Institution ID not found');
      return;
    }
  
    const url = `${environment.otherUrl}/allVehicles`;
    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log(response);
        const filteredVehicles = response.filter(
          vehicle => !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c')
            && vehicle.registrationStatus?.name === 'APPROVED'
        );
        this.maincount4= filteredVehicles.length;
        const governmentVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Government Vehicles').length;
        const projectVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Project Vehicles').length;
        const coOwnedVehicles = filteredVehicles.filter(vehicle => vehicle.ownershipType.name === 'Co-owned Vehicles').length;
        this.maincount1= governmentVehicles;
        this.maincount2 = projectVehicles;
        this.maincount3 = coOwnedVehicles;
        this.institutionalVehicles = filteredVehicles;
        this.fetchItemsBasedOnRole();
      },
      (error) => {
        console.error('Error fetching vehicles:', error);
      }
    );
    this.fetchItemsBasedOnRole();
  }


  
  fetchAllUsersBasedOnInstitution() {
    if (!this.institutionId) {
        console.error('Institution ID not found');
        console.log(this.institutionId);
        return;
    }

    const url = `${environment.baseUrl}/user-management/institution-users/${this.institutionId}`;
    this.http.get<User[]>(url).subscribe(
        (users) => {
            // Log the response to check its format
            console.log('Fetched users:', users);

            // Check if the response is an array
            if (Array.isArray(users)) {
                this.filteredUsers = users.map(user => ({
                    ...user,
                    fullName: `${user.firstName} ${user.lastName}`,
                    status: user.isEmailValid ? 'Active' : 'Inactive'
                }));

                // Update counts based on filteredUsers
                this.allusers2 = this.filteredUsers.length;
                const activeUsers = this.filteredUsers.filter(user => user.status === 'Active').length;
                const inactiveUsers = this.filteredUsers.filter(user => user.status === 'Inactive').length;

                // Assign the counts to the respective variables
                this.userscount3 = activeUsers;
                this.userscount4 = inactiveUsers;
            } else {
                console.error('Expected an array of users but received:', users);
            }

            // Call fetchItemsBasedOnRole after processing the users
            this.fetchItemsBasedOnRole();
        },
        (error) => {
            console.error('Error fetching users:', error);
        }
    );
}

  fetchAllInstitutions() {
    const url = `${environment.baseUrl}/user-management/institutions`;
    
    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log('All institutions:', response);
        this.institutions = response; // Store the institutions in a local variable
        this.institutionCount = this.institutions.length; // Get the length of the institutions array
        console.log('Number of institutions:', this.institutionCount);
      },
      (error) => {
        console.error('Error fetching institutions:', error);
      }
    );
  }
  
  fetchItemsBasedOnRole() {
    switch (this.role) {
      case 'SUPER-ADMIN':
        this.items2 = [
          { icon: faUsers, title: 'Total Users', count: this.allusers,filter: '/user-management/all-users'},
          { icon: faUsers, title: 'Active Users', count: this.userscount1,filter: '/user-management/all-users',  },
          { icon: faUsers, title: 'Inactive Users', count: this.userscount2,filter: '/user-management/all-users',  },
          { icon: faInstitution, title: 'Institutions', count: this.institutionCount ,filter: '/user-management/all-users', },
        ];
        break;
      case 'ADMIN':
        this.items2 = [
          { icon: faUsers, title: 'Total Users', count: this.allusers2, filter: `/user-management/institution-users/${this.institutionId}` },
          { icon: faUsers, title: 'Active Users', count: this.userscount3, filter: `/user-management/institution-users/${this.institutionId}`  }, // Assuming this is what you meant
          { icon: faUsers, title: 'Inactive Users', count: this.userscount4, filter: `/user-management/institution-users/${this.institutionId}`  }, // Assuming this is what you meant
          { icon: faInstitution, title: 'Institutions', count: 1, filter: `/user-management/institution-users/${this.institutionId}`  }, // Assuming this is what you meant
      ];
      
        break;
       
        case 'Fleet Mgt Senior Engineer':
        case 'DG Transport':
        case 'Permanent Secretary':
        case 'Minister':
          this.items2 = [
            { icon: faCar, title: 'Government Vehicles', count: this.maincount1, filter: 'gr' },
            { icon: faCar, title: 'Project Vehicles', count: this.maincount2, filter: 'gp' },
            { icon: faCar, title: 'Co-owned Vehicles', count: this.maincount3, filter: 'co-owned' },
            { icon: faCar, title: 'Total Vehicles', count: this.maincount4, filter: 'all' },
          ];    
        break;
      
      default:
        // console.error('Unrecognized user role');
        return;

    }
  }
isLogistics():boolean{
  const data = localStorage.getItem('localUserData');
  if(data !=null){
    const parseObj = JSON.parse(data);
    const roleName = parseObj?.data?.user?.role?.name;
    return roleName === 'Institutional logistics';
  }
  return false;
}
isLogisticsOrCBM(): boolean {
  return this.role === 'Institutional logistics' || this.role === 'Institutional CBM';
}
isSAdmin(): boolean {
  return this.role === 'ADMIN' || this.role === 'SUPER-ADMIN';
}
fetchAcquisitions() {
  if (!this.institutionId || !this.role) {
    console.error('Institution ID or User Role not found');
    return;
  }

  let url: string;
  switch (this.role) {
    case 'Institutional logistics':
    case 'Institutional CBM':
      url = `${environment.otherUrl}/AllAcquisitionsByInstitution?acquisitionId=${this.institutionId}`;
      break;

    case 'Fleet Mgt Senior Engineer':
    case 'DG Transport':
    case 'Permanent Secretary':
    case 'Minister':
      url = `${environment.otherUrl}/AllAcquisitions`;
      break;

    default:
      console.error('Unrecognized user role');
      return;
  }

  this.http.get<any[]>(url).subscribe(
    (response:any[]) => {
      this.acquisitions = response.filter(
        acquisition => !(acquisition.requestType.id === '0ad9916d-e5f8-4204-b5dd-e50fd13a4640' &&
                         acquisition.statusType.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c') &&
                        !(acquisition.isAllVehicleregrequestSubmitted === true && acquisition.statusType.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c')
      );  
      this.filterAcquisitionsByRole(); 
      this.updateDisplayedAcquisitions(); 
    },
    (error) => {
      console.error('Error fetching data:', error);
    }
  );
}

filterAcquisitionsByRole() {
    
  this.filteredAcquisitions = [...this.acquisitions]; 

  switch (this.role) {
    case 'Institutional logistics':
     
      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        acquisition =>
          acquisition.institutionId === this.institutionId &&
          acquisition.statusType.name  === 'APPROVED'
      );
      console.log("Filtered Acquisitions:", this.filteredAcquisitions);
      break;

    case 'Institutional CBM':

      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        acquisition =>
          acquisition.institutionId === this.institutionId &&
          acquisition.approvalLevel?.name === 'Institutional CBM'
      );
      
      break;

    case 'Fleet Mgt Senior Engineer':
    
      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        acquisition =>
        acquisition.approvalLevel?.name === 'Fleet Mgt Senior Engineer'
      );

      break;
      
    case 'DG Transport':
     
      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        acquisition =>
        acquisition.approvalLevel?.name === 'DG Transport'

      );
      break;

    case 'Permanent Secretary':
     
      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        acquisition =>
        acquisition.approvalLevel?.name === 'Permanent Secretary'

      );
      break;

    case 'Minister':
      
      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        acquisition => 
        acquisition.approvalLevel?.name === 'Minister' &&
        acquisition.statusType.name  === 'PROGRESS'
      );
      break;

    default:
      console.error('Unrecognized user role:', this.role);
      break;
  }
  //console.log("After filtering:", this.filteredAcquisitions); 
  if (this.filteredAcquisitions.length === 0) {
    this.noNewRequests = true; 
  } else {
    this.noNewRequests = false; 
  }
  this.updateDisplayedAcquisitions();
}
noNewRequests: boolean = false;
searchVehicles() {
  this.filterAcquisitionsByRole();
  if (this.searchText.trim() === '') {
    this.filteredAcquisitions = [...this.filteredAcquisitions];
  } else {
    this.filteredAcquisitions = this.filteredAcquisitions.filter(acquisition =>
      acquisition.institution.toLowerCase().includes(this.searchText.trim().toLowerCase()) ||
      acquisition.requestType.name.toLowerCase().includes(this.searchText.trim().toLowerCase()) ||
      acquisition.ownershipType.name.toLowerCase().includes(this.searchText.trim().toLowerCase()) ||
      acquisition.statusType.name.toLowerCase().includes(this.searchText.trim().toLowerCase())
    );
  }
}

viewAcquisition(acquisitionId: string): void {
  console.log('Acquisition ID:', acquisitionId);
  this.router.navigateByUrl(`/vehicle-management/acquisition-details/${acquisitionId}`).then(success => {
    if (success) {
      console.log('Navigation successful');
    } else {
      console.error('Navigation failed');
    }
  }).catch(error => {
    console.error('Error navigating:', error);
  });
}
activeTab: string = 'allAcquisitions'; 
setActiveTab(tab: string) {
  this.activeTab = tab;
}

getTabClass(tab: string): string {
  return this.activeTab === tab ? 'active-tab' : '';
}
navigateToRequestVehicle(): void {
  this.router.navigateByUrl('vehicle-management/request-vehicle');
}
getStatusButtonClass(status: string): string {
  switch (status.toLowerCase()) {
      case 'approved':
          return 'bg-success'; // Corresponds to green
      case 'pending':
          return 'bg-secondary'; // Corresponds to yellow/orange
      case 'denied':
          return 'bg-danger'; // Corresponds to red
      case 'rfac':
          return 'bg-info'; // Corresponds to blue
      case 'progress':
          return 'bg-warning'; // Corresponds to blue
      default:
          return ''; // Default or fallback class
  }
}
getProgressWidth(status: string): string {
switch (status.toLowerCase()) {
  case 'approved':
    return '100%'; // Corresponds to green
  case 'pending':
    return '70%'; // Corresponds to yellow/orange
  case 'denied':
    return '100%'; // Corresponds to red
  case 'rfac':
    return '60%'; // Corresponds to blue
  case 'progress':
    return '80%'; // Corresponds to blue
  default:
    return '';
}
}
registerVehicle(acquisitionId: string):void{
console.log("Acquisition Id: ", acquisitionId);
this.router.navigateByUrl(`/vehicle-management/register-vehicle/${acquisitionId}`).then(success =>{
  if(success){
    console.log('Navigation Successful');
  }else{
    console.error('Navigation Failed');
  }
}).catch(error => {
  console.error('Error navigating: ', error);
});
}
editAcquisition(acquisitionId: string): void{
  console.log("AcquisitionId: ",acquisitionId);
  this.router.navigateByUrl(`/vehicle-management/edit-requested-vehicle/${acquisitionId}`).then(success => {
    if (success) {
      console.log('Navigation successful');
    } else {
      console.error('Navigation failed');
    }
  }).catch(error => {
    console.error('Error navigating:', error);
  });
}
///registration

fetchRegisteredVehicles() {
  if (!this.institutionId || !this.role) {
    console.error('Institution ID or User Role not found');
    return;
  }

  let url: string;

  switch (this.role) {
    case 'Institutional logistics':
    case 'Institutional CBM':
      // Fetch registered vehicles by institution for Logistics and CBM roles
      url = `${environment.otherUrl}/allVehicles/${this.institutionId}`;
      break;

    case 'Fleet Mgt Senior Engineer':
    case 'DG Transport':
    case 'Permanent Secretary':
    case 'Minister':
      // Fetch all registered vehicles for other roles
      url = `${environment.otherUrl}/allVehicles`;
      break;

    default:
      console.error('Unrecognized user role');
      return;
  }

  this.http.get<any[]>(url).subscribe(
    (response: any[]) => {
      console.log(response);
      // const notDisposedVehicles = response.filter(
      //   (vehicle) => vehicle.isDisposalRequestSubmitted === false
      // );
      this.registeredVehicles = response
      .filter(
        vehicle => !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c')
      ); 
      this.filterVehiclesByRole(); 
      this.updateDisplayedVehicles(); 
    },
    (error) => {
      console.error('Error fetching registered vehicles:', error);
    }
  );
}
noNewRegistrations: boolean = false;
filterVehiclesByRole() {
  // Start with all registered vehicles
  this.filteredRegisteredVehicles = [...this.registeredVehicles];

  switch (this.role) {
      case 'Institutional logistics':
          // Show only vehicles for the current institution
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle => vehicle.vehicleRegRequest.institutionId === this.institutionId &&
              vehicle.registrationStatus.name === 'APPROVED'
          );
          break;
      
      case 'Institutional CBM':
          // Exclude vehicles with 'Institutional logistics' approval level
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
             
              vehicle => vehicle.vehicleRegRequest.institutionId === this.institutionId &&
              vehicle.approvalLevel?.name === 'Institutional CBM'
          );
          break;

      case 'Fleet Mgt Senior Engineer':
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
              vehicle.approvalLevel?.name === 'Fleet Mgt Senior Engineer'
          );
          break;

      case 'DG Transport':
          // Exclude multiple approval levels
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
              vehicle.approvalLevel?.name === 'DG Transport'
          );
          break;

      case 'Permanent Secretary':
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
              vehicle.approvalLevel?.name === 'Permanent Secretary'
          );
          break;
      
      case 'Minister':
         
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle => 
                  vehicle.approvalLevel?.name === 'Minister' &&
                  vehicle.registrationStatus.name !== 'APPROVED'
          );
          break;

      default:
          console.error('Unrecognized user role:', this.role);
          break;
  }
  if (this.filteredRegisteredVehicles.length === 0) {
    this.noNewRegistrations = true; 
  } else {
    this.noNewRegistrations = false; 
  }
  this.updateDisplayedVehicles(); // Update the displayed vehicles based on the current page
}

viewVehicle(vehicleId: string): void {
  console.log('Vehicle ID:', vehicleId);
  this.router.navigateByUrl(`/vehicle-management/vehicle-details/${vehicleId}`).then(success => {
    if (success) {
      console.log('Navigation successful');
    } else {
      console.error('Navigation failed');
    }
  }).catch(error => {
    console.error('Error navigating:', error);
  });
}
updateDisplayedData(): void {
  if (this.activeTab === 'allAcquisitions') {
    this.updateDisplayedAcquisitions();
  } else if (this.activeTab === 'registrations') {
    this.updateDisplayedVehicles();
  } 
  else if (this.activeTab === 'disposals') {
    this.updateDisplayedDisposals();
  }else{
    this.updateDisplayedUsers()
  }
}

fetchAllDisposals() {
  const url = `${environment.baseUrl}/disposal/allVehicleDisposal`;

  this.http.get<any[]>(url).subscribe(
    (response: any[]) => {
      // const notAuctioned = response.filter(
      //   (disposal) => disposal.isActionReportsubmitted === false
      // );
      this.disposals = response.filter(
        disposal => !(disposal.isActionReportsubmitted === true 
          && disposal.requestStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c' && disposal.disposalTypes.id === 'b6f69d06-c9b9-4f35-ba96-267a86c2a7de')
      ); 
      this.filterDisposalsByRole();
      this.updateDisplayedDisposals(); 
    },
    (error) => {
      console.error('Error fetching disposals:', error); 
    }
  );
  }
  filterDisposalsByRole() {
    this.filteredDisposals = [...this.disposals];
  
    switch (this.role) {
      case 'Institutional logistics':
            this.filteredDisposals = this.filteredDisposals.filter(
               
                disposal => disposal.vehicle.vehicleRegRequest.institutionId === this.institutionId &&
                disposal.requestStatus?.name === 'APPROVED' &&
                disposal.disposalTypes?.name == 'Public Auction'
            );
            break;
    case 'Institutional CBM':
            this.filteredDisposals = this.filteredDisposals.filter(
               
                disposal => disposal.vehicle.vehicleRegRequest.institutionId === this.institutionId &&
                disposal.approvalLevel?.name === 'Institutional CBM'
            );
            break;
  
        case 'Fleet Mgt Senior Engineer':
          this.filteredDisposals = this.filteredDisposals.filter(
           disposal =>
           disposal.approvalLevel?.name === 'Fleet Mgt Senior Engineer'
            );
            break;
  
        case 'DG Transport':
            // Exclude multiple approval levels
            this.filteredDisposals = this.filteredDisposals.filter(
              disposal =>
              disposal.approvalLevel?.name === 'DG Transport'
            );
            break;
  
        case 'Permanent Secretary':
          this.filteredDisposals = this.filteredDisposals.filter(
            disposal =>
            disposal.approvalLevel?.name === 'Permanent Secretary'
            );
            break;
        
        case 'Minister':
           
        this.filteredDisposals = this.filteredDisposals.filter(
          disposal =>
          disposal.approvalLevel?.name === 'Minister' &&
          disposal.requestStatus?.name !== 'APPROVED'
            );
            break;
  
        default:
            console.error('Unrecognized user role:', this.role);
            break;
    }
    if (this.filteredDisposals.length === 0) {
      this.noNewDisposals = true; 
    } else {
      this.noNewDisposals = false; 
    }
    this.updateDisplayedDisposals();
  }
  getDisposalStatusClass(requestStatus: any): string {
    if (requestStatus) {
      switch (requestStatus.name.toLowerCase()) {
        case 'approved':
          return 'status-registered';
        case 'pending':
          return 'status-pending'; 
        case 'denied':
          return 'status-denied'; 
        default:
          return '';
      }
    }
    return '';
  }
  viewDisposal(disposalId: string): void {
    console.log('Disposal ID:', disposalId);
    this.router.navigateByUrl(`/vehicle-management/disposal-details/${disposalId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  viewAuction(disposalId: string): void {
    this.router.navigateByUrl(`/vehicle-management/auction/${disposalId}`).then(success => {
      if(success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating: ', error);
    });
  }



pageSize: number = 5; // Number of items per page
currentPage: number = 1; // Current page
totalPages: number = 0; // Total number of pages
getFirstEntryIndex(): number {
  return (this.currentPage - 1) * this.pageSize + 1;
}

getLastEntryIndex(): number {
  const lastEntryIndex = this.currentPage * this.pageSize;
  if (this.activeTab === 'allAcquisitions') {
    return Math.min(lastEntryIndex, this.displayedAcquisitions.length);
  } else if (this.activeTab === 'registrations') {
    return Math.min(lastEntryIndex, this.filteredRegisteredVehicles.length);
  } 
  else if (this.activeTab === 'disposals') {
    return Math.min(lastEntryIndex, this.displayedDisposals.length);
  }else {
    return Math.min(lastEntryIndex, this.displayedUsers.length);
  }
}

goToPage(pageNumber: number) {
  if (pageNumber >= 1 && pageNumber <= this.totalPages) {
    this.currentPage = pageNumber;
    this.updateDisplayedData();
  }
}

getPageNumbers(): number[] {
  const pageNumbers = [];
  for (let i = 1; i <= this.totalPages; i++) {
    pageNumbers.push(i);
  }
  return pageNumbers;
}

previousPage() {
  if (this.currentPage > 1) {
    this.currentPage--;
    this.updateDisplayedData();
  }
}

nextPage() {
  if (this.currentPage < this.totalPages) {
    this.currentPage++;
    this.updateDisplayedData();
  }
}

updateDisplayedUsers() {
  const startIndex = (this.currentPage - 1) * this.pageSize;
  const endIndex = startIndex + this.pageSize;
  this.displayedUsers = this.filteredUsers.slice(startIndex, endIndex);
  this.totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
}

updateDisplayedVehicles() {
  const startIndex = (this.currentPage - 1) * this.pageSize;
  const endIndex = Math.min(startIndex + this.pageSize, this.filteredRegisteredVehicles.length);
  this.displayedVehicles = this.filteredRegisteredVehicles.slice(startIndex, endIndex);
  this.totalPages = Math.ceil(this.filteredRegisteredVehicles.length / this.pageSize);
}

updateDisplayedDisposals() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredDisposals.length);

    this.displayedDisposals = this.filteredDisposals.slice(startIndex, endIndex); // Update based on current page

    this.totalPages = Math.ceil(this.filteredDisposals.length / this.pageSize); // Recalculate total pages
  }
  updateDisplayedAcquisitions() {
  const startIndex = (this.currentPage - 1) * this.pageSize;
  const endIndex = Math.min(startIndex + this.pageSize, this.filteredAcquisitions.length); 
  this.displayedAcquisitions = this.filteredAcquisitions.slice(startIndex, endIndex);
  this.totalPages = Math.ceil(this.filteredAcquisitions.length / this.pageSize);
}
setTab(tab: string) {
  this.activeTab = tab;
  this.currentPage = 1; // Reset to first page
  this.updateDisplayedData();
}

}
function compare (a: string, b: string, isAsc: boolean):number {
  const comparison = a.localeCompare(b);
  return isAsc ? comparison : -comparison;
}


