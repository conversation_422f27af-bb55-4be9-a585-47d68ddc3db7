@import 'bootstrap/dist/css/bootstrap.min.css';

.dashboard{
    background-color: #D9D9D94D;
    width: 83%;
    position: absolute;
    right: 0;
    top: 10%;
    padding: 15px;
}
.main-card{
    margin-bottom: 20px;
}
a{
    text-decoration: none;
}
.checklist-item {
    position: relative;
    display: flex;
    padding-left: .75rem;
    justify-content: space-between;
    align-items: center;
}
.card-deck .card {
    margin-bottom: 30px;
}

.h3, h3 {
    font-size: 1.0625rem;
}
.checklist-item:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    content: '';
    border-radius: 8px;
    background-color: #5e72e4;
}
.card-stats .card-body {
    padding: 1rem 1.5rem;
}
.btn-link {
   color: #6c757d;
    text-decoration: none;
    font-size: 15px;
  }
  
  .btn-link.active {
    color: #28A4E2;
    font-weight: 500;
    border-bottom: 3px solid #28A4E2;
};
  
.card-body {
    padding: 1.5rem;
    flex: 1 1 auto;
}
.col {
    max-width: 100%;
    flex-basis: 0;
    flex-grow: 1;
}
h5 {
    font-size: .8125rem;
}
.h2, h2 {
    font-size: 1.25rem;
}
.card{
    background-color: #fff;
    border-radius: 12px;
    padding: 10px 0px;
}
.card-body .rounded-circle{
    background: linear-gradient(201.18deg, #D3FFE7 3.14%, #EFFFF6 86.04%);
    color: #28A4E2;
}
.card-title{
    font-size: 15px;
    
}
.card-title2{
    font-size: 20px;
}
.card-title3{
    font-size: 16px;
}

.card-2, .card-3{
    border-left: 2px solid #F0F0F0;
    border-right: 2px solid #F0F0F0;
}

.iconContainer {
    margin-right: 20px; /* Adjust the value as needed */
  }
  
  .details .count-and-title {
    margin-right: 30px; /* Adjust the value as needed */
  }
  

.calIcon{
    color: #FB7D5B;
}
.clockIcon{
    color: #FCC43E;
}
.trend-container{
    background-color: #28A4E2;
    color: #ffffff;
    font-size: 18px;
}
.request-id{
    font-size: 12px;
    color: #A098AE;
}
.institution{
    color: #757575;
}
.text-completed {
    color: #4CBC9A;
  }
  
  .text-pending {
    color: #A098AE;
  }
  
  .text-canceled {
    color: #FF0000;
  }
  .search-group{
    background-color: #ffffff;
    border-radius: 10px;
    width: 23%;
    height: 40px;
    border: 1px solid #edebf0;
    margin-left: 23%;
}
.icon{
    background-color: #ffffff;
    color: #28A4E2;
    font-size: 13px;
    border-radius: 10px;
    padding: 9px 0px 7px 9px !important;
    text-align: center;
}
.global-input{
    color: #A098AE;
    background-color: #fff;
    border-radius: 10px;
    font-size: 13px;
}
.sorting-group{
    font-size: 12px;
    background-color: #F9FBFF;
    width: 20%;
    height: 10%;
    border-radius: 10px;
}
.select{
    font-size: 12px;
    font-weight: bold;
    width: 60%;
    border: none !important;
    background-color: #F9FBFF;
    outline: none !important;
}
.table-container{
    background-color: #ffffff;
}
.table-title{
    font-size: 16px;
    color: #28A4E2;
}
.card{
    border: none;
}
th{
    font-size: 12px;
    color: #252525 !important;
    line-height: 21px;
    font-weight: 600;
}
.table-container td{
    color: #757575 !important;
    font-size: small; 
}
.status-active{
    color: rgb(33, 190, 33);
    font-weight: 500;
    font-size: 13px;
}
.status-inactive{
    color: #DF0404;
    font-weight: 500;
    font-size: 13px;
}
.table-body.empty {
    display: none;
  }
.nav{
    padding: 0px 20px;
}
.nav li .pages{
    height: 30px;
    width: 30px;
    border-radius: 50%;
    font-size: 12px;
    padding: 5px;
}
.nav li{
    margin: 0px 5px;
}
.caret{
    background-color: #ffffff;
    border: none;
    font-size: 20px;
    color: #A098AE;
    padding: 5px 0px;
}
.pagination-info{
    font-size: 14px;
    color: #B5B7C0;
}
.left-container{
    width: 70%;
    height: 400px;
}
.right-container{
    width: 30%;
}
@media (min-width: 576px){
    .card-deck .card {
       margin-right: 15px;
       margin-bottom: 0;
       margin-left: 15px;
       flex: 1 0;
   }   
   }
@media screen and (max-width: 991px) {
    .dashboard{
        width: 95%;
        right: 0;
        top: 7%;
        // z-index: -1;
    }
    .left-container{
        width: 100%;
    }
    .main-card{
        display: flex;
        flex-direction: column !important;
       
    }
    .card-body{
        display: flex;
        padding-left: 50px;
    }
    .card-2, .card-3{
        border-bottom: 2px solid #F0F0F0;
        border-top: 2px solid #F0F0F0;
        border-left: none;
        border-right: none;
    }
    .cards-container {
        display: flex;
        flex-direction: column !important;
        height: 1000px;
    }
    
    .table-container{
        height: 90% !important;
        
    }
    .table-header {
        justify-content: space-between;
    }
    .table-header .search-group{
        display: none !important;
    }
    .sorting-group{
        width: 50%;
        margin-right: 0;
    }
    .table-only{
        overflow: auto;
        white-space: nowrap;
    }
    .table-only::-webkit-scrollbar {
        width: 0;  
    }
}
