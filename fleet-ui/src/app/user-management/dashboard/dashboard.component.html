<!-- <div *ngIf="loaderService.isLoading | async" class="loader">Loading...</div> -->
<!-- <div *ngIf="loaderService.isLoading | async" class="loader">Loading...</div> -->
<div class="dashboard-container d-flex">
  <app-side-bar class="sidebar"></app-side-bar>
  <app-top-nav class="topnav"></app-top-nav> 
  <div class="dashboard">
    <!-- <app-breadcrumb></app-breadcrumb> -->
    <div *ngIf="isLogisticsOrCBM()" class="card w-100 d-flex flex-row justify-content-between main-card">
      <div class="card-body d-flex flex-row align-items-center card-{{ index + 1 }}" *ngFor="let item of items; let index = index">
      
        <div class="iconContainer d-flex justify-content-center align-items-center rounded-circle" style="width: 60px; height: 60px;">

          <fa-icon [icon]="item.icon" size="2x"></fa-icon>
        </div>
    
        <div class="details">
          <h5 class="card-title text-secondary">{{ item.title }}</h5>
          <div class="count-and-details">
            <div class="d-flex flex-row justify-content-between">
              <div class="count-and-title me-4">
                <h2 class="card-title fw-bold">{{ item.count1 }}</h2>
                <p class="card-text">Owned</p>
              </div>
              <div class="count-and-title">
                <h2 class="card-title fw-bold">{{ item.count2 }}</h2>
                <p class="card-text">Reporting</p>
              </div>
            </div>
            <div class="view-details-div mt-2">
              <a [routerLink]="['/vehicle-management/reports']" [queryParams]="{ filter: item.filter2 }" class="view-details">View Details <fa-icon [icon]="faArrowRight"></fa-icon></a>
            </div>
          </div>
        </div>
      </div>
    </div>
    

    
    <div *ngIf="!isLogisticsOrCBM()" class="card w-100 d-flex flex-row justify-content-between main-card">
      <div class="card-body d-flex flex-column align-items-start card-{{index + 1}}" *ngFor="let item of items2; let index = index">
          <div class="d-flex align-items-center">
              <div class="iconContainer d-flex justify-content-center align-items-center rounded-circle me-3" style="width: 50px; height: 50px;">
                  <fa-icon [icon]="item.icon"></fa-icon>
              </div>
              <div class="details me-2">
                  <h5 class="card-title fw-bold">{{ item.count}}</h5>
                  <p class="card-text">{{ item.title }}</p>
                  <div *ngIf="!isSAdmin()" class="mt-2">
                    <a [routerLink]="['/vehicle-management/reports']" [queryParams]="{ filter: item.filter }" class="view-details">View Details <fa-icon [icon]="faArrowRight"></fa-icon></a>         
                 </div>
              </div>
          </div>
        </div>
      </div>
      <div *ngIf="!isLogisticsOrCBM() && !isSAdmin()">
          <app-charts></app-charts>
      </div>
     
      <div class="row mt-4">
          <div class="col-xl-8 left-container" style="width: 70%; height: 400px;">
              <app-cards></app-cards> 
              <div *ngIf="!shouldShowUsersTable()" class="card table-container d-flex flex-column p-2">       
                  <div class="table-header d-flex flex-row p-3">
                      <h2 class="table-title">Vehicles Management</h2>
                  </div>    
                  <div class="d-flex justify-content-start mb-1">
                      <button class="btn btn-link" [ngClass]="{'active': activeTab === 'allAcquisitions'}" (click)="setTab('allAcquisitions')">
                        Acquisitions <span class="badge" [ngClass]="{'bg-secondary': activeTab !== 'allAcquisitions', 'bg-primary': activeTab === 'allAcquisitions'}">{{ filteredAcquisitions.length }}</span>
                      </button>
                      <button *ngIf="role!== 'Institutional logistics'" class="btn btn-link" [ngClass]="{'active': activeTab === 'registrations'}" (click)="setTab('registrations')">
                        Registrations <span class="badge" [ngClass]="{'bg-secondary': activeTab !== 'registrations', 'bg-primary': activeTab === 'registrations'}">{{filteredRegisteredVehicles.length}}</span>
                      </button>
                      <button class="btn btn-link" [ngClass]="{'active': activeTab === 'disposals'}" (click)="setTab('disposals')">
                        Disposals <span class="badge" [ngClass]="{'bg-secondary': activeTab !== 'disposals', 'bg-primary': activeTab === 'disposals'}">{{filteredDisposals.length}}</span>
                      </button>
                      <!-- <div class="search-group d-flex">
                        <fa-icon [icon]="searchIcon" class="icon"></fa-icon>
                        <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchVehicles()">
                      </div> -->
                    </div>
                    
              <div *ngIf="activeTab === 'allAcquisitions' &&  noNewRequests " class="text-center text-success fw-bold mb-5 mt-3">No New Acquisitions To Approve!!!</div>
              <div *ngIf="activeTab === 'allAcquisitions' && !noNewRequests" class="table-only p-3">
                <table class="table table-stripped">
                  <thead>
                      <tr>
                          <th *ngIf="!isLogisticsOrCBM()" sortable="institution">Institution</th>
                          <th sortable="requestType">Type of Request</th>
                          <th sortable ="ownershipType">Type of ownership</th>
                          <th sortable = "status">Status</th>
                          <th>Action</th>
                      </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of displayedAcquisitions">
                      <td *ngIf="!isLogisticsOrCBM()">{{ item.institution }}</td>
                      <td>{{ item.requestType.name }}</td>
                      <td>{{ item.ownershipType.name }}</td>
                      <td>
                        <div class="progress">
                            <div class="progress-bar"
                                 [ngClass]="getStatusButtonClass(item.statusType.name)"
                                 role="progressbar"
                                 [style.width]="getProgressWidth(item.statusType.name)" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                                {{ item.statusType.name }}
                            </div>
                        </div>
                    </td>  
                    <td class="d-flex flex-row"> 
                      <button 
                      class="btn btn-outline-primary view-btn btn-sm" 
                      (click)="viewAcquisition(item.id)"
                      *ngIf="role !== 'Institutional logistics'"
                    >
                      View
                    </button>
                    <button *ngIf="item.approvalLevel.name == 'Minister' && item.statusType.name == 'APPROVED' && role === 'Institutional logistics' && item.isAllVehicleregrequestSubmitted === false" class="btn register-btn btn-outline-primary view-btn btn-sm" (click)="registerVehicle(item.id)">Register</button>
                    <!-- <button *ngIf="item.approvalLevel.name == 'Institutional logistics' && item.statusType.name == 'RFAC' && role === 'Institutional logistics'" type="button" class="btn btn-success" style="margin: 0px 14px;" (click)="editAcquisition(item.id)"> Edit </button> -->
                  </td>
                    </tr>                          
                  </tbody>
              </table>
              <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                <div class="pagination-info">
                  Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredAcquisitions.length }} entries
                </div>
                <ul class="pagination justify-content-center">
                  <li class="page-item">
                    <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                      <fa-icon [icon]="caretLeft"></fa-icon>
                    </button>
                  </li>
                  <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                    <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                      {{ pageNumber }}
                    </button>
                  </li>
                  <li class="page-item">
                    <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                      <fa-icon [icon]="caretRight"></fa-icon>
                    </button>
                  </li>
                </ul>
              </nav>               
              </div>
              <div *ngIf="activeTab === 'registrations' &&  noNewRegistrations " class="text-center text-success fw-bold mb-5 mt-3">No New Vehicle Registrations To Approve!!!</div>
              <div *ngIf="activeTab === 'registrations' && !noNewRegistrations" class="table-only p-3">
                  <table class="table table-stripped">
                      <thead>
                        <tr>
                          <th>Chassis Number</th>
                          <th>Ownership Type</th>
                          <th>Beneficiary Agency</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let vehicle of displayedVehicles">
                          <td>{{ vehicle.chassisNumber }}</td>                          
                          <td>{{ vehicle.ownershipType.name }}</td>                      
                          <td>{{ vehicle.beneficiaryAgency }}</td>
                          <!-- <td [ngClass]="getVehicleStatusClass(vehicle.registrationStatus)">
                            {{ vehicle.registrationStatus?.name === 'APPROVED' ? 'REGISTERED' : 'Unregistered' }}
                          </td> -->
                          <td class="d-flex flex-row">
                            <button class="btn btn-outline-primary view-btn btn-sm" (click)="viewVehicle(vehicle.id)">View</button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                      <div class="pagination-info">
                        Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ registeredVehicles.length }} entries
                      </div>
                      <ul class="pagination justify-content-center">
                        <li class="page-item">
                          <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                            <fa-icon [icon]="caretLeft"></fa-icon>
                          </button>
                        </li>
                        <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                          <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                            {{ pageNumber }}
                          </button>
                        </li>
                        <li class="page-item">
                          <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                            <fa-icon [icon]="caretRight"></fa-icon>
                          </button>
                        </li>
                      </ul>
                    </nav>
                  </div>
                  <div *ngIf="activeTab === 'disposals' &&  noNewDisposals " class="text-center text-success fw-bold mb-5 mt-3">No New Vehicle Disposals To Approve!!!</div>
                  <div *ngIf="activeTab === 'disposals' && !noNewDisposals" class="table-only p-3">
                    <table class="table table-stripped">
                      <thead>
                          <tr>
                              <th>Disposal Type</th>
                              <th>Disposal Reason</th>
                      
                              <th>Status</th>
                              <th>Actions</th>
                          </tr>
                      </thead>
                      <tbody>
                          <tr *ngIf="displayedDisposals.length <= 0">
                              <td colspan="9" class="no-vehicles-message">No Disposal Requests!!!</td>
                          </tr>
                          <tr *ngFor="let disposal of displayedDisposals">
                              <td>{{ disposal.disposalTypes?.name }}</td>
                              <td>{{ disposal.disposalReasons?.name }}</td>
                              <td [ngClass]="getDisposalStatusClass(disposal.requestStatus)">
                                  {{ disposal.requestStatus?.name }}
                              </td>
                              <td class="d-flex flex-row">
                                  <button class="btn btn-outline-primary view-btn btn-sm" (click)="viewDisposal(disposal.id)" *ngIf="role !== 'Institutional logistics'"> View </button>
                                  <button class="btn btn-outline-primary auction-btn btn-sm" *ngIf="disposal.disposalTypes?.name == 'Public Auction' && role === 'Institutional logistics' && disposal.isActionReportsubmitted === false && disposal.requestStatus.name === 'APPROVED'" (click)="viewAuction(disposal.id)" style="margin: 0px 14px;"> Auction </button>
                              </td>
                          </tr>
                      </tbody>
                  </table>
                  <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                    <div class="pagination-info">
                        Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ displayedDisposals.length }} entries
                    </div>
                    <ul class="pagination justify-content-center">
                        <li class="page-item">
                            <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                                <fa-icon [icon]="caretLeft"></fa-icon>
                            </button>
                        </li>
                        <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                            <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                                {{ pageNumber }}
                            </button>
                        </li>
                        <li class="page-item">
                            <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                                <fa-icon [icon]="caretRight"></fa-icon>
                            </button>
                        </li>
                    </ul>
                </nav>
                      </div>
          </div> 
         
              <div *ngIf="shouldShowUsersTable()" class="card table-container d-flex flex-column p-2">       
                      <div class="table-header d-flex flex-row p-3">
                      <h2 class="table-title">All Users</h2>
      
                      <div class="search-group d-flex">
                          <fa-icon [icon]="searchIcon" class="icon"></fa-icon>
                          <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchUsers()">
                      </div>
      
                      <div class="sorting-group d-flex flex-row p-2">
                          <label for="sort-field" class="text-muted p-1">Sort by:</label>
                          <select id="sort-field" class="select" (change)="sortTable($event)">
                              <option value="">Select Field</option>
                              <option value="fullName">Full Name</option>
                              <option value="role">Role</option>
                              <option value="status">Status</option>
                            </select>
                      </div>
                      
                  </div>
                  
                  <div class="table-only">
                      <table class="table table-stripped">
                          <thead>
                              <tr>
                                  <th>Emp ID</th>
                                  <th sortable = "fullName">Full Names</th>
                                  <th sortable = "institution">Role</th>
                                  <th>Phone Number</th>
                                  <th>Email</th>
                                  <th sortable = "status">Status</th>
                              </tr>
                          </thead>
                          <tbody>
                              <tr *ngFor="let user of filteredUsers | slice: 0:pageSize">
                                  <td>{{ user.identification}}</td>
                                  <td>{{ user.fullName}}</td>
                                  <td>{{ user.role.name}}</td>
                                  <td>{{ user.phoneNumber}}</td>
                                  <td>{{ user.email}}</td>
                                  <td>
                                      <p [ngClass]="{
                                          'status-active': user.status === 'Active',
                                          'status-registrations': user.status === 'registrations'
                                        }"
                                      >{{ user.status}}</p>
                                  </td>
                              </tr>
                          </tbody>
                      </table>
          
                  </div>
                  
                  <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                    <div class="pagination-info">
                        Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ displayedUsers.length }} entries
                    </div>
                    <ul class="pagination justify-content-center">
                      <li class="page-item">
                        <button class=" caret" (click)="previousPage()" [disabled]="currentPage === 1">
                            <fa-icon [icon]="caretLeft" ></fa-icon>
                        </button>
                      </li>
                      <li class="page-item " *ngFor="let pageNumber of getPageNumbers()">
                        <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                          {{ pageNumber }}
                        </button>
                      </li>
                      <li class="page-item">
                        <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                            <fa-icon [icon]="caretRight" ></fa-icon>
                        </button>
                      </li>
                    </ul>
                  </nav>  
              </div> 
          </div>
      
          <div class="col-xl-4 right-container" style="width: 30%;">
              <div class="card">
                  <div class="card-header" style="background-color: white;">
                      <h5 class="h3 text-primary">Recent Activities</h5>
                  </div>
                  <div class="card-body p-0">
                      <ul class="list-group list-group-flush" data-toggle="checklist">
                          <li *ngFor="let item of todoList; let i = index" class="checklist-entry list-group-item flex-column align-items-start py-4 px-4">
                              <div class="checklist-item">
                                  <div class="checklist-info">
                                      <h5 class="checklist-title mb-0">{{ item.title }}</h5>
                                      <small>{{ item.date }}</small>
                                  </div>
                              </div>
                          </li>
                      </ul>
                  </div>
              </div>
          </div>
      </div>
      </div>
  </div> 


<app-institution *ngIf="showPopup"></app-institution>