import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'groupActivities'
})
export class GroupActivitiesPipe implements PipeTransform {
  transform(activities: any[], groupSize: number = 2): any[][] {
    const groupedActivities = [];
    for (let i = 0; i < activities.length; i += groupSize) {
      groupedActivities.push(activities.slice(i, i + groupSize));
    }
    return groupedActivities;
  }
}
