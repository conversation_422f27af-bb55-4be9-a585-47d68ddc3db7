<div class="button-container">
    <button
      type="submit"
      class="btn submit-btn col-md-4 mt-4"
      [disabled]="isSaving || isSaved"
      [ngClass]="{ 'saving': isSaving, 'saved': isSaved }"
    >
      <ng-container *ngIf="!isSaving && !isSaved">
        <fa-icon [icon]="saveIcon" class="pe-1"></fa-icon> {{ buttonText }}
      </ng-container>
      <ng-container *ngIf="isSaving">
        <span class="spinner-border spinner-border-sm me-1"></span> Saving...
      </ng-container>
      <ng-container *ngIf="isSaved">
        <fa-icon [icon]="checkIcon" class="pe-1"></fa-icon> Saved
      </ng-container>
    </button>
  </div>
  