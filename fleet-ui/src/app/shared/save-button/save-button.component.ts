import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-save-button',
  templateUrl: './save-button.component.html',
  styleUrl: './save-button.component.scss'
})
export class SaveButtonComponent {
  @Input() isSaving: boolean = false;
  @Input() isSaved: boolean = false;
  @Input() saveIcon: any; // Replace with the correct type for your icon
  @Input() checkIcon: any; // Replace with the correct type for your icon
  @Input() buttonText: string = 'Save';
}
