import { Component, ElementRef, OnInit, ViewChild ,ViewChildren, QueryList} from '@angular/core';
import { faBars, faCodePullRequest,faFile, faPenToSquare,faChevronRight, faChevronDown, faDashboard, faUser, faCar, faInstitution, faUserCircle, faGear, faFlorinSign, faExclamationCircle, faScaleBalanced, faBoxArchive } from '@fortawesome/free-solid-svg-icons';
import { Router } from '@angular/router';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { slideInOut } from '../animations';

declare interface RouteInfo {
  path: string;
  title: string;
  icon: IconDefinition;
  class: string;
  chevron: IconDefinition;
  children?: RouteInfo[];
}

export const ROUTES: RouteInfo[] = [
  { path: '/user-management/dashboard', title: 'Dashboard', icon: faDashboard, class: '', chevron: faChevronRight },
  { 
    path: '/user-management/', 
    title: 'Users', 
    icon: faUser, 
    class: '',
    children: [
      { path: '/auth/register', title: 'Register User', icon: faUser, class: '', chevron: faChevronRight},
      { path: '/user-management/all-users', title: 'All Users', icon: faUser, class: '', chevron: faChevronRight}
    ],
    chevron: faChevronRight},
  { 
    path: '/vehicle-management/all-registered', 
    title: 'Vehicles', 
    icon: faCar, 
    class: '', 
    children: [
      { path: '/vehicle-management/all-registered', title: 'All Vehicles', icon: faCar, class: '', chevron: faChevronRight  },
      { path: '/vehicle-management/register-existing-vehicle', title: 'Existing Vehicle Recording', icon: faCar, class: '', chevron: faChevronRight  }

    ],
    chevron: faChevronRight 
  },

  { 
    path: '/vehicle-management/all-vehicles', 
    title: 'Acquisitions', 
    icon: faFile, 
    class: '', 
    children: [
      { path: '/vehicle-management/request-vehicle', title: 'Request Vehicle', icon: faCodePullRequest, class: '', chevron: faChevronRight},
      { path: '/vehicle-management/all-vehicles', title: 'All Acquisitions', icon: faCar, class: '', chevron: faChevronRight  },
      { path: '/vehicle-management/vehicle-allocation', title: 'Allocations', icon: faCar, class: '', chevron: faChevronRight}
    ],
    chevron: faChevronRight 
  },
  { 
    path: '/vehicle-management/all-disposals', 
    title: 'Disposals', 
    icon: faScaleBalanced, 
    class: '', 
    children: [
      { path: '/vehicle-management/all-disposals', title: 'Disposal Requests', icon: faCodePullRequest, class: '', chevron: faChevronRight},
      { path: '/vehicle-management/auction-reports', title: 'Auction Reports', icon: faCodePullRequest, class: '', chevron: faChevronRight},
    ],
    chevron: faChevronRight 
  },
  { 
    path: '/vehicle-management/reports', 
    title: 'Reports', 
    icon: faExclamationCircle, 
    class: '', 
    children: [
      { path: '/vehicle-management/reports', title: 'All Reports', icon: faCodePullRequest, class: '', chevron: faChevronRight},
      { path: '/vehicle-management/operation', title: 'Operation Reports', icon: faCodePullRequest, class: '', chevron: faChevronRight},
      { path: '/vehicle-management/all-quartely-reports', title: 'Quartely Reports', icon: faCodePullRequest, class: '', chevron: faChevronRight},
      { path: '/vehicle-management/all-project-extensions', title: 'All Project Extensions', icon: faCodePullRequest, class: '', chevron: faChevronRight},

    ],
    chevron: faChevronRight 
  },
  //{ path: '/vehicle-management/archive', title: 'Archive', icon: faBoxArchive , class: '', chevron: faChevronRight },
  // { path: '/upgrade', title: 'Settings', icon: faGear, class: '', chevron: faChevronRight  }
];

@Component({
  selector: 'app-side-bar',
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.scss'],
  animations: [ slideInOut]
})
export class SideBarComponent implements OnInit {
  menuItems: RouteInfo[] = [];
  chevronIcon = faChevronRight;
  hamburgerIcon = faBars;
  isSidebarOpen: boolean = false;
  currentRoute: string = ' ';
  menuVisibility: { [path: string]: boolean } = {};
  role: string = '';

  @ViewChild('sidebarWrapper') sidebarWrapper!: ElementRef;

  constructor(private router: Router) { }

  ngOnInit(): void {
  this.getUserData();
  this.currentRoute = this.router.url;
  this.updateMenuItemsBasedOnRole(this.role);
}


  isActive(menuItem: RouteInfo): boolean {
    return this.currentRoute === menuItem.path;
  }

  toggleSidebar() {
    if (this.sidebarWrapper) {
      const sidebar = this.sidebarWrapper.nativeElement as HTMLElement;
      sidebar.style.transform = sidebar.style.transform === 'translateX(0%)'
        ? 'translateX(-100%)' : 'translateX(0%)';
    }
  }

  isVisible(menuItem: RouteInfo): boolean {
    return this.menuVisibility[menuItem.path] || this.isActive(menuItem);
  }
  toggleDropdown(menuItem: RouteInfo): void {
    if (menuItem) {
      const path = menuItem.path;
      // Toggle the visibility only if it's a parent item
      if (menuItem.children) {
        this.menuVisibility[path] = !this.menuVisibility[path];
        menuItem.chevron = this.menuVisibility[path]
          ? faChevronDown
          : faChevronRight;
      }
    }
  }

  onChildLinkClick(event: Event): void {
    event.stopPropagation();
  }
  getUserData(){
    const data = localStorage.getItem('localUserData');
    if(data !== null){
      const parseObj = JSON.parse(data);
      this.role = parseObj.data.user.role.name;
      this.updateMenuItemsBasedOnRole(this.role);
    }
  }
  updateMenuItemsBasedOnRole(role: string): void {
    switch(role) {
      case 'SUPER-ADMIN':
          this.menuItems = ROUTES.filter(item => item.title !== 'Vehicles' && item.title !== 'Acquisitions' && item.title !== 'Registration' && item.title !== 'Requests' && item.title !== 'Disposals'&& item.title !== 'Reports');
        break;
      case 'ADMIN':
        this.menuItems = ROUTES.filter(item => item.title !== 'Vehicles' && item.title !== 'Acquisitions' && item.title !== 'Registration' && item.title !== 'Requests' && item.title !== 'Disposals'&& item.title !== 'Reports');
        break;
      case 'Institutional logistics':
        this.menuItems = ROUTES.filter((item) => item.title !== 'Users'
        ).map((item) => {
          if(item.title === 'Acquisitions' && item.children){
            return{
              ...item,
              children: item.children.filter(
                (child) => child.title !== 'Allocations'
              )
            }
          }else if (item.title == 'Reports' && item.children) {
            return{
              ...item,
              // children: item.children.filter(
              //   (child) => child.title !== 'Operation Reports'
              // )
            }
          }
          return item
        });
        break;
        case 'Institutional CBM':
          this.menuItems = ROUTES.filter(
            (item) =>
              item.title !== 'Users' 
          ).map((item) => {
            if (item.title === 'Acquisitions' && item.children) {
              return {
                ...item,
                children: item.children.filter(
                  (child) => child.title !== 'Request Vehicle'&& child.title !== 'Allocations'
                ),
              };
            } else if (item.title === 'Vehicles' && item.children) {
              return {
                ...item,
                children: item.children.filter(
                  (child) => child.title !== 'Existing Vehicle Recording' 
                ),
              };
            }else if (item.title == 'Reports' && item.children) {
              return{
                ...item,
                children: item.children.filter(
                  (child) => child.title !== 'Operation Reports'
                )
              }
            }
            return item;
          });
          break;
      case 'Fleet Mgt Senior Engineer':
        this.menuItems = ROUTES.map(item => {
          if (item.title === 'Vehicles' && item.children) {
            return {
              ...item,
              children: item.children.filter(child => child.title !== 'Existing Vehicle Recording' && child.title !== 'Request Vehicle')
            };
            
          } else if (item.title === 'Acquisitions' && item.children) {
            return {
              ...item,
              children: item.children.filter(
                (child) => child.title !== 'Request Vehicle'
              ),
            }
          
          }
          
          return item;
        }).filter(item => item !== null) as RouteInfo[];
        break;
      case 'DG Transport':
        this.menuItems = ROUTES.map(item => {
          if (item.title === 'Vehicles' && item.children) {
            return {
              ...item,
              children: item.children.filter(child => child.title !== 'Existing Vehicle Recording' && child.title !== 'Request Vehicle')
            };
          }  else if (item.title === 'Acquisitions' && item.children) {
            return {
              ...item,
              children: item.children.filter(
                (child) => child.title !== 'Request Vehicle' && child.title !== 'Allocations'
              ),
            }}else if (item.title == 'Users' ) {
            return null;
          } 
          // else if (item.title == 'Reports' && item.children) {
            // return{
            //   ...item,
            //   children: item.children.filter(
            //     (child) => child.title !== 'Quartely Reports' && child.title !== 'All Project Extensions'
            //   )
            // }
          // }
          return item;
        }).filter(item => item !== null) as RouteInfo[];
        break;
      case 'Permanent Secretary':
        this.menuItems = ROUTES.map(item => {
          if (item.title === 'Vehicles' && item.children) {
            return {
              ...item,
              children: item.children.filter(child => child.title !== 'Existing Vehicle Recording' && child.title !== 'Request Vehicle')
            };
          }  else if (item.title === 'Acquisitions' && item.children) {
            return {
              ...item,
              children: item.children.filter(
                (child) => child.title !== 'Request Vehicle' && child.title !== 'Allocations'
              ),
            }}else if (item.title === 'Users' && item.children) {
              return null;
          }
          // else if (item.title == 'Reports' && item.children) {
          //   return{
          //     ...item,
          //     children: item.children.filter(
          //       (child) => child.title !== 'Quartely Reports' && child.title !== 'All Project Extensions'
          //     )
          //   }
          // }
          return item;
        }).filter(item => item !== null) as RouteInfo[];
        break;
        case 'Minister':
          this.menuItems = ROUTES.filter(
            (item) =>
              item.title !== 'Users' 
          ).map((item) => {
            if (item.title === 'Acquisitions' && item.children) {
              return {
                ...item,
                children: item.children.filter(
                  (child) => child.title !== 'Request Vehicle' && child.title !== 'Allocations'
                ),
              };
            } else if (item.title === 'Vehicles' && item.children) {
              return {
                ...item,
                children: item.children.filter(
                  (child) => child.title !== 'Existing Vehicle Recording'
                ),
              };
            }else if (item.title == 'Reports' && item.children) {
              return{
                ...item,
                children: item.children.filter(
                  (child) => child.title !== 'Quartely Reports' && child.title !== 'All Project Extensions'
                )
              }
            }
            return item;
          });
          break;
      default:
        this.menuItems = ROUTES;
    }
  }
  
    
  }
