.nav-link{
    color: #757575;
}
.nav-link:hover {
    background-color: #f2f2f2; /* Add hover background color */
}
.wrapper {
    width: 17%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    box-shadow: 0 0 10px rgba(61, 61, 61, 0.1); /* Adjust the shadow properties as needed */
}

.sidebar-wrapper{
    display: block;
}
.img-fluid{
    width: 30%;
}
h1{
    font-size: 14px;
    color: #757575;
}
.icons{
    font-size: 15px;
}
span{
    font-size: 15px;
}
.chevronIcon{
    font-size: 12px;
}
.nav-link.active {
    background-color: #28A4E2;
    color: #fff;
    border-radius: 8px;
}
li{
    border-radius: 8px;
    width: 85%;
    margin-left: 10%;
    
}

.sidebar-toggle {
    display: none;
    border: none;
    background: none;
    font-size: 1rem;
    cursor: pointer;
    color: #757575;
    margin-bottom: 10px;
}
@media screen and (max-width: 991px) {
    .wrapper {
        width:5%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
    }

    .logo, h1 {
        display: none;
    }

    .sidebar-toggle {
        display: block;
        padding-top: 10px;
    }

    .sidebar-wrapper.active {
        max-height: 100vh;
    }
    .sidebar-wrapper {
        position: fixed; 
        top: 0;
        left: 0;
        width: 70%; 
        height: 100%; 
        background-color: #fff; 
        z-index: 1000000; 
        overflow: hidden; 
        transition: transform 0.3s ease-in-out; 
        transform: translateX(-100%); 
        margin-top: 30px !important;
    }
    .sidebar-wrapper ul{
       font-size: 10px !important;
       margin-top: 10px;
       padding-top: 10px;
    }
    .sidebar-wrapper.active {
        left: 0; 
    }
    .chevronIcon{
        display: none;
    }
}
