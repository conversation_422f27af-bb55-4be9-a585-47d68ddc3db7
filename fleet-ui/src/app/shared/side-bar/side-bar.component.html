<div class="wrapper d-flex flex-column">
  <div class="logo text-center mt-3">
    <img src="../../../assets/images/coatOfArm.jpg" class="img-fluid" alt="Logo">
    <h1 class="fw-bold mt-1">Government Fleet Management</h1>
  </div>
  <button class="sidebar-toggle" (click)="toggleSidebar()">
    <fa-icon [icon]="hamburgerIcon"></fa-icon>
  </button>
  <div #sidebarWrapper class="sidebar-wrapper mt-4" [ngClass]="{'active': isSidebarOpen}">
    <ul class="nav flex-column">
      <ng-container *ngFor="let menuItem of menuItems">
        <li class="{{menuItem.class}} nav-item mb-2" *ngIf="!menuItem.children; else dropdownMenu">
          <a [routerLink]="[menuItem.path]"
             class="nav-link d-flex align-items-center {{ isActive(menuItem) ? 'active' : ''}}">
            <fa-icon [icon]="menuItem.icon" class="icons me-3"></fa-icon>
            <span>{{menuItem.title}}</span>
          </a>
        </li>
        <ng-template #dropdownMenu>
          <li class="nav-item mb-2">
            <a href="javascript:;" class="nav-link d-flex align-items-center"
               (click)="toggleDropdown(menuItem)">
              <fa-icon [icon]="menuItem.icon" class="icons me-3"></fa-icon>
              <span>{{menuItem.title}}</span>
              <fa-icon [icon]="menuItem.chevron" class="ms-auto chevronIcon"
                       (click)="toggleDropdown(menuItem)"></fa-icon>
            </a>
            <ul class="nav flex-column" *ngIf="isVisible(menuItem)">
              <li *ngFor="let childItem of menuItem.children" class="{{childItem.class}} nav-item mb-2">
                <a [routerLink]="[childItem.path]"
                   class="nav-link d-flex align-items-center {{ isActive(childItem) ? 'active' : ''}}"
                   (click)="onChildLinkClick($event)">
                  <span>{{childItem.title}}</span>
                </a>
              </li>
            </ul>
          </li>
        </ng-template>
      </ng-container>
    </ul>
  </div>
</div>
