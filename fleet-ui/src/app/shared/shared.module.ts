// shared/shared.module.ts

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SideBarComponent } from './side-bar/side-bar.component';
import { TopNavComponent } from './top-nav/top-nav.component';
import { ToastrComponent } from './toastr/toastr.component';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { SpinnerComponent } from './spinner/spinner.component';
import { GroupActivitiesPipe } from './group-activities.pipe';
import { SaveButtonComponent } from './save-button/save-button.component';

@NgModule({
  declarations: [
    SideBarComponent, 
    TopNavComponent,
    ToastrComponent,
    BreadcrumbComponent,
    SpinnerComponent,
    GroupActivitiesPipe,
    SaveButtonComponent
  ],
  imports: [
    ReactiveFormsModule,
    RouterModule,
    CommonModule,
    FormsModule,
    FontAwesomeModule
  ],
  exports: [
    ReactiveFormsModule,
    RouterModule,
    CommonModule,
    FormsModule,
    FontAwesomeModule,
    SideBarComponent,
    TopNavComponent,
    ToastrComponent,
    BreadcrumbComponent,
    SpinnerComponent,
    GroupActivitiesPipe,
    SaveButtonComponent
  ],
  providers: [],
})
export class SharedModule { }
