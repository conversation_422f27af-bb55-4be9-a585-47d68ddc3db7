.toastr-container {
    position: fixed;
    top: 20px;  /* Position at the top */
    right: 1px;  /* Position at the right */
    z-index: 9999; /* Make sure it is on top of other content */
    display: flex;
    justify-content: flex-end;
    pointer-events: none;  /* Prevent interaction with the background */
    animation: slideIn 0.5s ease-out forwards; /* Smooth slide-in */
  }

  .toastr-message {
    
    background-color: #c42217; /* Success background color */
    color: white;
    padding: 12px 20px;  /* Add padding for better appearance */
    // border-radius: 8px;  /* Rounded corners */
    font-size: 15px;
    max-width: 350px;  /* Limit the width of the toast */
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);  /* Light shadow */
    cursor: pointer;
    pointer-events: auto;  /* Enable interaction for closing the toast */
  }

  .toastr-message.error {
    background-color: #288d2c;/* Error background color */
  }

  /* Small and smooth slide-in effect */
  @keyframes slideIn {
    from {
      transform: translateX(10px);  /* Start just off-screen to the right */
      opacity: 0;
    }
    to {
      transform: translateX(0);  /* Slide in to the normal position */
      opacity: 1;
    }
  }

  /* Fade-out after the message disappears */
  .toastr-container.ng-leave {
    animation: fadeOut 0.5s ease-out forwards;
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
