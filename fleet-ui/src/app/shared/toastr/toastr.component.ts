import { Component } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-toastr',
  templateUrl: './toastr.component.html',
  styleUrl: './toastr.component.scss'
})
export class ToastrComponent {
  showToastr: boolean = false;
  toastrMessage: string = '';
  toastrSuccess: boolean = false;
  
  constructor( private toastr: ToastrService) {
  
  }
  handleToastrClick() {
    this.showToastr = false;
  }

  showToastrMessage(message: string, isSuccess: boolean) {
    this.toastrMessage = message;
    this.toastrSuccess = isSuccess;
    this.showToastr = true;
    setTimeout(() => {
      this.showToastr = false;
    }, 3500); 
  }
}
