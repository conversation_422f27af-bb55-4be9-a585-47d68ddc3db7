@import 'bootstrap/dist/css/bootstrap.min.css';
*{
    text-decoration: none;
}
.navbar{
    background-color: #ffffff;
    width: 83%;
    height: 10%;
    position: fixed;
    top: 0;
    right: 0;
    padding: 15px;
    box-shadow: 0px 0px 10px 3px rgba(146, 146, 146, 0.1);
    z-index: 1000;
   
}
.search-group{
    width: 40%;
    font-size: 15px;
    color: #555555;
}
.highlight {
    background-color: yellow; /* Set the background color for the highlighted text */
    font-weight: bold; /* Optionally, set the font weight to make the text bold */
  }
  
.icon{
    background-color: #f0f0f0;
    color: #A098AE;
    font-size: 12px;
    border-radius: 50%;
    padding: 7px 0px 7px 12px ;
}
.global-input{
    color: #A098AE;
    background-color: #f0f0f0;
    font-size: 13px;
}
.nav-link{
    margin-left: 40%;
}
.notification{
    height: 40px;
    width: 40px;
    border-radius: 50%;
    background-color: #e6e6e6;
}
.user-profile{
    margin-left: 10px;
    font-size: 12px;
}
.dropdown-menu {
    position: absolute;
    margin-top: 150px; 
  }
  
  .dropdown-menu.show {
    display: block;
  }
  
@media screen and (max-width: 991px) {
    .navbar{
        width: 90%;
        height: 7%;
        z-index: -1;
    }
    .search-group, .nav-link{
        display: none !important;
    }
}