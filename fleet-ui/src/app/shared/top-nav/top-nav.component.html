<nav class="navbar navbar-expand-lg">
  <div class="search-group d-flex p-1">
    <span class="fw-bold"> {{ institution }}</span>, Logged In

   </div>
  <a [routerLink]="notification" class="nav-link">
    <div class="notification d-flex justify-content-center align-items-center">
      <fa-icon [icon]="bellIcon" class="bellIcon"></fa-icon>
    </div>
  </a>
  <div class="user-profile d-flex align-items-center">
    <img src="../../../assets/images/boyAvatar.png" alt="Profile picture" class="rounded-circle me-2" style="width: 40px; height: 40px;">
    <!-- <span>{{institution}}</span> -->
    <div class="user-info d-flex flex-column">
      <span class="fw-bold">{{ username }}</span>
      <small class="text-muted">{{ role }} </small> 
      <!-- <small *ngIf="isLogisticsOrCBM()" class="text-muted">{{ institution }}</small>   -->
      </div>
    <button class="btn btn-link ms-auto dropdown-toggle" (click)="toggleDropdown()"></button>
    <div class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
      <a class="dropdown-item" href="#">Profile</a>
      <a class="dropdown-item" href="#">Settings</a>
      <a class="dropdown-item" href="" (click)="logout()">Logout</a>
    </div>
  </div>
</nav>
