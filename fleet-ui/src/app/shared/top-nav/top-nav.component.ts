import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { faBell, faSearch} from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-top-nav',
  templateUrl: './top-nav.component.html',
  styleUrl: './top-nav.component.scss'
})
export class TopNavComponent implements OnInit{
  username: string ='';
  role: string = '';
  institution: string = '';
  bellIcon = faBell;
  searchIcon = faSearch;
  notification = '/user-management/users';
  isDropdownOpen: boolean = false;
  @Output() search: EventEmitter<string> = new EventEmitter<string>();

  onSearch(query: string): void {
    this.search.emit(query);
  }
  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown() {
    this.isDropdownOpen = false;
  }
  constructor(private router: Router) { }
  ngOnInit(): void {
    this.getUserData();
    this.isLogisticsOrCBM();
  }

  getUserData(){
    const data= localStorage.getItem('localUserData');
    if(data != null){
      const parseObj= JSON.parse(data)
      this.role = parseObj.data.user.role.name
      this.username = parseObj.data.user.firstName +" "+ parseObj.data.user.lastName;
      this.institution = parseObj.data.user.institution.name
      console.log(this.username)
      console.log(this.role)
      console.log(this.institution)
    }
  }
  logout() {
    localStorage.removeItem('localUserData');
    localStorage.removeItem('accessToken');
    this.router.navigate(['/login']);
  }
  isLogisticsOrCBM(): boolean {
    return this.role === 'Institutional logistics' || this.role === 'Institutional CBM';
  }

}
