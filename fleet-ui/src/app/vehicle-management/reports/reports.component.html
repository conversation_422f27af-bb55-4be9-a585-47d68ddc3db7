<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="vehicles-container">
        <div class="header d-flex flex-row justify-content-between w-50">
            <button class="btn go-back-btn" (click)="goBack()">
                <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
              </button>
            <h2 class="table-title">REPORTING</h2>
        </div>
        <div class="card">
            <div class="table-header">
                <div class="d-flex flex-row justify-content-start align-items-center statuses">
                    <button class="btn btn-outline" (click)="setFilter('all')" [class.active]="currentFilter === 'all'">All</button>
                    <button class="btn btn-outline" (click)="setFilter('gr')" [class.active]="currentFilter === 'gr'">Government Vehicles</button>
                    <button class="btn btn-outline" (click)="setFilter('gp')" [class.active]="currentFilter === 'gp'">Project Vehicles</button>
                    <button class="btn btn-outline" (click)="setFilter('co-owned')" [class.active]="currentFilter === 'co-owned'">Co-owned Vehicles</button>
                    <!-- <button class="btn btn-outline" *ngIf="userRole === 'Institutional logistics' || userRole === 'Institutional CBM'" (click)="setFilter('institution')" [class.active]="currentFilter === 'institution'">Institution</button> -->
                    <button *ngIf="showReportingVehiclesTab" class="btn btn-outline" (click)="setFilter('reporting')" [class.active]="currentFilter === 'reporting'">Reporting Vehicles</button>          
                </div>
                <div class="main-container">
                    <div class="row mt-2">
                        <div class="col-9">
                            <form [formGroup]="searchForm">
                                <div class="row">
                                  <div *ngIf="userRole !== 'Institutional logistics' && userRole !== 'Institutional CBM'"
                                  class="form-group col-md-4">
                                    <select id="beneficiaryAgency" class="form-control select selectpicker" formControlName="beneficiaryAgency">
                                      <option value="" disabled selected>Select Beneficiary Agency</option>
                                      <option *ngFor="let institution of beneficiaryAgencies" [value]="institution.name">{{ institution.name }}</option>
                                    </select>
                                  </div>
                                 
                                  <div class="form-group col-md-4">
                                    <select id="vehicleType" class="form-control select selectpicker" formControlName="vehicleType">
                                      <option value="" disabled selected>Select Vehicle Type</option>
                                      <option *ngFor="let vehicleType of vehicleTypes" [value]="vehicleType.name">{{ vehicleType.name }}</option>
                                    </select>
                                  </div>
                                  <div class="form-group col-md-4">
                                    
                                  </div>
                                </div>
                                <div class="row mt-2">
                                  <div class="form-group col-md-4">
                                    <select class="form-control select selectpicker" formControlName="vehicleManufacture" id="vehicleManufacture">
                                      <option value="" disabled selected>Select Vehicle Manufacture</option>
                                      <option *ngFor="let vehicleManufacture of vehicleManufactures" [value]="vehicleManufacture.name">{{ vehicleManufacture.name }}</option>
                                    </select>
                                  </div>
                                  <div class="form-group col-md-4">
                                    <select class="form-control select selectpicker" formControlName="vehicleModel" id="vehicleModel">
                                      <option value="" disabled selected>Select Vehicle Model</option>
                                      <option *ngFor="let vehicleModel of vehicleModels" [value]="vehicleModel.name">{{ vehicleModel.name }}</option>
                                    </select>
                                  </div>
                                  <div class="col-md-4 d-flex">
                                    <button type="button" class="btn btn-outline-danger btn-sm"  (click)="clearForm()">Clear</button>
                                </div>

                                </div>
                              </form>
                       
                        </div>
                        <div class="col-3 d-flex align-items-end justify-content-end flex-column">
                            <div class="row">
                                <div  *ngIf="filteredRegisteredVehicles.length > 0">
                                    <h3>Available Vehicles: <span class="text-center text-success fw-bold">{{filteredRegisteredVehicles.length}}</span> </h3>
                                </div>
                            </div>
                            <div class="row d-flex">
                                <div class="btn-group">
                      <div class="dropdown">
                         <button class="btn view-btn dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                               Download
                      </button>
                       <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                         <li><a class="dropdown-item" (click)="exportVehiclesToExcel('csv')">Download CSV</a></li>
                         <li><a class="dropdown-item" (click)="exportVehiclesToExcel('pdf')">Download PDF</a></li>
    </ul>
  </div>
  
                                      <button class="btn edit-btn d-flex align-items-center ml-3" (click)="printVehicles()">
                                     Print <fa-icon [icon]="printIcon" style="margin-left:2px"></fa-icon></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="search-group d-flex mt-2">
                        <fa-icon [icon]="searchIcon" class="icon"></fa-icon>
                        <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchVehicles()">
                    </div>
                   
                   
                    <div class="text-center text-danger fw-bold mt-4 mb-5" *ngIf="filteredRegisteredVehicles.length === 0">
                        <h3>No vehicles found.</h3>
                    </div>
                    <div *ngIf="filteredRegisteredVehicles.length > 0" id="printableArea">
                        <div class="table-only mt-2" >
                            <table class="table table-stripped">
                                <thead>
                                    <tr>
                                        <th>Plate Number</th>
                                        <th>Vehicle Type</th>
                                        <th>Chassis Number</th>
                                        <th>Vehicle Model</th>
                                        <th>Vehicle Manufacture</th>
                                        <th>Beneficiary Agency</th>
                                        <th>Ownership Type</th>      
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let vehicle of displayedVehicles">
                                        <td>{{ vehicle.plateNumber }}</td>
                                        <td>{{ vehicle.vehicleType.name }}</td>
                                        <td>{{ vehicle.chassisNumber }}</td>
                                        <td>{{ vehicle.vehicleModel.name }}</td>
                                        <td>{{ vehicle.vehicleManufacture.name }}</td>
                                        <td>{{ vehicle.beneficiaryAgency }}</td>
                                        <td>{{ vehicle.ownershipType.name }}</td>
                                        <td [style.color]="vehicle.isVehicleActive ? 'green' : 'red'">{{ vehicle.isVehicleActive ? 'Active' : 'Inactive' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                        <div class="pagination-info">
                            Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredRegisteredVehicles.length }} entries
                        </div>
                        <ul class="pagination justify-content-center">
                            <li class="page-item">
                                <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                                    <fa-icon [icon]="caretLeft"></fa-icon>
                                </button>
                            </li>
                            <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                                <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                                    {{ pageNumber }}
                                </button>
                            </li>
                            <li class="page-item">
                                <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                                    <fa-icon [icon]="caretRight"></fa-icon>
                                </button>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
