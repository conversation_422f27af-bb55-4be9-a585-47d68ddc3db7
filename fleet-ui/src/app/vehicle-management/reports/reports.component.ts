import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { faSearch, faCaretLeft, faCaretRight, faCar, faDownload, faPrint, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';
import { FormBuilder, FormGroup } from '@angular/forms';
import html2pdf from 'html2pdf.js';
import * as XLSX from 'xlsx';
import { debounceTime, distinctUntilChanged } from 'rxjs';
import { Location } from '@angular/common';

interface RegistrationStatus {
  id: string;
  name: string;
}

interface Vehicle {
  ownershipType: any;
  beneficiaryAgencyId: string;
  isDisposalRequestSubmitted: boolean;
  registrationStatus: RegistrationStatus;
  
}

interface ApiResponse {
  vehicles: Vehicle[]; 
}
@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss'
})
export class ReportsComponent implements OnInit {
  
  registeredVehicles: any[] = [];
  filteredRegisteredVehicles: any[] = [];
  pageSize: number = 10; // Number of items per page
  currentPage: number = 1; // Current page
  totalPages: number = 0; // Total number of pages
  institutionId: string = '';
  userRole: string = '';
  searchText: string = '';
  faSearch = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  downloadIcon=faDownload;
  printIcon=faPrint;
  carIcon = faCar;
  currentFilter: string = 'all'; 
  displayedVehicles: any[] = [];
  showReportingVehiclesTab: boolean = false;
  reportingVehicles: any[] = [];
  vehicleModels: any[] = [];
  vehicleManufactures: any[] =[];
  beneficiaryAgencies: any[] = [];
  ownershipTypes: any[] = [];
  vehicleTypes: any[] = [];
  searchForm!: FormGroup;
  searchOtherText: any;
  institutionName: any;
  searchIcon = faSearch;
  backwardIcon = faArrowLeft;
  searchedVehiclesCount: number =0;

  constructor(
    private http: HttpClient, 
    private router: Router,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private location: Location
  ) {
    this.searchForm = this.formBuilder.group({
      beneficiaryAgency: [''],
      ownershipType: [''],
      vehicleType: [''],
      vehicleManufacture: [''],
      vehicleModel: ['']
    });
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      const filter = params['filter'];
      if (filter) {
        console.log('Filter parameter:', filter);
        this.setFilter(filter);
      }
    });
    this.getUserDetails(); 
    this.fetchRegisteredVehicles(); 
    this.fetchVehicleModels();
    this.fetchVehicleManufactures();
    this.fetchBeneficiaryAgencies();
    this.fetchOwnershipTypes();
    this.fetchVehicleTypes();
    this.fetchReportingVehicles();
    this.searchForm.valueChanges
    .pipe(
      debounceTime(300), 
      distinctUntilChanged()
    )
    .subscribe(values => {
      this.onSubmit(values);
    });

  }

  goBack(){
    this.location.back();
  }

  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id; // Store institution ID
      this.userRole = parsedObj.data.user.role.name; // Store user role
      this.institutionName = parsedObj.data.user.institution.name;
    }
  }

  ///fetching the searching fields
  fetchVehicleModels(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleModels`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleModels = response.map(vehicleModel => ({ id: vehicleModel.id, name: vehicleModel.name }));
      },
      (error) => {
        console.error('Error fetching vehicle models:', error)
      }
    );
  }

  fetchVehicleManufactures(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleManufacture`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleManufactures = response.map(vehicleManufacture => ({ id: vehicleManufacture.id, name: vehicleManufacture.name }));
      },
      (error) => {
        console.error('Error fetching vehicle manufactures:', error)
      }
    );
  }

  fetchBeneficiaryAgencies(): void {
    const apiUrl = `${environment.baseUrl}/user-management/institutions`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.beneficiaryAgencies = response.map(institution => ({ id: institution.id, name: institution.name }));
      },
      (error) => {
        console.error('Error fetching beneficiary agencies:', error);
      }
    );
  }

  fetchOwnershipTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/ownershipTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.ownershipTypes = response.map(ownership => ({ id: ownership.id, name: ownership.name }));
      },
      (error) => {
        console.error('Error fetching ownershipTypes:', error)
      }
    );
  }

  fetchVehicleTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleTypes = response.map(vehicleType => ({ id: vehicleType.id, name: vehicleType.name }));
      },
      (error) => {
        console.error('Error fetching vehicleTypes:', error)
      }
    );
  }
  fetchReportingVehicles() {
    if (!this.institutionId) {
      console.error('Institution ID not found');
      return;
    }
  
    const url = `${environment.otherUrl}/allVehiclesByRepInstitution/${this.institutionId}`;
    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log('Reporting vehicles:', response);
        this.reportingVehicles= response.filter(
          vehicle => !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c')
            && vehicle.registrationStatus?.name === 'APPROVED'
        );
        
        this.showReportingVehiclesTab = this.reportingVehicles.length > 0 && (this.userRole === 'Institutional logistics' || this.userRole === 'Institutional CBM');
        console.log('Show reporting vehicles tab:', this.showReportingVehiclesTab);
      },
      (error) => {
        console.error('Error fetching reporting vehicles:', error); 
        this.showReportingVehiclesTab = false;
      }
    );
  }
 

  fetchRegisteredVehicles() {
    if (!this.institutionId || !this.userRole) {
      console.error('Institution ID or User Role not found');
      return;
    }
  
    let url: string;
  
    switch (this.userRole) {
      case 'Institutional logistics':
      case 'Institutional CBM':
        url = `${environment.otherUrl}/allVehiclesbybeneficiaryAgencyId/${this.institutionId}`;
        break;
  
      case 'Fleet Mgt Senior Engineer':
      case 'DG Transport':
      case 'Permanent Secretary':
      case 'Minister':
        url = `${environment.otherUrl}/allVehicles`;
        break;
  
      default:
        console.error('Unrecognized user role');
        return;
    }
    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log(response);
        this.registeredVehicles = response
        .filter(
          vehicle => !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c')
          && vehicle.registrationStatus?.name === 'APPROVED'
        ); 
        this.updateDisplayedVehicles();
        this.applyCurrentFilter();
      },
      (error) => {
        console.error('Error fetching registered vehicles:', error);
      }
    );
  }
  onSubmit(filters: any): void {
    console.log('Filter Criteria:', filters);
  
    const hasFilters = Object.values(filters).some(value => !!value);
    if (!hasFilters) {
      console.log('No filter criteria provided.');
      this.filteredRegisteredVehicles = [...this.registeredVehicles];
      this.updateDisplayedVehicles();
      this.applyCurrentFilter();
      return;
    }
  
    const apiUrl = `${environment.otherUrl}/gettingVehicleByFilter`;
    this.http.post<ApiResponse>(apiUrl, filters).subscribe(
      (response: ApiResponse) => {
        let vehicles = response.vehicles;
        if (vehicles && Array.isArray(vehicles)) {
          vehicles = vehicles.filter(vehicle => 
            !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c') &&
            vehicle.registrationStatus?.name === 'APPROVED'
          );
  
          // Check for ownership type filter
          if (filters.ownershipType) {
            vehicles = vehicles.filter(vehicle => vehicle.ownershipType?.id === filters.ownershipType);
          }
  
          // Additional filtering based on user role
          if (this.userRole === 'Institutional logistics' || this.userRole === 'Institutional CBM') {
            vehicles = vehicles.filter(vehicle => vehicle.beneficiaryAgencyId === this.institutionId);
          }
  
          this.filteredRegisteredVehicles = vehicles;
          console.log('Filtered Vehicles:', this.filteredRegisteredVehicles);
        } else {
          console.error('Unexpected response format:', response);
          this.filteredRegisteredVehicles = [];
        }
        this.updateDisplayedVehicles();
      },
      (error) => {
        console.error('Error fetching filtered vehicles:', error);
      }
    );
  }
  
  applyCurrentFilter() {
    this.filteredRegisteredVehicles = [...this.registeredVehicles];
    switch (this.currentFilter) {
      case 'all': 
     //this.filteredRegisteredVehicles=[...this.reportingVehicles];
        break;
      case 'institution':
        this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
          (item) => item.beneficiaryAgencyId === this.institutionId
          
        );
        break;
        case 'gr':
        this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
          (item) => item.ownershipType.name === "Government Vehicles" 
        );
        break;
        case 'gp':
        this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
          (item) => item.ownershipType.name === "Project Vehicles"
          
        );
        break;
        case 'co-owned':
        this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
          (item) => item.ownershipType.name === "Co-Owned Vehicles"
          
        );
        break;
        case 'reporting':
          this.filteredRegisteredVehicles = [...this.reportingVehicles];
          break;
      default:
        console.error('Invalid filter type:', this.currentFilter);
    }
  
    this.currentPage = 1; // Reset to the first page when changing filters
    this.updateDisplayedVehicles(); // Refresh the displayed acquisitions
  }
  
  setFilter(filterType: string) {
    this.currentFilter = filterType;
    this.clearForm();
    this.applyCurrentFilter(); // Apply the selected filter
  }  


updateDisplayedVehicles() {
 
  const startIndex = (this.currentPage - 1) * this.pageSize;
  const endIndex = Math.min(startIndex + this.pageSize, this.filteredRegisteredVehicles.length);
  this.displayedVehicles = this.filteredRegisteredVehicles.slice(startIndex, endIndex); // Slice within the filtered list
  this.totalPages = Math.ceil(this.filteredRegisteredVehicles.length / this.pageSize);
}


getFirstEntryIndex(): number {
  return (this.currentPage - 1) * this.pageSize + 1;
}

getLastEntryIndex(): number {
  const lastEntryIndex = this.currentPage * this.pageSize;
  return Math.min(lastEntryIndex, this.filteredRegisteredVehicles.length);
}

goToPage(pageNumber: number) {
  if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedVehicles(); // Refresh the displayed vehicles based on the new page
  }
}

getPageNumbers(): number[] {
  const pageNumbers = [];
  for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
  }
  return pageNumbers;
}

previousPage() {
  if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedVehicles();
  }
}

nextPage() {
  if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedVehicles();
  }
}
 
  isLogistics():boolean{
    const data = localStorage.getItem('localUserData');
    if(data !=null){
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }

  searchVehicles() {
    const searchTextLower = this.searchText.toLowerCase();
    const filteredBySearch= this.filteredRegisteredVehicles.filter(item => {
      console.log('search item', item);
    
      return (
        (item.plateNumber || '').toLowerCase().includes(searchTextLower) ||
        (item.pickCardNumber || '').toLowerCase().includes(searchTextLower) ||
        (item.vehicleType?.name || '').toLowerCase().includes(searchTextLower) ||
        (item.chassisNumber || '').toLowerCase().includes(searchTextLower) ||
        (item.vehicleModel?.name || '').toLowerCase().includes(searchTextLower) ||
        (item.vehicleManufacture?.name || '').toLowerCase().includes(searchTextLower) ||
        (item.beneficiaryAgency || '').toLowerCase().includes(searchTextLower) ||
        (item.ownershipType?.name || '').toLowerCase().includes(searchTextLower) ||
        (item.isVehicleActive ? 'active' : 'inactive').includes(searchTextLower)
      );
    });
   // Update displayed vehicles based on the search result
   this.displayedVehicles = filteredBySearch.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
   this.totalPages = Math.ceil(filteredBySearch.length / this.pageSize);
}

printVehicles(): void {
    const printContents = document.getElementById('printableArea')?.innerHTML;
    if (printContents) {
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents;
      window.print();
      document.body.innerHTML = originalContents;
      location.reload(); // To reload the original page content
    }
  }

  exportVehiclesToExcel(format: string): void {
    const cleanedData = this.filteredRegisteredVehicles.map(vehicle => {
      const cleanedVehicle: { [key: string]: any } = {};
      Object.keys(vehicle).forEach(key => {
        if (vehicle[key] !== null && vehicle[key] !== undefined && vehicle[key] !== '') {
          cleanedVehicle[key] = vehicle[key];
        }
      });
      return cleanedVehicle;
    });
  
    // Set the cleaned data for the template to render
    this.displayedVehicles = cleanedData;
  
    const currentDate = new Date();
    const dateString = currentDate.toISOString().split('T')[0];
    const uniqueId = currentDate.getTime();
  
    if (format === 'csv') {
      this.exportToCSV(`report_${dateString}_${uniqueId}.csv`);    } 
    else if (format === 'pdf') {
      this.exportToPDF(`report_${dateString}_${uniqueId}.pdf`);
    }
  }
  
 exportToCSV(fileName: string): void {
  const table = document.getElementById('printableArea') as HTMLTableElement; // The table in your HTML
  
  if (!table) {
    console.error('Table not found');
    return;
  }

  const headers: string[] = [];
  const data: string[][] = [];

  // Get table headers (assuming the first row is the header row)
  const headerRow = table.querySelector('thead tr');
  if (headerRow) {
    headerRow.querySelectorAll('th').forEach((th) => {
      headers.push(th.innerText.trim());
    });
  }

  // Get table rows data
  const bodyRows = table.querySelectorAll('tbody tr');
  bodyRows.forEach(row => {
    const rowData: string[] = [];
    row.querySelectorAll('td').forEach((td) => {
      rowData.push(td.innerText.trim());
    });
    data.push(rowData);
  });

  // Prepare CSV content
  const csvContent = [headers, ...data].map(e => e.join(",")).join("\n");

  // Create a blob for the CSV
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute("download", fileName); // Use the generated filename
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

  

  exportToPDF(fileName: string): void {
    const element = document.getElementById('printableArea');  // Select the HTML element to export
  
    if (!element) {
      console.error('Printable area not found');
      return;
    }
  
    const options = {
      margin: [0.5, 0.5, 0.5, 0.5],  // Reduce margins to fit more content
      filename: fileName,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { 
        scale: 3,  // Increase scale for better quality
        useCORS: true  // Allow cross-origin resources (if any images are used)
      },
      jsPDF: {
        unit: 'in',
        format: 'a2',  // Change page size to A2 for a larger page
        orientation: 'landscape'  // Use landscape orientation to fit wide tables
      }
    };
  
    html2pdf().from(element).set(options).save();
  }
  
  
  
clearForm(): void {
  this.searchForm.reset({
    beneficiaryAgency: '',
    ownershipType: '',
    vehicleType: '',
    vehicleManufacture: '',
    vehicleModel: ''
  });

  this.applyCurrentFilter(); // Re-apply the current filter to ensure the correct vehicles are displayed
  this.searchText = '';
  this.updateDisplayedVehicles();
}

// clearForm(): void {
//   this.searchForm.reset({
//     beneficiaryAgency: '',
//     ownershipType: '',
//     vehicleType: '',
//     vehicleManufacture: '',
//     vehicleModel: ''
//   });
//   this.onSubmit(this.searchForm.value);  // Trigger the search with reset form values
//   this.currentFilter = 'all';
// }



}
