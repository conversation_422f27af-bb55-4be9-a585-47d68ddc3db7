import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../shared/shared.module';
import { RequestVehicleComponent } from './request-vehicle/request-vehicle.component';
import { VehicleManagementRoutingModule } from './vehicle-management-routing.module';
import { AcquisitionDetailsComponent } from './acquisition-details/acquisition-details.component';
import { AllVehiclesComponent } from './all-vehicles/all-vehicles.component';
import { MultiStepFormComponent } from './multi-step-form/multi-step-form.component';
import { AcquisitionHistoryComponent } from './acquisition-history/acquisition-history.component';
import { RegisterVehicleComponent } from './register-vehicle/register-vehicle.component';
import { AllRegisteredComponent } from './all-registered/all-registered.component';
import { VehicleDetailsComponent } from './vehicle-details/vehicle-details.component';
import { EditRegisteredVehicleComponent } from './edit-registered-vehicle/edit-registered-vehicle.component';
import { RegisterNewVehicleComponent } from './register-new-vehicle/register-new-vehicle.component';
import { VehicleDisposalComponent } from './vehicle-disposal/vehicle-disposal.component';
import { RegistrationHistoryComponent } from './registration-history/registration-history.component';
import { DisposalDetailsComponent } from './disposal-details/disposal-details.component';
import { AllDisposalsComponent } from './all-disposals/all-disposals.component';
import { AuctionComponent } from './auction/auction.component';
import { AuthorizationLetterComponent } from './authorization-letter/authorization-letter.component';
import { AuctionDetailsComponent } from './auction-details/auction-details.component';
import { AuctionReportsComponent } from './auction-reports/auction-reports.component';
import { VehicleAllocationComponent } from './vehicle-allocation/vehicle-allocation.component';
import { ReturnedVehiclesComponent } from './returned-vehicles/returned-vehicles.component';
import { ReportsComponent } from './reports/reports.component';
import { ReturnedDetailsComponent } from './returned-details/returned-details.component';
import { SpinnerComponent } from '../shared/spinner/spinner.component';
import { QuartelyReportComponent } from './quartely-report/quartely-report.component';
import { AllQuartelyReportsComponent } from './all-quartely-reports/all-quartely-reports.component';
import { QuartelyReportDetailsComponent } from './quartely-report-details/quartely-report-details.component';
import { ProjectExtensionRequestComponent } from './project-extension-request/project-extension-request.component';
import { AllProjectExtensionsComponent } from './all-project-extensions/all-project-extensions.component';
import { ProjExtensionApproveComponent } from './proj-extension-approve/proj-extension-approve.component';
import { DisposalHistoryComponent } from './disposal-history/disposal-history.component';
import { CostBenefitComponent } from './cost-benefit/cost-benefit.component';
import { DeviceLocationComponent } from './device-location/device-location.component';
import { ArchiveComponent } from './archive/archive.component';
import { PinkCardComponent } from './pink-card/pink-card.component';
import { QRCodeModule } from 'angularx-qrcode';
import { DisposalLetterComponent } from './disposal-letter/disposal-letter.component';
import { OperationTablesComponent } from './operation-tables/operation-tables.component';
import { SelectDropDownModule } from 'ngx-select-dropdown';
import { EditAuctionComponent } from './edit-auction/edit-auction.component';
import { EditDisposalComponent } from './edit-disposal/edit-disposal.component';
import { AuctionHistoryComponent } from './auction-history/auction-history.component';


@NgModule({
  declarations: [
    RequestVehicleComponent, 
    AllVehiclesComponent, 
    MultiStepFormComponent, 
    AcquisitionDetailsComponent,
    DisposalDetailsComponent,
    AllDisposalsComponent,
    AcquisitionHistoryComponent,
    RegistrationHistoryComponent, 
    RegisterVehicleComponent,
    AllRegisteredComponent,
    VehicleDetailsComponent,
    EditRegisteredVehicleComponent,
    RegisterNewVehicleComponent,
    VehicleDisposalComponent,
    AuctionComponent,
    AuctionDetailsComponent,
    AuctionReportsComponent, 
    AuthorizationLetterComponent,
    DisposalLetterComponent,
    VehicleAllocationComponent,
    ReturnedVehiclesComponent,
    ReturnedDetailsComponent,
    DisposalHistoryComponent,
    ReportsComponent,
    QuartelyReportComponent,
    AllQuartelyReportsComponent,
    QuartelyReportDetailsComponent,
    ProjectExtensionRequestComponent,
    AllProjectExtensionsComponent,
    ProjExtensionApproveComponent,
    CostBenefitComponent,
    DeviceLocationComponent,
    ArchiveComponent,
    PinkCardComponent,
    OperationTablesComponent,
    EditAuctionComponent,
    EditDisposalComponent, 
    AuctionHistoryComponent,
  ],
  imports: [
    CommonModule,SharedModule, VehicleManagementRoutingModule,QRCodeModule, SelectDropDownModule, SharedModule
  ]
})
export class VehicleManagementModule { }
