import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component } from '@angular/core';
import { faSearch, faCaretLeft, faCaretRight, faCar, faCheck } from '@fortawesome/free-solid-svg-icons';
import { User } from '../../models/user.interface';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-vehicle-allocation',
  templateUrl: './vehicle-allocation.component.html',
  styleUrl: './vehicle-allocation.component.scss'
})
export class VehicleAllocationComponent {
  allFormsSubmitted = false;
  searchIcon = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  carIcon = faCar;
  checkIcon = faCheck;
  searchText: string = '';
  acquisitions: any[] = [];
  filteredAcquisitions: any[] = [];
  displayedAcquisitions: any[] = []; 
  currentFilter: string = 'all'; 
  pageSize = 7; 
  currentPage = 1;
  totalPages: number = 0;
  
  institutionId: string = ''; 
  userRole: string='';
  
  constructor(private http: HttpClient, private router: Router, private cd: ChangeDetectorRef) { }
   
  ngOnInit() {
      this.getUserDetails();
      this.fetchAcquisitions();
      this.isLogistics();
  }

  isLogistics():boolean{
    const data = localStorage.getItem('localUserData');
    if(data !=null){
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }

  
  fetchAcquisitions() {
    if (!this.institutionId || !this.userRole) {
      console.error('Institution ID or User Role not found');
      return;
    }

    let url: string;
    switch (this.userRole) {
      case 'Institutional logistics':
      case 'Institutional CBM':
        url = `${environment.otherUrl}/AllAcquisitionsByInstitution?acquisitionId=${this.institutionId}`;
        break;

      case 'Fleet Mgt Senior Engineer':
      case 'DG Transport':
      case 'Permanent Secretary':
      case 'Minister of state':
        url = `${environment.otherUrl}/AllAcquisitions`;
        break;

      default:
        console.error('Unrecognized user role');
        return;
    }

    this.http.get<any[]>(url).subscribe(
      (response:any[]) => {
        const allocationRequests = response.filter(
          // (acquisition) => acquisition.requestType.id === '0ad9916d-e5f8-4204-b5dd-e50fd13a4640' && acquisition.statusType.id === 'dd242825-ad6d-4f40-aa67-d9bf35d4da1c' && acquisition.isAllVehicleregrequestSubmitted == false
          (acquisition) => acquisition.requestType.id === '0ad9916d-e5f8-4204-b5dd-e50fd13a4640' 
        );
        this.acquisitions = allocationRequests;
        this.applyCurrentFilter();
      },
      (error) => {
        console.error('Error fetching data:', error);
      }
    );
  }

  filterAcquisitionsByRole() {
    
    this.filteredAcquisitions = [...this.acquisitions]; 
  
    switch (this.userRole) {
      case 'Institutional logistics':
       
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          acquisition =>
            acquisition.institutionId === this.institutionId 
            // && !acquisition.isAllVehicleregrequestSubmitted
        );
        console.log("Filtered Acquisitions:", this.filteredAcquisitions);
        break;
  
      case 'Institutional CBM':

        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          acquisition =>
            acquisition.institutionId === this.institutionId &&
            acquisition.approvalLevel?.name !== 'Institutional logistics'
        );
        
        break;
  
      case 'Fleet Mgt Senior Engineer':
      
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          acquisition =>
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM'
        );

        break;
        
      case 'DG Transport':
       
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          acquisition =>
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM' &&
            acquisition.approvalLevel?.name !== 'Fleet Mgt Senior Engineer'
        );
        break;
  
      case 'Permanent Secretary':
       
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          acquisition =>
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM' &&
            acquisition.approvalLevel?.name !== 'Fleet Mgt Senior Engineer' &&
            acquisition.approvalLevel?.name !== 'DG Transport'
        );
        break;
  
      case 'Minister of state':
        
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          acquisition => 
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM' &&
            acquisition.approvalLevel?.name !== 'Fleet Mgt Senior Engineer' &&
            acquisition.approvalLevel?.name !== 'DG Transport'&&
            acquisition.approvalLevel?.name !== 'Permanent Secretary'
            
        );
        break;
  
      default:
        console.error('Unrecognized user role:', this.userRole);
        break;
    }
    console.log("After filtering:", this.filteredAcquisitions); 
   
    this.updateDisplayedAcquisitions();
  }
  
  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id;
      this.userRole = parsedObj.data.user.role.name;
      console.log('User Role:', this.userRole); 
    }
  }
  
  allocateVehicle(acquisitionId: string): void {
    console.log('Acquisition ID:', acquisitionId);
    this.router.navigateByUrl(`/vehicle-management/returned-vehicles/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  viewAcquisition(acquisitionId: string): void {
    console.log('Acquisition ID:', acquisitionId);
    this.router.navigateByUrl(`/vehicle-management/acquisition-details/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
  
  allPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  updateDisplayedAcquisitions() {
    // Determine the start and end indices based on the current page and page size
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredAcquisitions.length);
    
    this.displayedAcquisitions = this.filteredAcquisitions.slice(startIndex, endIndex);
    // Slice the filtered acquisitions array to get the current page's data
  
    // Calculate the total number of pages based on the length of the filtered acquisitions
    this.totalPages = Math.ceil(this.filteredAcquisitions.length / this.pageSize);
  }
  
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedAcquisitions();
      this.cd.markForCheck(); // Trigger change detection
    }
  }
  
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedAcquisitions();
      this.cd.markForCheck(); // Trigger change detection
    }
  }
  
  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedAcquisitions();
      this.cd.markForCheck(); // Trigger change detection
    }
  }
  getPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
      console.log("Page number: ",pageNumbers);
    }
    return pageNumbers;
  }
  
  navigateToRequestVehicle(): void {
    this.router.navigateByUrl('vehicle-management/request-vehicle');
  }

  getFirstEntryIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }
  
  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.filteredAcquisitions.length);
  }
  
  sortable(event: any) {
    const selectedField = event.target.value;
  
    if (!selectedField) {
      this.filteredAcquisitions.sort((a, b) => a.institution.localeCompare(b.institution));
    } else {
      this.filteredAcquisitions.sort((a, b) => {
        const fieldA = a[selectedField];
        const fieldB = b[selectedField];
        if (typeof fieldA === 'string' && typeof fieldB === 'string') {
          return fieldA.localeCompare(fieldB);
        }
        return 0;
      });
    }
  
    // Re-apply the displayed data logic after sorting
    this.updateDisplayedAcquisitions();
  }
  
  editAcquisition(acquisitionId: string): void{
    console.log("AcquisitionId: ",acquisitionId);
    this.router.navigateByUrl(`/vehicle-management/edit-requested-vehicle/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'bg-success'; // Corresponds to green
        case 'pending':
            return 'bg-secondary'; // Corresponds to yellow/orange
        case 'denied':
            return 'bg-danger'; // Corresponds to red
        case 'rfac':
            return 'bg-info'; // Corresponds to blue
        case 'progress':
            return 'bg-warning'; // Corresponds to blue
        default:
            return ''; // Default or fallback class
    }
}
getProgressWidth(status: string): string {
  switch (status.toLowerCase()) {
    case 'approved':
      return '100%'; // Corresponds to green
    case 'pending':
      return '70%'; // Corresponds to yellow/orange
    case 'denied':
      return '100%'; // Corresponds to red
    case 'rfac':
      return '60%'; // Corresponds to blue
    case 'progress':
      return '80%'; // Corresponds to blue
    default:
      return '';
  }
}
registerVehicle(acquisitionId: string):void{
  console.log("Acquisition Id: ", acquisitionId);
  this.router.navigateByUrl(`/vehicle-management/register-vehicle/${acquisitionId}`).then(success =>{
    if(success){
      console.log('Navigation Successful');
    }else{
      console.error('Navigation Failed');
    }
  }).catch(error => {
    console.error('Error navigating: ', error);
  });
}

applyCurrentFilter() {
  // Apply role-based filtering
  this.filterAcquisitionsByRole();

  // Apply status-based filtering based on the current filter
  switch (this.currentFilter) {
    case 'all':
      break; // No additional filtering for 'all'
    case 'in-progress':
      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        (item) => item.statusType?.name === 'PENDING' || item.statusType?.name === 'PROGRESS'
      );
      break;
    case 'completed':
      this.filteredAcquisitions = this.filteredAcquisitions.filter(
        (item) => item.statusType?.name === 'APPROVED' || item.statusType?.name === 'DENIED' 
      );
      break;
    default:
      console.error('Invalid filter type:', this.currentFilter);
  }

  this.currentPage = 1; // Reset to the first page when changing filters
  this.updateDisplayedAcquisitions(); // Refresh the displayed acquisitions
}

setFilter(filterType: string) {
  this.currentFilter = filterType;
  this.applyCurrentFilter(); // Apply the selected filter
}
}
