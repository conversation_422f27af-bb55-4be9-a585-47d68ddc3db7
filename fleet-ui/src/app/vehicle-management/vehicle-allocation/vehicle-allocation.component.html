<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicles-container">
      <div class="header d-flex flex-row justify-content-between">
          <h2 class="table-title">List Of Allocation Requests</h2>
      </div>
      <div class="card">
          <div class="table-header d-flex flex-row justify-content-between">
              <div class="sorting-group d-flex flex-row p-2">
                  <label for="sort-field" class="text-muted p-1">Sort by:</label>
                  <select id="sort-field" class="select" (change)="sortable($event)">
                      <option value="">Select Field</option>
                      <option value="institution">Institution</option>
                      <option value="requestType">Request Type</option>
                      <option value="ownershipType">Ownership Type</option>
                      <option value="status">Status</option>
                    </select>
              </div>
          </div>

          <div class="table-only p-3">
              <table class="table table-stripped">
                  <thead>
                      <tr>
                          <!-- <th>Request Description</th> -->
                          <th sortable = "institution">Institution</th>
                          <th sortable="requestType">Type of Request</th>
                          <th sortable ="ownershipType">Type of ownership</th>
                          <th sortable = "status">Status</th>
                          <th sortable="approvalLevel"> Current Level</th>
                          <th>Action</th>
                      </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of displayedAcquisitions">
                      <!-- <td>{{ item.description }}</td> -->
                      <td>{{ item.institution }}</td>
                      <td>{{ item.requestType.name }}</td>
                      <td>{{ item.ownershipType.name }}</td>
                      <td>
                        <div class="progress">
                            <div class="progress-bar"
                                 [ngClass]="getStatusButtonClass(item.statusType.name)"
                                 role="progressbar"
                                 [style.width]="getProgressWidth(item.statusType.name)"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                {{ item.statusType.name }}
                            </div>
                        </div>
                    </td>

                      <td>{{item.approvalLevel.name}}</td>
                      <td class="d-flex flex-row">
                        <button class="btn btn-outline-primary view-btn" (click)="viewAcquisition(item.id)">View </button>
                        <button class="btn register-btn" *ngIf="item.isAllVehicleregrequestSubmitted == false" (click)="allocateVehicle(item.id)">Allocate Vehicle</button>
                      </td>
                    </tr>
                  </tbody>
              </table>
          </div>

          <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
              <div class="pagination-info">
                Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredAcquisitions.length }} entries
              </div>
              <ul class="pagination justify-content-center">
                <li class="page-item">
                  <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                    <fa-icon [icon]="caretLeft"></fa-icon>
                  </button>
                </li>
                <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                  <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                    {{ pageNumber }}
                  </button>
                </li>
                <li class="page-item">
                  <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                    <fa-icon [icon]="caretRight"></fa-icon>
                  </button>
                </li>
              </ul>
            </nav>

      </div>
  </div>
</div>
