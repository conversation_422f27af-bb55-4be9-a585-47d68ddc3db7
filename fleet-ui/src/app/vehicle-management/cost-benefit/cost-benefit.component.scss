*{
    text-decoration: none;
}
.page{
    background-color:#D9D9D94D;
    width: 83% !important;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
    height: 90%;
}
.form-container::-webkit-scrollbar{
    width: 0;
}
h1{
    font-size: 20px;
    color: #28A4E2;
    font-weight: 500;
    margin-bottom: 5px;
}
p{
    font-size: 12px;
    color: #999797;
}
.card {
    z-index: 0;
    border: none;
    border-radius: 0.5rem;
    position: relative;
    padding: 15px;
}
label{
    color: #000000AB;
    font-size: 14px;
    padding-bottom: 8px;
}
input, select, textarea {
    border: 1px solid #eaeaf5;
    font-size: 13px;
    border-radius: 8px;
    padding: 10px;
}
.submit-btn{
    background-color: #099c42;
    color: #ffffff;
    border: none;
    font-size: 15px;
    font-weight: 700;
    padding: 8px;
    transition: background-color 0.3s, transform 0.3s;
    position: relative;
    
}
.hidden{
    display: none !important;
}
.remove-btn{
    // border: 1px solid rgb(170, 30, 30);
    color: rgb(170, 30, 30);
    font-size: 12px;
    cursor: pointer;
    width: 150px;
    margin-left: 15px;
}
.add-btn{
    // border: 1px solid rgb(57, 141, 57);
    color: rgb(57, 141, 57);
    font-size: 13px;
    cursor: pointer;
    font-weight: 500;
    width: 150px;
    margin-left: 15px;
}
.main-label{
    font-weight: bold;
}

.d-flex label{
    width: 250px;
}
.small-group{
    background-color: rgb(240, 245, 240);
    width: 95%;
    padding: 20px;
    border-radius: 0px 0px 20px 20px;
    margin-bottom: 20px;
    
}
.button-container{
    display: flex;
    justify-content: flex-end;
}
.group{
    margin-bottom: 20px;
}
.form-group .card:hover{
    cursor: pointer;
}
.small-cards{
    background-color: rgb(240, 245, 240);
    border-radius: 20px 20px 0px 0px;
    width: 95%;
    // margin-bottom: 20px;
}
.iconContainer{
    background-color: rgb(240, 245, 240);
    border: 1px solid #000000;
}
  .saving {
    cursor: not-allowed;
  }
  
  .saved {
    background-color: #28a745; 
    transform: scale(1.05);
    animation: success-animation 0.4s ease-in-out;
  }
  
  @keyframes success-animation {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1.05);
    }
  }
  
  