
<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>
  <div class="page">
    <div class="header d-flex flex-row justify-content-between w-50">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
      <h1>Vehicle Operation Cost</h1>
    </div>
    <div class="card main-card">
      <p>Please select an activity to report vehicle operation cost</p>
      <div class="row form-container">
        <form id="registerForm" [formGroup]="costBenefitForm" class="text-center" method="post" (ngSubmit)="onSubmit()" enctype="multipart/form-data">
          <div *ngFor="let group of activityOnVehicles | groupActivities" class="row mt-4">
            <div *ngFor="let activity of group" class="form-group col-md-6 group">
              <div class="card shadow small-cards" [class.selected]="selectedActivity === activity.id" (click)="selectActivity(activity)">
                <div class="card-body d-flex align-items-center">
                  <div class="iconContainer d-flex justify-content-center align-items-center rounded-circle me-3" style="width: 40px; height: 40px;">
                      <fa-icon [icon]="plusIcon"></fa-icon>
                  </div>
                  <div class="text-title" style="font-size: 16px;">
                      <span class="link-text main-label">{{activity.name}}</span>
                  </div>
                </div>
              </div>
              <!-- <label class="main-label">
                <input type="radio" name="selectedActivity" (change)="selectActivity(activity)" />
                {{ activity.name }}
              </label> -->
              <!-- <div *ngIf="dynamicControls[activity.code]" [formGroupName]="activity.code" class="card small-group mt-2"> -->
                <div *ngIf="dynamicControls[activity.code]" [formGroupName]="activity.code" [@toggleHeight]="selectedActivity === activity.id ? 'expanded' : 'collapsed'" class="card shadow small-group" [ngStyle]="{ overflow: activity.id === 'collapsed' ? 'hidden' : 'visible' }">
                  <!-- <ng-container > -->
                    <ng-container [ngSwitch]="activity.code">
    
                      <div *ngSwitchCase="'FC'">
                        <div class="d-flex align-items-center mt-2">
                          <label>Fuel Quantity (liters)<span *ngIf="isFieldRequired('fuelQuantity')" class="text-danger"> *</span>:</label>
                          <input type="number" class="form-control" formControlName="fuelQuantity" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Fuel Cost<span *ngIf="isFieldRequired('fuelCost')" class="text-danger"> *</span>:</label>
                          <input type="number" class="form-control" formControlName="fuelCost" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Fuel Mileage<span *ngIf="isFieldRequired('fuelMileage')" class="text-danger"> *</span>:</label>
                          <input type="number" class="form-control" formControlName="fuelMileage" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Driver's Name<span *ngIf="isFieldRequired('driveName')" class="text-danger"> *</span>:</label>
                          <input type="text" class="form-control" formControlName="driveName" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Fuel Consumption Date<span *ngIf="isFieldRequired('fuelConsumptionDate')" class="text-danger"> *</span>:</label>
                          <input type="date" class="form-control" formControlName="fuelConsumptionDate" />
                        </div>
    
                        <div class="button-container">
                          <button type="submit" class="btn submit-btn col-md-4 mt-4" [disabled]="isSaving || isSaved"  [ngClass]="{ 'saving': isSaving, 'saved': isSaved }" >
                            <ng-container *ngIf="!isSaving && !isSaved">
                              <fa-icon [icon]="saveIcon" class="pe-1"></fa-icon> Save
                            </ng-container>
                            <ng-container *ngIf="isSaving">
                              <span class="spinner-border spinner-border-sm me-1"></span> Saving...
                            </ng-container>
                            <ng-container *ngIf="isSaved">
                              <fa-icon [icon]="checkIcon" class="pe-1"></fa-icon> Saved
                            </ng-container>
                          </button>
                        </div>                                               
                        
                        <!-- <app-save-button
                          [isSaving]="isSaving"
                          [isSaved]="isSaved"
                          [saveIcon]="saveIcon"
                          [checkIcon]="checkIcon"
                          buttonText="Save"
                        ></app-save-button> -->
                      </div>
    
                      <div *ngSwitchCase="'SC'">
                        <div class="d-flex align-items-center mt-2">
                          <label>Oil Service Name<span *ngIf="isFieldRequired('oilServiceCategoryID')" class="text-danger"> *</span>:</label>
                          <select class="form-control" formControlName="oilServiceCategoryID">
                            <option *ngFor="let service of services" [value]="service.id">{{ service.name }}</option>
                          </select>
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Oil Service Cost<span *ngIf="isFieldRequired('oilServiceCost')" class="text-danger"> *</span>:</label>
                          <input type="number" class="form-control" formControlName="oilServiceCost" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Oil Service Mileage<span *ngIf="isFieldRequired('oilServiceMileage')" class="text-danger"> *</span>:</label>
                          <input type="number" class="form-control" formControlName="oilServiceMileage" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Oil Service Date<span *ngIf="isFieldRequired('oilServiceDate')" class="text-danger"> *</span>:</label>
                          <input type="date" class="form-control" formControlName="oilServiceDate" />
                        </div>
    
                        <!-- <div class="button-container">
                          <div></div>
                          <button type="submit" class="btn submit-btn col-md-4 mt-4" [disabled]="isSaving">
                            <ng-container *ngIf="!isSaving">
                              <fa-icon [icon]="saveIcon" class="pe-1"></fa-icon> Save
                            </ng-container>
                            <ng-container *ngIf="isSaving">
                              <span class="spinner-border spinner-border-sm me-1"></span> Saving...
                            </ng-container>
                          </button>
                        </div> -->

                        <div class="button-container">
                          <button type="submit" class="btn submit-btn col-md-4 mt-4" [disabled]="isSaving || isSaved"  [ngClass]="{ 'saving': isSaving, 'saved': isSaved }" >
                            <ng-container *ngIf="!isSaving && !isSaved">
                              <fa-icon [icon]="saveIcon" class="pe-1"></fa-icon> Save
                            </ng-container>
                            <ng-container *ngIf="isSaving">
                              <span class="spinner-border spinner-border-sm me-1"></span> Saving...
                            </ng-container>
                            <ng-container *ngIf="isSaved">
                              <fa-icon [icon]="checkIcon" class="pe-1"></fa-icon> Saved
                            </ng-container>
                          </button>
                        </div>
                        
    
                      </div>
    
                      <div *ngSwitchCase="'IC'">
                        <div class="d-flex align-items-center mt-2">
                          <label>Insurance Type<span *ngIf="isFieldRequired('insuranceType')" class="text-danger"> *</span>:</label>
                          <select id="insuranceType" formControlName="insuranceType" class="form-control">
                            <option value="Comprehensive Insurance">Comprehensive Insurance</option>
                            <option value="Third Party Insurance">Third Party Insurance</option>
                          </select>
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Insurance Period (in months)<span *ngIf="isFieldRequired('insurancePeriod')" class="text-danger"> *</span>:</label>
                          <select id="insurancePeriod" formControlName="insurancePeriod" class="form-control">
                            <option value="3">3 Months</option>
                            <option value="6">6 Months</option>
                            <option value="9">9 Months</option>
                            <option value="12">12 Months</option>
                          </select>
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Insurance Cost<span *ngIf="isFieldRequired('insuranceCost')" class="text-danger"> *</span>:</label>
                          <input type="number" class="form-control" formControlName="insuranceCost" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Insurance Acquisition Date<span *ngIf="isFieldRequired('insuranceAcquisitionDate')" class="text-danger"> *</span>:</label>
                          <input type="date" class="form-control" formControlName="insuranceAcquisitionDate" />
                        </div>
    
                        <div class="button-container">
                          <button type="submit" class="btn submit-btn col-md-4 mt-4" [disabled]="isSaving || isSaved"  [ngClass]="{ 'saving': isSaving, 'saved': isSaved }" >
                            <ng-container *ngIf="!isSaving && !isSaved">
                              <fa-icon [icon]="saveIcon" class="pe-1"></fa-icon> Save
                            </ng-container>
                            <ng-container *ngIf="isSaving">
                              <span class="spinner-border spinner-border-sm me-1"></span> Saving...
                            </ng-container>
                            <ng-container *ngIf="isSaved">
                              <fa-icon [icon]="checkIcon" class="pe-1"></fa-icon> Saved
                            </ng-container>
                          </button>
                        </div>
    
                      </div>
    
                      <div *ngSwitchCase="'MSC'">
                        <div class="d-flex align-items-center mt-2">
                          <label>Activity Description<span *ngIf="isFieldRequired('activityDescription')" class="text-danger"> *</span>:</label>
                          <textarea class="form-control" formControlName="activityDescription" rows="4"></textarea>
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Spare Parts Repaired<span *ngIf="isFieldRequired('sparePartsRepaired')" class="text-danger"> *</span>:</label>
                          <input type="text" class="form-control" formControlName="sparePartsRepaired" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Maintenance Cost<span *ngIf="isFieldRequired('maintenanceCost')" class="text-danger"> *</span>:</label>
                          <input type="number" class="form-control" formControlName="maintenanceCost" />
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Maintenance Activity Observation<span *ngIf="isFieldRequired('maintenanceActivityObeservation')" class="text-danger"> *</span>:</label>
                          <textarea class="form-control" formControlName="maintenanceActivityObeservation" rows="4"></textarea>
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label>Maintenance Activity Date<span *ngIf="isFieldRequired('maintenanceActivityDate')" class="text-danger"> *</span>:</label>
                          <input type="date" class="form-control" formControlName="maintenanceActivityDate" />
                        </div>
    
                        <div class="button-container">
                          <button type="submit" class="btn submit-btn col-md-4 mt-4" [disabled]="isSaving || isSaved"  [ngClass]="{ 'saving': isSaving, 'saved': isSaved }" >
                            <ng-container *ngIf="!isSaving && !isSaved">
                              <fa-icon [icon]="saveIcon" class="pe-1"></fa-icon> Save
                            </ng-container>
                            <ng-container *ngIf="isSaving">
                              <span class="spinner-border spinner-border-sm me-1"></span> Saving...
                            </ng-container>
                            <ng-container *ngIf="isSaved">
                              <fa-icon [icon]="checkIcon" class="pe-1"></fa-icon> Saved
                            </ng-container>
                          </button>
                        </div>
    
                      </div>
    
                      <div *ngSwitchCase="'VC'" >
                        <div class="d-flex align-items-center mt-2">
                          <label for="vehicleStatus">Vehicle Status<span *ngIf="isFieldRequired('vehicleStatus')" class="text-danger"> *</span>:</label>
                          <select class="form-control" formControlName="vehicleStatus" id="vehicleStatus">
                            <option *ngFor="let vehicleStatus of vehicleStatuses" [value]="vehicleStatus.id">{{vehicleStatus.name}}</option>
                          </select>
                        </div>
                        <div class="d-flex align-items-center mt-2">
                          <label for="isVehicleActive">Is the vehicle still in use? <span *ngIf="isFieldRequired('isVehicleActive')" class="text-danger"> *</span>:</label>
                          <div class="form-checks d-flex align-items-center" >
                            <label for="isVehicleActiveYes">
                              <input type="radio" id="isVehicleActiveYes" value="true" formControlName="isVehicleActive" name="isVehicleActive"> 
                              Yes
                            </label>
    
                            <label for="isVehicleActiveNo">
                              <input type="radio" id="isVehicleActiveNo" value="false" formControlName="isVehicleActive" name="isVehicleActive">
                              No
                            </label>
    
                            <!-- <div class="d-flex align-items-center mt-2">
                              <input class="form-check-input" type="radio" id="isVehicleActiveYes" value="true" formControlName="isVehicleActive" name="isVehicleActive">  -->
                              <!-- <label class="form-check-label" for="isVehicleActiveYes">Yes</label> -->
                            <!-- </div> -->
                            <!-- <div class="d-flex align-items-center mt-2">
                              <input class="form-check-input" type="radio" id="isVehicleActiveNo" value="false" formControlName="isVehicleActive" name="isVehicleActive">
                              <label class="form-check-label" for="isVehicleActiveNo">No</label>
                            </div> -->
                          </div>
                        </div>
                        <div class="button-container">
                          <button type="submit" class="btn submit-btn col-md-4 mt-4" [disabled]="isSaving || isSaved"  [ngClass]="{ 'saving': isSaving, 'saved': isSaved }" >
                            <ng-container *ngIf="!isSaving && !isSaved">
                              <fa-icon [icon]="saveIcon" class="pe-1"></fa-icon> Save
                            </ng-container>
                            <ng-container *ngIf="isSaving">
                              <span class="spinner-border spinner-border-sm me-1"></span> Saving...
                            </ng-container>
                            <ng-container *ngIf="isSaved">
                              <fa-icon [icon]="checkIcon" class="pe-1"></fa-icon> Saved
                            </ng-container>
                          </button>
                        </div>
                      </div>
    
                    </ng-container>
                  <!-- </ng-container> -->
              </div>
            </div>
          </div>

          <!-- <div class="mt-4">
            <div class="small-group row">
              <div class="form-group col-md-6 d-flex align-items-center">
                <label for="vehicleStatus">Vehicle Status:</label>
                <select class="form-control" formControlName="vehicleStatus" id="vehicleStatus">
                  <option *ngFor="let vehicleStatus of vehicleStatuses" [value]="vehicleStatus.id">{{vehicleStatus.name}}</option>
                </select>
              </div>
  
              <div class="form-group col-md-6">
                <label for="isVehicleActive">Is the vehicle still in use?</label>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" id="isVehicleActiveYes" value="true" formControlName="isVehicleActive" name="isVehicleActive"> 
                  <label class="form-check-label" for="isVehicleActiveYes">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" id="isVehicleActiveNo" value="false" formControlName="isVehicleActive" name="isVehicleActive">
                  <label class="form-check-label" for="isVehicleActiveNo">No</label>
                </div>
              </div>
            </div>
          </div> -->
        </form>
      </div>
    </div>
  </div>
</div>
