import { HttpClient } from '@angular/common/http';
import { Component, OnInit} from '@angular/core';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { faAdd, faArrowLeft, faArrowRightFromBracket, faCheck } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';

@Component({
  selector: 'app-cost-benefit',
  animations: [
    trigger('toggleHeight', [
    state('collapsed', style({ height: '0px', opacity: 0 })),
    state('expanded', style({ height: '*', opacity: 1 })),
    transition('collapsed => expanded', [
      animate('300ms ease-in-out', style({ height: '*', opacity: 1 })),
    ]),
    transition('expanded => collapsed', [
      animate('300ms ease-in-out', style({ height: '0px', opacity: 0 })),
    ]),
  ])
  ],
  templateUrl: './cost-benefit.component.html',
  styleUrls: ['./cost-benefit.component.scss']
})
export class CostBenefitComponent implements OnInit {
  vehicleId: any = '';
  costBenefitForm: FormGroup;
  plusIcon = faAdd;
  activityOnVehicles: any[] = [];
  services: any[] = [];
  dynamicControls: { [key: string]: boolean } = {};
  selectedActivity: any = null; 
  vehicleStatuses: any[] = [];
  saveIcon = faArrowRightFromBracket;
  submitting = false;
  response: any = {};
  isSaving = false;
  isSaved = false;
  checkIcon = faCheck
  backwardIcon = faArrowLeft;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private router: Router,
    private location: Location
  ) {
    this.costBenefitForm = this.fb.group({
      vehicleID: [''],
      activityOnVehicleID: ['', Validators.required],
      fuelQuantity: [null],
      fuelCost: [null],
      fuelMileage: [null],
      driveName: [null],
      fuelConsumptionDate: [null],
      oilServiceCategoryID: [null],
      oilServiceCost: [null],
      oilServiceMileage: [null],
      oilServiceDate: [null],
      insuranceType: [null],
      insurancePeriod: [null],
      insuranceCost: [null],
      insuranceAcquisitionDate: [null],
      activityDescription: [null],
      sparePartsRepaired: [null],
      maintenanceCost: [null],
      maintenanceActivityObeservation: [null],
      maintenanceActivityDate: [null],
      vehicleStatus: ['f41b70f7-8168-400e-a8ba-0c7117e4e372', Validators.required],
      isVehicleActive: [true, Validators.required],
      dateofActivity: [null, Validators.required]
    });
  }

  ngOnInit() {
    this.fetchVehicleId();
    this.fetchActivitiesOnVehicle();
    this.fetchServices();
    this.fetchVehicleStatus();
  }

  goBack(){
    this.location.back();
  }

  fetchVehicleId(): void {
    this.route.params.subscribe((params) => {
      if (params['vehicleId']) {
        this.vehicleId = params['vehicleId'];
        this.costBenefitForm.patchValue({ vehicleID: this.vehicleId });
      }
    });
  }

  fetchActivitiesOnVehicle(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/AllActivityOnVehicle`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.activityOnVehicles = response;
        this.initializeDynamicControls();
      },
      (error) => {
        console.error('Error fetching activityOnVehicle:', error);
      }
    );
  }

  fetchServices(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/AllOilServiceCategory`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.services = response;
      },
      (error) => {
        console.error('Error fetching services:', error);
      }
    );
  }

  fetchVehicleStatus(): void {
    const url = `${environment.otherUrl}/vehicle-management/vehicleStatus`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        const filteredResponse = response.filter(
          (type) => type.id === 'f41b70f7-8168-400e-a8ba-0c7117e4e372' || type.id === '46977316-67bd-497f-9778-159d8399cff5'
        );
        this.vehicleStatuses = filteredResponse.map((type) => ({ id: type.id, name: type.name }));
      }
    );
  }

  initializeDynamicControls() {
    this.activityOnVehicles.forEach(activity => {
      this.dynamicControls[activity.code] = false;
    });
  }

  selectActivity(activity: any) {
    this.costBenefitForm.reset({
      vehicleID: this.vehicleId,
      activityOnVehicleID: activity.id
    });
    
    this.costBenefitForm.setValidators([]);
    this.costBenefitForm.updateValueAndValidity();
    
    this.dynamicControls = {};
    this.costBenefitForm.removeControl('dynamicActivities');
    
    this.dynamicControls[activity.code] = true;
    this.selectedActivity = activity.id;

    const activityFormGroup = this.createActivityFormGroup(activity);
    this.costBenefitForm.addControl(activity.code, activityFormGroup);

    for (const control in activityFormGroup.controls) {
      if (activityFormGroup.controls.hasOwnProperty(control)) {
        activityFormGroup.get(control)?.setValidators([Validators.required]);
        activityFormGroup.get(control)?.updateValueAndValidity();
      }
    }
  }

  createActivityFormGroup(activity: { code: any; id: any; name: any; }): FormGroup {
    let group: FormGroup;
    switch (activity.code) {
      case 'FC':
        group = this.fb.group({
          fuelQuantity: [null],
          fuelCost: [null],
          fuelMileage: [null],
          driveName: [null],
          fuelConsumptionDate: [null]
        });
        break;
      case 'SC':
        group = this.fb.group({
          oilServiceCategoryID: [null],
          oilServiceCost: [null],
          oilServiceMileage: [null],
          oilServiceDate: [null]
        });
        break;
      case 'IC':
        group = this.fb.group({
          insuranceType: [null],
          insurancePeriod: [null],
          insuranceCost: [null],
          insuranceAcquisitionDate: [null]
        });
        break;
      case 'MSC':
        group = this.fb.group({
          activityDescription: [null],
          sparePartsRepaired: [null],
          maintenanceCost: [null],
          maintenanceActivityObeservation: [null],
          maintenanceActivityDate: [null]
        });
        break;
      case 'VC':
        group = this.fb.group({
          vehicleStatus: ['f41b70f7-8168-400e-a8ba-0c7117e4e372'],
          isVehicleActive: [true],
        });
        break;
      default:
        group = this.fb.group({});
    }
    return group;
  }

  isFieldRequired(fieldName: string): boolean {
    const requiredFields: { [key: string]: string[] } = {
      'FC': ['fuelQuantity', 'fuelCost', 'fuelMileage', 'driveName', 'fuelConsumptionDate'],
      'SC': ['oilServiceCategoryID', 'oilServiceCost', 'oilServiceMileage', 'oilServiceDate'],
      'IC': ['insuranceType', 'insurancePeriod', 'insuranceCost', 'insuranceAcquisitionDate'],
      'MSC': ['activityDescription', 'sparePartsRepaired', 'maintenanceCost', 'maintenanceActivityObeservation', 'maintenanceActivityDate'],
      'VC': ['vehicleStatus', 'isVehicleActive']
    };
  
    const selectedActivityCode = this.activityOnVehicles.find(activity => activity.id === this.selectedActivity)?.code;
  
    return selectedActivityCode && requiredFields[selectedActivityCode as keyof typeof requiredFields]?.includes(fieldName);
  }
  

  // onSubmit() {
  //   // Validate only the dynamic activity form group
  //   const selectedActivityCode = this.activityOnVehicles.find(activity => activity.id === this.selectedActivity)?.code;
  //   const activityFormGroup = this.costBenefitForm.get(selectedActivityCode);
  
  //   // If the form group is invalid, display an error and return
  //   if (activityFormGroup && activityFormGroup.invalid) {
  //     this.toastr.error('Please fill all required fields for the selected activity.');
  //     this.isSaving = false; // Ensure the button is not stuck in disabled state
  //     return;
  //   }
  
  //   this.submitting = true;
  //   this.isSaving = true;
  
  //   const formValue = this.costBenefitForm.value;
  
  //   if (this.selectedActivity !== '6e7f4ef4-d622-402c-9bf6-de111e99b698') {
  //     this.response = {
  //       vehicleID: formValue.vehicleID,
  //       activityOnVehicleID: this.selectedActivity,
  //       vehicleStatus: 'f41b70f7-8168-400e-a8ba-0c7117e4e372',
  //       isVehicleActive: true,
  //       ...formValue[selectedActivityCode]
  //     };
  //   } else {
  //     this.response = {
  //       vehicleID: formValue.vehicleID,
  //       activityOnVehicleID: this.selectedActivity,
  //       ...formValue[selectedActivityCode]
  //     };
  //   }
  
  //   console.log(JSON.stringify(this.response));
  
  //   // Delay submission with setTimeout
  //   setTimeout(() => {
  //     this.http.post(`${environment.otherUrl}/create-activity-performed`, this.response).subscribe(
  //       (response: any) => {
  //         console.log(response);
  //         this.toastr.success('Activity Recorded Successfully!');
  //         this.submitting = false;
  //         this.isSaving = false;
  //         this.costBenefitForm.reset();
  //         // this.router.navigate(['/vehicle-management/all-registered']);
  //       },
  //       (error) => {
  //         this.submitting = false;
  //         this.isSaving = false;
  //         this.toastr.error('Error Recording Activity ' + error.message);
  //       }
  //     );
  //   }, 2000);
  // }
  
  onSubmit() {
    console.log('Form Value:', this.costBenefitForm.value);
    console.log('Form Status:', this.costBenefitForm.status);
    
    const selectedActivityCode = this.activityOnVehicles.find(activity => activity.id === this.selectedActivity)?.code;
    console.log('Selected Activity Code:', selectedActivityCode);
    
    if (selectedActivityCode) {
      const activityFormGroup = this.costBenefitForm.get(selectedActivityCode) as FormGroup;
      console.log(this.costBenefitForm.get('FC') as FormGroup);
      
      if (activityFormGroup && activityFormGroup.invalid) {
        console.log('Invalid Form Group:', activityFormGroup.errors);
        this.toastr.error('Please fill all required fields for the selected activity.');
        this.isSaving = false;
        return;
      }
    }
  
    this.submitting = true;
    this.isSaving = true;
    this.isSaved = false; 
  
    const formValue = this.costBenefitForm.value;
  
    if (this.selectedActivity !== '6e7f4ef4-d622-402c-9bf6-de111e99b698') {
      this.response = {
        vehicleID: formValue.vehicleID,
        activityOnVehicleID: this.selectedActivity,
        vehicleStatus: 'f41b70f7-8168-400e-a8ba-0c7117e4e372',
        isVehicleActive: true,
        ...formValue[selectedActivityCode]
      };
    } else {
      this.response = {
        vehicleID: formValue.vehicleID,
        activityOnVehicleID: this.selectedActivity,
        ...formValue[selectedActivityCode]
      };
    }
  
    console.log(JSON.stringify(this.response));
  
    
    setTimeout(() => {
      this.http.post(`${environment.otherUrl}/create-activity-performed`, this.response).subscribe(
        (response: any) => {
          console.log(response.statusCode);
          if (response.statusCode === 404 || response.statusCode === 500) {
            this.toastr.error(`Unexpected response status: ${response.statusCode} - ${response.message || 'Unknown error'}`);
          } else {
            console.log(response);
            this.toastr.success('Activity Recorded Successfully!');
            this.costBenefitForm.reset();
            this.isSaved = true; 
          }
          this.submitting = false;
          this.isSaving = false;

          setTimeout(() => {
            this.isSaved = false;
            this.submitting = false;
          }, 2000); 
        
        },
        (error) => {
          this.submitting = false;
          this.isSaving = false;
          if (error.status === 404) {
            this.toastr.error('Error: The date you are reporting has not been reached.');
          } else {
            this.toastr.error(`Error Recording Activity: ${error.message}`);
          }
        }
      );
    }, 2000);
  }
  
}
