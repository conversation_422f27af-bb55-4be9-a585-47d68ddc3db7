import { Component } from '@angular/core';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { faArrowLeft, faMinus, faPlus} from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';


@Component({
  selector: 'app-register-new-vehicle',
  templateUrl: './register-new-vehicle.component.html',
  styleUrl: './register-new-vehicle.component.scss'
})

export class RegisterNewVehicleComponent {
  registerVehicleForm !: FormGroup;
  files !:FormArray;
  vehicleModels: any[] = [];
  vehicleManufactures: any[] =[];
  beneficiaryAgencies: any[] = [];
  dropdownConfig: any = {
      displayKey: "name",   // Display the name of the institution
      valueField: "id",
      search: true, 
      height: '300px', // set the height
      placeholder: 'Select', // placeholder text
      customComparator: () => {}, // optional, for custom sorting
      limitTo: 0, // number of items to display in the dropdown. 0 = all
      moreText: 'more', // text for more
      noResultsFound: 'No results found!', // text for no results
      searchPlaceholder: 'Search...', 
      clearOnSelection: true, 
      inputDirection: 'ltr',
  };
  ownershipTypes: any[] = [];
  vehicleTypes: any[] = [];
  userId: any = '';
  institution: any = '';
  institutionId: any = '';
  vehicleRegRequest: any = '';
  plusIcon = faPlus;
  minusIcon = faMinus;
  selectedFileName: any;
  today: string;
  submitted = false;
  vehicleStatuses: any[] = [];
  years: number[] = [];
  backwardIcon = faArrowLeft;


  constructor(
    private formBuilder: FormBuilder, 
    private http: HttpClient, 
    private toastr: ToastrService, 
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ){
    this.registerVehicleForm = this.formBuilder.group({
      beneficiaryAgencyId: ['', Validators.required],
      beneficiaryAgency:[''],
      ownershipType: ['', Validators.required],
      vehicleType: ['', Validators.required],
      fuelType: ['', Validators.required],
      plateNumber: ['', Validators.required],
      vehicleManufacture: ['', Validators.required],
      vehicleModel: [''],
      chassisNumber: ['', [Validators.required, Validators.maxLength(50)]],
      engineNumber: ['', Validators.maxLength(50)],
      transmissionType: ['', Validators.required],
      manufactureYear: ['', Validators.required],
      odometerReading: ['', [ Validators.maxLength(50)]],
      acquisitionDate: ['', Validators.required],
      invoiceNumber: ['', [Validators.maxLength(50)]],
      invoiceDate: [''],
      customsDeclarationNumber: ['', [Validators.maxLength(50)]],
      customsDeclarationDate: [''],
      declaredAmount: ['', [Validators.maxLength(50), Validators.required]],
      projectName: ['', Validators.maxLength(50)],
      projectDescription: [''],
      projectStartDate: [''],
      projectEndDate: [''],
      vehicleStatus: ['', Validators.required],
      isVehicleActive: [true, Validators.required],
      file: [''],
      frontView: [''],
      backView: [''],
      leftView: [''],
      rightView: [''],

      files: this.formBuilder.array([this.createFileFormGroup()])
    }, { validators: this.dateValidator })
    this.files = this.registerVehicleForm.get('files') as FormArray;

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const day = currentDate.getDate().toString().padStart(2, '0');
    this.today = `${year}-${month}-${day}`;
  }
  dateValidator(form: FormGroup) {
    const startDate = form.get('projectStartDate')?.value;
    const endDate = form.get('projectEndDate')?.value;
  
    if (startDate && endDate && endDate < startDate) {
      return { invalidDateRange: true };
    }
    return null;
  }
  
  isFieldRequired(fieldName: string): boolean {
    const control = this.registerVehicleForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }
  
  goBack(){
    this.location.back();
  }

  ngOnInit():void {
    this.fetchVehicleModels();
    this.fetchVehicleManufactures();
    this.fetchBeneficiaryAgencies();
    this.fetchOwnershipTypes();
    this.fetchVehicleTypes();
    this.getUserDetails();
    this.fetchVehicleStatus();
    this.populateYears();

     this.updateRequiredValidators();

     this.registerVehicleForm.get('ownershipType')?.valueChanges.subscribe(() => {
       this.updateRequiredValidators();
     });
  }

  updateRequiredValidators(): void {
    const ownershipType = this.registerVehicleForm.get('ownershipType')?.value;

    if (ownershipType === '639bf044-1cbb-4954-b170-9843347073ca') {
      this.registerVehicleForm.get('projectName')?.setValidators([Validators.required, Validators.maxLength(50)]);
      this.registerVehicleForm.get('projectDescription')?.setValidators([Validators.required]);
      this.registerVehicleForm.get('projectStartDate')?.setValidators([Validators.required]);
      this.registerVehicleForm.get('projectEndDate')?.setValidators([Validators.required]);
    } else {
      this.registerVehicleForm.get('projectName')?.clearValidators();
      this.registerVehicleForm.get('projectDescription')?.clearValidators();
      this.registerVehicleForm.get('projectStartDate')?.clearValidators();
      this.registerVehicleForm.get('projectEndDate')?.clearValidators();
    }

    // Update form controls with the new validators
    this.registerVehicleForm.get('projectName')?.updateValueAndValidity();
    this.registerVehicleForm.get('projectDescription')?.updateValueAndValidity();
    this.registerVehicleForm.get('projectStartDate')?.updateValueAndValidity();
    this.registerVehicleForm.get('projectEndDate')?.updateValueAndValidity();
  }

  populateYears() {
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= currentYear -25; year--) {

      this.years.push(year);
    }
  }
  fetchVehicleModels(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleModels`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleModels = response.map(vehicleModel => ({ id: vehicleModel.id, name: vehicleModel.name }));
      },
      (error) => {
        console.error('Error fetching vehicle models:', error)
      }
    );
  }

  fetchVehicleManufactures(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleManufacture`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleManufactures = response.map(vehicleManufacture => ({ id: vehicleManufacture.id, name: vehicleManufacture.name }));
      },
      (error) => {
        console.error('Error fetching vehicle manufactures:', error)
      }
    );
  }

  fetchBeneficiaryAgencies(): void {
    const apiUrl = `${environment.baseUrl}/user-management/institutions`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.beneficiaryAgencies = response.map(institution => ({ id: institution.id, name: institution.name }));
      },
      (error) => {
        console.error('Error fetching beneficiary agencies:', error);
      }
    );
}

  fetchOwnershipTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/ownershipTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.ownershipTypes = response.map(ownership => ({ id: ownership.id, name: ownership.name }));
      },
      (error) => {
        console.error('Error fetching ownershipTypes:', error)
      }
    );
  }

  fetchVehicleTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleTypes = response.map(vehicleType => ({ id: vehicleType.id, name: vehicleType.name }));
      },
      (error) => {
        console.error('Error fetching vehicleTypes:', error)
      }
    );
  }

  fetchVehicleStatus(): void {
    const url = `${environment.otherUrl}/vehicle-management/vehicleStatus`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        const filteredResponse = response.filter(
          (type) => type.name === 'GOOD CONDITION' || type.name === 'BAD CONDITION'
        );
        this.vehicleStatuses = filteredResponse.map((type) => ({ id: type.id, name: type.name }));
      }
    );
  }

  getUserDetails(){
    const localUserData = localStorage.getItem('localUserData');
    if(localUserData){
      const parseObj = JSON.parse(localUserData)
      this.userId = parseObj.data.user.id;
      this.institution = parseObj.data.user.institution.name;
      this.institutionId = parseObj.data.user.institution.id;
      console.log('Logged In user: ', this.userId);
      console.log('Institution: ', this.institution);
    }
  }

  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }
  
  removeFile(index: number): void {
    this.files.removeAt(index);
  }
  
  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: [''],
      applicationId: ['']
    });
  }
  
  // onFileChange(event: any, index: number): void {
  //   event.preventDefault();
  //   const file = event.target.files[0];
  //   if (file) {
  //     const fileControl = this.files.at(index).get('file');
  //     if (fileControl) { 
  //       fileControl.setValue(file);
  //     }
  //     this.selectedFileName = file.name;
  //   }
  // }

  onFileChange(event: any, fileType: string): void {
    const file = event.target.files[0];
    if (file) {
      this.registerVehicleForm.patchValue({ [fileType]: file });
    }
  }
//   onFileChange(event: any, fileType: string): void {
//   event.preventDefault(); // To match behavior in onFileChangee()
//   const file = event.target.files[0];
  
//   if (file) {
//     // Check if the file type is jj
//     if (file.type !== 'image/jpeg' || file.type !== 'image/png' || file.type !== 'image/jpg' ) {
//       this.toastr.error('Wrong file type. File should be either jpeg, png or jpg.', 'Error');
      
//       // Clear the file input
//       event.target.value = ''; 
      
//       // Clear the form control value
//       this.registerVehicleForm.patchValue({ [fileType]: null });
      
//       return; // Exit function if validation fails
//     }

//     // If validation passes, update the form control
//     this.registerVehicleForm.patchValue({ [fileType]: file });
    
//   }
// }

onFileChangee(event: any, index: number): void {
  event.preventDefault();
  const file = event.target.files[0];
  if (file) {
    const fileControl = this.files.at(index).get('file');
    if (file.type !== 'application/pdf') {
      this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
      if (fileControl) {
        fileControl.setValue(null);
      }
      event.target.value = '';
      return;
    }
    if (fileControl) { 
      fileControl.setValue(file);
    }
    this.selectedFileName = file.name;
  }
}

  validatePicture(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    const maxSize = 5 * 1024 * 1024; // 5MB
  
    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  validateFile(file: File): boolean {
    const allowedMimeTypes = ['application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB
  
    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitAdditionalFile(vehicleId: any): void {
    const fileUploadDataFormArray = this.registerVehicleForm.get('files') as FormArray;
    fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
      if (control instanceof FormGroup) {
        const fileItem = control.value;
        if (fileItem.file) {
          const fileFormData = new FormData();
          const file = fileItem.file as File;
  
          console.log('File name:', file.name);
          console.log('File size:', file.size);
          console.log('File type:', file.type)
  
          if (this.validateFile(file)) {
            fileFormData.append('file', file);
            fileFormData.append('documentDescription', fileItem.documentDescription);
            fileFormData.append('applicationId', vehicleId);
  
            this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
              (response) => {
                console.log('API Response:', response);
                console.log("File uploaded successfully");
                this.registerVehicleForm.reset();
              },
              (error) => {
                let errorMessage = 'An unexpected error occurred.';
                if (error && error.error && error.error.message) {
                  errorMessage = error.error.message;
                }
                console.error('API Error:', errorMessage);
                this.toastr.error("An error occurred!!", 'Error');
              }
            );
          } else {
            console.warn('File size or type is invalid.');
          }
        } else {
          console.log('No file selected...');
        }
      }
    });
  }

  submitFile(file: File, fileType: string, vehicleId: string): void {
    if (file) {
      const fileFormData = new FormData();
  
      if (this.validatePicture(file)) {
        fileFormData.append('file', file);
        fileFormData.append('documentDescription', fileType);
        fileFormData.append('applicationId', vehicleId);
        this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
          (response) => {
            console.log(`${fileType} submitted successfully`, response);
          },
          (error) => {
            console.error(`Error submitting ${fileType}`, error);
            this.toastr.error(`Error submitting ${fileType}`, 'Error');
          }
        );
      } else {
        console.warn(`${fileType} size or type is invalid.`);
        this.toastr.warning(`Invalid file type or size. Only jpg, jpeg, png are allowed.`);
      }
    }
  }

  getFullDate() {
    const year = this.registerVehicleForm.get('manufactureYear')?.value;
    if (year) {
      const fullDate = `${year}-01-01`; // Format as YYYY-01-01
      console.log('Full Date:', fullDate);
      return fullDate;
    }
    return null;
  }
  // onSubmit() {
  //   this.submitted = true;
  //   if (this.registerVehicleForm.valid) {

  //     this.registerVehicleForm.patchValue({
  //       manufactureYear: this.getFullDate()
  //     })

  //     const beneficiaryAgencyId = this.registerVehicleForm.get('beneficiaryAgencyId')?.value;
  //     console.log('Selected beneficiaryAgencyId:', beneficiaryAgencyId);
  //     const selectedAgency = this.beneficiaryAgencies.find(agency => agency.id === beneficiaryAgencyId);
  //     console.log('Selected beneficiary agency:', selectedAgency);
  
  //     if (selectedAgency) {
  //       this.registerVehicleForm.patchValue({
  //         beneficiaryAgency: selectedAgency.name
  //       });
  //     } else {
  //       console.error('Selected beneficiary agency not found.');
  //       this.toastr.error('Selected beneficiary agency not found.', 'Error');
  //       return; // Exit early if the agency is not found
  //     }
  
  //     const requestData = { ...this.registerVehicleForm.value };
  //     console.log('Request Data:', requestData);
  
  //     this.http.post(`${environment.otherUrl}/existing-vehicle-registration`, requestData).subscribe(
  //       (response: any) => {
  //         console.log('response id',response.id)
  //         const vehicleId = response.id;
  //         console.log(response.id)
  //         this.submitAdditionalFile(vehicleId);
  //         this.submitFile(this.registerVehicleForm.get('frontView')?.value, 'frontView', vehicleId);
  //         this.submitFile(this.registerVehicleForm.get('backView')?.value, 'backView', vehicleId);
  //         this.submitFile(this.registerVehicleForm.get('leftView')?.value, 'leftView', vehicleId);
  //         this.submitFile(this.registerVehicleForm.get('rightView')?.value, 'rightView', vehicleId);

  //         console.log('Vehicle Registration successful: ', response);
  //         this.toastr.success(`Vehicle Registered Successfully`);
  //         this.registerVehicleForm.reset();
  //         this.router.navigate(['/vehicle-management/all-registered']);
  //       },
  //       (error) => {
  //         console.error('Error registering vehicle:', error);
  //         this.toastr.error('Error registering vehicle', 'Error');
  //       }
  //     );
  //   } else {
  //     console.error('Form Errors:');
  //     for (const controlName in this.registerVehicleForm.controls) {
  //       if (this.registerVehicleForm.get(controlName)?.errors) {
  //         console.error(`*${controlName}: `, this.registerVehicleForm.get(controlName)?.errors);
  //       }
  //     }
  //     this.toastr.error('Please fill in all fields to submit', 'Error');
  //   }
  // }
  
  onSubmit() {
  this.submitted = true;

  // Check if all required files are uploaded and have valid types
  const frontView = this.registerVehicleForm.get('frontView')?.value;
  const backView = this.registerVehicleForm.get('backView')?.value;
  const leftView = this.registerVehicleForm.get('leftView')?.value;
  const rightView = this.registerVehicleForm.get('rightView')?.value;

  const additionalFilesArray = this.registerVehicleForm.get('files') as FormArray;

  const files = [frontView, backView];
  const fileTypes = ['frontView', 'backView'];
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  
  let allFilesValid = true;
  let errorMessage = '';

  // files.forEach((file, index) => {
  //   if (file) {
  //     if (!allowedTypes.includes(file.type)) {
  //       allFilesValid = false;
  //       errorMessage += `Invalid file type for ${fileTypes[index]}. Only jpg, jpeg, png are allowed.\n`;
  //     }
  //   } else {
  //     allFilesValid = false;
  //     errorMessage += `${fileTypes[index]} is required.\n`;
  //   }
  // });

  if (!allFilesValid) {
    this.toastr.error(errorMessage.trim(), 'File Validation Error');
    return; // Stop further processing if files are invalid
  }

  // Proceed with form validation if files are valid
  if (this.registerVehicleForm.valid) {
    this.registerVehicleForm.patchValue({
      manufactureYear: this.getFullDate()
    });

    const selectedAgency = this.registerVehicleForm.get('beneficiaryAgencyId')?.value;
    console.log('Selected beneficiaryAgencyId:', selectedAgency);
    const selectedAgencyt = this.beneficiaryAgencies.find(agency => agency.id === selectedAgency);
    console.log('Selected beneficiary agency:', selectedAgencyt);

    if (selectedAgency) {
      this.registerVehicleForm.patchValue({
        beneficiaryAgency: selectedAgency.name,
        beneficiaryAgencyId: selectedAgency.id,
        vehicleType:(this.registerVehicleForm.get('vehicleType')?.value).id,
        // vehicleModel:(this.registerVehicleForm.get('vehicleModel')?.value).id,
        vehicleManufacture:(this.registerVehicleForm.get('vehicleManufacture')?.value).id
      });
    } else {
      console.error('Selected beneficiary agency not found.');
      this.toastr.error('Selected beneficiary agency not found.', 'Error');
      return; // Exit early if the agency is not found
    }

    const requestData = { ...this.registerVehicleForm.value };
    console.log('Request Data:', JSON.stringify(requestData));

    this.http.post(`${environment.otherUrl}/existing-vehicle-registration`, requestData).subscribe(
      (response: any) => {
        console.log('response id', response.id);
        const vehicleId = response.id;
        console.log(response.id);

        if (additionalFilesArray && additionalFilesArray.length > 0) {
          this.submitAdditionalFile(vehicleId);
        }

        try {

          // this.submitFile(this.registerVehicleForm.get('frontView')?.value, 'frontView', vehicleId);
          // this.submitFile(this.registerVehicleForm.get('backView')?.value, 'backView', vehicleId);

          if (frontView && allowedTypes.includes(frontView.type)) {
            this.submitFile(frontView, 'frontView', vehicleId);
          } else if (leftView) {
            this.toastr.error('Invalid file type for frontView. Only jpg, jpeg, png are allowed.', 'File Validation Error');
          }
          
          if (backView && allowedTypes.includes(backView.type)) {
            this.submitFile(backView, 'backView', vehicleId);
          } else if (backView) {
            this.toastr.error('Invalid file type for backView. Only jpg, jpeg, png are allowed.', 'File Validation Error');
          }
        } catch (error) {
          console.error('An error occurred while submitting the files:', error);
        
        }
        

        console.log('Vehicle Registration successful: ', response);
        this.toastr.success(`Vehicle Registered Successfully`);
        this.registerVehicleForm.reset();
        this.router.navigate(['/vehicle-management/all-registered']);
      },
      (error) => {
        console.error('Error registering vehicle:', error);
        this.toastr.error('Error registering vehicle', 'Error');
      }
    );
  } else {
    console.error('Form Errors:');
    for (const controlName in this.registerVehicleForm.controls) {
      if (this.registerVehicleForm.get(controlName)?.errors) {
        console.error(`*${controlName}: `, this.registerVehicleForm.get(controlName)?.errors);
      }
    }
    this.toastr.error('Please fill in all fields to submit', 'Error');
  }
}

}