<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <div class="header d-flex flex-row justify-content-between w-50">
            <button class="btn go-back-btn" (click)="goBack()">
                <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
              </button>
            <h1>Existing Vehicle Registration</h1>
        </div>

        <div class="card">
            <p>Fill in vehicle details issued by RRA to get the plate number and pink card.</p>
            <div class="row form-container">
                <form id="registerForm" [formGroup]="registerVehicleForm" class="text-center" method="post" (ngSubmit)="onSubmit()" enctype="multipart/form-data">
                    
                    <!-- First Row -->
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="beneficiaryAgencyId" [ngClass]="{'required-label': isFieldRequired('beneficiaryAgencyId')}">
                                Beneficiary Agency
                            </label>
                            <ngx-select-dropdown 
                                [options]="beneficiaryAgencies" 
                                [config]="dropdownConfig"
                                formControlName="beneficiaryAgencyId"
                                class="custom-dropdown">
                            </ngx-select-dropdown>
                            <div *ngIf="registerVehicleForm?.get('beneficiaryAgencyId')?.invalid && 
                                         (registerVehicleForm?.get('beneficiaryAgencyId')?.dirty || 
                                          registerVehicleForm?.get('beneficiaryAgencyId')?.touched || 
                                          submitted)" 
                                 class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('beneficiaryAgencyId')?.errors?.['required']">
                                    Beneficiary Agency is required.
                                </div>
                            </div>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="ownershipType" [ngClass]="{'required-label': isFieldRequired('ownershipType')}">Ownership Type</label>
                            <select id="ownershipType" class="form-control" formControlName="ownershipType">
                                <option *ngFor="let ownershipType of ownershipTypes" [value]="ownershipType.id"> {{ownershipType.name}} </option>
                            </select>
                            <div *ngIf="registerVehicleForm?.get('ownershipType')?.invalid && (registerVehicleForm?.get('ownershipType')?.dirty || registerVehicleForm?.get('ownershipType')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('ownershipType')?.errors?.['required']">
                                    Ownership Type is required.
                                </div>
                            </div>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="plateNumber" [ngClass]="{'required-label': isFieldRequired('plateNumber')}">Plate Number</label>
                            <input type="text" class="form-control" id="plateNumber" formControlName="plateNumber">
                            <div *ngIf="registerVehicleForm?.get('plateNumber')?.invalid && (registerVehicleForm?.get('plateNumber')?.dirty || registerVehicleForm?.get('plateNumber')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('plateNumber')?.errors?.['required']">
                                    Plate number is required.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Second Row -->
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="vehicleStatus" [ngClass]="{'required-label': isFieldRequired('vehicleStatus')}">Vehicle Status</label>
                            <select id="vehicleStatus" class="form-control" formControlName="vehicleStatus">
                              <option *ngFor="let status of vehicleStatuses" [value]="status.id"> {{ status.name }} </option>
                            </select>
                            <div *ngIf="registerVehicleForm?.get('vehicleStatus')?.invalid && (registerVehicleForm?.get('vehicleStatus')?.dirty || registerVehicleForm?.get('vehicleStatus')?.touched || submitted)" class="text-danger">
                              <div *ngIf="registerVehicleForm?.get('vehicleStatus')?.errors?.['required']">
                                Vehicle Status is required.
                              </div>
                            </div>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="chassisNumber" [ngClass]="{'required-label': isFieldRequired('chassisNumber')}">Chassis Number</label>
                            <input type="text" class="form-control" id="chassisNumber" formControlName="chassisNumber">
                            <div *ngIf="registerVehicleForm?.get('chassisNumber')?.invalid && (registerVehicleForm?.get('chassisNumber')?.dirty || registerVehicleForm?.get('chassisNumber')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('chassisNumber')?.errors?.['required']">
                                    Chassis Number is required.
                                </div>
                            </div>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="vehicleType" [ngClass]="{'required-label': isFieldRequired('vehicleType')}">Vehicle Type</label>
                            <!-- <select id="vehicleType" class="form-control" formControlName="vehicleType">
                                <option *ngFor="let vehicleType of vehicleTypes" [value]="vehicleType.id"> {{vehicleType.name}} </option>                                
                            </select> -->
                            <ngx-select-dropdown 
                                [options]="vehicleTypes" 
                                [config]="dropdownConfig"
                                formControlName="vehicleType"
                                class="custom-dropdown">
                            </ngx-select-dropdown>
                            <div *ngIf="registerVehicleForm?.get('vehicleType')?.invalid && (registerVehicleForm?.get('vehicleType')?.dirty || registerVehicleForm?.get('vehicleType')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('vehicleType')?.errors?.['required']">
                                    Vehicle Type is required.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Third Row -->
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="fuelType" [ngClass]="{'required-label': isFieldRequired('fuelType')}">Fuel Type</label>
                            <select id="fuelType" formControlName="fuelType" class="form-control">
                                <option value="Diesel">Diesel</option>
                                <option value="Gasoline">Gasoline</option>
                                <option value="LPG">LPG</option>
                                <option value="Gasoline and electric">Gasoline and electric</option>
                                <option value="Full Electric">Full Electric</option>
                            </select>
                            <div *ngIf="registerVehicleForm?.get('fuelType')?.invalid && (registerVehicleForm?.get('fuelType')?.dirty || registerVehicleForm?.get('fuelType')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('fuelType')?.errors?.['required']">
                                    Fuel Type is required.
                                </div>
                            </div>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="vehicleManufacture" [ngClass]="{'required-label': isFieldRequired('vehicleManufacture')}">Vehicle Manufacture/Brand</label>
                            <!-- <select class="form-control" formControlName="vehicleManufacture" id="vehicleManufacture">
                                <option *ngFor="let vehicleManufacture of vehicleManufactures" [value]="vehicleManufacture.id"> {{vehicleManufacture.name}} </option>
                            </select> -->
                            <ngx-select-dropdown 
                                [options]="vehicleManufactures" 
                                [config]="dropdownConfig"
                                formControlName="vehicleManufacture"
                                class="custom-dropdown">
                            </ngx-select-dropdown>
                            <div *ngIf="registerVehicleForm?.get('vehicleManufacture')?.invalid && (registerVehicleForm?.get('vehicleManufacture')?.dirty || registerVehicleForm?.get('vehicleManufacture')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('vehicleManufacture')?.errors?.['required']">
                                    Vehicle Manufacture is required.
                                </div>
                            </div>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="vehicleModel" [ngClass]="{'required-label': isFieldRequired('vehicleModel')}">Vehicle Model</label>
                            <select class="form-control" formControlName="vehicleModel" id="vehicleModel">
                                <option *ngFor="let vehicleModel of vehicleModels" [value]="vehicleModel.id">{{vehicleModel.name}}</option>
                            </select>
                            <!-- <ngx-select-dropdown 
                                [options]="vehicleModels" 
                                [config]="dropdownConfig"
                                formControlName="vehicleModel"
                                class="custom-dropdown">
                            </ngx-select-dropdown> -->
                            <div *ngIf="registerVehicleForm?.get('vehicleModel')?.invalid && (registerVehicleForm?.get('vehicleModel')?.dirty || registerVehicleForm?.get('vehicleModel')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('vehicleModel')?.errors?.['required']">
                                    Vehicle Model is required.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fourth Row -->
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="declaredAmount"  [ngClass]="{'required-label': isFieldRequired('declaredAmount')}">Purchase Amount</label>
                            <input type="number" class="form-control" id="declaredAmount" formControlName="declaredAmount">
                            <div *ngIf="registerVehicleForm?.get('declaredAmount')?.invalid && (registerVehicleForm?.get('declaredAmount')?.dirty || registerVehicleForm?.get('declaredAmount')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('declaredAmount')?.errors?.['required']">
                                    Purchase Amount is required.
                                </div>
                            </div>
                        </div>

                        

                        <div class="form-group col-md-4">
                            <label for="acquisitionDate" [ngClass]="{'required-label': isFieldRequired('acquisitionDate')}">Acquisition Date</label>
                            <input type="date" class="form-control" id="acquisitionDate" formControlName="acquisitionDate" max="{{today}}">
                            <div *ngIf="registerVehicleForm?.get('acquisitionDate')?.invalid && (registerVehicleForm?.get('acquisitionDate')?.dirty || registerVehicleForm?.get('acquisitionDate')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('acquisitionDate')?.errors?.['required']">
                                    Acquisition Date is required.
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="engineNumber" [ngClass]="{'required-label': isFieldRequired('engineNumber')}">Engine Number</label>
                            <input type="text" class="form-control" id="engineNumber" formControlName="engineNumber">
                            <div *ngIf="registerVehicleForm?.get('engineNumber')?.invalid && (registerVehicleForm?.get('engineNumber')?.dirty || registerVehicleForm?.get('engineNumber')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('engineNumber')?.errors?.['required']">
                                    Engine Number is required.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fifth Row -->
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="transmissionType" [ngClass]="{'required-label': isFieldRequired('transmissionType')}">Transmission Type</label>
                            <select id="transmissionType" formControlName="transmissionType" class="form-control">
                                <option value="Manual">Manual</option>
                                <option value="Automatic">Automatic</option>
                                <option value="Continuously Variable Transmission">Continuously Variable Transmission</option>
                                <option value="Semi-automatic transmission">Semi-automatic transmission</option>
                                <option value="Tiptronic transmission">Tiptronic transmission</option>
                                <option value="Dual Clutch Transmission">Dual Clutch Transmission</option>
                            </select>
                            <div *ngIf="registerVehicleForm?.get('transmissionType')?.invalid && (registerVehicleForm?.get('transmissionType')?.dirty || registerVehicleForm?.get('transmissionType')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('transmissionType')?.errors?.['required']">
                                    Transmission Type is required.
                                </div>
                            </div>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="manufactureYear" [ngClass]="{'required-label': isFieldRequired('manufactureYear')}">Manufacture Year</label>
                            <select class="form-control" id="manufactureYear" formControlName="manufactureYear">
                                <option *ngFor="let year of years" [value]="year">{{ year }}</option>
                            </select>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="odometerReading">Odometer Reading</label>
                            <input type="text" class="form-control" id="odometerReading" formControlName="odometerReading">
                        </div>
                    </div>
                    <!-- sixth Row -->
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="invoiceDate" [ngClass]="{'required-label': isFieldRequired('invoiceDate')}">Invoice Date</label>
                            <input type="date" class="form-control" id="invoiceDate" formControlName="invoiceDate" [max]="today">
                        </div>

                        <div class="form-group col-md-4">
                            <label for="invoiceNumber" >Invoice Number</label>
                            <input type="text" class="form-control" id="invoiceNumber" formControlName="invoiceNumber">
                        </div>

                        <div class="form-group col-md-4">
                            <label for="customsDeclarationNumber">Customs Declaration Number</label>
                            <input type="text" class="form-control" id="customsDeclarationNumber" formControlName="customsDeclarationNumber">
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="customsDeclarationDate" [ngClass]="{'required-label': isFieldRequired('customsDeclarationDate')}">Customs Declaration Date</label>
                            <input type="date" class="form-control" id="customsDeclarationDate" formControlName="customsDeclarationDate" [max]="today">
                        </div>
        
                        <div class="form-group col-md-4" *ngIf="registerVehicleForm.get('ownershipType')?.value === '639bf044-1cbb-4954-b170-9843347073ca'">
                            
                            <label for="projectName" class="required-label">Project Name</label>
                            <input type="text" class="form-control" id="projectName" formControlName="projectName">
                            
                       </div>

                       <div class="form-group col-md-4" *ngIf="registerVehicleForm.get('ownershipType')?.value === '639bf044-1cbb-4954-b170-9843347073ca'">
                            <label for="projectStartDate" class="required-label">Project Start Date</label>
                            <input type="date" class="form-control" id="projectStartDate" formControlName="projectStartDate">  
                        </div>
                    </div>

                    <div class="row mt-4" >
                        <div class="form-group col-md-4" *ngIf="registerVehicleForm.get('ownershipType')?.value === '639bf044-1cbb-4954-b170-9843347073ca'">
                            <label for="projectEndDate" class="required-label">Project End Date</label>
                            <input type="date" class="form-control" id="projectEndDate" formControlName="projectEndDate">
                            <div *ngIf="registerVehicleForm.errors?.['invalidDateRange'] && registerVehicleForm.get('projectEndDate')?.touched" class="text-danger">
                              Project end date cannot be before the start date.
                            </div>
                          </div>

                        <div class="form-group col-md-4" *ngIf="registerVehicleForm.get('ownershipType')?.value === '639bf044-1cbb-4954-b170-9843347073ca'">
                            <label for="projectDescription" class="required-label">Project Description </label>
                            <textarea class="form-control" id="projectDescription" formControlName="projectDescription" rows="4"></textarea>
                        </div>

                        <!-- <div class="form-group col-md-4">
                            <label for="vehicleStatus" [ngClass]="{'required-label': isFieldRequired('vehicleStatus')}">Vehicle Status</label>
                            <select class="form-control" formControlName="vehicleStatus" id="vehicleStatus">
                                <option *ngFor="let vehicleStatus of vehicleStatuses" [value]="vehicleStatus.id">{{vehicleStatus.name}}</option>
                            </select>
                        </div> -->

                        <div class="form-group col-md-4">
                            <label for="isVehicleActive" [ngClass]="{'required-label': isFieldRequired('isVehicleActive')}">Is the vehicle still in use?</label>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" id="isVehicleActiveYes" value="true" formControlName="isVehicleActive" name="isVehicleActive"> 
                                <label class="form-check-label" for="isVehicleActiveYes">Yes</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" id="isVehicleActiveNo" value="false" formControlName="isVehicleActive" name="isVehicleActive"> 
                                <label class="form-check-label" for="isVehicleActiveNo">No</label>
                            </div>
                        </div>
                    </div>

                    <h2 class="mt-3">Picture Upload Section </h2>
                    <p>Upload at least 2 pictures of the vehicle to register it</p>

                    <!-- <div class="row" formArrayName="files">
                        <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                            <div class="form-group col-md-6">
                              <input type="file" (change)="onFileChange($event, i)" formControlName="file" class="form-control" />
                            </div>
                            <div class="form-group col-md-6">
                              <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                            </div>

                            <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                              <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                          </button>
                          </div>
                          <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                            <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add New File
                          </button>
                    </div> -->

                    <div class="row mt-4">
                        <div class="form-group col-md-6">
                            <label for="frontView" [ngClass]="{'required-label': isFieldRequired('frontView')}">Front View of the Vehicle</label>
                            <input type="file" (change)="onFileChange($event, 'frontView')" class="form-control" id="frontView">
                            <div *ngIf="registerVehicleForm.get('frontView')?.errors && registerVehicleForm.get('frontView')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('frontView')?.errors?.['required']">Vehicle front view is required.</span>
                              </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label for="backView" [ngClass]="{'required-label': isFieldRequired('backView')}">Back View of the Vehicle</label>
                            <input type="file" (change)="onFileChange($event, 'backView')" class="form-control" id="backView">
                            <div *ngIf="registerVehicleForm.get('backView')?.errors && registerVehicleForm.get('backView')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('backView')?.errors?.['required']">Vehicle behind view is required.</span>
                              </div>
                        </div>
                    </div>
                    
                    <!-- <div class="row mt-4">
                        <div class="form-group col-md-6">
                            <label for="leftView" [ngClass]="{'required-label': isFieldRequired('leftView')}">Left View of the Vehicle</label>
                            <input type="file" (change)="onFileChange($event, 'leftView')" class="form-control" id="leftView">
                            <div *ngIf="registerVehicleForm.get('leftView')?.errors && registerVehicleForm.get('leftView')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('leftView')?.errors?.['required']">Vehicle left view is required.</span>
                              </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label for="rightView" [ngClass]="{'required-label': isFieldRequired('rightView')}">Right View of the Vehicle</label>
                            <input type="file" (change)="onFileChange($event, 'rightView')" class="form-control" id="rightView">
                            <div *ngIf="registerVehicleForm.get('rightView')?.errors && registerVehicleForm.get('rightView')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('rightView')?.errors?.['required']">Vehicle right view is required.</span>
                              </div>
                        </div>
                    </div> -->

                    <div class="row mt-4">
                        <div class="array" formArrayName="files">
                                      <p>Any Additional Supporting files? </p>
                                    <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                                      <div class="form-group col-md-6">
                                        <input type="file" (change)="onFileChangee($event, i)" formControlName="file" class="form-control" />
                                      </div>
                                      <div class="form-group col-md-6">
                                        <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                                      </div>
        
                                      <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                                        <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                                    </button>
                                    </div>
                                  </div>
                                  <!-- [disabled]="files.length >= 2" -->
                                  <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                                    <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add another file
                                  </button>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-8"></div> 
                        <div class="col-md-4 buttons">
                            <button type="submit" class="btn submit-btn col-md-9 float-end">Register Vehicle</button>
                          </div>
                    </div>                    
                </form>
            </div>
        </div>
    </div>
</div>