import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-project-extension-request',
  templateUrl: './project-extension-request.component.html',
  styleUrl: './project-extension-request.component.scss'
})
export class ProjectExtensionRequestComponent {
  requestForm !: FormGroup;
  files !: FormArray;
  vehicle: any = null;
  isVehicleDetailsVisible = false; 
  vehicleId: any = '';
  userId: any = '';
  vehicleStatuses: any[] = [];
  plusIcon = faPlus;
  minusIcon = faMinus;
  selectedFileName: any;
  acquisitionId = '';

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.requestForm = this.formBuilder.group({
      vehicleId: [''],
      newProjectEndDate: ['', Validators.required],
      description: [''],
      file: [''],
      files: this.formBuilder.array([this.createFileFormGroup()])
    });
    this.files = this.requestForm.get('files') as FormArray;
  }
  isFieldRequired(fieldName: string): boolean {
    const control = this.requestForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }

  ngOnInit():void{
    this.fetchVehicleId();
  }

  fetchVehicleId(): void {
    this.route.params.subscribe((params) => {
      if (params['vehicleId']) {
        this.vehicleId = params['vehicleId'];
        this.fetchVehicleDetails(this.vehicleId);
      }
    });
  }

  fetchVehicleDetails(vehicleId: string) {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.vehicle = response;
        console.log(response);
        this.acquisitionId = response.vehicleRegRequest.VehicleAcquisition?.id;
        console.log("acq id: ", this.acquisitionId)
      },
      (error) => {  
        console.error('Error fetching vehicle details:', error);
      }
    );
  }

  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }
  
  removeFile(index: number): void {
    this.files.removeAt(index);
  }
  
  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: [''],
      applicationId: ['']
    });
  }
  
  onFileChange(event: any, index: number): void {
    event.preventDefault();
    const file = event.target.files[0];
    if (file) {
      const fileControl = this.files.at(index).get('file');
      if (file.type !== 'application/pdf') {
        this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
        if (fileControl) {
          fileControl.setValue(null);
        }
        event.target.value = ''; 
        return;
      }
      if (fileControl) { 
        fileControl.setValue(file);
      }
      this.selectedFileName = file.name;
    }
  }
  
  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB
  
    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitFile(vehicleId: any): void {
    const fileUploadDataFormArray = this.requestForm.get('files') as FormArray;
    fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
      if (control instanceof FormGroup) {
        const fileItem = control.value;
        if (fileItem.file) {
          const fileFormData = new FormData();
          const file = fileItem.file as File;
  
          console.log('File name:', file.name);
          console.log('File size:', file.size);
          console.log('File type:', file.type)
  
          if (this.validateFile(file)) {
            fileFormData.append('file', file);
            fileFormData.append('documentDescription', fileItem.documentDescription);
            fileFormData.append('applicationId', vehicleId);
  
            this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
              (response) => {
                console.log('API Response:', response);
                console.log("File submitted successfully")
                this.requestForm.reset();
              },
              (error) => {
                let errorMessage = 'An unexpected error occurred.';
                if (error && error.error && error.error.message) {
                  errorMessage = error.error.message;
                }
                console.error('API Error:', errorMessage);
                this.toastr.error("An error occurred!!", 'Error');
              }
            );
          } else {
            console.warn('File size or type is invalid.');
          }
        } else {
          console.log('No file selected...');
        }
      }
    });
  }

  onSubmit(){
    if(this.requestForm.valid) {
      this.requestForm.patchValue({
        vehicleId: this.vehicleId
      });

      const formData = {...this.requestForm.value};
      console.log(formData);
      this.http.post(`${environment.otherUrl}/submit-Project-Extension-Request`, formData).subscribe(
        (response) => {
          console.log(response);
          this.submitFile(this.vehicleId);
          this.toastr.success('Extension Request submitted Successfully!');
          this.requestForm.reset();
          this.router.navigate(['/vehicle-management/all-registered'])
        }
      )
    }
  }

  toggleVehicleDetails() {
    this.isVehicleDetailsVisible = !this.isVehicleDetailsVisible;
  }
}
