<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <div class="header">
            <h1>Request Project Extension</h1>
        </div>

        <div class="card">
            <p>Fill in the fields to send the Quartely Report</p>
            <div class="row form-container">
                <form id="disposalForm" [formGroup]="requestForm" class="text-center" (ngSubmit)="onSubmit()" enctype="multipart/form-data">
                    <div class="row mt-4">
                        <div class="form-group col-md-6">
                            <label for="newProjectEndDate" [ngClass]="{'required-label': isFieldRequired('newProjectEndDate')}">New Project End Date</label>
                            <input type="date" name="newProjectEndDate" id="newProjectEndDate" formControlName="newProjectEndDate" class="form-control">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="description">Description</label>
                            <textarea class="form-control" formControlName="description" id="description" rows="2"></textarea>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12 text-center">
                            <button type="button" class="btn show-details-btn" (click)="toggleVehicleDetails()">
                                {{ isVehicleDetailsVisible ? 'Hide Vehicle Details' : 'Show Vehicle Details' }}
                            </button>

                            <div
                                *ngIf="isVehicleDetailsVisible"
                                class="vehicle-details"
                                style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin-top: 20px; margin-bottom: 10px;">
                                <h4>Vehicle Information</h4>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label>Chassis Number</label>
                                        <p>{{ vehicle?.chassisNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Engine Number</label>
                                        <p>{{ vehicle?.engineNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Transmission Type</label>
                                        <p>{{ vehicle?.transmissionType || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Manufacturing Year</label>
                                        <p>{{ vehicle?.manufacturingYear || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Vehicle Type</label>
                                        <p>{{ vehicle?.vehicleType?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Ownership Type</label>
                                        <p>{{ vehicle?.ownershipType?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Vehicle Manufacture</label>
                                        <p>{{ vehicle?.vehicleManufacture?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Beneficiary Agency</label>
                                        <p>{{ vehicle?.beneficiaryAgency || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Plate Number</label>
                                        <p>{{ vehicle?.plateNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Pink Card Number</label>
                                        <p>{{ vehicle?.pickCardNumber|| 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h2 class="mt-3">File Upload Section </h2>
                    <div class="row" formArrayName="files">
                        <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                            <div class="form-group col-md-6">
                              <input type="file" (change)="onFileChange($event, i)" formControlName="file" class="form-control" />
                            </div>
                            <div class="form-group col-md-6">
                              <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="3"></textarea>
                            </div>

                            <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                              <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                          </button>
                          </div>
                          <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                            <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add another file
                          </button>
                    </div>
                        <div class="row mt-4">
                            <div class="col-md-9"></div> 
                            <div class="col-md-3 buttons">
                                <button type="submit" class="btn submit-btn">Request Project Extension</button>
                              </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
