<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>
  <div class="acquisition-container">
    <div class="card m-3 p-3">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
      <div class="row">
        <!-- Left Column: Requestee Details and Acquisition Details -->
        <div class="col-md-6">
          <!-- Requestee Details -->
          <div class="requestee-details" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
            <h4>Requestee Details</h4>
            <div class="row">
              <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                  <fa-icon [icon]="userIcon" class="bellIcon"></fa-icon>
                  <span class="ms-2">{{ userDetails.firstName || 'N/A' }} {{ userDetails.lastName || 'N/A' }}</span>
                </div>
                <div class="d-flex align-items-center">
                  <fa-icon [icon]="locationIcon" class="bellIcon"></fa-icon>
                  <span class="ms-2">{{ acquisitionDetails.institution?.toUpperCase() || 'N/A' }}</span>
                </div>
                <div class="d-flex align-items-center mt-3">
                  <fa-icon [icon]="roleIcon" class="bellIcon"></fa-icon>
                  <span class="ms-2">{{ userDetails.role?.name?.toUpperCase() || 'N/A' }}</span>
                </div>
              </div>

              <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                  <fa-icon [icon]="phoneIcon" class="bellIcon"></fa-icon>
                  <span class="ms-2">0{{ userDetails.phoneNumber || 'N/A' }}</span>
                </div>
                <div class="d-flex align-items-center">
                  <fa-icon [icon]="emailIcon" class="bellIcon"></fa-icon>
                  <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 250px;">
                    <span class="ms-2">{{ userDetails.email || 'N/A' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Acquisition/Vehicle/Project Details -->
          <div class="acquisition-details mt-3" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
            <h4>Acquisition Details</h4>
            <div class="row m-3">
              <div class="col-md-6">
                <label>Description</label>
                <p>{{ acquisitionDetails.description || 'N/A' }}</p>
              </div>
              <div class="col-md-6">
                <label>Request Type</label>
                <p>{{ acquisitionDetails.requestType?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6">
                <label>Ownership Type</label>
                <p>{{ acquisitionDetails.ownershipType?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6">
                <label>Current Level</label>
                <p>{{ acquisitionDetails.approvalLevel?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6">
                <label>Status</label>
                <p [ngClass]="{
                'text-success fw-bold': acquisitionDetails.statusType?.name === 'APPROVED', 
                'text-warning' : acquisitionDetails.statusType?.name === 'PROGRESS',
                'text-danger': acquisitionDetails.statusType?.name === 'DENIED', 
                'text-secondary': acquisitionDetails.statusType?.name === 'PENDING' || !acquisitionDetails.statusType?.name
              }">
                  {{
                  acquisitionDetails.statusType?.name

                  }}
                </p>
                <!-- <p class="text-warning fw-bold">{{ acquisitionDetails.statusType?.name || 'N/A' }}</p> -->
              </div>
              <div class="row" *ngIf="documents.length > 0">
                <div class="col-md-12">
                  <h5>Documents</h5>
                  <div class="document-container">
                    <div class="document" *ngFor="let document of documents">
                      <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                      <a (click)="fetchBase64Data(document.fileName)" class="file-link">{{ document.fileName || 'N/A'
                        }}</a>
                    </div>
                  </div>
                </div>
              </div>


            </div>

            <!-- Vehicle Details -->
            <h4>Vehicle Details</h4>

            <ng-container *ngFor="let vehicle of acquisitions">

              <div class="row m-3">
                <div class="col-md-6 mb-3">
                  <label>Vehicle Type</label>
                  <p>{{ vehicle.vehicleType?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Number Of Vehicles</label>
                  <p class="fw-bold">{{ vehicle.numberOfVehicles || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Beneficiary Agency</label>
                  <p>{{ vehicle.beneficiaryAgency || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Intended Usage</label>
                  <p>{{ vehicle.intendedUsage || 'N/A' }}</p>
                </div>
              </div>
              <hr class="separator-line" />
            </ng-container>

            <!-- Project Details -->
            <div *ngIf="hasProjectDetails()" class="project-details">
              <h4>Project Details</h4>
              <div class="row m-3">
                <ng-container *ngFor="let vehicle of acquisitions">
                  <div class="col-md-6">
                    <label>Project Name</label>
                    <p>{{ vehicle.projectName || 'Not specified' }}</p>
                  </div>
                  <div class="col-md-6">
                    <label>Project Start Date</label>
                    <p>{{ vehicle.projectStartDate || 'Not specified' }}</p>
                  </div>
                  <div class="col-md-6">
                    <label>Project End Date</label>
                    <p>{{ vehicle.projectEndDate || 'Not specified' }}</p>
                  </div>
                </ng-container>
              </div>
            </div>
            <!--View Analysis Buttons-->
            <div class="analysis-confirmation" class="col-md-6" *ngIf="!isLogistics()">
              <h4>ANALYSIS BREAKDOWN</h4>
            </div>
            
              <div class="d-flex align-items-center" *ngIf="acquisitionDetails.ownershipType?.name !=='Project Vehicles'">
                <span *ngIf="staffInfoConfirmed" class="check-icon me-2">✔️</span>
                <button class="btn btn-view btn-info" (click)="openStaffInfoPopup()" data-bs-toggle="modal" data-bs-target="#staffInfoModal">
                  <fa-icon [icon]="faStaff" class="pe-1"></fa-icon> View Staff Information
                </button>
              </div>
              
              <div class="d-flex align-items-center" *ngIf="acquisitionDetails.ownershipType?.name !=='Project Vehicles'">
                <span *ngIf="driverInfoConfirmed" class="check-icon me-2">✔️</span>
                <button class="btn btn-view btn-info" (click)="openDriverInfoPopup()" data-bs-toggle="modal" data-bs-target="#driverInfoModal">
                  <fa-icon [icon]="faDriver" class="pe-1"></fa-icon> View Driver's Information
                </button> 
              </div>
              
              <!-- Add the ngIf condition here -->
          
                <div class="d-flex align-items-center" *ngIf="acquisitionDetails.ownershipType?.name !=='Project Vehicles'">
                  <span *ngIf="costBenefitConfirmed" class="check-icon me-2">✔️</span>
                  <button class="btn btn-view btn-info" (click)="openCostBenefitPopup()" data-bs-toggle="modal" data-bs-target="#costBenefitModal">
                    <fa-icon [icon]="faBalance" class="pe-1"></fa-icon> View Cost Benefit Analysis
                  </button>
                </div>
        
              </div>
            


          <!-- Action Buttons -->
          <div class="container mt-3">
            <!-- <div class="alert alert-warning text-center" *ngIf="!allConfirmed()">
              Please confirm all information before proceeding.
            </div> -->
            <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canSeeActionButtons()">
              <button class="btn btn-success" *ngIf="canApprove()"
                (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
                <fa-icon [icon]="approveIcon" class="pe-1"></fa-icon>Approve
              </button>
              <button class="btn btn-success" *ngIf="!canApprove()"
                (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
                <fa-icon [icon]="submitIcon" class="pe-1"></fa-icon>Submit To Next Level
              </button>
              <button class="btn btn-danger" *ngIf="canDeny()"
                (click)="setAction('DENIED')" data-bs-toggle="modal" data-bs-target="#actionModal">
                <fa-icon [icon]="denyIcon" class="pe-1"></fa-icon> Deny
              </button>
              <button class="btn btn-secondary" (click)="setAction('RFAC')" data-bs-toggle="modal"
                data-bs-target="#actionModal">
                <fa-icon [icon]="moreIcon" class="pe-1"></fa-icon> More Actions
              </button>
            </div>
            
            

            <!-- Display the Edit button only if canEditVehicle() returns true -->
            <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canEditVehicle()">
              <button class="btn btn-info" (click)="editVehicle(acquisitionDetails.id)">Edit</button>
            </div>

          </div>
        </div>
        <!-- Right Column: Acquisition History -->
        <div class="col-md-6">
          <div
            *ngIf="acquisitionDetails.statusType?.name === 'APPROVED'"
            class="mb-5 p-4 approval-message">
            <p>Your request to purchase vehicles has been approved. Click below to view the authorization letter.</p>
            <button class="btn" (click)="redirectToAuthorizationLetter()">Open Letter</button>
          </div>

          <!-- Acquisition History Table -->
          <app-acquisition-history></app-acquisition-history>
        </div>



        <!-- Cost Benefit Modal -->
        <div class="modal fade" id="costBenefitModal" aria-labelledby="costBenefitModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content animate__animated animate__fadeIn">
              <!-- Modal Header -->
              <div class="modal-header border-0 bg-info text-white">
                <h4 class="modal-title w-100 text-center text-white" id="costBenefitModalLabel">Cost Benefit Analysis</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
        
              <!-- Modal Body -->
              <div class="modal-body p-4">
                <!-- Display for Non-Institutional Logistics/CBM Roles -->
                <ng-container *ngIf="localUserRole !== 'Institutional Logistics' && localUserRole !== 'Institutional CBM'">
                  <!-- Main Comparison Section -->
                  <div class="comparison-container p-4 mb-4 bg-light rounded shadow-sm">
                    <h4 class="text-center mb-4 fw-bold text-uppercase">Annual Cost Comparison</h4>
                    <div class="row">
                      <!-- Buying Section -->
                      <div class="col-md-6">
                        <div class="comparison-card bg-white p-4 rounded shadow-sm position-relative">
                          <label class="text-center text-primary fw-bold mb-3">Buying a Car</label>
                          <div class="icon-container text-center mb-2">
                            <i class="fas fa-car fa-3x text-primary"></i>
                          </div>
                          <div class="callout-box bg-light p-3 mb-3 rounded">
                            <label class="text-primary fw-bold">Cost of Buying</label>
                            <p>RWF {{ calculateTotalBuyingCost(costBenefitData) || 'N/A' }}</p> 
                          </div>
                          <div class="text-muted small">
                            <i class="fas fa-info-circle"></i> Includes Acquisition cost and maintenance, oil refilling, driver, and fuel costs across the car's lifespan.
                          </div>
                        </div>
                      </div>
        
                      <!-- Hiring Section -->
                      <div class="col-md-6">
                        <div class="comparison-card bg-white p-4 rounded shadow-sm position-relative">
                          <label class="text-center text-warning fw-bold mb-3">Hiring a Car Service</label>
                          <div class="icon-container text-center mb-2">
                            <i class="fas fa-taxi fa-3x text-warning"></i>
                          </div>
                          <div class="callout-box bg-light p-3 mb-3 rounded">
                            <h4 class="fw-bold text-warning">Hiring Cost</h4>
                            <p>RWF {{ calculateFinalPrice(hiringCostData?.hiringCost || 0) }}</p>
                          </div>
                          <div class="text-muted small">
                            <i class="fas fa-info-circle"></i> Consider total annual cost for hiring a vehicle service.
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
        
                  <!-- Button to Display Detailed Costs -->
                  <div class="text-center mt-4">
                    <button type="button" class="btn btn-secondary animate__animated animate__fadeInUp"
                      (click)="showDetailedCosts = !showDetailedCosts">
                      {{ showDetailedCosts ? 'Hide Detailed Costs' : 'View Detailed Costs' }}
                    </button>
                  </div>
        
                  <!-- Detailed Costs Section (Toggle Display) -->
                  <div class="detailed-costs mt-4 p-4 rounded bg-white shadow-sm animate__animated animate__fadeInUp"
                  *ngIf="showDetailedCosts">
                  <h4 class="text-center mb-3 fw-bold text-uppercase">Detailed Cost Breakdown</h4>
                  <div class="row">
                    <div class="col-md-6">
                      <h3 class="fw-bold text-primary">Buying Costs</h3>
                      <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex flex-column justify-content-between align-items-start">
                          <span class="fw-bold">Acquisition Cost:</span>
                          <span>RWF {{ costBenefitData?.acquisitionCost || 'N/A' }}</span>
                        </li>
                        <li class="list-group-item d-flex flex-column justify-content-between align-items-start">
                          <span class="fw-bold">Maintenance Cost:</span>
                          <span>RWF {{ costBenefitData?.maintenanceCost || 'N/A' }} (Per Year) X5 = RWF {{ calculateFinalPrice(costBenefitData?.maintenanceCost || 0) }}</span>
                        </li>
                        <li class="list-group-item d-flex flex-column justify-content-between align-items-start">
                          <span class="fw-bold">Oil Refilling Cost:</span>
                          <span>RWF {{ costBenefitData?.oilRefillingCost || 'N/A' }} (Per Year) X5 = RWF {{ calculateFinalPrice(costBenefitData?.oilRefillingCost || 0) }}</span>
                        </li>
                        <li class="list-group-item d-flex flex-column justify-content-between align-items-start">
                          <span class="fw-bold">Driver Cost:</span>
                          <span>RWF {{ costBenefitData?.driveCost || 'N/A' }} (Per Year) X5 = RWF {{ calculateFinalPrice(costBenefitData?.driveCost || 0) }}</span>
                        </li>
                        <li class="list-group-item d-flex flex-column justify-content-between align-items-start">
                          <span class="fw-bold">Fuel Cost:</span>
                          <span>RWF {{ costBenefitData?.fuelCost || 'N/A' }} (Per Year) X5 = RWF {{ calculateFinalPrice(costBenefitData?.fuelCost || 0) }}</span>
                        </li>
                      </ul>
                      
                    </div>
                    <div class="col-md-6">
                      <h4 class="fw-bold text-warning">Hiring Costs</h4>
                      <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex flex-column justify-content-between align-items-start">
                          <span class="fw-bold">Hiring Cost:</span>
                          <span>RWF {{ hiringCostData?.hiringCost|| 'N/A' }} (Per Year) X5 = RWF {{ calculateFinalPrice(hiringCostData?.hiringCost || 0) }}</span>
                          <!-- <span>Normal Price: RWF {{ hiringCostData?.hiringCost || 'N/A' }}</span> -->
                          <!-- <span>Final Price: RWF {{ calculateFinalPrice(hiringCostData?.hiringCost || 0) }}</span> -->
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                </ng-container>
        
                <!-- CBM/Logistics Display for Institutional Roles -->
                <ng-container *ngIf="localUserRole === 'Institutional Logistics' || localUserRole === 'Institutional CBM'">
                  <div class="detailed-costs mt-4 p-4 rounded bg-white shadow-sm animate__animated animate__fadeInUp">
                    <h4 class="text-center mb-3 fw-bold text-uppercase">Detailed Cost Breakdown</h4>
                    <div class="row">
                      <div class="col-md-6">
                        <h3 class="fw-bold text-primary">Buying Costs</h3>
                        <ul class="list-group list-group-flush">
                          <li class="list-group-item d-flex justify-content-between align-items-center">
                            Acquisition Cost:
                            <span>RWF {{ costBenefitData?.acquisitionCost || 'N/A' }}</span>
                          </li>
                          <li class="list-group-item d-flex justify-content-between align-items-center">
                            Maintenance Cost:
                            <span>RWF {{ costBenefitData?.maintenanceCost || 'N/A' }}</span>
                          </li>
                          <li class="list-group-item d-flex justify-content-between align-items-center">
                            Oil Refilling Cost:
                            <span>RWF {{ costBenefitData?.oilRefillingCost || 'N/A' }}</span>
                          </li>
                          <li class="list-group-item d-flex justify-content-between align-items-center">
                            Driver Cost:
                            <span>RWF {{ costBenefitData?.driveCost || 'N/A' }}</span>
                          </li>
                          <li class="list-group-item d-flex justify-content-between align-items-center">
                            Fuel Cost:
                            <span>RWF {{ costBenefitData?.fuelCost || 'N/A' }}</span>
                          </li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <h4 class="fw-bold text-warning">Hiring Costs</h4>
                        <ul class="list-group list-group-flush">
                          <li class="list-group-item d-flex justify-content-between align-items-center">
                            <i class="fas fa-taxi"></i> Total Hiring Cost:
                            <span>RWF {{ hiringCostData?.hiringCost || 'N/A' }}</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
        
              <!-- Modal Footer -->
              <div class="modal-footer border-0">
                <button type="button" class="btn btn-info animate__animated animate__fadeInUp" (click)="confirmCostBenefit()">Proceed</button>
              </div>
            </div>
          </div>
        </div>
        


        <!-- Staff Info Modal -->
        <div class="modal fade" id="staffInfoModal" aria-labelledby="staffInfoModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content animate__animated animate__fadeIn">
              <!-- Modal Header -->
              <div class="modal-header border-0 bg-info text-white">
                <h4 class="modal-title w-100 text-center text-white" id="staffInfoModalLabel">Staff Relevance
                  Information</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <!-- <span aria-hidden="true">&times;</span>
        </button> -->
              </div>
              <div class="modal-body">
                <!-- <h4 class="modal-title" id="staffInfoModalLabel">Cost Benefit Analysis</h4> -->
                <div class="row m-3">
                  <div class="col-md-6 mb-3">
                    <label>Staff Position</label>
                    <p>{{ staffInfoData?.staffPosition|| 'N/A' }}</p>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label>Staff Level</label>
                    <p>{{ staffInfoData?.staffLevel || 'N/A' }}</p>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label>Work Location</label>
                    <p>{{ staffInfoData?.workLocation || 'N/A' }}</p>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label>Office Location</label>
                    <p>{{ staffInfoData?.officeLocation || 'N/A' }}</p>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label>Daily Frequency</label>
                    <p>{{ staffInfoData?.dailyWorkFrequency || 'N/A' }}</p>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label>Other Description</label>
                    <p>{{ staffInfoData?.OthersDescription || 'N/A' }}</p>
                  </div>
                </div>

              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="confirmStaffInfo()">Proceed</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Driver Info Modal -->

        <div class="modal fade" id="driverInfoModal" aria-labelledby="driverInfoModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content animate__animated animate__fadeIn">
              <!-- Modal Header -->
              <div class="modal-header border-0 bg-info text-white">
                <h4 class="modal-title w-100 text-center text-white" id="driverInfoModalLabel">Cost Benefit Analysis
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <!-- <span aria-hidden="true">&times;</span>
        </button> -->
              </div>
              <div class="modal-body">
                <!-- <h4 class="modal-title" id="driverInfoModalLabel">Driver Information</h4> -->
                <div class="row m-3">
                  <div class="col-md-6 mb-3">
                    <label>Driver Availability</label>
                    <p>{{ driverInfoData?.isDriveAvailable || 'N/A' }}</p>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label>Driver Qualification</label>
                    <p>{{ driverInfoData?.driverQualification || 'N/A' }}</p>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label>Plan To Recruit</label>
                    <p>{{ driverInfoData?.planToRecruitDrive|| 'N/A' }}</p>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label>Number of Drivers</label>
                    <p>{{ driverInfoData?.numberOfDriver || 'N/A' }}</p>
                  </div>

                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="confirmDriverInfo()">Proceed</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal for Action Confirmation -->
        <div class="modal fade" id="actionModal" aria-labelledby="actionModalLabel" aria-hidden="true">
          <div class="modal-dialog w-75">
            <div class="modal-content animate__animated animate__fadeInUp">
              <!-- Modal Header -->
              <div class="modal-header border-0 bg-info text-white position-relative">
                <h4 class="modal-title w-100 text-center text-white" id="actionModalLabel">Action Confirmation</h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                  aria-label="Close"></button>
              </div>

              <!-- Loading State -->
              <div *ngIf="isLoading"
                class="loader-wrapper d-flex justify-content-center align-items-center flex-column m-3">
                <app-spinner></app-spinner>
                <h2 class="loading-text mt-2">Loading...</h2>
              </div>

              <!-- Success Message -->
              <div *ngIf="!isLoading && actionSuccessful" class="success-wrapper text-center p-4">
                <h2 class="text-success mb-3">Success</h2>
                <img src="https://img.icons8.com/color/96/000000/ok--v2.png" class="fit-image mb-3" alt="Success Icon">
                <p class="success-message">Your action has been completed successfully.</p>
              </div>

              <!-- Action Form -->
              <div class="modal-body p-4" *ngIf="!isLoading && !actionSuccessful">
                <p class="instruction-text mb-3">Add a comment if needed, then click "Confirm" to proceed.</p>
                <textarea class="form-control comment-box mb-3" rows="3" [(ngModel)]="actionComment"
                  placeholder="Add your comments here..."></textarea>
              </div>

              <!-- Modal Footer -->
              <div class="modal-footer" *ngIf="!isLoading && !actionSuccessful">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-info" (click)="confirmAction()">Confirm</button>
              </div>
            </div>
          </div>
        </div>

      </div>