import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { faArrowLeft,faBalanceScaleLeft,faBell, faCashRegister, faDriversLicense, faEnvelope, faFile, faLocationDot, faMapPin, faMoneyBill, faMoneyBillTransfer, faMoneyBillWave, faPaperPlane, faPeopleGroup, faPhone, faQuestion, faStaffAesculapius, faThumbsDown, faThumbsUp, faUser, faUserTie } from '@fortawesome/free-solid-svg-icons';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';
import { SharedModule } from "../../shared/shared.module";


// interface ProjectDetails {
//   projectName: string | null;
//   projectStartDate: string | null;
//   projectEndDate: string | null;
// }

@Component({
  selector: 'app-acquisition-details',
  templateUrl: './acquisition-details.component.html',
  styleUrls: ['./acquisition-details.component.scss'],
 
})
export class AcquisitionDetailsComponent implements OnInit {
  acquisitions: any[] = [];
  acquisitionDetails: any = {};
  userDetails: any = {};
  localUserRole = ''; 
  phoneIcon = faPhone;
  emailIcon = faEnvelope;
  userIcon = faUser;
  locationIcon = faLocationDot;
  roleIcon = faMapPin;
  backwardIcon = faArrowLeft;
  approveIcon = faThumbsUp;
  submitIcon=faPaperPlane;
  denyIcon = faThumbsDown;
  moreIcon = faQuestion;
  fileIcon = faFile;
  faBalance = faBalanceScaleLeft;
  faStaff = faPeopleGroup;
  faDriver= faDriversLicense;
  faDrive= faUserTie;

  acquisitionId = '';
  fetchedAcquisitionId=''
  selectedAction = '';
  actionComment = '';
  isLoading: boolean = false;
  actionSuccessful = false; 
  acquisitionHistory: any[] = [];
  acquisitionStatus: any;
  loggedInUser: string='';
  documents: any[] = [];
  base64Data: any;
  costBenefitData: any = {}; 
  driverInfoData: any = {};  
  staffInfoData: any = {}; 
  costBenefitConfirmed = false;
  driverInfoConfirmed = false; 
  staffInfoConfirmed = false;
  hiringCostData: any; 
  totalHiringCost: number = 0;
  showDetailedCosts: boolean = false;

  // uniqueProjectDetails: ProjectDetails = {
  //   projectName: '',
  //   projectStartDate: '',
  //   projectEndDate: ''
  // };
  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private router: Router,
    private toastr: ToastrService,
    private modalService: NgbModal,
    private location: Location
  ) {}

  ngOnInit() {
    // this.loadConfirmationStatus();
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj= JSON.parse(localUserData)
      this.localUserRole = parseObj.data.user.role.name
      this.loggedInUser = parseObj.data.user.id;
      console.log('logged in user',this.loggedInUser)
      console.log('localUSerRole',this.localUserRole) 
    }

    this.route.paramMap.subscribe(params => {
      const acquisitionId = params.get('acquisitionId');
      if (acquisitionId) {
        this.fetchedAcquisitionId = acquisitionId;
        this.fetchRequestDetails(acquisitionId);
        this.fetchDocuments(acquisitionId);
        this.fetchStaffInfoData(acquisitionId);
        this.fetchCostBenefitData(acquisitionId);
        this.fetchHiringCostData(acquisitionId);
        this.fetchDriverInfoData(acquisitionId);
      } 
    });
  }

  setAction(action: string) {
    this.selectedAction = action;
    console.log('Decision: ',this.selectedAction)
  }

  openModal(action: string) {
    this.selectedAction = action; 
    this.modalService.open('confirmActionModal'); 
  }

  fetchRequestDetails(acquisitionId: string) {
    const url = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;
    this.http.get<any>(url).subscribe(response => {
      console.log(response)
      if (response && response.vehicles && Array.isArray(response.vehicles)) {
        this.acquisitions = response.vehicles;
        
        this.acquisitionDetails = response.acquisition;
        this.acquisitionId = response.id;

        const userId = this.acquisitionDetails.userId;
        if (userId) {
          this.fetchUserDetails(userId);
        }
      } else {
        console.error('Unexpected response structure:', response);
      }
    }, error => {
      console.error('Error fetching acquisition details:', error);
    });
  }

  fetchUserDetails(userId: string) {
    const userUrl = `${environment.baseUrl}/auth/getUserByUser?userId=${userId}`;
    this.http.get<any>(userUrl).subscribe(
      (user) => {
        this.userDetails = user;
      },
      (error) => {
        console.error('Error fetching user details:', error);
      }
    );
  }
  fetchDocuments(acquisitionId: string) {
    const url = `${environment.fileUrl}/${acquisitionId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.documents = response; 
        console.log('doucments',this.documents)
      },
      (error) => {
        console.error('Error fetching documents:', error);
      }
    );
  }
  fetchBase64Data(fileName: string) {
    const url = `${environment.fileUrl}/${encodeURIComponent(fileName)}/base64`;

    this.http.get<any>(url).subscribe(
        (response) => {
            
            const binaryData = atob(response.base64Data);
            const arrayBuffer = new ArrayBuffer(binaryData.length);
            const uint8Array = new Uint8Array(arrayBuffer);
            for (let i = 0; i < binaryData.length; i++) {
                uint8Array[i] = binaryData.charCodeAt(i);
            }
            const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            window.open(url);
        },
        (error) => {
            console.error('Error fetching Base64 data:', error);
        }
    );
}

  
  confirmAction() {
    console.log("Acquisition ID before submission:", this.fetchedAcquisitionId); 
  
    
    this.isLoading = true;
    this.actionSuccessful = false; 
    
    const approvalData = {
      userId: this.loggedInUser,
      acquisitionId: this.fetchedAcquisitionId,
      comments: this.actionComment,
      decision: this.selectedAction.toUpperCase(),
    };
  
    console.log("Body being sent", approvalData); 
  
    this.http.post(`${environment.baseUrl}/approval/approval`, approvalData).subscribe(
      (response) => {
        
        setTimeout(() => {
          this.isLoading = false; 
          this.actionSuccessful = true; 
          this.toastr.success("Action confirmed successfully");
          console.log("final body", response);
  
          
          setTimeout(() => {
            window.location.reload();
          }, 3000); 
        }, 3000); 
      },
      (error) => {
        this.isLoading = false; 
        this.toastr.error("Error sending approval");
        console.error("Error during approval:", error);
        console.log("Error details:", error.error);
      }
    );
  }
  
  

  editAcquisition(acquisitionId: string): void {
    this.router.navigateByUrl(`/vehicle-management/edit-requested-vehicle/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  redirectToAuthorizationLetter() {
    const acquisitionId = this.fetchedAcquisitionId; // Assuming you have access to the acquisition ID
    if (acquisitionId) {
      this.router.navigate([`/vehicle-management/acquisition-details/${acquisitionId}/authorization-letter`]);
    } else {
      console.error('No acquisition ID available.');
    }
  }
   canSeeActionButtons(): boolean {
    const approvalLevel = this.acquisitionDetails?.approvalLevel?.name?.trim(); 
    return this.localUserRole && approvalLevel && this.localUserRole === approvalLevel && this.acquisitionDetails.statusType?.name !== 'APPROVED';
  }
  hasProjectDetails(): boolean {
    
    return this.acquisitions.some(vehicle => vehicle.projectName);
  }
  goBack(){
    this.location.back();
  }
  editVehicle(vehicleId: string) {
    this.router.navigateByUrl(`/vehicle-management/edit-vehicle/${vehicleId}`).then((success) => {
      if (!success) {
        console.error('Navigation to edit page failed');
      }
    });
  }
  canEditVehicle(): boolean {
    
    return (
     this.acquisitionDetails?.status === 'RFAC' 
    
    
    );
  }
  canApprove(): boolean {
    return this.localUserRole === "Minister"; 
  }
  canDeny():boolean{
    return this.localUserRole ==='Minister' ||this.localUserRole === 'Institutional CBM';
  }

  
fetchCostBenefitData(acquisitionId: string) {
  this.isLoading = false; // Start loading
  const url = `${environment.otherUrl}/cost-benefit-by-acquisitionID?id=${acquisitionId}`;
  
  this.http.get<any[]>(url).subscribe(
      response => {
        console.log('API Response:', response);

          if (response && response.length > 0) {
              this.costBenefitData = response[0];
              console.log('costbenefitdata', this.costBenefitData) // Get the first object from the array
            
          } else {
              this.costBenefitData = {}; // Handle the case where there is no data
          }
          this.isLoading = false; // Set loading to false after data is fetched
      },
      error => {
          console.error('Error fetching cost-benefit data:', error);
          this.isLoading = false; // Ensure loading is false in case of error
      }
  );
}
fetchHiringCostData(acquisitionId: string) {
  const url = `${environment.otherUrl}/hiring-cost-by-vehicleId?id=${acquisitionId}`;
  this.http.get<any[]>(url).subscribe(
    response => {
      if (response && response.length > 0) {
        this.hiringCostData = response[0]; 
        console.log('',this.hiringCostData)// Assign the first object
      } else {
        this.hiringCostData = null; // Reset if no data
      }
    },
    error => {
      console.error('Error fetching hiring costs:', error);
      // this.hiringCostData = null; // Reset if error occurs
    }
  );
}

  fetchStaffInfoData(acquisitionId: string) {
    this.isLoading = false; // Start loading
    const url = `${environment.otherUrl}/staff-relevance-by-acquisitionID?id=${acquisitionId}`;
    this.http.get<any[]>(url).subscribe(
      response => {
        if (response && response.length > 0) {
          this.staffInfoData = response[0]
          console.log('staffinfodata',this.staffInfoData); // Get the first object from the array
        } else {
          this.staffInfoData = {}; // Handle the case where there is no data
        }
        this.isLoading = false; // Set loading to false after data is fetched
      },
      error => {
        console.error('Error fetching cost-benefit data:', error);
        this.isLoading = false; // Ensure loading is false in case of error
      }
    );
  }
  fetchDriverInfoData(acquisitionId: string) {
   
    const url = `${environment.otherUrl}/drive-details-by-vehicleId?id=${acquisitionId}`;
    this.http.get<any[]>(url).subscribe(
      response => {
        if (response && response.length > 0) {
          this.driverInfoData = response[0]
          console.log('driverinfodata',this.driverInfoData); // Get the first object from the array
        } else {
          this.driverInfoData = {}; // Handle the case where there is no data
        }
        this.isLoading = false; // Set loading to false after data is fetched
      },
      error => {
        console.error('Error fetching cost-benefit data:', error);
        this.isLoading = false; // Ensure loading is false in case of error
      }
    );
  }
  
  openCostBenefitPopup() {
    this.costBenefitConfirmed = false; // Reset the confirmation status each time the popup is opened
    this.modalService.open('costBenefitModal'); // Open the modal with the cost-benefit analysis
  }
  openStaffInfoPopup() {
    this.costBenefitConfirmed = false; // Reset the confirmation status each time the popup is opened
    this.modalService.open('staffInfoModal'); // Open the modal with the cost-benefit analysis
  }
  openDriverInfoPopup() {
    this.costBenefitConfirmed = false; // Reset the confirmation status each time the popup is opened
    this.modalService.open('driverInfotModal'); // Open the modal with the cost-benefit analysis
  }
   calculateTotalBuyingCost(costBenefitData: any): number {
    if (!costBenefitData) {
      return 0;
    }

    // Add the costs that are part of buying, adjust as needed for your data structure
    const totalCost = 
      (costBenefitData.acquisitionCost || 0) + this.calculateTotalMaintenanceCost()

    return totalCost;
  }
  calculateTotalMaintenanceCost(): number {
    const { maintenanceCost = 0, oilRefillingCost = 0, fuelCost = 0, driveCost = 0 } = this.costBenefitData || {};
    // Sum maintenance costs and multiply by 10
    return (maintenanceCost + oilRefillingCost + fuelCost + driveCost) * 5;
  }
  calculateFinalPrice(normalPrice: number): number {
    return normalPrice * 5;
  }
  
  confirmCostBenefit() {
    this.costBenefitConfirmed = true; // Set the confirmation status to true
    this.toastr.success("Cost Benefit Analysis confirmed successfully");
    this.modalService.dismissAll(); // Dismiss all open modals
    // this.saveConfirmationStatus(); // Save the status to local storage
  }
  
  confirmStaffInfo() {
    this.staffInfoConfirmed = true; // Set the confirmation status to true
    this.toastr.success("Staff Information confirmed successfully");
    this.modalService.dismissAll(); // Dismiss all open modals
    // this.saveConfirmationStatus(); // Save the status to local storage
  }
  
  confirmDriverInfo() {
    this.driverInfoConfirmed = true; // Set the confirmation status to true
    this.toastr.success("Driver Information confirmed successfully");
    this.modalService.dismissAll(); // Dismiss all open modals
    // this.saveConfirmationStatus(); // Save the status to local storage
  }
  
  allConfirmed(): boolean {
    // Check if the localUserRole is 'Institutional CBM'
    if (this.localUserRole === 'Institutional CBM') {
      // Only return true if all conditions are confirmed
      return this.staffInfoConfirmed && this.driverInfoConfirmed && this.costBenefitConfirmed;
    }
  
    // If the role is not 'Institutional CBM', return false or handle as needed
    return true;
  }
  
  
  // saveConfirmationStatus(): void {
  //   const confirmationStatus = {
  //     staffInfoConfirmed: this.staffInfoConfirmed,
  //     driverInfoConfirmed: this.driverInfoConfirmed,
  //     costBenefitConfirmed: this.costBenefitConfirmed,
  //   };
  //   localStorage.setItem('confirmationStatus', JSON.stringify(confirmationStatus));
  // }
  
  // loadConfirmationStatus(): void {
  //   const confirmationStatus = localStorage.getItem('confirmationStatus');
  //   if (confirmationStatus) {
  //     const status = JSON.parse(confirmationStatus);
  //     this.staffInfoConfirmed = status.staffInfoConfirmed;
  //     this.driverInfoConfirmed = status.driverInfoConfirmed;
  //     this.costBenefitConfirmed = status.costBenefitConfirmed;
  //   }
  // }
  isLogistics():boolean{
    const data = localStorage.getItem('localUserData');
    if(data !=null){
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }
//   getUniqueProjectDetails(): void {
//   if (this.acquisitions.length === 0) {
//     return;
//   }

//   // Assuming the first vehicle's details are the reference
//   const firstVehicle = this.acquisitions[0];
//   const allSame = this.acquisitions.every(vehicle =>
//     vehicle.projectName === firstVehicle.projectName &&
//     vehicle.projectStartDate === firstVehicle.projectStartDate &&
//     vehicle.projectEndDate === firstVehicle.projectEndDate
//   );

//   if (allSame) {
//     this.uniqueProjectDetails = {
//       projectName: firstVehicle.projectName,
//       projectStartDate: firstVehicle.projectStartDate,
//       projectEndDate: firstVehicle.projectEndDate
//     };
//   } else {
//     this.uniqueProjectDetails = {
//       projectName: 'Varies',
//       projectStartDate: 'Varies',
//       projectEndDate: 'Varies'
//     };
//   }
// }
}
