@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');

*{
    font-family: 'Poppins', sans-serif;
   
  }
.acquisition-container{
    background-color: #D9D9D94D;
    height: 90%;
    width: 83%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 3px;
}
.card{
  height: 94%;
  overflow-y: scroll;
}
.card::-webkit-scrollbar {
  width: 0;  
}
.card-header{
  background-color: #28A4E2;
  color:  white;
  position: fixed;
  z-index: 1;
  width: 80%;
}
h4{
  color: #28A4E2;
  font-family: 'Poppins', sans-serif;
  font-weight: bold;
}
h3{
  color: #478daf;
  font-family: 'Poppins', sans-serif;
  font-weight: bold;
}
label{
  // color: #757575;
  color: rgb(146, 146, 146);
  font-weight: bold;
}
span,p{
  // color: #757575;
  color: black;
}
.buttons{
  right: 0;
}
.separator-line {
  border-top: 2px solid rgb(146, 146, 146);
}
.action, .btn {
  margin: 10px;
  color: white;
 
}
.btn-success{
  background-color: white;
  color: green;
  border: 1px solid green;
}
.btn-danger{
  background-color: white;
  color: red;
  border: 1px solid red;
}
.btn-secondary{
  background-color: white;
  color: grey;
  border: 1px solid grey;
}
.btn-warning{
  background-color: white;
  color: yellow;
  border: 1px solid yellow;

}
.btn-primary,.btn-info{
  background-color: white;
  color: #28A4E2;
  border: 1px sold #28A4E2;
}
.notification{
  height: 30px;
  width: 30px;
  border-radius: 50%;
 

}

.bellIcon{
  color: #28A4E2;
  font-size: large;
}
.btn-approve{
  background-color: rgb(48, 178, 48);
  color: white;
}
.btn-deny{
  color: white;
  background-color: rgb(153, 20, 20);
}
.modal-header {
  justify-content: center; /* This centers the content within the modal header */
}

/* Change the color of the modal title */
.modal-title {
  color: #28A4E2; /* Set the title color to the desired value */
}
.go-back-btn{
  border: 1px solid #28A4E2;
  color: #28A4E2;
  width: 130px;
  font-size: 14px;
  font-weight: 500;

}
.approval-message {
  background-color: rgba(144, 238, 144, 0.5); /* Greenish color with transparency */
  border: 1px solid #6c9; /* Darker border color */
  border-radius: 5px;
  padding: 15px;
}

.approval-message p {
  margin-bottom: 10px;
}

.approval-message button {
  margin-top: 10px;
  background-color: #4caf50; /* Green button color */
  color: white; /* Text color */
  border: none; /* Remove border */
  border-radius: 5px; /* Rounded corners */
  padding: 10px 20px; /* Padding */
  cursor: pointer; /* Cursor on hover */
  transition: background-color 0.3s; /* Smooth transition */
}

.approval-message button:hover {
  background-color: #388e3c; /* Darker green on hover */
}
.document-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
  
  .document {
    display: flex;
    align-items: center;
    background: #dcdcdc;
    padding: 10px;
    border-radius: 5px;
    transition: background-color 0.3s ease;

    &:hover {
      background: #e9ecef;
    }

    .file-icon {
      color: #2a89f6;
      margin-right: 10px;
    }

    .file-link {
      color: #5d5d5d;
      text-decoration: underline;
      font-weight: 500;
      cursor: pointer;
      transition: color 0.3s ease;
      text-decoration: none;
      &:hover {
        color: #77a6d9;
      }
    }
  }
}
.btn{
  font-size: 13px;
}
.btn-view{
  font-size: 13px;
}
.modal-backdrop {
  display: none !important;
}
/* Main Modal Animation */
/* Modern Modal Styling */
.modal-content {
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #17a2b8, #117a8b);
}

.btn-close-white {
  filter: invert(1);
}

.loader-wrapper {
  min-height: 150px;
}

.loading-text {
  color: #17a2b8;
}

.success-wrapper {
  background-color: #f0f9ff;
  border-radius: 10px;
  padding: 20px;
}

.fit-image {
  width: 80px;
  height: 80px;
}

.instruction-text {
  font-size: 1.1rem;
  color: #555;
}

.comment-box {
  border-radius: 10px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.comment-box:focus {
  border-color: #17a2b8;
  box-shadow: 0 0 8px rgba(23, 162, 184, 0.1);
}

.btn-outline-secondary {
  border: 1px solid #6c757d;
  border-radius: 20px;
}


.btn-info:hover {
  background-color: #138496;
  border-color: #138496;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Comparison Cards Hover Flip Effect */
.comparison-card {
  transition: transform 0.4s, box-shadow 0.3s;
  cursor: pointer;
}

.comparison-card:hover {
  transform:  scale(1.08) rotateX(5deg);;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Button Smooth Transition */
.btn-outline-dark {
  transition: background-color 0.3s, color 0.3s;
}

.btn-outline-dark:hover {
  background-color: #343a40;
  color: white;
}

/* Detailed Costs Section Animation */
.detailed-costs {
  animation: slideInUp 0.4s ease-in-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Font Awesome Icons Alignment */
.list-group-item i {
  margin-right: 10px;
  color: #6c757d;
}
