import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RequestVehicleComponent } from './request-vehicle/request-vehicle.component'
import { AllVehiclesComponent } from './all-vehicles/all-vehicles.component';
import { MultiStepFormComponent } from './multi-step-form/multi-step-form.component';
import { AcquisitionDetailsComponent } from './acquisition-details/acquisition-details.component';
import { RegisterVehicleComponent } from './register-vehicle/register-vehicle.component';
import { AllRegisteredComponent } from './all-registered/all-registered.component';
import { VehicleDetailsComponent } from './vehicle-details/vehicle-details.component';
import { EditRegisteredVehicleComponent } from './edit-registered-vehicle/edit-registered-vehicle.component';
import { RegisterNewVehicleComponent } from './register-new-vehicle/register-new-vehicle.component';
import { VehicleDisposalComponent } from './vehicle-disposal/vehicle-disposal.component';
import { AllDisposalsComponent } from './all-disposals/all-disposals.component';
import { DisposalDetailsComponent } from './disposal-details/disposal-details.component';
import { AuctionComponent } from './auction/auction.component';
import { AuthorizationLetterComponent } from './authorization-letter/authorization-letter.component';
import { AuctionDetailsComponent } from './auction-details/auction-details.component';
import { AuctionReportsComponent } from './auction-reports/auction-reports.component';
import { VehicleAllocationComponent } from './vehicle-allocation/vehicle-allocation.component';
import { ReturnedVehiclesComponent } from './returned-vehicles/returned-vehicles.component';
import { ReportsComponent } from './reports/reports.component';
import { ReturnedDetailsComponent } from './returned-details/returned-details.component';
import { QuartelyReportComponent } from './quartely-report/quartely-report.component';
import { AllQuartelyReportsComponent } from './all-quartely-reports/all-quartely-reports.component';
import { QuartelyReportDetailsComponent } from './quartely-report-details/quartely-report-details.component';
import { ProjectExtensionRequestComponent } from './project-extension-request/project-extension-request.component';
import { AllProjectExtensionsComponent } from './all-project-extensions/all-project-extensions.component';
import { ProjExtensionApproveComponent } from './proj-extension-approve/proj-extension-approve.component';
import { CostBenefitComponent } from './cost-benefit/cost-benefit.component';
import { DeviceLocationComponent } from './device-location/device-location.component';
import { ArchiveComponent } from './archive/archive.component';
import { PinkCardComponent } from './pink-card/pink-card.component';
import { OperationTablesComponent } from './operation-tables/operation-tables.component';
import { DisposalLetterComponent } from './disposal-letter/disposal-letter.component';
import { EditAuctionComponent } from './edit-auction/edit-auction.component';
import { EditDisposalComponent } from './edit-disposal/edit-disposal.component';



const routes: Routes = [
  { path: 'request-vehicle', component: MultiStepFormComponent },
  { path: 'all-vehicles', component: AllVehiclesComponent},
  { path: 'all-registered', component: AllRegisteredComponent},
  { path: 'all-disposals', component: AllDisposalsComponent},
  { path: 'auction-reports', component: AuctionReportsComponent},
  { path: 'multi-step-form', component: RequestVehicleComponent},
  { path: 'register-existing-vehicle', component: RegisterNewVehicleComponent},
  { path: 'register-vehicle/:acquisitionId', component: RegisterVehicleComponent},
  { path: 'vehicle-disposal/:vehicleId', component: VehicleDisposalComponent},
  { path: 'acquisition-details/:acquisitionId', component: AcquisitionDetailsComponent },
  { path: 'vehicle-details/:vehicleId', component: VehicleDetailsComponent },
  { path: 'disposal-details/:disposalId', component: DisposalDetailsComponent },
  { path: 'auction-details/:auctionId', component: AuctionDetailsComponent },
  { path: 'acquisition-details/:acquisitionId/authorization-letter', component: AuthorizationLetterComponent },
  { path: 'disposal-details/:disposalId/authorization-letter', component: DisposalLetterComponent },
  { path: 'vehicle-details/:vehicleId/pinkCard', component: PinkCardComponent },
  { path: 'edit-requested-vehicle/:acquisitionId', component: MultiStepFormComponent},
  { path: 'edit-registered-vehicle/:vehicleId', component: EditRegisteredVehicleComponent},
  { path: 'edit-auction-report/:id', component: EditAuctionComponent}, 
  {path: 'edit-disposal/:disposalId', component: EditDisposalComponent}, 
  { path: 'auction/:disposalId', component: AuctionComponent},
  { path: 'vehicle-allocation', component: VehicleAllocationComponent},
  { path: 'returned-vehicles/:acquisitionId', component: ReturnedVehiclesComponent},
  { path: 'returned-details/:acquisitionId/:vehicleId', component: ReturnedDetailsComponent},
  { path: 'reports',component: ReportsComponent},
  { path: 'quartely-report/:vehicleId', component: QuartelyReportComponent},
  { path: 'all-quartely-reports', component: AllQuartelyReportsComponent},
  { path: 'quartely-report-details/:reportId', component: QuartelyReportDetailsComponent},
  { path: 'project-extension-request/:vehicleId', component: ProjectExtensionRequestComponent},
  { path: 'project-extension-details/:reportId', component: ProjExtensionApproveComponent},
  { path: 'all-project-extensions', component: AllProjectExtensionsComponent},
  { path: 'cost-benefit/:vehicleId', component: CostBenefitComponent},
  { path: 'gps', component: DeviceLocationComponent},
  { path:'archive',component:ArchiveComponent},
  { path:'operation',component:OperationTablesComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class VehicleManagementRoutingModule { }
