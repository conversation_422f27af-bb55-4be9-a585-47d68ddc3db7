<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicle-container">
    <div class="card m-3 p-3">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>

      <div class="row">
        <div class="col-md-6">
          <!-- Vehicle Details -->
          <div class="vehicle-details" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
            <h4>Vehicle Information</h4>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label>Chassis Number</label>
                <p>{{ disposal.vehicle?.chassisNumber || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Engine Number</label>
                 <p>{{ disposal.vehicle?.engineNumber || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Transmission Type</label>
                <p>{{ disposal.vehicle?.transmissionType || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Manufacturing Year</label>
                <p>{{ disposal.vehicle?.manufacturingYear || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Vehicle Type</label>
                <p>{{ disposal.vehicle?.vehicleType?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Ownership Type</label>
                <p>{{ disposal.vehicle?.ownershipType?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Beneficiary Agency</label>
                <p>{{ disposal.vehicle?.beneficiaryAgency || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Plate Number</label>
                <p>{{ disposal.vehicle?.plateNumber || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Pick Card Number</label>
                <p>{{ disposal.vehicle?.pickCardNumber || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="additional-info mt-3" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
            <h4>Additional Information</h4>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label>Disposal Description</label>
                <p>{{ disposal?.description || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Disposal Type</label>
                <p>{{ disposal?.disposalTypes?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Disposal Reason</label>
                <p>{{ disposal?.disposalReasons?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Request Status</label>
                <p>{{ disposal?.requestStatus?.name || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Approval Level</label>
                <p>{{ disposal?.approvalLevel?.name || 'N/A' }}</p>
              </div>

              <div class="row" *ngIf="documents.length > 0">
                <div class="col-md-6">
                  <label>Documents</label>
                  <div class="document-container" *ngFor="let document of documents">
                    <div class="document">
                      <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                      <!-- Display the Font Awesome file icon -->
                      <a style="cursor: pointer;" (click)="fetchBase64Data(document.fileName)" class="file-link">{{
                        document.fileName || 'N/A' }}</a>
                      <!-- <a href="{{ document.fileUrl }}" target="_blank" class="file-link">{{ document.fileName }}</a> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Disposal History Table -->
        <div class="col-md-6">
          <div

          *ngIf="disposal.requestStatus?.name === 'APPROVED'"
          class="mb-5 p-4 approval-message">
          <p>Your Disposal Request has been approved. Click below to view the authorization letter.</p>
          <button class="btn" (click)="redirectToAuthorizationLetter()">Open Letter</button>
        </div>
          <app-disposal-history></app-disposal-history>
        </div>
      </div>

      <!-- Action Buttons Row -->
      <div class="row action-buttons mt-3 d-flex" *ngIf="canSeeActionButtons()">
        <div class="col">
          <button class="btn btn-success" *ngIf="canApprove()" (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
            <fa-icon [icon]="approveIcon" class="pe-1"></fa-icon>Approve
          </button>
          <button class="btn btn-success" *ngIf="!canApprove()" (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
            <fa-icon [icon]="submitIcon" class="pe-1"></fa-icon>Submit To Next Level
          </button>
          <button class="btn btn-danger" *ngIf="canDeny()" (click)="setAction('DENIED')" data-bs-toggle="modal" data-bs-target="#actionModal">
            <fa-icon [icon]="denyIcon" class="pe-1"></fa-icon> Deny
          </button>
          <button class="btn btn-secondary" (click)="setAction('RFAC')" data-bs-toggle="modal" data-bs-target="#actionModal">
            <fa-icon [icon]="moreIcon" class="pe-1"></fa-icon> More Actions
          </button>
        </div>
      </div>

       <!-- Modal for Action Confirmation -->
       <div class="modal fade" id="actionModal" aria-labelledby="actionModalLabel" aria-hidden="true">
        <div class="modal-dialog w-75">
          <div class="modal-content animate__animated animate__fadeInUp">
            <!-- Modal Header -->
            <div class="modal-header border-0 bg-info text-white position-relative">
              <h4 class="modal-title w-100 text-center text-white" id="actionModalLabel">Action Confirmation</h4>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                aria-label="Close"></button>
            </div>
            <div *ngIf="isLoading" class="loader-wrapper d-flex justify-content-center align-items-center flex-column m-3">
              <app-spinner></app-spinner>
              <h2 class="loading-text">Loading...</h2>
            </div>

            <!-- Success Message -->
            <div *ngIf="!isLoading && actionSuccessful" class="success-wrapper text-center">
              <div class="form-card">
                <h2 class="text-success">Success</h2>
                <div class="row justify-content-center">
                  <div class="col-md-4">
                    <img src="https://img.icons8.com/color/96/000000/ok--v2.png" class="fit-image" alt="Success Icon">
                  </div>
                </div>
                <br><br>
              </div>
            </div>

            <div class="modal-body" *ngIf="!isLoading && !actionSuccessful">
              <p>Add a comment if needed, then click "Confirm" to proceed.</p>
              <textarea class="form-control" [(ngModel)]="actionComment" placeholder="Add your comments here..."></textarea>
            </div>
            <div class="modal-footer" *ngIf="!isLoading && !actionSuccessful">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button *ngIf="!isLoading && !actionSuccessful" type="button" class="btn btn-info" (click)="confirmAction()">Confirm</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
