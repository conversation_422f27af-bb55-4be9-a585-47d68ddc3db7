import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { faArrowLeft,faThumbsUp, faThumbsDown,faQuestion, faPaperPlane, faFileArchive } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-disposal-details',
  templateUrl: './disposal-details.component.html',
  styleUrls: ['./disposal-details.component.scss']
})
export class DisposalDetailsComponent implements OnInit {
  disposal: any = {};  // Store the vehicle details
  actionComment = ''; // For modal comments
  selectedAction = ''; // To track the action selected
  plateNumber = ''; // New field for plate number
  pickCardNumber = ''; 
  userRole=''; 
  backwardIcon = faArrowLeft;
  approveIcon = faThumbsUp;
  submitIcon=faPaperPlane;
  fileIcon = faFileArchive;
  denyIcon = faThumbsDown;
  moreIcon = faQuestion;
  loggedInUser='';
  isLoading: boolean = false;
  actionSuccessful = false; // Tracks success status
  fetchedDisposalId='';
  documents: any[] = [];
  base64Data: any;

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private router: Router,
    private modalService: NgbModal
  ) {}

  ngOnInit() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
      this.userRole = parseObj?.data?.user?.role?.name; // Store the user's role
      this.loggedInUser = parseObj.data.user.id;

      // console.log("User Role in ngOnInit:", this.userRole);
    }
  
    this.route.paramMap.subscribe((params) => {
      const disposalId = params.get('disposalId');
      if (disposalId) {
        this.fetchedDisposalId= disposalId
        this.fetchDisposalDetails(disposalId);
        this.fetchDocuments(disposalId);
      }
    });
  }
  
  isFleetMgtSeniorEngineer(): boolean {
    return this.userRole === 'Fleet Mgt Senior Engineer' && this.selectedAction=== 'APPROVED' // Check if user is Fleet Mgt Senior Engineer
  }
  fetchDisposalDetails(disposalId: string) {
    const url = `${environment.baseUrl}/disposal/disposal/${disposalId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.disposal = response; // Store the disposal data
        console.log("Disposal Data:", response);
      },
      (error) => {
        console.error('Error fetching disposal details:', error);
        this.toastr.error('Error fetching disposal details.');
      }
    );
  }
  setAction(action: string) {
    this.selectedAction = action;
    console.log('Decision: ',this.selectedAction)
  }

  openModal(action: string) {
    this.selectedAction = action; // Set the selected action
    this.modalService.open('confirmActionModal'); // Open the modal
  }
  confirmAction() {
    console.log("Acquisition ID before submission:", this.disposal.id); 
  
    // Start loading
    this.isLoading = true;
    this.actionSuccessful = false; // Reset action success state
    
    const approvalData = {
      userId: this.loggedInUser,
      disposalId: this.disposal.id,
      comments: this.actionComment || '', // Include comments, if any
      decision: this.selectedAction.toUpperCase() // Decision based on the selected action
    };

    this.http.post(`${environment.baseUrl}/approval/DisposalApproval`, approvalData).subscribe(
      (response) => {
        // Ensure the loader is shown for at least 3 seconds
        setTimeout(() => { 
          this.isLoading = false; // End loading
          this.actionSuccessful = true; // Mark action as successful
          this.toastr.success("Action confirmed successfully");
          console.log("final body", response);
  
          // After showing the success message for 3 seconds, reload the page
          setTimeout(() => {
            window.location.reload();
          }, 3000); // Wait 3 seconds before reloading
        }, 3000); // Minimum loading time
      },
      (error) => {
        this.isLoading = false; // End loading on error
        this.toastr.error("Error sending approval");
        console.error("Error during approval:", error);
        console.log("Error details:", error.error);
      }
    );
  }
 
  fetchDocuments(disposalId: string) {
    const url = `${environment.fileUrl}/${disposalId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.documents = response;
        console.log(this.documents)// Assuming 'documents' is an array property in your component
      },
      (error) => {
        console.error('Error fetching documents:', error);
      }
    );
  }

  fetchBase64Data(fileName: string) {
    const url = `${environment.fileUrl}/${fileName}/base64`;
    this.http.get<any>(url).subscribe(
        (response) => {
            // Decode the Base64 data and display it in a new window
            const binaryData = atob(response.base64Data);
            const arrayBuffer = new ArrayBuffer(binaryData.length);
            const uint8Array = new Uint8Array(arrayBuffer);
            for (let i = 0; i < binaryData.length; i++) {
                uint8Array[i] = binaryData.charCodeAt(i);
            }
            const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            window.open(url);
        },
        (error) => {
            console.error('Error fetching Base64 data:', error);
        }
    );
}
  

  editVehicle(vehicleId: string) {
    this.router.navigateByUrl(`/vehicle-management/edit-vehicle/${vehicleId}`).then((success) => {
      if (!success) {
        console.error('Navigation to edit page failed');
      }
    });
  }
  canSeeActionButtons(): boolean {
    const approvalLevel = this.disposal?.approvalLevel?.name.trim(); 
    return this.userRole && approvalLevel && this.userRole === approvalLevel;
  }
  canEditDisposal(): boolean {
    // Check if the status is 'RFAC', user role is 'Institutional Logistics', and approval level is also 'Institutional Logistics'
    return (
      this.disposal?.status === 'RFAC' &&
      this.userRole === 'Institutional Logistics' &&
      this.disposal?.approvalLevel?.name === 'Institutional Logistics'
    );
  }
 
  goBack() {
    this.router.navigate(['/vehicle-management/all-disposals']); // Navigate back to the disposals list
  }
  canApprove(): boolean {
    return this.userRole === "Minister";
  }
  canDeny():boolean{
    return this.userRole ==='Minister' ||this.userRole === 'Institutional CBM';
  }
  redirectToAuthorizationLetter() {
        const disposalId = this.fetchedDisposalId; 
        if (disposalId) {
          this.router.navigate([`/vehicle-management/disposal-details/${disposalId}/authorization-letter`]);
        } else {
          console.error('No disposal ID available.');
        }
      }
}
