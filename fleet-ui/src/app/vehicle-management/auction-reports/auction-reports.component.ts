import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faSearch, faCaretLeft, faCaretRight, faCar, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-auction-reports',
  templateUrl: './auction-reports.component.html',
  styleUrls: ['./auction-reports.component.scss']
})
export class AuctionReportsComponent implements OnInit {
  auctions: any[] = [];
  filteredAuctions: any[] = [];
  displayedAuctions: any[] = [];
  pageSize: number = 30;
  currentPage: number = 1;
  totalPages: number = 0;
  institutionId: string = '';
  userRole: string = '';
  searchText: string = '';
  faSearch = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  currentFilter: string = 'all';
  backwardIcon = faArrowLeft;


  constructor(private http: HttpClient, private router: Router, private location: Location) {}

  ngOnInit() {
    this.getUserDetails(); // Load user data
    this.fetchAllAuctions(); // Fetch all Auctions
  }

  goBack(){
    this.location.back();
  }

  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id;
      console.log('institutionId',this.institutionId)
      this.userRole = parsedObj.data.user.role.name;
    }
  }

  fetchAllAuctions() {
    let url: string;


    // Determine API based on the user role
    switch (this.userRole) {
      case 'Institutional logistics':
      case 'Institutional CBM':
        if (this.institutionId) {
          url = `${environment.baseUrl}/disposal/auctionReportByInstitutionID/${this.institutionId}`;
        } else {
          console.error('Institution ID not found in localStorage');
          return;
        }
        break;

      case 'Fleet Mgt Senior Engineer':
      case 'DG Transport':
      case 'Permanent Secretary':
      case 'Minister':
        url = `${environment.baseUrl}/disposal/allAuctionReports`;
        break;

      default:
        console.error('Unrecognized user role');
        return;
    }

    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        this.auctions = response;
        console.log('Fetched Auctions:', this.auctions);
        this.applyCurrentFilter(); // Apply the filter after fetching data
      },
      (error) => {
        console.error('Error fetching Auctions:', error);
      }
    );
  }


  applyCurrentFilter() {
    // Start with all Auctions
    this.filteredAuctions = [...this.auctions];

    // Apply filtering based on the current filter
    switch (this.currentFilter) {
      case 'all':
        break;
      case 'pending-progress':
        this.filteredAuctions = this.filteredAuctions.filter(
          (item) => item.requestStatus?.name === 'PENDING' || item.requestStatus?.name === 'PROGRESS' || item.requestStatus?.name === 'RFAC'
        );
        break;
      case 'completed':
        this.filteredAuctions = this.filteredAuctions.filter(
          (item) => item.requestStatus?.name === 'APPROVED'
        );
        break;
      case 'denied':
        this.filteredAuctions = this.filteredAuctions.filter(
          (item) => item.requestStatus?.name === 'DENIED'
        );
        break;
         case 'pending':
        this.filteredAuctions = this.filteredAuctions.filter(
          (item) => item.requestStatus?.name === 'PENDING'&&item.approvalLevel?.name === this.userRole ||item.requestStatus?.name === 'PROGRESS' && item.approvalLevel?.name === this.userRole
        );
        break;
      default:
        console.error('Invalid filter type:', this.currentFilter);
    }

    this.currentPage = 1; // Reset to the first page when changing filters
    this.updateDisplayedAuctions(); // Refresh the displayed Auctions
  }

  setFilter(filterType: string) {
    this.currentFilter = filterType;
    this.applyCurrentFilter(); // Apply the selected filter
  }

  updateDisplayedAuctions() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredAuctions.length);

    this.displayedAuctions = this.filteredAuctions.slice(startIndex, endIndex); // Update based on current page

    this.totalPages = Math.ceil(this.filteredAuctions.length / this.pageSize); // Recalculate total pages
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedAuctions(); // Go to the previous page
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedAuctions(); // Go to the next page
    }
  }

  getPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedAuctions(); // Refresh the displayed Auctions based on the new page
    }
  }
  getFirstEntryIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.filteredAuctions.length);
  }
  sortable(event: any) {
    const selectedField: string = event.target.value;

    this.filteredAuctions.sort((a, b) => {
      const fieldA = a[selectedField];
      const fieldB = b[selectedField];
      if (typeof fieldA === 'string' && typeof fieldB === 'string') {
        return fieldA.localeCompare(fieldB);
      }
      return 0;
    });

    this.updateDisplayedAuctions(); // Refresh after sorting
  }
  getAuctionStatusClass(requestStatus : any): string {
    if (requestStatus?.name ) {
      switch (requestStatus?.name .toLowerCase()) {
        case 'approved':
          return 'status-registered'; // Indicate approved status
        case 'pending':
          return 'status-pending';
          case 'progress':
          return 'status-progress'; // Indicate pending status
        case 'denied':
          return 'status-denied'; // Indicate denied status
        default:
          return '';
      }
    }
    return '';
  }
  getProgressWidth(requestStatus: string): string {
    switch (requestStatus.toLowerCase()) {
      case 'approved':
        return '100%'; // Corresponds to green
      case 'pending':
        return '70%'; // Corresponds to yellow/orange
      case 'denied':
        return '100%'; // Corresponds to red
      case 'rfac':
        return '60%'; // Corresponds to blue
      case 'progress':
        return '80%'; // Corresponds to blue
      default:
        return '';
    }
  }
  viewAuction(auctionId: string): void {
    console.log('Disposal ID:', auctionId);
    this.router.navigateByUrl(`/vehicle-management/auction-details/${auctionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
  searchAuctions() {
    const searchTextLower = this.searchText.toLowerCase();

    // Apply search only to the already filtered auctions
    const filteredBySearch = this.filteredAuctions.filter(item => {
      return (
        (item.createddate || '').toLowerCase().includes(searchTextLower) ||
        (item.description || '').toLowerCase().includes(searchTextLower) ||
        (item.disposalReasons?.name || '').toLowerCase().includes(searchTextLower) ||
        (item.approvalLevel?.name || '').toLowerCase().includes(searchTextLower)
      );
    });

    // Update displayed auctions based on the search result
    this.displayedAuctions = filteredBySearch.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
    this.totalPages = Math.ceil(filteredBySearch.length / this.pageSize);
  }
  editAuctionReport(auctionId: string): void {
    console.log("AuctionReportId:", auctionId);
    this.router.navigateByUrl(`/vehicle-management/edit-auction-report/${auctionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
}


}
