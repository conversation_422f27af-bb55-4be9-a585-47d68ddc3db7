<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicles-container">
      <div class="header d-flex flex-row justify-content-between">
          <button class="btn go-back-btn" (click)="goBack()">
              <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
            </button>
          <h2 class="table-title">All Auction Reports</h2>

          <div class="search-group d-flex">
              <fa-icon [icon]="faSearch" class="icon"></fa-icon>
              <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchAuctions()">
          </div>
      </div>

      <div class="card">
          <div class="table-header d-flex flex-row justify-content-between">
              <!-- Table Filter -->
              <div class="d-flex flex-row justify-content-start align-items-center statuses">
                <button class="btn btn-outline position-relative" (click)="setFilter('all')" [class.active]="currentFilter === 'all'">All  <span *ngIf="currentFilter === 'all'" class="badge badge-circle">{{ filteredAuctions.length }}</span></button>
                <button class="btn btn-outline position-relative" (click)="setFilter('pending-progress')" [class.active]="currentFilter === 'pending-progress'">Pending/Progress  <span *ngIf="currentFilter === 'pending-progress'" class="badge badge-circle">{{ filteredAuctions.length }}</span></button>
                <button *ngIf="userRole != 'Institutional logistics' && userRole != 'Institutional CBM'"class="btn btn-danger text-white position-relative pending-btn"[class.active]="currentFilter === 'pending'"(click)="setFilter('pending')">Pending Your Approval<span *ngIf="currentFilter === 'pending'" class="badge badge-circle">{{ filteredAuctions.length }}</span></button>
                <button class="btn btn-outline position-relative" (click)="setFilter('completed')" [class.active]="currentFilter === 'completed'">Completed<span *ngIf="currentFilter === 'completed'" class="badge badge-circle">{{ filteredAuctions.length }}</span> </button>
                <button class="btn btn-outline position-relative" (click)="setFilter('denied')" [class.active]="currentFilter === 'denied'">Denied  <span *ngIf="currentFilter === 'denied'" class="badge badge-circle">{{ filteredAuctions.length }}</span></button>
              </div>

              <div class="sorting-group d-flex flex-row p-2">
                  <label for="sort-field" class="text-muted p-1">Sort by:</label>
                  <select id="sort-field" class="select" (change)="sortable($event)">
                      <option value="">Select Field</option>
                      <option value="createddate">Created Date</option>
                      <option value="description">Description</option>
                      <option value="disposalTypeId">Disposal Type</option>
                      <option value="disposalReasonId">Disposal Reason</option>
                      <option value="requestStatusId">Status</option>
                  </select>
              </div>
          </div>

          <div class="table-only p-3">
              <table class="table table-stripped">
                  <thead>
                      <tr>
                          <th>Date Created</th>
                          <th>Auction Description</th>
                          <th>Approval Level</th>
                          <th>Status</th>
                          <th>Actions</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr *ngFor="let auction of displayedAuctions">
                          <td>{{ auction.createddate }}</td>
                          <td>{{ auction.description }}</td>
                          <td>{{ auction.approvalLevel?.name }}</td>
                          <td   [ngClass]="getAuctionStatusClass(auction.requestStatus)" role="progressbar">{{ auction.requestStatus?.name }}</td>
                          <!-- <div class="progress">
                          <div class="progress-bar"
                                  [ngClass]="getAuctionStatusClass(auction.requestStatus)" role="progressbar"
                                  [style.width]="getProgressWidth(auction.requestStatus?.name)"
                                  aria-valuemin="0"
                                  aria-valuemax="100"
                                  >

                                  {{ auction.requestStatus?.name }}

                              </div>
                          </div> -->
                          <td class="d-flex flex-row">
                              <button class="btn view-btn" (click)="viewAuction(auction.id)"> View </button>
                              <button class="btn auction-btn" *ngIf="auction.auctionTypes?.name == 'Public Auction' && userRole === 'Institutional logistics'" (click)="viewAuction(auction.id)" style="margin: 0px 14px;"> Auction </button>
                              <button class="btn edit-btn"    style="margin: 0px 8px;" (click)="editAuctionReport(auction.id)"> Edit </button>

                          </td>

                      </tr>
                  </tbody>
              </table>
          </div>

          <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
              <div class="pagination-info">
                  Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredAuctions.length }} entries
              </div>
              <ul class="pagination justify-content-center">
                  <li class="page-item">
                      <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                          <fa-icon [icon]="caretLeft"></fa-icon>
                      </button>
                  </li>
                  <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                      <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                          {{ pageNumber }}
                      </button>
                  </li>
                  <li class="page-item">
                      <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                          <fa-icon [icon]="caretRight"></fa-icon>
                      </button>
                  </li>
              </ul>
          </nav>
      </div>
  </div>
</div>
