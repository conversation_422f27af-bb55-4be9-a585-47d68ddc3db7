import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { faArrowLeft, faFileArchive, faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';

@Component({
  selector: 'app-edit-disposal',
  templateUrl: './edit-disposal.component.html',
  styleUrls: ['./edit-disposal.component.scss'],
})
export class EditDisposalComponent implements OnInit {
  disposalForm!: FormGroup;
  vehicle: any = null;
  isVehicleDetailsVisible = false;
  disposalId: string = '';
  userId: string = '';
  disposalTypes: any[] = [];
  disposalReasons: any[] = [];
  backwardIcon = faArrowLeft;
  files!: FormArray;
    selectedFileName: any;
    selectedFiles: { [key: string]: File } = {};
    documents: any[] = [];
    fileIcon = faFileArchive;
    plusIcon = faPlus;
    minusIcon = faMinus;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {
    this.disposalForm = this.formBuilder.group({
      disposalTypeId: ['', Validators.required],
      disposalReasonId: ['', Validators.required],
      description: [''],
      files: this.formBuilder.array([this.createFileFormGroup()])
    });
    this.files = this.disposalForm.get('files') as FormArray;
  }

  ngOnInit(): void {
    this.getUserDetails();
    this.fetchDisposalTypes();
    this.fetchDisposalReasons();
    this.fetchDisposalDetails();
    this.fetchDocuments(this.disposalId);
  }

  fetchDocuments(disposalId: string) {
    const url = `${environment.fileUrl}/${disposalId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.documents = response;
        console.log(this.documents)// Assuming 'documents' is an array property in your component
      },
      (error) => {
        console.error('Error fetching documents:', error);
      }
    );
  }

  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }
  
  removeFile(index: number): void {
    this.files.removeAt(index);
  }
  
  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: [''],
      applicationId: ['']
    });
  }

  fetchBase64Data(fileName: string) {
    const url = `${environment.fileUrl}/${fileName}/base64`;
    this.http.get<any>(url).subscribe(
        (response) => {
            // Decode the Base64 data and display it in a new window
            const binaryData = atob(response.base64Data);
            const arrayBuffer = new ArrayBuffer(binaryData.length);
            const uint8Array = new Uint8Array(arrayBuffer);
            for (let i = 0; i < binaryData.length; i++) {
                uint8Array[i] = binaryData.charCodeAt(i);
            }
            const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            window.open(url);
        },
        (error) => {
            console.error('Error fetching Base64 data:', error);
        }
    );
  }

  onFileChangee(event: any, index: number): void {
    event.preventDefault();
    const file = event.target.files[0];
    if (file) {
      const fileControl = this.files.at(index).get('file');
      if (file.type !== 'application/pdf') {
        this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
        if (fileControl) {
          fileControl.setValue(null);
        }
        event.target.value = '';
        return;
      }
      if (fileControl) { 
        fileControl.setValue(file);
      }
      this.selectedFileName = file.name;
    }
  }

  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitAdditionalFile(auctionId: any): void {
    const fileUploadDataFormArray = this.disposalForm.get('files') as FormArray;
    fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
      if (control instanceof FormGroup) {
        const fileItem = control.value;
        if (fileItem.file) {
          const fileFormData = new FormData();
          const file = fileItem.file as File;
  
          console.log('File name:', file.name);
          console.log('File size:', file.size);
          console.log('File type:', file.type)
  
          if (this.validateFile(file)) {
            fileFormData.append('file', file);
            fileFormData.append('documentDescription', fileItem.documentDescription);
            fileFormData.append('applicationId', auctionId);
  
            this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
              (response) => {
                console.log('API Response:', response);
                console.log("File uploaded successfully");
                this.disposalForm.reset();
              },
              (error) => {
                let errorMessage = 'An unexpected error occurred.';
                if (error && error.error && error.error.message) {
                  errorMessage = error.error.message;
                }
                console.error('API Error:', errorMessage);
                this.toastr.error("An error occurred!!", 'Error');
              }
            );
          } else {
            console.warn('File size or type is invalid.');
          }
        } else {
          console.log('No file selected...');
        }
      }
    });
  }

  getUserDetails() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
      this.userId = parseObj.data.user.id;
    }
  }

  fetchDisposalTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/disposalType`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.disposalTypes = response.map((type) => ({ id: type.id, name: type.name }));
      },
      (error) => {
        console.error('Error fetching disposal types:', error);
      }
    );
  }

  fetchDisposalReasons(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/disposalReason`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.disposalReasons = response.map((reason) => ({ id: reason.id, name: reason.name }));
      },
      (error) => {
        console.error('Error fetching disposal reasons:', error);
      }
    );
  }

  fetchDisposalDetails(): void {
    this.route.params.subscribe((params) => {
      if (params['disposalId']) {
        this.disposalId = params['disposalId'];
        const url = `https://fleettesting.mininfra.gov.rw/disposal/disposal/${this.disposalId}`;
        this.http.get<any>(url).subscribe(
          (response) => {
            // Populating the form with fetched data
            this.disposalForm.patchValue({
              disposalTypeId: response.disposalTypes.id,
              disposalReasonId: response.disposalReasons.id,
              description: response.description || '',
            });

            // Assigning vehicle details for the vehicle section visibility
            this.vehicle = response.vehicle;
          },
          (error) => {
            console.error('Error fetching disposal details:', error);
          }
        );
      }
    });
  }

  isFieldRequired(fieldName: string): boolean {
    const control = this.disposalForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }

  goBack() {
    this.location.back();
  }

  onSubmit() {
    if (this.disposalForm.valid) {

      const additionalFilesArray = this.disposalForm.get('files') as FormArray;

      const updatedDisposalData = {
        UserId: this.userId,
        disposalTypeId: this.disposalForm.controls['disposalTypeId'].value,
        disposalReasonId: this.disposalForm.controls['disposalReasonId'].value,
        description: this.disposalForm.controls['description'].value,
      };

      if (this.disposalId) {
        this.http.patch(`${environment.baseUrl}/disposal/updateDisposalVehicle/${this.disposalId}`, updatedDisposalData).subscribe(
          (response: any) => {
            const disposalId = response.id;

            if (additionalFilesArray && additionalFilesArray.length > 0) {
              this.submitAdditionalFile(disposalId);
            }
            this.toastr.success('Vehicle Disposal Updated Successfully');
            this.router.navigate(['/vehicle-management/all-disposals']);
          },
          (error) => {
            this.toastr.error('Error updating vehicle disposal', 'Error');
          }
        );
      }
    } else {
      this.toastr.error('Please fill in all fields to submit', 'Error');
    }
  }

  toggleVehicleDetails() {
    this.isVehicleDetailsVisible = !this.isVehicleDetailsVisible;
  }
}
