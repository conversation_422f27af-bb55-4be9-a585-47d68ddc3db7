<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
      <div class="header d-flex flex-row justify-content-between w-50">
        <button class="btn go-back-btn" (click)="goBack()">
          <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
        </button>
        <h1>Edit Vehicle Disposal</h1>
      </div>
  
      <div class="card">
        <p>Update vehicle disposal details</p>
        <div class="row form-container">
          <form id="disposalForm" [formGroup]="disposalForm" class="text-center" (ngSubmit)="onSubmit()">
            <div class="row mt-4">
              <div class="form-group col-md-4">
                <label for="disposalTypeId" [ngClass]="{'required-label': isFieldRequired('disposalTypeId')}">Disposal Type</label>
                <select class="form-control" formControlName="disposalTypeId" id="disposalTypeId">
                  <option *ngFor="let disposalType of disposalTypes" [value]="disposalType.id">{{disposalType.name}}</option>
                </select>
              </div>
              <div class="form-group col-md-4">
                <label for="disposalReasonId" [ngClass]="{'required-label': isFieldRequired('disposalReasonId')}">Disposal Reason</label>
                <select class="form-control" formControlName="disposalReasonId" id="disposalReasonId">
                  <option *ngFor="let disposalReason of disposalReasons" [value]="disposalReason.id">{{disposalReason.name}}</option>
                </select>
              </div>
              <div class="form-group col-md-4">
                <label for="description">Description</label>
                <textarea class="form-control" formControlName="description" id="description" rows="4"></textarea>
              </div>
            </div>

            <h2>File Upload Section</h2>

            <div class="row" *ngIf="documents.length > 0">
                        <div class="col-md-12">
                          <p>These are the existing documents</p>
                          <div class="document-container" *ngFor="let document of documents; let i = index">
                            <!-- Group documents two by two -->
                            <div class="row" *ngIf="i % 2 === 0">
                              <div class="col-md-6" *ngIf="documents[i]">
                                <div class="document">
                                  <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                                  <a style="cursor: pointer;" (click)="fetchBase64Data(documents[i].fileName)" class="file-link">{{ documents[i].fileName || 'N/A' }}</a>
                                </div>
                              </div>
                              
                              <!-- Check if the next document exists and is part of the pair -->
                              <div class="col-md-6" *ngIf="documents[i + 1]">
                                <div class="document">
                                  <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                                  <a style="cursor: pointer;" (click)="fetchBase64Data(documents[i + 1].fileName)" class="file-link">{{ documents[i + 1].fileName || 'N/A' }}</a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

            <div class="row" *ngIf="disposalForm.controls['disposalTypeId'].value === 'b6f69d06-c9b9-4f35-ba96-267a86c2a7de'" formArrayName="files">
              <h2 class="text-warning">Need to upload other files?</h2>

                          <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                              <div class="form-group col-md-6">
                                <input type="file" (change)="onFileChangee($event, i)" formControlName="file" class="form-control" />
                              </div>
                              <div class="form-group col-md-6">
                                <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                              </div>
  
                              <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                                <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                            </button>
                            </div>
                            <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                              <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add New File
                            </button>
            </div>
  
            <div class="row mt-4">
              <div class="col-md-12 text-center">
                <button type="button" class="btn show-details-btn" (click)="toggleVehicleDetails()">
                  {{ isVehicleDetailsVisible ? 'Hide Vehicle Details' : 'Show Vehicle Details' }}
                </button>
  
                <div *ngIf="isVehicleDetailsVisible" class="vehicle-details">
                  <h4>Vehicle Information</h4>
                  <div class="row">
                    <div class="col-md-3 mb-3">
                      <label>Chassis Number</label>
                      <p>{{ vehicle?.chassisNumber || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                      <label>Engine Number</label>
                      <p>{{ vehicle?.engineNumber || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                      <label>Transmission Type</label>
                      <p>{{ vehicle?.transmissionType || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                      <label>Manufacturing Year</label>
                      <p>{{ vehicle?.manufacturingYear || 'N/A' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
  
            <div class="row mt-4">
              <div class="col-md-9"></div>
              <div class="col-md-3 buttons">
                <button type="submit" class="btn submit-btn">Update Disposal</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  