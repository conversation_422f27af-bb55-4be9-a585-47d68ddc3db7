<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <!-- MultiStep Form -->
        <div class="justify-content-center card">
            <div class="header">
                <h1>Request Vehicle</h1>
                <p>Fill all form fields to go to next step</p>
            </div>
            <div class="row ">
                <form id="msform"[formGroup]="requestForm" class="text-center" method="post" enctype="multipart/form-data">
                    <ul id="progressbar">
                        <li class="active" id="vehicleDetails"><strong>Vehicle Details</strong></li>
                        <li id="numberOfVehicles"><strong>Number of Vehicles</strong></li>
                        <li id="projectDetails"><strong>Project Details</strong></li>
                        <li id="costBenefitAnalysis"><strong>Cost Benefit Details</strong></li>
                        <li id="supplementFiles"><strong>Supplement Files</strong></li>
                    </ul>

    
                    
                            <fieldset>
                                <!-- <div>   -->
                                    <div class="row mt-4">
                                        <div class="form-group col-md-6">
                                            <label for="institution" [ngClass]="{'required-label': isFieldRequired('institution')}">Institution</label>
                                            <input type="text" [value]="institution" class="form-control" id="institution" readonly>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="ownershipType" [ngClass]="{'required-label': isFieldRequired('ownershipType')}">Ownership Type</label>
                                            <select id="ownershipType" class="form-control" formControlName="ownershipType">
                                                <option *ngFor="let ownership of ownerships" [value]="ownership.id">{{ ownership.name }}</option>
                                            </select>
                                            <div *ngIf="requestForm.get('ownershipType')?.hasError('required') && requestForm.get('ownershipType')?.touched" class="text-danger">Ownership Type is required</div>
                                        </div>
                                    </div>
                                
                                    <div class="row mt-4">
                                        <div class="form-group col-md-6">
                                            <label for="requestType" [ngClass]="{'required-label': isFieldRequired('requestType')}">Request Type</label>
                                            <select id="requestType" class="form-control" formControlName="requestType">
                                                <option *ngFor="let requestType of requestTypes" [value]="requestType.id">{{ requestType.name }}</option>
                                            </select>
                                            <div *ngIf="requestForm.get('requestType')?.hasError('required') && requestForm.get('requestType')?.touched" class="text-danger">Request Type is required</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="description">Description</label>
                                            <textarea class="form-control" formControlName="description" rows="4"></textarea>
                                        </div>                                          
                                    </div>
                                    <div class="buttons d-flex justify-content-between">
                                        <div></div>
                                        <button type="button" name="next" class="btn next action-button mt-4" (click)="handleNextStep()"> Next Step</button>
                                    </div>
                            </fieldset>

                            <fieldset id="secondFieldset">
                                    <div class="array" formArrayName="vehicleDetails">
                                        <div class="" *ngFor="let detail of vehicleDetails?.controls; let i = index" [formGroupName]="i">
                                            <div class="row mt-4">
                                                <div class="form-group col-md-6">
                                                    <label for="vehicleTypeid{{ i }}" [ngClass]="{'required-label': isFieldRequired('vehicleTypeid')}">Vehicle Type {{ i+1 }}</label>
                                                    <select id="vehicleTypeid{{ i }}" class="form-control" formControlName="vehicleTypeid">
                                                        <option *ngFor="let vehicleType of vehicleTypes" [value]="vehicleType.id">{{ vehicleType.name }} </option>
                                                    </select>
                                                    <div *ngIf="requestForm.get('vehicleTypeid')?.hasError('required') && requestForm.get('vehicleTypeid')?.touched" class="text-danger">Vehicle Type is required</div>
                                                </div>
                                    
                                                <div class="form-group col-md-6">
                                                    <label class="mr-2" for="numberOfVehicles{{ i }}" [ngClass]="{'required-label': isFieldRequired('numberOfVehicles')}">Number of Vehicles</label>
                                                    <input type="number" class="form-control" id="numberOfVehicles{{ i }}" formControlName="numberOfVehicles" required="true">
                                                      <div *ngIf="requestForm.get('numberOfVehicles')?.hasError('required') && requestForm.get('numberOfVehicles')?.touched" class="text-danger">Number of Vehicles is required</div>
                                                </div>
                                            </div>
                                    
                                            <div class="row mt-4">
                                                <div class="form-group col-md-6">
                                                    <label for="beneficiaryAgencyId{{ i }}" class="required-label">Beneficiary Agency</label>
                                                    <!-- <select id="beneficiaryAgencyId{{ i }}" class="form-control" formControlName="beneficiaryAgencyId">
                                                        <option *ngFor="let institution of beneficiaryAgencies" [value]="institution.id">{{ institution.name }}</option>
                                                    </select> -->
                                                    <ngx-select-dropdown 
                                                        [options]="beneficiaryAgencies" 
                                                        [config]="dropdownConfig"
                                                        formControlName="beneficiaryAgencyId"
                                                        class="custom-dropdown w-100">
                                                    </ngx-select-dropdown>
                                                    <!-- <div class="input-group">
                                                        <input type="text" id="beneficiaryAgency{{ i }}" formControlName="beneficiaryAgency" class="form-control">
                                                    </div> -->
                                                </div>
                                    
                                                <div class="form-group col-md-6">
                                                    <label for="intendedUsage{{ i }}" [ngClass]="{'required-label': isFieldRequired('intendedUsage')}">Intended Usage</label>
                                                    <textarea name="intendedUsage" id="intendedUsage{{ i }}" formControlName="intendedUsage" class="form-control" required="true" rows="4"></textarea>
                                                    <div *ngIf="requestForm.get('intendedUsage')?.hasError('required') && requestForm.get('intendedUsage')?.touched" class="text-danger">Intended Usage is required</div>
                                                </div>
                                            </div>
                                            <button type="button" class=" btn remove-btn mt-2" *ngIf="i > 0" (click)="removeVehicleDetails(i)"> 
                                                <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete Vehicle Type
                                            </button>
                                        </div>
                                        <button type="button" class="btn add-btn mt-4" (click)="addVehicleDetails()">
                                            <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add New Vehicle Type
                                        </button>
                                    </div>
                                    <div class="buttons d-flex justify-content-between p-2">
                                        <button type="button" name="previous" class="previous action-button-previous">Previous</button>
                                        <!-- <input type="button" name="previous" class="previous action-button-previous" value="Previous"/> -->
                                        <button *ngIf="requestForm.controls['ownershipType'].value !== '639bf044-1cbb-4954-b170-9843347073ca'; else nextButton" type="button" class="action-button" (click)="navigateToLastFieldset()"> Next Step </button>
                                        <!-- <button *ngIf="requestForm.controls['ownershipType'].value !== '639bf044-1cbb-4954-b170-9843347073ca'; else nextButton" type="button" class="action-button" (click)="onSubmit()"> Submit</button> -->
                                        <ng-template #nextButton>
                                            <button type="button" name="next" class="btn action-button" (click)="handleNextStep()"> Next Step</button>
                                        </ng-template>
                                    </div>                           
                              </fieldset>
                            
                              <fieldset id="thirdFieldset" *ngIf="requestForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">
                                <div class="form-card">
                                    <div class="row mt-4">
                                        <div class="form-group col-md-6">
                                        <label for="projectName" [ngClass]="{'required-label': isFieldRequired('projectName')}">Project Name</label>
                                        <input type="text" name="projectName" formControlName="projectName" class="form-control"/>
                                        <div *ngIf="requestForm.get('projectName')?.hasError('required') && (requestForm.get('projectName')?.dirty)" class="text-danger">Project Name is required</div>
                                        </div>
                    
                                        <div class="form-group col-md-6">
                                        <label for="projectDescription" [ngClass]="{'required-label': isFieldRequired('projectDescription')}">Project Description</label>
                                        <input type="text" name="projectDescription" formControlName="projectDescription" class="form-control"/>
                                        <div *ngIf="requestForm.get('projectDescription')?.hasError('required') && (requestForm.get('projectDescription')?.dirty)" class="text-danger">Project Description is required</div>
                                        </div>
                                    </div>
                                    <div class="row mt-4">
                                        <div class="form-group col-md-6">
                                        <label for="projectStartDate" [ngClass]="{'required-label': isFieldRequired('projectStartDate')}">Project Start Date</label>
                                        <input type="date" name="projectStartDate" formControlName="projectStartDate" class="form-control"/>
                                        <div *ngIf="requestForm.get('projectStartDate')?.hasError('required') && (requestForm.get('projectStartDate')?.dirty)" class="text-danger">Project Start Date is required</div>
                                        </div>
                                        
                                        <div class="form-group col-md-6">
                                        <label for="projectEndDate" [ngClass]="{'required-label': isFieldRequired('projectEndDate')}">Project End Date</label>
                                        <input type="date" name="projectEndDate" formControlName="projectEndDate" class="form-control"/>
                                        <div *ngIf="requestForm.get('projectEndDate')?.hasError('required') && (requestForm.get('projectEndDate')?.dirty)" class="text-danger">Project End Date is required</div>
                                        </div>      
                                        <div *ngIf="requestForm.errors?.['invalidDateRange'] && requestForm.get('projectEndDate')?.touched" class="text-danger">
                                            Project end date cannot be before the start date.
                                          </div>
                                    </div>
                                </div>
                                <div class="buttons d-flex justify-content-between p-3 mt-4">

                                    <button type="button" name="previous" class="previous action-button-previous" (click)="handlePreviousStep()">Previous</button>
                                    <!-- <input type="button" name="previous" class="previous action-button-previous" value="Previous"/> -->
                                    <!-- <input type="button" name="next" class="btn next action-button mt-4" value="Next Step"/> -->
                                    <button type="button" name="next" class="btn action-button" (click)="navigateToLastLastFieldset()"> Next Step</button>
                                    <!-- <input type="submit" name="submit" class="next action-button" value="Submit" (click)="onSubmit()"/> -->
                                </div>
                            </fieldset>

                            <fieldset id="fourthFieldset" *ngIf="requestForm.controls['ownershipType'].value !== '639bf044-1cbb-4954-b170-9843347073ca'">
                                <div class="row mt-2">
        
                                    <div class="form-group col-md-6">
                                        <label class="main-label required-label">Cost of Acquisition Details: </label>
                                        <div formGroupName="costBenefitAnalysis" class="small-group">
        
                                            <div formGroupName="acquisitionCost">
                                                <div class="form-group">
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.acquisitionCost.acquisitionCost')}">Acquisition Cost: </label>
                                                    <input type="text" formControlName="acquisitionCost" class="form-control">
                                                </div>
                    
                                                <div class="form-group mt-2">
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.acquisitionCost.maintenanceCost')}">Maintenance and Repair Cost/ per year:</label>
                                                    <input type="number" formControlName="maintenanceCost" class="form-control" />
                                                </div>
                                                <div class="form-group mt-2">
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.acquisitionCost.fuelCost')}">Fuel Cost/ per year:</label>
                                                    <input type="number" formControlName="fuelCost" class="form-control" />
                                                </div>
                                                <div class="form-group mt-2">
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.acquisitionCost.oilRefillingCost')}">Oil Refilling Cost/ per year:</label>
                                                    <input type="number" formControlName="oilRefillingCost" class="form-control" />
                                                </div>
                                                <div class="form-group mt-2">
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.acquisitionCost.insuranceCost')}">Insurance Cost/ per year:</label>
                                                    <input type="number" formControlName="insuranceCost" class="form-control" />
                                                </div>
                                                <div class="form-group mt-2">
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.acquisitionCost.driveCost')}">Driver Cost/ per year:</label>
                                                    <input type="number" formControlName="driveCost" class="form-control" />
                                                </div>
                                                <div class="form-group mt-2">
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.acquisitionCost.depreciationCost')}">Depreciation Cost:</label>
                                                    <input type="number" formControlName="depreciationCost" class="form-control" />
                                                </div>
                                            </div>
         
                                            <div class="form-group  mt-4">
        
                                                    <label class="main-label required-label" style="margin-bottom: 10px;">Hiring Details: </label>
                                                    <label [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.hiringCostPerDay')}">Cost of Hiring/ per day: </label>
                                                    <input type="number" formControlName="hiringCostPerDay" class="form-control">
                                                
                                                    <label class="mt-4" [ngClass]="{'required-label': isFieldRequired('costBenefitAnalysis.hiringCost')}">Total Hiring Cost: (System Generated)</label>
                                                    <input type="number" formControlName="hiringCost" class="form-control">
                                            </div>
                                        </div>
        
                                        <!-- <div class="form-group mt-4"> -->
                                            <div formGroupName="driverDetails" class="small-group">
                                                <div class="form-group">
                                                    <label class="main-label" [ngClass]="{'required-label': isFieldRequired('driverDetails.isDriveAvailable')}">Is Driver Available?</label>
                                                    <div class="row ownershipRow">
                                                        <label class="col">
                                                            <input type="radio" formControlName="isDriveAvailable" value="true" />
                                                            Yes
                                                        </label>
                                                        <label class="col">
                                                            <input type="radio" formControlName="isDriveAvailable" value="false" />
                                                            No
                                                        </label>
                                                    </div>
                                                </div>
                
                                                <div *ngIf="showDriverPlan" style="width: 100%;" >
                                                    <div class="form-group mt-2">
                                                        <label [ngClass]="{'required-label': isFieldRequired('driverDetails.palanToRecruitDrive')}">Plan to Recruit Driver:</label>
                                                        <textarea formControlName="palanToRecruitDrive" class="form-control" rows="4"></textarea>
                                                    </div>
                                                </div>
                                                <div  *ngIf="showDriverDocument" style="width: 100%;">
                                                    <div class="form-group mt-2">
                                                        <label [ngClass]="{'required-label': isFieldRequired('driverDetails.driverQualification')}">Driver Qualification (eg: A, B, C or D): </label>
                                                        <input type="text" formControlName="driverQualification" class="form-control" />
                                                    </div>
                
                                                    <div class="form-group mt-2">
                                                        <label [ngClass]="{'required-label': isFieldRequired('driverDetails.numberOfDriver')}">Number of Drivers: </label>
                                                        <input type="number" formControlName="numberOfDriver" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        <!-- </div>  -->
                                    </div>
        
                                    <div class="form-group col-md-6">
                                        <label class="main-label required-label"> Staff Relevance:</label>
                                        <div formGroupName="staffRelevance" class="small-group">
        
                                            <div class="form-group">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.staffCategory')}">Staff Category:</label>
                                                <select name="staffCategory" id="staffCategory" class="form-control" formControlName="staffCategory">
                                                    <option value=""></option>
                                                    <option value="Institution Staff">Institution Staff</option>
                                                    <option value="SPIU">SPIU</option>
                                                </select>
                                            </div>
                                            <div class="form-group mt-2" *ngIf="requestForm.get('staffRelevance.staffCategory')?.value === 'Institution Staff'">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.staffsDepartments')}">Staff Department:</label>
                                                <input type="text" formControlName="staffsDepartments" class="form-control" />
                                            </div>
                                            <div class="form-group mt-2">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.numberOfStaff')}">Number of Staff</label>
                                                <input type="number" formControlName="numberOfStaff" class="form-control" />
                                            </div>
                                            <div class="form-group mt-2">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.staffPosition')}">Staff Position:</label>
                                                <textarea class="form-control" formControlName="staffPosition" rows="3"></textarea>
                                            </div>
                                            
                                            <div class="form-group mt-2">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.staffLevel')}">Staff Level:</label>
                                                <textarea class="form-control" formControlName="staffLevel" rows="3"></textarea>
                                            </div>
                                            <div class="form-group mt-2">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.workLocation')}">Work Location:</label>
                                                <input type="text" formControlName="workLocation" class="form-control" />
                                            </div>
                                            <div class="form-group mt-2">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.officeLocation')}">Office Location:</label>
                                                <input type="text" formControlName="officeLocation" class="form-control" />
                                            </div>
                                            <div class="form-group mt-2">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.dailyWorkFrequency')}">Frequency (number of days per week):</label>
                                                <input type="number" formControlName="dailyWorkFrequency" class="form-control" />
                                            </div>
                                            <div class="form-group mt-2">
                                                <label [ngClass]="{'required-label': isFieldRequired('staffRelevance.numberOfMonthPerYear')}">Number of Months per Year:</label>
                                                <input type="number" formControlName="numberOfMonthPerYear" class="form-control" />
                                            </div>
                                            <div class="form-group mt-2">
                                                <label>Other Description:</label>
                                                <textarea formControlName="OthersDescription" class="form-control" rows="4"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
        
                                <!-- <div class="row mt-2"> -->
                                    
                                <!-- </div> -->
        
                                <!-- <ng-template #previousButton>
                                        <button type="button" name="previous" class="previous action-button-previous" *ngIf="requestForm.controls['ownershipType'].value !== '639bf044-1cbb-4954-b170-9843347073ca'" (click)="PreviousToSecondFieldset()">Previous</button>
                                    </ng-template> -->
                                    <!-- <button type="button" name="previous" class="previous action-button-previous" *ngIf="requestForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">Previous</button> -->
        
                                <!-- <button type="button" (click)="submitCostBenefit('c18a0f2c-4427-4a60-bc80-81954da13323')">Submit</button> -->
                                <!-- <div class="row buttons d-flex justify-content-between mt-2 pe-2">
                                    <button type="button" name="previous" class="btn previous action-button-previous" (click)="handlePreviousStep()">Previous</button>
                                    <button type="button" name="next" class="btn next action-button mt-4" (click)="handleNextStep()"> Next Step</button>      
                                </div> -->
                                <div class="row flex justify-content-between mt-4 pe-2 ">
                                    <button type="button" name="previous" class="btn previous action-button-previous" (click)="PreviousToSecondFieldset()"> Previous </button>
                                    <button type="button" name="next" class="btn next action-button" (click)="handleNextStep()"> Next </button>
                                     <!-- <button type="button" (click)="submitCostBenefit('1f6c308b-7179-4d28-be2f-f6dd27e4bbff')">Submit</button> -->
                                </div>
                            </fieldset>
                   

                    <fieldset >
                        <div class="form-card">
                          <h2>Upload Supporting Files</h2>
                          <p *ngIf="requestForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'; else nextP">Upload at least one project document</p>
                          <ng-template #nextP>
                            <p>Upload these mandatory documents</p>
                          </ng-template>

                          <div class="row mt-4 mb-4">
                            <div class="form-group col-md-6">
                              <label for="operationPlan" [ngClass]="{'required-label': isFieldRequired('operationPlan')}">Operation Plan</label>
                              <input type="file" id="operationPlan" (change)="onFileChange($event, 'operationPlan')" class="form-control">
                              <div *ngIf="requestForm.get('operationPlan')?.errors && requestForm.get('operationPlan')?.touched" class="text-danger">
                                  <span *ngIf="requestForm.get('operationPlan')?.errors?.['required']">Operation Plan is required.</span>
                              </div>
                            </div>
  
                            <div class="form-group col-md-6" *ngIf="requestForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">
                              <label for="projectDocument" [ngClass]="{'required-label': isFieldRequired('projectDocument')}">Project Plan Document</label>
                              <input type="file" id="projectDocument" (change)="onFileChange($event, 'projectDocument')" class="form-control">
                              <div *ngIf="requestForm.get('projectDocument')?.errors && requestForm.get('projectDocument')?.touched" class="text-danger">
                                  <span *ngIf="requestForm.get('projectDocument')?.errors?.['required']">Project Document is required.</span>
                              </div>
                            </div>
                        </div>

                        <p>Any Additional Supporting files? </p>
                          <div class="array" formArrayName="files">
                            <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                              <div class="form-group col-md-6">
                                <input 
                                    type="file" 
                                    (change)="onFileChangee($event, i)" 
                                    accept="application/pdf"
                                    #fileInput
                                    class="form-control" 
                                    />
                              </div>
                              <div class="form-group col-md-6">
                                <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                              </div>

                              <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                                <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                            </button>
                            </div>
                          </div>
                          <!-- [disabled]="files.length >= 2" -->
                          <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                            <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add another file
                          </button>

                          
                        </div>

                        <div class="buttons d-flex justify-content-between p-2">
                            <button type="button" name="previous" *ngIf="requestForm.controls['ownershipType'].value !== '639bf044-1cbb-4954-b170-9843347073ca'; else previousField" class="previous action-button-previous">Previous</button>

                            <ng-template #previousField>
                                <button type="button" name="previous" class="previous action-button-previous" (click)="PreviousToSecondLastFieldset()">Previous</button>
                            </ng-template>


                            <input type="submit" name="submit" class="btn action-button" value="Submit" (click)="onSubmit()"/>
                        </div>
                    </fieldset>
                            
                            <!-- <fieldset>
                                <div class="form-card">
                                    <h2 class="text-success text-center">Successfully Requested A Vehicle !</h2>
                                    <div class="row justify-content-center">
                                        <div class="col-md-4" >
                                            <img src="https://img.icons8.com/color/96/000000/ok--v2.png" class="fit-image">
                                            <p>You Have successfully requested for a vehicle</p>
                                        </div>
                                    </div>
                                    <br><br>
                                </div>
                            </fieldset> -->
                            <!-- <div class="buttons d-flex justify-content-between p-3"> -->
                                <!-- <input type="button" name="previous" *ngIf="requestForm.controls['ownership'].value === '639bf044-1cbb-4954-b170-9843347073ca'" class="previous action-button-previous" value="Previous"/> -->
                                <!-- <input type="submit" name="submit" class="next action-button" value="Submit files" (click)="submitFile()"/> -->
                                <!-- <input type="submit" name="submit" class="btn btn-primary" value="Submit files" (click)="submitFile()"/> -->
                                <!-- <button type="button" class="btn btn-primary" (click)="submitFile()">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    Submitting...
                                </button> -->
                                <!-- <input type="submit" name="submit" class="next action-button" value="Submit" (click)="onSubmit()"/>
                            </div> -->
                        </form>
                    </div>
                </div>
            </div>
        </div>