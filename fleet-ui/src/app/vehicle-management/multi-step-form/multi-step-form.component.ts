import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl, AbstractControl, Form } from '@angular/forms';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';
import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, catchError, forkJoin, map, of, throwError } from 'rxjs';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-multi-step-form',
  templateUrl: './multi-step-form.component.html',
  styleUrls: ['./multi-step-form.component.scss']
})
export class MultiStepFormComponent implements OnInit {
  
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  ownerships: any[] = [];
  institutions: any[] = [];
  requestTypes: any[] = [];
  vehicleTypes: any[] = [];
  beneficiaryAgencies: any[] = [];
  dropdownConfig: any = {
    displayKey: "name",   // Display the name of the institution
    valueField: "id",
    search: true, 
    height: '300px', // set the height
    placeholder: 'Select', // placeholder text
    customComparator: () => {}, // optional, for custom sorting
    limitTo: 0, // number of items to display in the dropdown. 0 = all
    moreText: 'more', // text for more
    noResultsFound: 'No results found!', // text for no results
    searchPlaceholder: 'Search...', 
    clearOnSelection: true, 
    inputDirection: 'ltr',
};
  showToastr: boolean = false;
  toastrMessage: string = '';
  toastrSuccess: boolean = false;
  userId: string = '';
  institution: string = '';
  institutionId: string = '';
  requestForm !: FormGroup;
  vehicleDetails !: FormArray;
  files !: FormArray;

  formValues: any = {};
  ownershipType: string = '';

  firstFieldsetValues: any;
  thirdFieldsetValues: any;
  plusIcon = faPlus;
  minusIcon = faMinus;
  acquisitionId: any;
  selectedFileName: any;

  showHiringCost = false;
  showOwningCost = false;
  showDriverDetails = false;
  showDriverPlan = false;
  showDriverDocument = false;
  showDepartment = false;

  constructor(
    private formBuilder: FormBuilder, 
    private http: HttpClient, 
    private toastr: ToastrService, 
    private route: ActivatedRoute, 
    private router: Router,
    private cdRef: ChangeDetectorRef
  ) {
    this.route.params.subscribe(params => {
      if (params['acquisitionId']) {
        this.requestForm = this.formBuilder.group({
          institution: ['', Validators.required],
          ownershipType: ['', Validators.required],
          requestType: ['', Validators.required],
          description: ['', Validators.required],
          vehicleTypeid: ['', Validators.required],
          numberOfVehicles: ['', Validators.required],
          beneficiaryAgencyId: [''],
          intendedUsage: ['', Validators.required],
          projectName: ['', Validators.required],
          projectDescription: ['', Validators.required],
          projectStartDate: ['', Validators.required],
          projectEndDate: ['', Validators.required],
          file: ['', Validators.required],
          documentDescription: [''],
          vehicleDetails: this.formBuilder.array([]), 
          files: this.formBuilder.array([this.createFileFormGroup()]),
          operationPlan: [null, Validators.required]          ,
          projectDocument: [null, Validators.required],
          costBenefitAnalysis: this.formBuilder.group({
            // vehicleOwnership: [null, Validators.required],
            hiringCost: [{ value: null, disabled: true }],
            hiringCostPerDay: [null, Validators.required],
            acquisitionCost: this.formBuilder.group({
              acquisitionCost: [null, Validators.required],
              maintenanceCost: [null, Validators.required],
              fuelCost: [null, Validators.required],
              oilRefillingCost: [null, Validators.required],
              insuranceCost: [null, Validators.required],
              driveCost: [null, Validators.required],
              // depreciationCost: [null, Validators.required]
              depreciationCost: [{ value: null, disabled: true }]
            })
          }),
          staffRelevance: this.formBuilder.group({
            staffCategory: [null, Validators.required],
            staffsDepartments: [null],
            numberOfStaff: [null, Validators.required],
            staffPosition: [null, Validators.required],
            staffLevel: [null, Validators.required],
            workLocation: [null, Validators.required],
            officeLocation: [null, Validators.required],
            dailyWorkFrequency: [null, Validators.required],
            numberOfMonthPerYear: [null, Validators.required],
            OthersDescription: [null]
          }),
          driverDetails: this.formBuilder.group({
            isDriveAvailable: [null, Validators.required],
            palanToRecruitDrive: [null, Validators.required],
            driverQualification: ["N/A", Validators.required],
            numberOfDriver: [0, Validators.required],
          })
        }, { validators: this.dateValidator });

        this.onChanges();

      } else {
        this.requestForm = this.formBuilder.group({
          institution: ['', Validators.required],
          ownershipType: ['', Validators.required],
          requestType: ['', Validators.required],
          description: ['', Validators.required],
          vehicleTypeid: ['', Validators.required],
          numberOfVehicles: ['', Validators.required],
          beneficiaryAgencyId: [''],
          intendedUsage: ['', Validators.required],
          projectName: ['', Validators.required],
          projectDescription: ['', Validators.required],
          projectStartDate: ['', Validators.required],
          projectEndDate: ['', Validators.required],
          file: ['', Validators.required],
          operationPlan: [null, Validators.required]          ,
          projectDocument: [null, Validators.required],
          documentDescription: [''],
          vehicleDetails: this.formBuilder.array([this.creatVehicleDetails()]), 
          files: this.formBuilder.array([this.createFileFormGroup()]),

          costBenefitAnalysis: this.formBuilder.group({
            // vehicleOwnership: [null, Validators.required],
            hiringCost: [{ value: null, disabled: true }],
            hiringCostPerDay: [null, Validators.required],
            acquisitionCost: this.formBuilder.group({
              acquisitionCost: [null, Validators.required],
              maintenanceCost: [null, Validators.required],
              fuelCost: [null, Validators.required],
              oilRefillingCost: [null, Validators.required],
              insuranceCost: [null, Validators.required],
              driveCost: [null, Validators.required],
              // depreciationCost: [null, Validators.required]
              depreciationCost: [{ value: null, disabled: true }]
            })
          }),
          staffRelevance: this.formBuilder.group({
            staffCategory: [null, Validators.required],
            staffsDepartments: [null],
            numberOfStaff: [null, Validators.required],
            staffPosition: [null, Validators.required],
            staffLevel: [null, Validators.required],
            workLocation: [null, Validators.required],
            officeLocation: [null, Validators.required],
            dailyWorkFrequency: [null, Validators.required],
            numberOfMonthPerYear: [null, Validators.required],
            OthersDescription: [null]
          }),
          driverDetails: this.formBuilder.group({
            isDriveAvailable: [null, Validators.required],
            palanToRecruitDrive: [null, Validators.required],
            driverQualification: ["N/A", Validators.required],
            numberOfDriver: [0, Validators.required]
          })
        }, { validators: this.dateValidator });
        this.onChanges();
      }
      
    this.vehicleDetails = this.requestForm.get('vehicleDetails') as FormArray;
    this.files = this.requestForm.get('files') as FormArray;
    // this.beneficiaryAgencyDetails = this.requestForm.get('beneficiaryAgencyDetails') as FormArray;
  });
  }

  dateValidator(form: FormGroup) {
    const startDate = form.get('projectStartDate')?.value;
    const endDate = form.get('projectEndDate')?.value;
  
    if (startDate && endDate && endDate < startDate) {
      return { invalidDateRange: true };
    }
    return null;
  }

  onChanges(): void {
    this.requestForm.get('costBenefitAnalysis.vehicleOwnership')?.valueChanges.subscribe(value => {
      this.showHiringCost = value === 'hiring';
      this.showOwningCost = value === 'owning';
    });

    this.requestForm.get('driverDetails.isDriveAvailable')?.valueChanges.subscribe(value => {
      this.showDriverPlan = value === 'false';
      this.showDriverDocument = value === 'true';
    });

  }

  isFieldRequired(fieldName: string): boolean {
    const control = this.requestForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }

  ngOnInit(): void {
    this.fetchVehicleTypes();
    this.fetchOwnershipTypes();
    this.fetchRequestTypes();
    this.getUserData();
    this.fetchBeneficiaryAgencies();
    this.checkEditMode();
    this.handlePreviousStep();
    this.onChanges();
    this.calculateTotalHiringCost();

    this.requestForm.get('costBenefitAnalysis.acquisitionCost.acquisitionCost')?.valueChanges.subscribe(value => {
      this.calculateDepreciation(value);
    });

    this.requestForm.get('costBenefitAnalysis.hiringCostPerDay')?.valueChanges.subscribe(() => {
      this.calculateTotalHiringCost();
    });
    this.requestForm.get('staffRelevance.dailyWorkFrequency')?.valueChanges.subscribe(() => {
      this.calculateTotalHiringCost();
    });
    this.requestForm.get('staffRelevance.numberOfMonthPerYear')?.valueChanges.subscribe(() => {
      this.calculateTotalHiringCost();
    });
  }

  calculateDepreciation(acquisitionCost: number): void {
    const depreciation = acquisitionCost * 0.1;
    // const depreciationCost = parseFloat(depreciation.toFixed(1))
    const depreciationCost = Math.round(depreciation);
    this.requestForm.get('costBenefitAnalysis.acquisitionCost.depreciationCost')?.setValue(depreciationCost);
  }

  calculateTotalHiringCost(): void {
    const hiringCostPerDay = this.requestForm.get('costBenefitAnalysis.hiringCostPerDay')?.value || 0;
    const dailyWorkFrequency = this.requestForm.get('staffRelevance.dailyWorkFrequency')?.value || 0;
    const numberOfMonthPerYear = this.requestForm.get('staffRelevance.numberOfMonthPerYear')?.value || 0;
  
    if (typeof hiringCostPerDay !== 'number' || typeof dailyWorkFrequency !== 'number' || typeof numberOfMonthPerYear !== 'number') {
      console.error("Invalid input for cost calculation");
      return;
    }
  
    const totalHiringCost = hiringCostPerDay * dailyWorkFrequency * numberOfMonthPerYear;
    
    this.requestForm.get('costBenefitAnalysis.hiringCost')?.setValue(totalHiringCost, { emitEvent: false });
  
    console.log("Hiring Cost:", totalHiringCost);
  }
  

  handleNextStep(): void {
    const currentFieldset = document.querySelector('fieldset:not([style="display: none;"])');

    if (currentFieldset) {
        const nextFieldset = currentFieldset.nextElementSibling as HTMLFieldSetElement;

        if (nextFieldset) {
            const fields = currentFieldset.querySelectorAll('input, select');
            let isValid = true;
            fields.forEach(field => {
                const inputField = field as HTMLInputElement;
                if (!inputField.value) {
                    isValid = false;
                }
            });

            if (isValid) {
                const progressBarItems = document.querySelectorAll('#progressbar li');
                progressBarItems[Array.from(document.querySelectorAll('fieldset')).indexOf(nextFieldset)]
                    .classList.add('active');

                (currentFieldset as HTMLElement).style.display = 'none';
                nextFieldset.style.display = 'block';
            } else {
                this.toastr.error('Please fill all fields before proceeding', 'Error');
            }
        }
    } else {
        console.error('Current fieldset is null');
    }
}

  handlePreviousStep(): void {
    const previousButtons = document.querySelectorAll('.previous');
  
    previousButtons.forEach(button => {
      button.addEventListener('click', () => {
        const currentFieldSet = button.closest('fieldset') as HTMLFieldSetElement;
        const previousFieldSet = currentFieldSet.previousElementSibling as HTMLFieldSetElement;
  
        if (previousFieldSet) {
          const progressBarItems = document.querySelectorAll('#progressbar li');
          progressBarItems[Array.from(document.querySelectorAll('fieldset')).indexOf(currentFieldSet)]
            .classList.remove('active');
  
          currentFieldSet.style.display = 'none';
          previousFieldSet.style.display = 'block';
        }
      });
    });
  }

  PreviousToSecondFieldset(): void {
    const currentFieldset = document.querySelector('fieldset:not([style="display: none;"])') as HTMLFieldSetElement;
    
    if (currentFieldset) {
      const secondFieldset = document.getElementById('secondFieldset') as HTMLFieldSetElement;
      const progressBarItems = document.querySelectorAll('#progressbar li');
      
      progressBarItems.forEach((item, index) => {
        item.classList.remove('active');
        if (index <= 1) { // Index of the second fieldset in the progress bar
          item.classList.add('active');
        }
      });
      
      currentFieldset.style.display = 'none';
      secondFieldset.style.display = 'block';
    } else {
      console.warn('Cannot navigate to the second fieldset from the current position.');
      // Handle potential warnings or errors if user tries to go back from a different fieldset
    }
  }
  
  PreviousToSecondLastFieldset(): void {
    const currentFieldset = document.querySelector('fieldset:not([style="display: none;"])') as HTMLFieldSetElement;
    
    if (currentFieldset) {
      const secondFieldset = document.getElementById('thirdFieldset') as HTMLFieldSetElement;
      const progressBarItems = document.querySelectorAll('#progressbar li');
      
      progressBarItems.forEach((item, index) => {
        item.classList.remove('active');
        if (index <= 2) { // Index of the second fieldset in the progress bar
          item.classList.add('active');
        }
      });
      
      currentFieldset.style.display = 'none';
      secondFieldset.style.display = 'block';
    } else {
      console.warn('Cannot navigate to the second fieldset from the current position.');
      // Handle potential warnings or errors if user tries to go back from a different fieldset
    }
  }
  
  
  fetchBeneficiaryAgencies(): void {
    const apiUrl = `${environment.baseUrl}/user-management/institutions`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.beneficiaryAgencies = response.map(institution => ({ id: institution.id, name: institution.name }));
      },
      (error) => {
        console.error('Error fetching beneficiary agencies:', error);
      }
    );
  }

  fetchVehicleTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleTypes = response.map(vehicleType => ({ id: vehicleType.id, name: vehicleType.name }));
      },
      (error) => {
        console.error('Error fetching vehicleTypes:', error)
      }
    );
  }

  fetchOwnershipTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/ownershipTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.ownerships = response.map(ownership => ({ id: ownership.id, name: ownership.name }));
      },
      (error) => {
        console.error('Error fetching ownershipTypes:', error)
      }
    );
  }
  
  fetchRequestTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/requestTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.requestTypes = response.map(requestType => ({ id: requestType.id, name: requestType.name }));
      },
      (error) => {
        console.error('Error fetching request types:', error)
      }
    );
  }

creatVehicleDetails(): FormGroup{
  return this.formBuilder.group({
    vehicleTypeid: ['', Validators.required],
    numberOfVehicles: ['', Validators.required],
    beneficiaryAgencyId: ['', Validators.required],
    intendedUsage: ['', Validators.required],
    projectName: ['', Validators.required],
    projectDescription: ['', Validators.required],
    projectStartDate: ['', Validators.required],
    projectEndDate: ['', Validators.required]
  });
}

addVehicleDetails(): void {
  this.vehicleDetails?.push(this.creatVehicleDetails());
}

removeVehicleDetails(index: number):void {
  this.vehicleDetails?.removeAt(index);
}

getFirstFieldsetValues(): void {
  if (
    this.requestForm.controls['institution'].value !== null &&
    this.requestForm.controls['ownershipType'].value !== null &&
    this.requestForm.controls['requestType'].value !== null &&
    this.requestForm.controls['vehicleTypeid'].value !== null &&
    this.requestForm.controls['description'].value !== null
  ) {
    const selectedOwnership = this.ownerships.find(
      (ownership) => ownership.id === this.requestForm.controls['ownershipType'].value
    );
    const selectedRequestType = this.requestTypes.find(
      (requestType) => requestType.id === this.requestForm.controls['requestType'].value
    );

    this.firstFieldsetValues = {
      userId: this.userId,
      requestType: selectedRequestType?.id,
      ownershipType: selectedOwnership?.id,
      institution: this.institution,
      institutionId: this.institutionId,
      // vehicleType: selectedVehicleType?.id,
      description: this.requestForm.controls['description'].value,
    };
  }
}

getVehicleDetails(): any[] {
  const vehicleDetailsArray: any[] = [];
  const vehicleDetailsFormArray = this.requestForm.get('vehicleDetails') as FormArray;
  vehicleDetailsFormArray.controls.forEach((control: AbstractControl) => {
    if (control instanceof FormGroup) {
      const vehicleDetail = {
        vehicleTypeid: control.get('vehicleTypeid')?.value,
        numberOfVehicles: control.get('numberOfVehicles')?.value,
        beneficiaryAgencyId: (control.get('beneficiaryAgencyId')?.value).id,
        // beneficiaryAgency: this.beneficiaryAgencies.find(agency => agency.id === control.get('beneficiaryAgencyId')?.value).name,
        beneficiaryAgency: (control.get('beneficiaryAgencyId')?.value).name,
        intendedUsage: control.get('intendedUsage')?.value,
        projectName: this.requestForm.get('projectName')?.value, 
        projectDescription: this.requestForm.get('projectDescription')?.value, 
        projectStartDate: this.requestForm.get('projectStartDate')?.value,
        projectEndDate: this.requestForm.get('projectEndDate')?.value,
      };
      vehicleDetailsArray.push(vehicleDetail);
    }
  });

  return vehicleDetailsArray;
}

getUserData(){
  const data= localStorage.getItem('localUserData');
  if(data != null){
    const parseObj= JSON.parse(data)
    this.userId = parseObj.data.user.id;
    this.institution = parseObj.data.user.institution.name;
    this.institutionId = parseObj.data.user.institution.id;
    console.log(this.institution, this.userId);
  }
}

submitAdditionalFile(acquisitionId: any): void {
  const fileUploadDataFormArray = this.requestForm.get('files') as FormArray;

  // Collect file upload promises for sequential processing
  const uploadPromises = fileUploadDataFormArray.controls.map((control: AbstractControl) => {
    if (control instanceof FormGroup) {
      const fileItem = control.value;
      const file = fileItem.file as File;

      if (file && this.validateFile(file)) {
        const fileFormData = new FormData();
        fileFormData.append('file', file);
        fileFormData.append('documentDescription', fileItem.documentDescription);
        fileFormData.append('applicationId', acquisitionId);

        return this.http.post(`${environment.fileUrl}/upload`, fileFormData).toPromise(); // Convert Observable to Promise
      } else if (file) {
        // Stop if validation fails
        const message = `File "${file.name}" failed validation (invalid size or type).`;
        this.toastr.error(message, 'Validation Error');
        return Promise.reject(new Error(message));
      }
    }
    return Promise.resolve(); // Skip if no file is provided
  });

  // Process file uploads sequentially
  (async () => {
    try {
      for (const uploadPromise of uploadPromises) {
        if (uploadPromise) {
          await uploadPromise; // Await each upload; stop if any fails
        }
      }
      this.toastr.success('All additional files uploaded successfully!');
      console.log('All files uploaded successfully');
    } catch (error) {
      let errorMessage = 'An unexpected error occurred during file upload.';

      // Check if error is an instance of Error
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      console.error('File upload failed:', errorMessage);
      this.toastr.error(errorMessage, 'Error');
    }
  })();
}




submitFile(file: File, fileType: string, acquisitionId: string): Observable<any> {
  if (file) {
    // Validate the file using the helper function
    if (!this.validateFile(file)) {
      console.warn(`${fileType} size or type is invalid.`);
      return of(null); // Skip the file if validation fails
    }

    const fileFormData = new FormData();
    fileFormData.append('file', file);
    fileFormData.append('documentDescription', fileType);
    fileFormData.append('applicationId', acquisitionId);

    return this.http.post(`${environment.fileUrl}/upload`, fileFormData);
  }

  return of(null); 
}

onSubmit(): void {
  // Retrieve files and form values
  const operationPlanFile = this.requestForm.get('operationPlan')?.value as File;
  const projectPlanFile = this.requestForm.get('projectDocument')?.value as File;
  const ownershipType = this.requestForm.controls['ownershipType'].value;
  const additionalFilesArray = this.requestForm.get('files') as FormArray;

  // Collect all files for validation
  const filesToValidate = [
    { file: operationPlanFile, name: 'Operation Plan' },
    ...(ownershipType === '639bf044-1cbb-4954-b170-9843347073ca'
      ? [{ file: projectPlanFile, name: 'Project Plan' }]
      : []),
    ...additionalFilesArray.controls.map((control, index) => ({
      file: this.getValidFile(control.value), // Ensure file object is valid
      name: `Additional File ${index + 1}`,
    })),
  ];

  // Filter out any files that are null or invalid
  const validFilesToValidate = filesToValidate.filter(({ file }) => file !== null) as { file: File, name: string }[];

  // Validate all files
  for (const { file, name } of validFilesToValidate) {
    console.log(`Validating file: ${name}`);
    const validationResult = this.validateFile(file);
    if (validationResult !== true) {
      console.log(`Validation failed for ${name}: ${validationResult}`);
      this.toastr.error(`Validation failed for ${name}: ${validationResult}`, 'Error');
      return; // Exit process if validation fails
    } else {
      console.log(`Validation passed for: ${name}`);
    }
  }

  // Proceed if all files pass validation
  this.getFirstFieldsetValues();

  // Step 1: Submit acquisition data
  this.http.post(`${environment.otherUrl}/acquisition`, this.firstFieldsetValues).subscribe(
    (response: any) => {
      const acquisitionId = response.acquisitionId;

      // Step 2: Submit vehicle details
      this.submitCostBenefit(acquisitionId);
      const vehicleDetails = this.getVehicleDetails();
      const submitVehicleDetails = (index: number) => {
        if (index < vehicleDetails.length) {
          const combinedData = {
            acquisitionId: acquisitionId,
            ...vehicleDetails[index],
          };

          this.http.post(`${environment.otherUrl}/createVehicle`, combinedData).subscribe(
            () => {
              submitVehicleDetails(index + 1);
            },
            (error) => {
              console.error('Error submitting vehicle detail:', error);
              this.toastr.error('An error occurred while submitting vehicle detail.', 'Error');
            }
          );
        } else {
          // All vehicle details submitted successfully
          this.toastr.success('All Vehicle Details Submitted!');
          this.submitFiles(acquisitionId, validFilesToValidate);
        }
      };
      submitVehicleDetails(0);
    },
    (error) => {
      console.error('Error submitting acquisition data:', error);
      this.toastr.error('An error occurred while submitting acquisition data.', 'Error');
    }
  );
}

// Function to create a valid File object from form control value
private getValidFile(file: any): File | null {
  if (file instanceof File) {
    return file;
  }
  if (file && file.file) {
    // Assume the file metadata is stored in a subfield like `file`
    return new File([file.file], file.file.name, { type: file.file.type });
  }
  console.log('Invalid file structure detected:', file);
  return null;
}

// Function to validate a file
private validateFile(file: File | null): string | boolean {
  if (!file) {
    return 'File is missing.';
  }
  if (!file.name || !file.type) {
    return 'Invalid file object.';
  }
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    return `Invalid file type: ${file.type}. Allowed types are PDF, JPEG, JPG, and PNG.`;
  }
  if (file.size > 5 * 1024 * 1024) {
    return `File size exceeds the 5MB limit. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB.`;
  }
  return true;
}

// Helper to submit files
private submitFiles(acquisitionId: string, files: { file: File; name: string }[]): void {
  const fileUploadObservables = files.map(({ file, name }) =>
    this.submitFile(file, this.getFileType(name), acquisitionId).pipe(
      catchError((error) => {
        console.error(`Error uploading ${name}:`, error);
        this.toastr.error(`Failed to upload ${name}.`, 'Error');
        return of(null); // Prevent the entire process from failing
      })
    )
  );

  forkJoin(fileUploadObservables).subscribe(
    (responses) => {
      if (responses.includes(null)) {
        this.toastr.error('One or more files failed to upload. Process aborted.', 'Error');
      } else {
        this.toastr.success('All files uploaded successfully!');
        this.requestForm.reset();
        this.navigateToLastFieldset();
        this.router.navigate(['/vehicle-management/all-vehicles']);
      }
    },
    (error) => {
      console.error('Error during file uploads:', error);
      this.toastr.error('File upload failed. Process aborted.', 'Error');
    }
  );
}

// private submitFiles(acquisitionId: string, files: { file: File; name: string }[]): void {
//   const fileUploadObservables = files.map(({ file, name }) => {
//     if (name === 'Operation Plan' || name === 'Project Plan') {
//       // For Operation Plan and Project Plan, use submitFile
//       return this.submitFile(file, this.getFileType(name), acquisitionId).pipe(
//         catchError((error) => {
//           console.error(`Error uploading ${name}:`, error);
//           this.toastr.error(`Failed to upload ${name}.`, 'Error');
//           return of(null); // Prevent the entire process from failing
//         })
//       );
//     } else {
//       // For additional files, use submitAdditionalFile
//       return this.submitAdditionalFile(acquisitionId).pipe(
//         catchError((error) => {
//           console.error(`Error uploading ${name}:`, error);
//           this.toastr.error(`Failed to upload ${name}.`, 'Error');
//           return of(null); // Prevent the entire process from failing
//         })
//       );
//     }
//   });

//   forkJoin(fileUploadObservables).subscribe(
//     (responses) => {
//       if (responses.includes(null)) {
//         this.toastr.error('One or more files failed to upload. Process aborted.', 'Error');
//       } else {
//         this.toastr.success('All files uploaded successfully!');
//         this.requestForm.reset();
//         this.navigateToLastFieldset();
//         this.router.navigate(['/vehicle-management/all-vehicles']);
//       }
//     },
//     (error) => {
//       console.error('Error during file uploads:', error);
//       this.toastr.error('File upload failed. Process aborted.', 'Error');
//     }
//   );
// }


// Helper to determine file type
private getFileType(name: string): string {
  if (name.includes('Operation Plan')) return 'operationPlan';
  if (name.includes('Project Plan')) return 'projectPlan';
  return 'additionalFile';
}



isAnyFieldEmpty(): boolean {
  const formValues = this.requestForm.value;
  for (const key in formValues) {
    if (formValues.hasOwnProperty(key)) {
      if (Array.isArray(formValues[key])) {
        if (formValues[key].length === 0) {
          return true;
        }
      } else {
        if (!formValues[key]) {
          return true;
        }
      }
    }
  }
  return false;
}

isSubmitVisible: boolean = false;

addFile(): void {
  if (this.files.length < 2) {
    this.files.push(this.createFileFormGroup());
  } else {
    console.warn("Maximum number of files (2) reached.");
  }
}

removeFile(index: number): void {
  this.files.removeAt(index);
}

createFileFormGroup(): FormGroup {
  return this.formBuilder.group({
    file: [null],
    documentDescription: [''],
    applicationId: ['']
  });
}

// onFileChangee(event: any, index: number): void {
//   const file = event.target.files[0];
//   if (file) {
//     const fileControl = this.files.at(index).get('file');
    
//     if (file.type !== 'application/pdf') {
//       this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
//       event.target.value = ''; // Reset only the input element
//       if (fileControl) {
//         fileControl.patchValue(null);
//       }
//       return;
//     }

//     if (fileControl) {
//       // Store the file object in the form control
//       fileControl.patchValue(file);
//     }
//     this.selectedFileName = file.name;
//   }
// }

onFileChangee(event: any, index: number): void {
  if (!event || !event.target || !event.target.files || event.target.files.length === 0) {
    this.toastr.error('No file selected. Please choose a file.', 'Error');
    return;
  }

  const file = event.target.files[0];
  console.log('Uploaded file:', file);

  if (file) {
    const fileControl = this.files.at(index).get('file');

    if (!fileControl) {
      this.toastr.error('File control not found. Please check the form setup.', 'Error');
      return;
    }

    // Validate file type
    if (file.type !== 'application/pdf') {
      this.toastr.error('Invalid file type. Only PDF files are allowed.', 'Error');
      event.target.value = ''; // Reset the input field
      fileControl.patchValue(null);
      return;
    }

    // Log file details
    console.log('Valid file type:', file.type);
    console.log('File size:', file.size);

    // Patch the file object to the form control
    fileControl.patchValue(file); // Store as a proper File object

    this.selectedFileName = file.name;
    console.log('File successfully uploaded:', file.name);
  }
}




// onFileChange(event: any, fileType: string): void {
//   const file = event.target.files[0];
//   if (file) {
//     this.requestForm.patchValue({ [fileType]: file });
//   }
// }

onFileChange(event: any, fileType: string): void {
  event.preventDefault(); 
  const file = event.target.files[0];
  
  if (file) {
    if (file.type !== 'application/pdf') {
      this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
      
      event.target.value = ''; 
      
      this.requestForm.patchValue({ [fileType]: null });
      
      return; 
    }

    this.requestForm.patchValue({ [fileType]: file });
    
  }
}

// validateFile(file: File): boolean {
//   // Log the file object to check its properties
//   console.log("Validating file:", file);

//   const allowedMimeTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
//   const maxSize = 5 * 1024 * 1024; // 5MB

//   // Check if the file is null or undefined
//   if (!file) {
//     this.toastr.error('No file uploaded. Please select a file to proceed.', 'Validation Error');
//     return false;
//   }

//   // Log file type and size for debugging
//   console.log("File type:", file.type);
//   console.log("File size:", file.size);

//   // Validate file type
//   if (!allowedMimeTypes.includes(file.type)) {
//     this.toastr.error(
//       `Invalid file type: ${file.type}. Allowed types are PDF, JPEG, JPG, and PNG.`,
//       'Validation Error'
//     );
//     return false;
//   }

//   // Validate file size
//   if (file.size > maxSize) {
//     const fileSizeInMB = (file.size / (1024 * 1024)).toFixed(2);
//     this.toastr.error(
//       `File size is too large (${fileSizeInMB}MB). Maximum allowed size is 5MB.`,
//       'Validation Error'
//     );
//     return false;
//   }

//   return true;
// }






navigateToLastFieldset(): void {
  const currentFieldset = document.querySelector('fieldset:not([style="display: none;"])');

  if (currentFieldset) {
      const fields = currentFieldset.querySelectorAll('input[required], select[required]');
      let isValid = true;

      fields.forEach(field => {
          const inputField = field as HTMLInputElement;
          if (!inputField.value) {
              isValid = false;
              return;
          }
      });

      if (!isValid) {
          this.toastr.error('Please fill all required fields before proceeding', 'Error');
          return;
      }
  } else {
      console.error('Current fieldset is null');
      return;
  }

  const fieldsets = document.querySelectorAll('fieldset');
  const lastFieldset = fieldsets[fieldsets.length - 2] as HTMLFieldSetElement;

  fieldsets.forEach(fieldset => fieldset.style.display = 'none');
  lastFieldset.style.display = 'block';

  const progressBarItems = document.querySelectorAll('#progressbar li');
  progressBarItems.forEach((item, index) => {
      if (index < progressBarItems.length - 1) {
          item.classList.add('active');
      } else {
          item.classList.remove('active');
      }
  });
}

navigateToLastLastFieldset(): void {
  const currentFieldset = document.querySelector('fieldset:not([style="display: none;"])');

  if (currentFieldset) {
      const fields = currentFieldset.querySelectorAll('input[required], select[required]');
      let isValid = true;

      fields.forEach(field => {
          const inputField = field as HTMLInputElement;
          if (!inputField.value) {
              isValid = false;
              return;
          }
      });

      if (!isValid) {
          this.toastr.error('Please fill all required fields before proceeding', 'Error');
          return;
      }
  } else {
      console.error('Current fieldset is null');
      return;
  }

  const fieldsets = document.querySelectorAll('fieldset');
  const lastFieldset = fieldsets[fieldsets.length - 1] as HTMLFieldSetElement;

  fieldsets.forEach(fieldset => fieldset.style.display = 'none');
  lastFieldset.style.display = 'block';

  const progressBarItems = document.querySelectorAll('#progressbar li');
  progressBarItems.forEach((item, index) => {
      if (index < progressBarItems.length) {
          item.classList.add('active');
      } else {
          item.classList.remove('active');
      }
  });
}

  submitCostBenefit(acquisitionId:any) {

    // this.calculateTotalHiringCost();
  // console.log(this.requestForm.get('costBenefitAnalysis')?.value)

  const acquisitionCostGroup = this.requestForm.get('costBenefitAnalysis.acquisitionCost') as FormGroup;

  const costBenefitValues = {
    acquisitionCost: acquisitionCostGroup.get('acquisitionCost')?.value,
    maintenanceCost: acquisitionCostGroup.get('maintenanceCost')?.value,
    fuelCost: acquisitionCostGroup.get('fuelCost')?.value,
    oilRefillingCost: acquisitionCostGroup.get('oilRefillingCost')?.value,
    insuranceCost: acquisitionCostGroup.get('insuranceCost')?.value,
    driveCost: acquisitionCostGroup.get('driveCost')?.value,
    depreciationCost: acquisitionCostGroup.get('depreciationCost')?.value,
    VehicleAcquisitionID: acquisitionId
  };
  console.log("Cosst benefi...",JSON.stringify(costBenefitValues));

  this.http.post(`${environment.otherUrl}/cost-benefit`, costBenefitValues).subscribe(
    (response: any) => {
      console.log(response);
    },
    (error) => {
      console.log(error);
    }
  )

  const driverDetailsValue = {
    ...this.requestForm.get('driverDetails')?.value,
    VehicleAcquisitionID: acquisitionId
  }

  console.log("driver det....",JSON.stringify(driverDetailsValue));

  this.http.post(`${environment.otherUrl}/crete-drive-details`, driverDetailsValue).subscribe(
    (response: any) => {
      console.log(response);
      // this.toastr.success('Driver details success');
    },
    (error) => {
      console.log(error);
      // this.toastr.error("driver error");
    }
  )

  const staffRelevanceValue = {
    ...this.requestForm.get('staffRelevance')?.value,
    VehicleAcquisitionID: acquisitionId
  }

  console.log("staff relve...",JSON.stringify(staffRelevanceValue));

  this.http.post(`${environment.otherUrl}/staff-relevance`, staffRelevanceValue).subscribe(
    (response: any) => {
      console.log(response);
      // this.toastr.success("staff succsssssssssssssssssssssess")
    },
    (error) => {
      console.log(error);
      // this.toastr.error("staff error")
    }
  )
  const hiringValue = {
    hiringCost: this.requestForm.get('costBenefitAnalysis.hiringCost')?.value,
    VehicleAcquisitionID: acquisitionId 
  }

  console.log("hiring dets....",JSON.stringify(hiringValue));

  this.http.post(`${environment.otherUrl}/crete-hiring-cost`, hiringValue).subscribe(
    (response: any) => {
      console.log(response);
    },
    (error) => {
      console.log(error);
    }
  )
}


checkEditMode(): void {
  this.route.params.subscribe(params => {
    if (params['acquisitionId']) {
      this.acquisitionId = params['acquisitionId'];
      this.fetchExistingData(this.acquisitionId);
    }
  });
}

acquisitions: any[] = [];
acquisitionDetails: any = {};

fetchExistingData(acquisitionId: string): void {
  const url = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;
  this.http.get<any>(url).subscribe(response => {
    console.log(response)
    if (response && response.vehicles && Array.isArray(response.vehicles)) {
      this.acquisitions = response.vehicles; 
      this.acquisitionDetails = response.acquisition; 
      this.acquisitionId = response.id
     
      const userId = this.acquisitionDetails.userId;
      if (userId) {
        // this.fetchUserDetails(userId);
      }

      this.requestForm.patchValue({
        institution: this.acquisitionDetails.acquisition,
        ownershipType: this.acquisitionDetails.ownershipType.id,
        requestType: this.acquisitionDetails.requestType.id,
        description: this.acquisitionDetails.description
      });

      this.acquisitions.forEach(vehicleDetail => {
        console.log("Each VehicleDetail: ", vehicleDetail.projectName);

        const group = this.formBuilder.group({
          vehicleTypeid: [vehicleDetail.vehicleType.id, Validators.required],
          numberOfVehicles: [vehicleDetail.numberOfVehicles, Validators.required],
          beneficiaryAgencyId: [vehicleDetail.beneficiaryAgencyId, Validators.required],
          beneficiaryAgency: [vehicleDetail.beneficiaryAgency, Validators.required],
          intendedUsage: [vehicleDetail.intendedUsage, Validators.required],
        });
        (this.requestForm.get('vehicleDetails') as FormArray).push(group);
      });

      this.acquisitions.forEach(vehicleDetail => {
        this.requestForm.patchValue({
          projectName: vehicleDetail.projectName, 
          projectDescription: vehicleDetail.projectDescription, 
          projectStartDate: vehicleDetail.projectStartDate, 
          projectEndDate: vehicleDetail.projectEndDate, 
        })
      })
    } else {
      console.error('Unexpected response structure:', response);
      this.acquisitions = [];
    }
  }, error => {
    console.error('Error fetching acquisition details:', error);
  });
}

isFormModified(): boolean {
  const ownershipModified = this.requestForm.value.ownershipType !== this.acquisitionDetails.ownershipType.id;
  const requestTypeModified = this.requestForm.value.requestType !== this.acquisitionDetails.requestType.id;
  const descriptionModified = this.requestForm.value.description !== this.acquisitionDetails.description;

  const vehicleDetailsFormArray = this.requestForm.get('vehicleDetails') as FormArray;
  const vehicleDetailsModified = vehicleDetailsFormArray.controls.some((control: AbstractControl, index: number) => {
    if (control instanceof FormGroup) {
      const vehicleDetail = control.value;
      const existingVehicleDetail = this.acquisitions[index];
      
      // Check if existingVehicleDetail is undefined
      if (!existingVehicleDetail) {
        console.warn(`No existing vehicle detail found for index ${index}`);
        return true; // Or you could decide to treat this as modified, based on your logic
      }

      console.log("existingg..", existingVehicleDetail);

      return (
        vehicleDetail.vehicleTypeid !== existingVehicleDetail.vehicleType.id ||
        vehicleDetail.numberOfVehicles !== existingVehicleDetail.numberOfVehicles ||
        vehicleDetail.beneficiaryAgencyId !== existingVehicleDetail.beneficiaryAgencyId ||
        vehicleDetail.intendedUsage !== existingVehicleDetail.intendedUsage ||
        vehicleDetail.projectName !== existingVehicleDetail.projectName ||
        vehicleDetail.projectDescription !== existingVehicleDetail.projectDescription ||
        vehicleDetail.projectStartDate !== existingVehicleDetail.projectStartDate ||
        vehicleDetail.projectEndDate !== existingVehicleDetail.projectEndDate
      );
    }
    return false;
  });

  return ownershipModified || requestTypeModified || descriptionModified || vehicleDetailsModified;
}


updateRequestedVehicle(): void {
  if (this.isFormModified()) {
    const acquisitionData = {
      requestType: this.requestForm.value.requestType,
      ownershipType: this.requestForm.value.ownershipType,
      description: this.requestForm.value.description
    };

    this.http.patch(`${environment.otherUrl}/updateAcquisition/${this.acquisitionId}`, acquisitionData).subscribe(
      (response) => {
        console.log('Acquisition updated successfully:', response);

        // this.acquisitions.forEach((vehicle:any) => {
          
          for (let i = 0; i < this.acquisitions.length; i++){
            const vehicleDetail: any = this.acquisitions[i];

            const vehicleId = vehicleDetail.id;
            console.log("Vehicle Detailll: ", JSON.stringify(vehicleDetail));
            console.log("request dataa: ", JSON.stringify(this.requestForm.value.vehicleDetails[i]));
            
            const data = {
              vehicleTypeid:this.requestForm.value.vehicleDetails[i].vehicleTypeid,
              numberOfVehicles:this.requestForm.value.vehicleDetails[i].numberOfVehicles,
              beneficiaryAgencyId:this.requestForm.value.vehicleDetails[i].beneficiaryAgencyId,
              beneficiaryAgency:this.requestForm.value.vehicleDetails[i].beneficiaryAgency,
              intendedUsage:this.requestForm.value.vehicleDetails[i].intendedUsage,
              projectName:this.requestForm.value.projectName,
              projectDescription:this.requestForm.value.projectDescription,
              projectStartDate:this.requestForm.value.projectStartDate,
              projectEndDate:this.requestForm.value.projectEndDate
              // ...vehicleDetail
            };
            console.log("dataaa....: ", JSON.stringify(data));

            this.http.patch(`${environment.otherUrl}/updateRequestedVehicle/${vehicleId}`, data).subscribe(
              (response) => {
                console.log('Vehicle detail updated successfully:', response);
                this.toastr.success('Vehicle detail updated Successfully');
                this.router.navigate([`/vehicle-management/acquisition-details/${this.acquisitionId}`]);  

              },
              (error) => {
                console.error('Error updating vehicle detail:', error);
                this.toastr.error('An error occurred while updating vehicle detail', 'Error');
              }
            );
          };

        const vehicleDetails = this.getVehicleDetails();
        console.log("Theee detaisl...", vehicleDetails);

      (error: any) => {
        console.error('Error updating acquisition:', error);
        this.toastr.error('An error occurred while updating acquisition', 'Error');
      }
    }
    );
  } else {
    this.toastr.info('No modifications have been made.', 'Info');
  }
}
}