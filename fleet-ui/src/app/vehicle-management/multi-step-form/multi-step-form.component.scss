.page{
    background-color:#D9D9D94D;
    width: 83% !important;
    height: 90%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
}
h1{
    font-size: 20px;
    color: #28A4E2;
    font-weight: 600;
    margin-bottom: 5px;
}
h2{
    font-size: 16px;
    color: #28A4E2;
    font-weight: 500;
}
p{
    font-size: 12px;
    color: #999797;
}
label{
    color: #070707ab;
    font-size: 14px;
    padding-bottom: 8px;
}
.header{
    padding: 10px 20px;
}
.card {
    z-index: 0;
    border: none;
    border-radius: 0.5rem 0.5rem 0px 0px;
    position: relative;
}
fieldset {
    background: #ffffff;
    border: 0 none;
    border-radius: 0px 0px 0.5rem 0.5rem;
    padding: 20px ;
    width: 100%;
    // height: 330px;
    position:absolute;
    margin-right: 10px !important;
    transition: left 0.5s ease-in-out;
    overflow-y: scroll;
}
fieldset::-webkit-scrollbar{
    width: 0;
}
input, select, textarea {
    border: 1px solid #eaeaf5;
    font-size: 13px;
    border-radius: 8px;
}
#msform fieldset:not(:first-of-type) {
    display: none;
}

#msform .action-button {
    background: #28A4E2;
    font-weight: 500;
    color: #ffffff;
    border: 0 none;
    border-radius: 8px;
    cursor: pointer;
    padding: 10px 5px;
    width: 15%;
}

#msform .action-button-previous {
    background: #2C3E50;
    font-weight: 500;
    color: #ffffff;
    border: 0 none;
    border-radius: 8px;
    cursor: pointer;
    padding: 10px 5px;
    width: 15%;
}
#progressbar {
    margin-bottom: 30px;
    overflow: hidden;
    color: lightgrey;
}

#progressbar .active {
    color: #000000;
}

#progressbar li {
    list-style-type: none;
    font-size: 12px;
    width: 20%;
    float: left;
    position: relative;
}

#progressbar #vehicleDetails:before {
    font-family: FontAwesome;
    content: "\f02d";
}
#progressbar #costBenefitAnalysis:before {
    font-family: FontAwesome;
    content: "\f02d";
}
#progressbar #numberOfVehicles:before {
    font-family: FontAwesome;
    content: "\f1b9";
}

#progressbar #projectDetails:before {
    font-family: FontAwesome;
    content: "\f07c";
}

#progressbar #supplementFiles:before {
    font-family: FontAwesome;
    content: "\f15b";
}

#progressbar li:before {
    width: 50px;
    height: 50px;
    line-height: 45px;
    display: block;
    font-size: 18px;
    color: #ffffff;
    background: lightgray;
    border-radius: 50%;
    margin: 0 auto 10px auto;
    padding: 2px;
}

#progressbar li:after {
    content: '';
    width: 100%;
    height: 2px;
    background: lightgray;
    position: absolute;
    left: 0;
    top: 25px;
    z-index: -1;
}
#progressbar li.active:before, #progressbar li.active:after {
    background: #28A4E2;
}
.remove-btn{
    border: 1px solid rgb(170, 30, 30);
    color: rgb(170, 30, 30);
    font-size: 12px;
    cursor: pointer;
    width: 150px;
}
.add-btn{
    border: 1px solid rgb(57, 141, 57);
    color: rgb(57, 141, 57);
    font-size: 13px;
    font-weight: 500;
}
.fit-image{
    width: 40%;
    object-fit: cover;
}

.small-group{
    // background-color: rgb(241, 241, 241);
    width: 100%;
    padding: 10px;
    border-radius: 5px;
}

.small-group .d-flex label{
    width: 250px;
}
.ownershipRow{
    width: 60%;
}
.main-label{
    font-weight: bold;
}