* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Gill Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;
    font-size: 18px;
    color: #000;
    
  }
  
  
  .letter-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f5f5f5;
    padding: 10px; /* Reduced overall padding */
  }
  
  .letter-container {
    max-width: 850px; /* Keep this to ensure it fits on letter size */
    background-color: #fff;
    border: 2px solid #ddd;
    padding: 12px; /* Further reduced padding */
    margin-bottom: 12px; /* Ensure this is minimal */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  
  .letter-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px; /* Reduced margin */
  }
  
  .header-left {
    flex: 1;
    
  }
  
  .header-left img {
    width: 80px; /* Reduced logo size */
    margin-bottom: 5px; /* Reduced margin */
  }
  
  .header-right {
    text-align: right;
  }
  
  .bold-text {
    font-weight: bold;
    color: #000;
    font-family: 'Times New Roman', Times, serif;
    
  }
  
  .letter-address {
    margin-bottom: 10px; /* Reduced margin */
  }
  
  .letter-body {
    margin-bottom: 10px; /* Reduced margin */
    line-height: 1.4; /* Adjusted line height */
    font-size: 13px;  /* Reduced font size for compactness */
  }
  
  .letter-body p {
    margin-bottom: 10px; /* Reduced spacing between paragraphs */
  }
  ul{
    margin-left: 15px;
  }
  
  .letter-signature {
    display: flex;
    align-items: center;
    margin-bottom: 10px; /* Reduced margin */
  }
  
  .letter-signature img {
    width: 100px; /* Reduced signature size */
    margin-right: 5px; /* Reduced margin */
  }
  
  .letter-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 10px;
  }
  
  .qr-code-container {
    margin: 10px; /* This will push the QR code to the right */
  }
  
  .pdf-download-btn {
    background-color: #28a745;
    color: white;
    padding: 8px 15px; /* Reduced padding */
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px; /* Reduced font size */
    margin-top: 10px; /* Ensure the button is not stretching full height */
  }
  
  .pdf-download-btn:hover {
    background-color: #218838;
  }
  