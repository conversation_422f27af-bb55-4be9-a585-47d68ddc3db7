import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { faBorderNone } from '@fortawesome/free-solid-svg-icons';
import html2pdf from 'html2pdf.js';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-auction-letter',
  templateUrl: './auction-letter.component.html',
  styleUrl: './auction-letter.component.scss'
})
export class AuctionLetterComponent {
  @ViewChild('container')
  containerRef!: ElementRef;
  acquisitions: any[] = [];
  acquisitionDetails: any = {};
  userDetails: any = {};
  acquisitionData: any = {};
  auction :any= {}
 
  constructor(
    private http: HttpClient,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
    }

    this.route.paramMap.subscribe(params => {
      const auctionId = params.get('auctionId');
      if (auctionId) { 
        this.fetchAuctionDetails(auctionId);
      }
    });
  }

  fetchUserDetails(userId: string) {
    const userUrl = `${environment.baseUrl}/auth/getUserByUser?userId=${userId}`;
    this.http.get<any>(userUrl).subscribe(
      (user) => {
        this.userDetails = user;
      },
      (error) => {
        console.error('Error fetching user details:', error);
      }
    );
  }

  fetchAcquisitionDetails(acquisitionId: string) {
    const url = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;
    this.http.get<any>(url).subscribe(response => {
      console.log(response);
      if (response && response.vehicles && Array.isArray(response.vehicles)) {
        this.acquisitions = response.vehicles;
        this.acquisitionDetails = response.acquisition;

        const userId = this.acquisitionDetails?.userId;
        if (userId) {
          this.fetchUserDetails(userId);
        }
      } else {
        console.error('Unexpected response structure:', response);
      }
    }, error => {
      console.error('Error fetching acquisition details:', error);
    });
  }
  fetchAuctionDetails(auctionId: string) {
    const url = `${environment.baseUrl}/disposal/auctionReport/${auctionId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.auction = response; // Store the auction data
        console.log("Auction Data:", response);
      },
      (error) => {
        console.error('Error fetching auction details:', error);
      }
    );
  }

 downloadPdf() {
  const element = document.getElementById('letter-container'); // Use ViewChild reference
  const opt = {
    filename: 'authorization_letter.pdf',
    html2canvas: { scale: 2 }, // Higher scale for better quality
    jsPDF: {
      format: 'letter', // Use 'letter' format
      orientation: 'portrait',
      unit: 'in',
      margin: [0.5, 0.5, 0.5, 0.5] // Set margins (top, left, bottom, right)
      
    },
    pagebreak: { mode: 'avoid-all', avoid: ['.pagebreak'] } // Avoid page breaks
  };

  html2pdf().set(opt).from(element).save();
}


  getTotalNumberOfVehicles(): string {
    let totalVehicles = 0;
    this.acquisitions.forEach(vehicle => {
      totalVehicles += vehicle.numberOfVehicles;
    });
    return totalVehicles.toString();
  }

  getProjectDetails(): string {
    let projectDetails = '';
    if (this.acquisitions.length > 0) {
      const firstAcquisition = this.acquisitions[0];
      if (firstAcquisition.projectName) {
        projectDetails = `, ${firstAcquisition.projectName} (Project ID No ${firstAcquisition.projectId || 'N/A'})`;
      }
    }
    return projectDetails;
  }
  generateQrLink(): string {
    const baseLink = `${environment.domainName}/vehicle-management/${this.acquisitionDetails?.id || 'default-id'}/authorization-letter`;
    return baseLink;
  }
  

}
