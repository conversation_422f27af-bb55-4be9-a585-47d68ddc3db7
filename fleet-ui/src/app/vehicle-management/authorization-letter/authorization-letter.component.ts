import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { faBorderNone } from '@fortawesome/free-solid-svg-icons';
import html2pdf from 'html2pdf.js';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-authorization-letter',
  templateUrl: './authorization-letter.component.html',
  styleUrls: ['./authorization-letter.component.scss']
})
export class AuthorizationLetterComponent {
  @ViewChild('container') 
  containerRef!: ElementRef;
  acquisitions: any[] = [];
  acq:any;
  acquisitionDetails: any = {};
  userDetails: any = {};
  acquisitionData: any = {};

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
    }

    this.route.paramMap.subscribe(params => {
      const acquisitionId = params.get('acquisitionId');
      if (acquisitionId) {
        this.fetchAcquisitionDetails(acquisitionId);
      }
    });
  }

  fetchUserDetails(userId: string) {
    const userUrl = `${environment.baseUrl}/auth/getUserByUser?userId=${userId}`;
    this.http.get<any>(userUrl).subscribe(
      (user) => {
        this.userDetails = user;
      },
      (error) => {
        console.error('Error fetching user details:', error);
      }
    );
  } 

  fetchAcquisitionDetails(acquisitionId: string) {
    const url = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;
    this.http.get<any>(url).subscribe(response => {
      console.log('responses',response);
      if (response && response.vehicles && Array.isArray(response.vehicles)) {
        this.acq= response.acquisition;
        this.acquisitions = response.vehicles;
        console.log('this.acquisitions',this.acquisitions);

        this.acquisitionDetails = response.acquisition;

        const userId = this.acquisitionDetails?.userId;
        if (userId) {
          this.fetchUserDetails(userId);
        }
      } else {
        console.error('Unexpected response structure:', response);
      }
    }, error => {
      console.error('Error fetching acquisition details:', error);
    });
  }

 downloadPdf() {
  const element = document.getElementById('letter-container'); // Use ViewChild reference
  const opt = {
    filename: 'authorization_letter.pdf',
    html2canvas: { scale: 2 }, // Higher scale for better quality
    jsPDF: {
      format: 'letter', // Use 'letter' format
      orientation: 'portrait',
      unit: 'in',
      margin: [0.5, 0.5, 0.5, 0.5] // Set margins (top, left, bottom, right)
      
    },
    pagebreak: { mode: 'avoid-all', avoid: ['.pagebreak'] } // Avoid page breaks
  };

  html2pdf().set(opt).from(element).save();
}


  getTotalNumberOfVehicles(): string {
    let totalVehicles = 0;
    this.acquisitions.forEach(vehicle => {
      totalVehicles += vehicle.numberOfVehicles;
    });
    console.log('totalvehicles',totalVehicles);
    return totalVehicles.toString();
  }

  getProjectDetails(): string {
    let projectDetails = '';
    if (this.acquisitions.length > 0) {

      const firstAcquisition = this.acquisitions[0];
      if (firstAcquisition.projectName) {
        projectDetails = `For Project '${firstAcquisition.projectName}'`;
      }
    }
    return projectDetails;
  }
  generateQrLink(): string {
    const baseLink = `${environment.domainName}/vehicle-management/acquisition-details/${this.acquisitionDetails?.id || 'default-id'}/authorization-letter`;
    return baseLink;
  }
  

}
