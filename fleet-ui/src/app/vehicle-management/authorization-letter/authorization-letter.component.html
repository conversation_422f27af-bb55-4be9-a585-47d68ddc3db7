<div class="letter-page">
  <div class="letter-container" id="letter-container">
    <div class="letter-header">
      <div class="header-left">
        <p class="bold-text">REPUBLIC OF RWANDA</p>
        <img src="../../../assets/img/logo.png" alt="Ministry Logo">
        <p class="bold-text">MINISTRY OF INFRASTRUCTURE</p>
        <p class="bold-text">E-mail: info&#64;mininfra.gov.rw</p>
        <p class="bold-text">P.O. Box 24 KIGALI</p>
      </div>
      <div class="header-right">
        <p class="bold-text">Kigali, {{ acquisitionDetails?.submittedDate }}</p>
      </div>
    </div> 
    <div class="letter-address">
      <p class="bold-text">{{ acquisitionDetails?.institution?.toUpperCase() || 'N/A' }}</p>
    </div>
    <div class="letter-body">
      <ng-container *ngIf="acq.requestType.name == 'Purchase'">
        <p class="text-center"><strong>RE: AUTHORIZATION TO PURCHASE {{ getTotalNumberOfVehicles() }} vehicle(s) {{ getProjectDetails() }}</strong></p>
        <p>Dear {{ userDetails?.role?.code || 'Sir/Madam' }},</p>
        <p>Reference to the acquisition request made through the Fleet MIS App, requesting authorization to purchase {{ getTotalNumberOfVehicles() }} vehicles:</p>
        <ul>
          <li class="fw-bold" *ngFor="let vehicle of acquisitions">
            {{ vehicle.vehicleType?.name || 'No Vehicle Type' }}
          </li>
        </ul>
        
        <p *ngIf="acquisitionDetails?.ownershipType?.name == 'Project Vehicles'">
          to facilitate the implementation of the {{ acquisitionDetails?.vehicle?.projectName || 'project' }},
        </p>
        
        <p *ngIf="acquisitionDetails?.ownershipType?.name == 'Government Vehicles'">
          to facilitate the implementation of the aforesaid duties and responsibilities,
        </p>
        
        <ul>
          <li class="fw-bold" *ngFor="let item of acquisitions">
            {{ item.intendedUsage || 'No Intended Usage' }}
          </li>
        </ul>
        
        <p *ngIf="acquisitionDetails?.ownershipType?.name == 'Project Vehicles'">Reference is also made to the Government Fleet policy 2014, which provides in Article 4.5 that at the end of the project, ownership of all vehicles, including those owned by donor agencies, will be transferred to the Government for further management;</p>
        <p>We advise you to respect the laws governing public procurement during the acquisition process. <span *ngIf="acquisitionDetails?.ownershipType?.name == 'Project Vehicles'">Moreover, you are requested to return the vehicles after the completion of the project as per provisions of the aforesaid Government Fleet Policy.</span></p>
        <p><strong>Sincerely,</strong></p>
        <p>Minister of State</p>
        <p class="fw-bold">MININFRA</p>
      </ng-container>
     
      <ng-container *ngIf="acq.requestType.name == 'Allocation'">
        <p class="text-center">
          <strong>RE: ALLOCATION OF {{ getTotalNumberOfVehicles() }} VEHICLE(S) {{ getProjectDetails() }}</strong>
        </p>
        <p>Dear {{ userDetails?.role?.code || 'Sir/Madam' }},</p>
        <p>
          Reference is made to the acquisition request made through the Fleet MIS App, requesting for allocation of 
          {{ getTotalNumberOfVehicles() }} vehicle(s):
        </p>
        <p>
          The Ministry of Infrastructure would like to inform you that your request is granted and allocates to your good office the 
          available {{ getTotalNumberOfVehicles() }} vehicle(s) to support in your daily activities.
        </p>
        <p>
          We advise you to liaise with Rwanda Revenue Authority (RRA) to settle any outstanding taxes associated with the aforementioned 
          vehicle(s) before proceeding further.
        </p>
        <p><strong>Sincerely,</strong></p>
        <p>Minister of State</p>
        <p class="fw-bold">MININFRA</p>
      </ng-container>
      
      <ng-container *ngIf="acq.requestType.name == 'Donation'">
        <p class="text-center">
          <strong>RE: APPROVAL OF DONATION OF {{ getTotalNumberOfVehicles() }} VEHICLE(S) {{ getProjectDetails() }}</strong>
        </p>
        <p>Dear {{ userDetails?.role?.code || 'Sir/Madam' }},</p>
        <p>Reference is made to the request made through the Fleet MIS App requesting approval for the donation of {{ getTotalNumberOfVehicles() }} vehicle(s):</p>
        <ul>
          <li class="fw-bold" *ngFor="let vehicle of acquisitions">
            {{ vehicle.vehicleType?.name || 'No Vehicle Type' }} with chassis {{ vehicle.chassisNumber || 'No Chassis Number' }}
          </li>
        </ul>
        <p>
          The Ministry of Infrastructure is pleased to inform you that your request for the approval of the donation of 
          {{ getTotalNumberOfVehicles() }} vehicle(s) has been granted. This donation is expected to support 
          <span *ngIf="acquisitionDetails?.ownershipType?.name == 'Project Vehicles'">
            the implementation of the {{ acquisitionDetails?.vehicle?.projectName || 'project' }}.
          </span>
          <span *ngIf="acquisitionDetails?.ownershipType?.name == 'Government Vehicles'">
            your daily activities in line with your duties and responsibilities.
          </span>
        </p>
        <p>
          We advise you to proceed to the next step for vehicle registration. Kindly ensure compliance with relevant laws and regulations, including those governing public procurement.
        </p>
        <p><strong>Sincerely,</strong></p>
        <p>Minister of State</p>
        <p class="fw-bold">MININFRA</p>
      </ng-container>
      

      <div class="letter-footer">
        <div class="qr-code-container">
          <qrcode [qrdata]="generateQrLink()" [width]="180" [errorCorrectionLevel]="'M'"></qrcode>
        </div>
      </div>
    </div>
  </div>
  
  <button (click)="downloadPdf()" class="pdf-download-btn">Download PDF</button>
</div>
