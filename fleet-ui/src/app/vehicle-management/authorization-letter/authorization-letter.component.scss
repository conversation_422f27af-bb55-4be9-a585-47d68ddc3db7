* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Times New Roman', Times, serif;
  font-size: 14px;
  color: #000;
}

.letter-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f8f8f8;
  padding: 20px;
}

.letter-container {
  max-width: 800px;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 30px 40px;
  margin-bottom: 20px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.letter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  flex: 1;
}

.header-left img {
  width: 70px;
  margin-bottom: 10px;
}

.header-right {
  text-align: right;
  font-size: 14px;
}

.bold-text {
  font-weight: bold;
  color: #000;
  line-height: 1.6;
}

.letter-address {
  margin-bottom: 20px;
  font-size: 14px;
}

.letter-body {
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 14px;
}

.letter-body p {
  margin-bottom: 12px;
  font-size: 16px;
}


.text-center {
  text-align: center;
  margin-bottom: 20px;
  font-size: 16px;
}

ul {
  margin-left: 20px;
  margin-bottom: 10px;
  list-style-type: disc;
}

.letter-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 10px;
}

.qr-code-container {
  margin-top: 10px;
}

.pdf-download-btn {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
}

.pdf-download-btn:hover {
  background-color: #0056b3;
}

@media (max-width: 634px) {
  .letter-container {
    max-width: 95%;
    padding: 20px; 
    margin-bottom: 15px; 
  }

  .letter-header {
    flex-direction: column; 
    text-align: center; 
    margin-bottom: 15px;
  }

  .header-left,
  .header-right {
    width: 100%; 
    margin-bottom: 10px; 
  }

  .header-left img {
    width: 60px; 
    margin-bottom: 5px;
  }

  .bold-text {
    font-size: 12px; 
  }

  .letter-address {
    font-size: 12px;
    margin-bottom: 15px;
  }

  .letter-body {
    font-size: 12px; 
    line-height: 1.4; 
  }

  .letter-body p {
    margin-bottom: 10px; 
  }

  .letter-footer {
    flex-direction: column; 
    align-items: center; 
  }

  .qr-code-container {
    width: 80px; 
  }

  .pdf-download-btn {
    width: 90%; 
    font-size: 14px; 
    padding: 8px 16px; 
    margin: 15px auto; 
  }
}
