import { Component, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { LocationService } from '../../location.service';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-device-location',
  templateUrl: './device-location.component.html',
  styleUrls: ['./device-location.component.scss']
})
export class DeviceLocationComponent implements OnInit {
  latitude: number | null = null;
  longitude: number | null = null;
  locationName: string | null = null;
  error: string | null = null;
  mapUrl: SafeResourceUrl | null = null;
  lastSeen: string | null = null;

  constructor(private sanitizer: DomSanitizer, private locationService: LocationService) {}

  ngOnInit(): void {
    this.getDeviceLocation();
  }

  getDeviceLocation(): void {
    this.locationService.getLocation().subscribe(
      (data) => {
        if (data && data.lat && data.lon) {
          this.latitude = data.lat;
          this.longitude = data.lon;
          this.updateMapUrl();
          this.updateLastSeenTime();
          this.getLocationName();
        } else {
          this.error = 'Location data is not available';
        }
      },
      (err) => {
        this.error = `ERROR: ${err.message}`;
      }
    );
  }

  updateMapUrl(): void {
    if (this.latitude && this.longitude) {
      const url = `https://www.google.com/maps?q=${this.latitude},${this.longitude}&z=15&output=embed`;
      this.mapUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
    }
  }

  getLocationName(): void {
    if (this.latitude && this.longitude) {
      this.locationService.reverseGeocode(this.latitude, this.longitude).subscribe(
        (location) => {
          this.locationName = location;
        },
        (err) => {
          this.error = `ERROR: ${err.message}`;
        }
      );
    }
  }

  updateLastSeenTime(): void {
    this.lastSeen = formatDate(new Date(), 'MMM d, y, h:mm:ss a', 'en');
  }
}
