import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component } from '@angular/core';
import { faSearch, faCaretLeft, faCaretRight, faCar, faCheck, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { User } from '../../models/user.interface';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { Location } from '@angular/common';

@Component({
  selector: 'app-all-vehicles',
  templateUrl: './all-vehicles.component.html',
  styleUrls: ['./all-vehicles.component.scss']
})
export class AllVehiclesComponent {
  allFormsSubmitted = false;
  searchIcon = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  carIcon = faCar;
  checkIcon = faCheck;
  searchText: string = '';
  faSearch = faSearch;
  
  // Data arrays - proper data flow
  acquisitions: any[] = []; // Raw data from API
  roleFilteredAcquisitions: any[] = []; // After role filtering
  filteredAcquisitions: any[] = []; // After role and status filtering
  finalDataSource: any[] = []; // Final data source (with search if applicable)
  displayedAcquisitions: any[] = []; // Data currently displayed based on pagination
  
  // Filter and pagination
  currentFilter: string = 'all';
  pageSize = 20;
  currentPage = 1;
  totalPages: number = 0;

  institutionId: string = '';
  userRole: string = '';
  backwardIcon = faArrowLeft;

  constructor(private http: HttpClient, private router: Router, private cd: ChangeDetectorRef, private location: Location) { }

  ngOnInit() {
    console.log('Component initialized');
    this.getUserDetails();
    this.fetchAcquisitions();
    this.isLogistics();
  }

  goBack() {
    this.location.back();
  }

  isLogistics(): boolean {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }
  fetchAcquisitions() {
    if (!this.institutionId || !this.userRole) {
      console.error('Institution ID or User Role not found');
      return;
    }
  
    let url: string;
    switch (this.userRole) {
      case 'Institutional logistics':
      case 'Institutional CBM':
        url = `${environment.otherUrl}/AllAcquisitionsByInstitution?acquisitionId=${this.institutionId}`;
        break;
  
      case 'Fleet Mgt Senior Engineer':
      case 'DG Transport':
      case 'Permanent Secretary':
      case 'Minister':
        url = `${environment.otherUrl}/AllAcquisitions`;
        break;
  
      default:
        console.error('Unrecognized user role');
        return;
    }
  
    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log('Raw acquisitions from API:', response.length);
        
        // Apply different filtering based on user role
        if (this.userRole === 'Institutional logistics' || this.userRole === 'Institutional CBM') {
          // For logistics and CBM, show acquisitions where vehicle registration is incomplete (all request types)
          this.acquisitions = response.filter(
            acquisition => acquisition.isAllVehicleregrequestSubmitted === false
          );
        } else {
        //   // For other roles, exclude incomplete Purchase requests (but show all other types including complete Purchases)
          this.acquisitions = response.filter(
            acquisition => !(acquisition.requestType.name === 'Allocation' && acquisition.isAllVehicleregrequestSubmitted === false)
          );
        }
        
        console.log('Filtered acquisitions after API filter:', this.acquisitions.length);
        this.applyCurrentFilter();
      },
      (error) => {
        console.error('Error fetching data:', error);
      }
    );
  }
  
  filterAcquisitionsByRole() {
    console.log('Applying role-based filtering');
    this.roleFilteredAcquisitions = [...this.acquisitions];
  
    switch (this.userRole) {
      case 'Institutional logistics':
        this.roleFilteredAcquisitions = this.roleFilteredAcquisitions.filter(
          acquisition =>
            acquisition.institutionId === this.institutionId &&
            acquisition.approvalLevel?.name !== 'RRA'
        );
        break;
  
      case 'Institutional CBM':
        this.roleFilteredAcquisitions = this.roleFilteredAcquisitions.filter(
          acquisition =>
            acquisition.institutionId === this.institutionId &&
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'RRA'
        );
        break;
  
      case 'Fleet Mgt Senior Engineer':
        this.roleFilteredAcquisitions = this.roleFilteredAcquisitions.filter(
          acquisition =>
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM' &&
            acquisition.approvalLevel?.name !== 'RRA'
        );
        break;
  
      case 'DG Transport':
        this.roleFilteredAcquisitions = this.roleFilteredAcquisitions.filter(
          acquisition =>
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM' &&
            acquisition.approvalLevel?.name !== 'RRA'
        );
        break;
  
      case 'Permanent Secretary':
        this.roleFilteredAcquisitions = this.roleFilteredAcquisitions.filter(
          acquisition =>
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM' &&
            acquisition.approvalLevel?.name !== 'RRA'
        );
        break;
  
      case 'Minister':
        this.roleFilteredAcquisitions = this.roleFilteredAcquisitions.filter(
          acquisition =>
            acquisition.approvalLevel?.name !== 'Institutional logistics' &&
            acquisition.approvalLevel?.name !== 'Institutional CBM'
        );
        break;
  
      default:
        console.error('Unrecognized user role:', this.userRole);
        break;
    }
    console.log('After role filtering:', this.roleFilteredAcquisitions.length);
  }
  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id;
      this.userRole = parsedObj.data.user.role.name;
      console.log('User Role:', this.userRole);
      console.log('Institution ID:', this.institutionId);
      console.log()
    }
  }

  applyCurrentFilter() {
    console.log('=== APPLYING FILTER ===');
    console.log('Current filter:', this.currentFilter);
    console.log('User role:', this.userRole);
    console.log('Institution ID:', this.institutionId);
    console.log('Total acquisitions from API:', this.acquisitions.length);
    
    // First apply role-based filtering
    this.filterAcquisitionsByRole();
    
    // Then apply status-based filtering
    this.filteredAcquisitions = [...this.roleFilteredAcquisitions];
    console.log('After role filtering:', this.filteredAcquisitions.length);
    
    switch (this.currentFilter) {
      case 'all':
        // No additional filtering for 'all'
        break;
      case 'in-progress':
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          (item) => item.statusType?.name === 'PENDING' || 
                   item.statusType?.name === 'PROGRESS' || 
                   item.statusType?.name === 'RFAC'
        );
        break;
      case 'completed':
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          (item) => item.statusType?.name === 'APPROVED' || 
                   item.statusType?.name === 'DENIED'
        );
        break;
      case 'pending':
        this.filteredAcquisitions = this.filteredAcquisitions.filter(
          (item) => (item.statusType?.name === 'PENDING' && item.approvalLevel?.name === this.userRole) ||
                   (item.statusType?.name === 'PROGRESS' && item.approvalLevel?.name === this.userRole)
        );
        break;
      default:
        console.error('Invalid filter type:', this.currentFilter);
    }

    console.log('After status filtering for', this.currentFilter + ':', this.filteredAcquisitions.length);
    
    // Debug: Show badge counts
    console.log('Badge counts:');
    console.log('- All:', this.getAllCount());
    console.log('- In Progress:', this.getInProgressCount());
    console.log('- Pending:', this.getPendingCount());
    console.log('- Completed:', this.getCompletedCount());
    
    // Reset pagination and apply search if exists
    this.currentPage = 1;
    this.applySearchAndUpdatePagination();
  }

  setFilter(filterType: string) {
    console.log('Setting filter to:', filterType);
    this.currentFilter = filterType;
    this.applyCurrentFilter();
  }

  searchVehicles() {
    console.log('Searching with text:', this.searchText);
    this.currentPage = 1; // Reset to first page when searching
    this.applySearchAndUpdatePagination();
  }

  applySearchAndUpdatePagination() {
    const searchTextLower = this.searchText.toLowerCase().trim();

    if (!searchTextLower) {
      // No search - use filtered acquisitions
      this.finalDataSource = [...this.filteredAcquisitions];
    } else {
      // Apply search to filtered acquisitions
      this.finalDataSource = this.filteredAcquisitions.filter(item => {
        return (
          (item.description || '').toLowerCase().includes(searchTextLower) ||
          (item.requestType?.name || '').toLowerCase().includes(searchTextLower) ||
          (item.institution || '').toLowerCase().includes(searchTextLower) ||
          (item.statusType?.name || '').toLowerCase().includes(searchTextLower) ||
          (item.approvalLevel?.name || '').toLowerCase().includes(searchTextLower) ||
          ((item.isVehicleActive ? 'active' : 'inactive').includes(searchTextLower))
        );
      });
    }

    console.log('Final data source length:', this.finalDataSource.length);
    this.updateDisplayedAcquisitions();
  }

  updateDisplayedAcquisitions() {
    console.log('=== UPDATING DISPLAYED ACQUISITIONS ===');
    console.log('Current page:', this.currentPage);
    console.log('Page size:', this.pageSize);
    console.log('Final data source length:', this.finalDataSource.length);
    console.log('Search text:', this.searchText);

    // Calculate pagination
    this.totalPages = Math.ceil(this.finalDataSource.length / this.pageSize);
    console.log('Calculated total pages:', this.totalPages);
    
    // Ensure current page is valid
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
      console.log('Adjusted current page to:', this.currentPage);
    }
    if (this.currentPage < 1) {
      this.currentPage = 1;
      console.log('Adjusted current page to:', this.currentPage);
    }

    // Calculate slice indices
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.finalDataSource.length);

    console.log('Slice indices - Start:', startIndex, 'End:', endIndex);

    this.displayedAcquisitions = this.finalDataSource.slice(startIndex, endIndex);
    
    console.log('Displayed acquisitions length:', this.displayedAcquisitions.length);
    console.log('Entry range:', this.getFirstEntryIndex(), '-', this.getLastEntryIndex(), 'of', this.finalDataSource.length);
    
    // Verify the data matches expectations
    if (this.displayedAcquisitions.length > this.pageSize) {
      console.error('ERROR: Displayed acquisitions exceed page size!');
      console.error('Expected max:', this.pageSize, 'Actual:', this.displayedAcquisitions.length);
    }
    
    this.cd.markForCheck();
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedAcquisitions();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedAcquisitions();
    }
  }

  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedAcquisitions();
    }
  }

  getPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  getFirstEntryIndex(): number {
    if (this.finalDataSource.length === 0) return 0;
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.finalDataSource.length);
  }

  // Count methods for badges - these should return the correct counts for each filter
  getAllCount(): number {
    return this.getCountForFilter('all');
  }

  getInProgressCount(): number {
    return this.getCountForFilter('in-progress');
  }

  getPendingCount(): number {
    return this.getCountForFilter('pending');
  }

  getCompletedCount(): number {
    return this.getCountForFilter('completed');
  }

  private getCountForFilter(filterType: string): number {
    // Apply role filtering first
    let roleFiltered = [...this.acquisitions];
    roleFiltered = this.applyRoleFilteringToArray(roleFiltered);
    
    // Then apply status filtering based on the filter type
    let statusFiltered = [...roleFiltered];
    
    switch (filterType) {
      case 'all':
        // No additional filtering for 'all'
        break;
      case 'in-progress':
        statusFiltered = statusFiltered.filter(
          (item) => item.statusType?.name === 'PENDING' || 
                   item.statusType?.name === 'PROGRESS' || 
                   item.statusType?.name === 'RFAC'
        );
        break;
      case 'completed':
        statusFiltered = statusFiltered.filter(
          (item) => item.statusType?.name === 'APPROVED' || 
                   item.statusType?.name === 'DENIED'
        );
        break;
      case 'pending':
        statusFiltered = statusFiltered.filter(
          (item) => (item.statusType?.name === 'PENDING' && item.approvalLevel?.name === this.userRole) ||
                   (item.statusType?.name === 'PROGRESS' && item.approvalLevel?.name === this.userRole)
        );
        break;
      default:
        console.error('Invalid filter type:', filterType);
    }
    
    return statusFiltered.length;
  }

  private applyRoleFilteringToArray(data: any[]): any[] {
    // Helper method to apply role filtering without affecting component state
    switch (this.userRole) {
      case 'Institutional logistics':
        return data.filter(acquisition =>
          acquisition.institutionId === this.institutionId &&
          acquisition.approvalLevel?.name !== 'RRA'
        );
      case 'Institutional CBM':
        return data.filter(acquisition =>
          acquisition.institutionId === this.institutionId &&
          acquisition.approvalLevel?.name !== 'Institutional logistics' &&
          acquisition.approvalLevel?.name !== 'RRA'
        );
      case 'Fleet Mgt Senior Engineer':
        return data.filter(acquisition =>
          acquisition.approvalLevel?.name !== 'Institutional logistics' &&
          acquisition.approvalLevel?.name !== 'Institutional CBM' &&
          acquisition.approvalLevel?.name !== 'RRA'
        );
      case 'DG Transport':
        return data.filter(acquisition =>
          acquisition.approvalLevel?.name !== 'Institutional logistics' &&
          acquisition.approvalLevel?.name !== 'Institutional CBM' &&
          acquisition.approvalLevel?.name !== 'RRA'
        );
      case 'Permanent Secretary':
        return data.filter(acquisition =>
          acquisition.approvalLevel?.name !== 'Institutional logistics' &&
          acquisition.approvalLevel?.name !== 'Institutional CBM' &&
          acquisition.approvalLevel?.name !== 'RRA'
        );
      case 'Minister':
        return data.filter(acquisition =>
          acquisition.approvalLevel?.name !== 'Institutional logistics' &&
          acquisition.approvalLevel?.name !== 'Institutional CBM'
        );
      default:
        return data;
    }
  }

  sortable(event: any) {
    const selectedField = event.target.value;

    if (!selectedField) {
      this.finalDataSource.sort((a, b) => a.institution.localeCompare(b.institution));
    } else {
      this.finalDataSource.sort((a, b) => {
        const fieldA = a[selectedField];
        const fieldB = b[selectedField];
        if (typeof fieldA === 'string' && typeof fieldB === 'string') {
          return fieldA.localeCompare(fieldB);
        }
        return 0;
      });
    }

    this.updateDisplayedAcquisitions();
  }

  viewAcquisition(acquisitionId: string): void {
    console.log('Acquisition ID:', acquisitionId);
    this.router.navigateByUrl(`/vehicle-management/acquisition-details/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  editAcquisition(acquisitionId: string): void {
    console.log("AcquisitionId: ", acquisitionId);
    this.router.navigateByUrl(`/vehicle-management/edit-requested-vehicle/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  registerVehicle(acquisitionId: string): void {
    console.log("Acquisition Id: ", acquisitionId);
    this.router.navigateByUrl(`/vehicle-management/register-vehicle/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation Successful');
      } else {
        console.error('Navigation Failed');
      }
    }).catch(error => {
      console.error('Error navigating: ', error);
    });
  }

  navigateToRequestVehicle(): void {
    this.router.navigateByUrl('vehicle-management/request-vehicle');
  }

  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-success';
      case 'pending':
        return 'bg-secondary';
      case 'denied':
        return 'bg-danger';
      case 'rfac':
        return 'bg-info';
      case 'progress':
        return 'bg-warning';
      default:
        return '';
    }
  }

  getProgressWidth(status: string): string {
    switch (status.toLowerCase()) {
      case 'approved':
        return '100%';
      case 'pending':
        return '70%';
      case 'denied':
        return '100%';
      case 'rfac':
        return '60%';
      case 'progress':
        return '80%';
      default:
        return '';
    }
  }
}