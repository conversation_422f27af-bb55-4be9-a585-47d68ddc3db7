<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicles-container">
      <div class="header d-flex flex-row justify-content-between">
        <button class="btn go-back-btn" (click)="goBack()">
          <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
        </button>

          <h2 class="table-title">List Of Acquisitions Requests</h2>

          <ng-container *ngIf="isLogistics()">
            <button class="btn request-btn d-flex flex-row justify-content-center align-items-center pe-4" (click) = "navigateToRequestVehicle()">
                <fa-icon [icon]="carIcon" class="pe-2"></fa-icon>
                Request Vehicle
            </button>
          </ng-container>
      </div>
      <div class="card">
          <div class="table-header d-flex flex-row justify-content-between">

   <div class="d-flex flex-row justify-content-start align-items-center statuses">
    <button class="btn btn-outline position-relative" [class.active]="currentFilter === 'all'" (click)="setFilter('all')">
      All 
      <span class="badge badge-circle">{{ getAllCount() }}</span>
    </button>
    <button class="btn btn-outline position-relative" [class.active]="currentFilter === 'in-progress'" (click)="setFilter('in-progress')">
      In Progress 
      <span class="badge badge-circle">{{ getInProgressCount() }}</span>
    </button>
    <button *ngIf="userRole != 'Institutional logistics' && userRole != 'Institutional CBM'"
    class="btn btn-danger text-white position-relative pending-btn"
    [class.active]="currentFilter === 'pending'"
    (click)="setFilter('pending')">
      Pending Your Approval
      <span class="badge badge-circle">{{ getPendingCount() }}</span>
    </button>
    <button class="btn btn-outline position-relative" [class.active]="currentFilter === 'completed'" (click)="setFilter('completed')">
      Completed 
      <span class="badge badge-circle">{{ getCompletedCount() }}</span>
    </button>
  </div>
              <!-- <div class="sorting-group d-flex flex-row p-2">
                  <label for="sort-field" class="text-muted p-1">Sort by:</label>
                  <select id="sort-field" class="select" (change)="sortable($event)">
                      <option value="">Select Field</option>
                      <option value="institutio">Institution</option>
                      <option value="requestType">Request Type</option>
                      <option value="ownershipType">Ownership Type</option>
                      <option value="status">Status</option>
                    </select>
              </div> -->
              <div class="search-group d-flex mt-2">
                <fa-icon [icon]="searchIcon" class="icon"></fa-icon>
                <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchVehicles()">
            </div>
            </div>

          <div class="table-only p-3">
              <table class="table table-stripped">
                  <thead>
                      <tr>
                          <!-- <th>Request Description</th> -->
                          <th sortable = "institution">Institution</th>
                          <th sortable="requestType">Type of Request</th>
                          <th sortable ="ownershipType">Type of ownership</th>
                          <th sortable = "status">Status</th>
                          <th sortable="approvalLevel"> Current Level</th>
                          <th>Action</th>
                      </tr>
                  </thead>
                  <tbody>
                    <tr *ngIf="displayedAcquisitions.length === 0">
                      <td colspan="6" class="no-vehicles-message text-center">
                        {{ searchText ? 'No results found for your search.' : 'No Acquisition Requests!!!' }}
                      </td>
                    </tr>
                    <tr *ngFor="let item of displayedAcquisitions; let i = index">
                      <!-- <td>{{ item.description }}</td> -->
                      <td>{{ item.institution }}</td>
                      <td>{{ item.requestType.name }}</td>
                      <td>{{ item.ownershipType.name }}</td>
                      <td>
                        <div class="progress">
                            <div class="progress-bar"
                                 [ngClass]="getStatusButtonClass(item.statusType.name)"
                                 role="progressbar"
                                 [style.width]="getProgressWidth(item.statusType.name)"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                {{ item.statusType.name }}
                            </div>
                        </div>
                    </td>

                      <td>{{item.approvalLevel.name}}</td>
                      <td class="d-flex flex-row">
                        <button class="btn btn-outline-primary view-btn me-2" (click)="viewAcquisition(item.id)">View</button>
                        <button *ngIf="item.approvalLevel.name == 'Minister' && item.statusType.name == 'APPROVED' && userRole === 'Institutional logistics' && item.isAllVehicleregrequestSubmitted === false" 
                                class="btn register-btn me-2" (click)="registerVehicle(item.id)">Register</button>
                        <button *ngIf="item.approvalLevel.name == 'Institutional logistics' && item.statusType.name == 'RFAC' || userRole == 'Institutional logistics'&& item.statusType.name != 'APPROVED' || userRole == 'Institutional CBM'&& item.statusType.name != 'APPROVED' || userRole == 'Fleet Mgt Senior Engineer'&& item.statusType.name != 'APPROVED'" 
                                type="button" class="btn btn-success" (click)="editAcquisition(item.id)">Edit</button>
                      </td>
                    </tr>
                  </tbody>
              </table>
          </div>

          <nav *ngIf="finalDataSource.length > 0" aria-label="Page navigation" class="nav d-flex flex-row justify-content-between align-items-center p-3">
              <div class="pagination-info">
                <span *ngIf="finalDataSource.length > 0">
                  Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ finalDataSource.length }} entries
                </span>
                <span *ngIf="finalDataSource.length === 0">
                  No entries to show
                </span>
              </div>
              <ul class="pagination justify-content-center mb-0" *ngIf="totalPages > 1">
                <li class="page-item">
                  <button class="btn btn-outline-secondary caret me-1" 
                          (click)="previousPage()" 
                          [disabled]="currentPage === 1"
                          [class.disabled]="currentPage === 1">
                    <fa-icon [icon]="caretLeft"></fa-icon>
                  </button>
                </li>
                <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                  <button class="btn me-1 page-link pages" 
                          [class.btn-primary]="currentPage === pageNumber"
                          [class.btn-outline-secondary]="currentPage !== pageNumber"
                          [class.active]="currentPage === pageNumber" 
                          (click)="goToPage(pageNumber)">
                    {{ pageNumber }}
                  </button>
                </li>
                <li class="page-item">
                  <button class="btn btn-outline-secondary caret" 
                          (click)="nextPage()" 
                          [disabled]="currentPage === totalPages"
                          [class.disabled]="currentPage === totalPages">
                    <fa-icon [icon]="caretRight"></fa-icon>
                  </button>
                </li>
              </ul>
            </nav>

      </div>
  </div>
</div>