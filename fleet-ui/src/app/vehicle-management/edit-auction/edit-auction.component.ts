import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { faArrowLeft, faFileArchive, faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';

@Component({
  selector: 'app-edit-auction',
  templateUrl: './edit-auction.component.html',
  styleUrl: './edit-auction.component.scss'
})
export class EditAuctionComponent implements OnInit {
  auctionForm!: FormGroup;
  auctionId!: string;
  plusIcon = faPlus;
  minusIcon = faMinus;
  backwardIcon = faArrowLeft;
  userId!: string;
  institution!: string;
  institutionId!: string;
  files!: FormArray;
  selectedFileName: any;
  selectedFiles: { [key: string]: File } = {};
  documents: any[] = [];
  fileIcon = faFileArchive;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.getUserData();
    this.getAuctionIdFromRoute();
    this.fetchDocuments(this.auctionId);
  }

  initializeForm(): void {
    this.auctionForm = this.formBuilder.group({
      buyer_FirstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      buyer_LastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      buyer_idNumber: ['', [Validators.required, Validators.pattern('^[0-9]{16}$')]],
      buyer_tinNumber: ['', [Validators.required, Validators.pattern('^[0-9]{9,15}$')]],
      sale_amount: ['', [Validators.required, Validators.min(1)]],
      valuation_amount: ['', [Validators.required, Validators.min(1)]],
      description: [''],
      // auctionReport: [null, Validators.required],
      // paymentProof: [null, Validators.required],
      // salesContract: [null, Validators.required],
      files: this.formBuilder.array([this.createFileFormGroup()])
    });
    this.files = this.auctionForm.get('files') as FormArray;
  }

  getAuctionIdFromRoute(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {  
        this.auctionId = params['id'];
        console.log("Auction ID received:", this.auctionId);
        this.loadAuctionData();
      } else {
        console.error("Auction ID is missing from route params!");
        this.toastr.error("Auction ID is missing!");
      }
    });
  }

  loadAuctionData(): void {
    const apiUrl = `${environment.baseUrl}/disposal/auctionReport/${this.auctionId}`;

    this.http.get<any>(apiUrl).subscribe(
      response => {
        if (response) {
          this.auctionForm.patchValue(response);
          this.auctionForm.patchValue({ disposalId: this.auctionId }); // Ensure disposalId is set
        }
      },
      error => {
        console.error('Error fetching auction report:', error);
        this.toastr.error("Failed to load auction data.");
      }
    );
  }

  fetchDocuments(auctionId: string) {
    const url = `${environment.fileUrl}/${auctionId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.documents = response;
        console.log(this.documents)// Assuming 'documents' is an array property in your component
      },
      (error) => {
        console.error('Error fetching documents:', error);
      }
    );
  }

  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }
  
  removeFile(index: number): void {
    this.files.removeAt(index);
  }
  
  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: [''],
      applicationId: ['']
    });
  }

  fetchBase64Data(fileName: string) {
    const url = `${environment.fileUrl}/${fileName}/base64`;
    this.http.get<any>(url).subscribe(
        (response) => {
            // Decode the Base64 data and display it in a new window
            const binaryData = atob(response.base64Data);
            const arrayBuffer = new ArrayBuffer(binaryData.length);
            const uint8Array = new Uint8Array(arrayBuffer);
            for (let i = 0; i < binaryData.length; i++) {
                uint8Array[i] = binaryData.charCodeAt(i);
            }
            const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            window.open(url);
        },
        (error) => {
            console.error('Error fetching Base64 data:', error);
        }
    );
}

  onFileChange(event: any, fileType: string): void {
    const file = event.target.files[0];
    if (file) {
      this.auctionForm.patchValue({ [fileType]: file });
    }
  }

  onFileChangee(event: any, index: number): void {
    event.preventDefault();
    const file = event.target.files[0];
    if (file) {
      const fileControl = this.files.at(index).get('file');
      if (file.type !== 'application/pdf') {
        this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
        if (fileControl) {
          fileControl.setValue(null);
        }
        event.target.value = '';
        return;
      }
      if (fileControl) { 
        fileControl.setValue(file);
      }
      this.selectedFileName = file.name;
    }
  }

  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitAdditionalFile(auctionId: any): void {
    const fileUploadDataFormArray = this.auctionForm.get('files') as FormArray;
    fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
      if (control instanceof FormGroup) {
        const fileItem = control.value;
        if (fileItem.file) {
          const fileFormData = new FormData();
          const file = fileItem.file as File;
  
          console.log('File name:', file.name);
          console.log('File size:', file.size);
          console.log('File type:', file.type)
  
          if (this.validateFile(file)) {
            fileFormData.append('file', file);
            fileFormData.append('documentDescription', fileItem.documentDescription);
            fileFormData.append('applicationId', auctionId);
  
            this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
              (response) => {
                console.log('API Response:', response);
                console.log("File uploaded successfully");
                this.auctionForm.reset();
              },
              (error) => {
                let errorMessage = 'An unexpected error occurred.';
                if (error && error.error && error.error.message) {
                  errorMessage = error.error.message;
                }
                console.error('API Error:', errorMessage);
                this.toastr.error("An error occurred!!", 'Error');
              }
            );
          } else {
            console.warn('File size or type is invalid.');
          }
        } else {
          console.log('No file selected...');
        }
      }
    });
  }

  // submitFile(file: File, fileType: string, auctionId: string): void {
  //   if (file) {
  //     const fileFormData = new FormData();

  //     if (this.validateFile(file)) {
  //       fileFormData.append('file', file);
  //       fileFormData.append('documentDescription', fileType);
  //       fileFormData.append('applicationId', auctionId);

  //       this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
  //         (response) => {
  //           console.log(`${fileType} submitted successfully`, response);
  //         },
  //         (error) => {
  //           console.error(`Error submitting ${fileType}`, error);
  //           this.toastr.error(`Error submitting ${fileType}`, 'Error');
  //         }
  //       );
  //     } else {
  //       console.warn(`${fileType} size or type is invalid.`);
  //     }
  //   }
  // }
  

  markAllAsTouched(): void {
    Object.values(this.auctionForm.controls).forEach(control => control.markAsTouched());
  }

  isFieldRequired(fieldName: string): boolean {
    const control = this.auctionForm.get(fieldName);
    return control ? control.hasError('required') && control.touched : false;
  }


  // onSub() : void {
  //   const auctionId = 'f7cc2974-b6c0-4a6d-b4f0-c1bbda0c5175'
  //   this.submitFile(this.auctionForm.get('auctionReport')?.value, 'auctionReport', auctionId);
  //    // this.submitFile(this.auctionForm.get('paymentProof')?.value, 'paymentProof', auctionId);
  //   this.submitFile(this.auctionForm.get('salesContract')?.value, 'salesContract', auctionId);
  // }

  onSubmit(): void {
    this.markAllAsTouched();

    
    if(this.auctionForm.valid) {
      
      const additionalFilesArray = this.auctionForm.get('files') as FormArray;
      // const hasUploadedFiles = this.auctionForm.get('auctionReport')?.value &&
      // // this.auctionForm.get('paymentProof')?.value &&
      // this.auctionForm.get('salesContract')?.value;

        const requestData = {
          ...this.auctionForm.value,
          disposalId: this.auctionId,  // Ensure disposalId is set
          UserId: this.userId  // ✅ Ensure UserId is included
        };
      
        // Debugging Logs
        console.log("🚀 Submitting PATCH request...");
        console.log("Auction ID:", this.auctionId);
        console.log("User ID:", this.userId);
        console.log("Final Request Payload (no files):", JSON.stringify(requestData, null, 2));

        this.http.patch(`https://fleettesting.mininfra.gov.rw/disposal/updateAuctionReport/${this.auctionId}`, requestData)
      .subscribe({
        next: (response: any) => {
          const auctionId = response.id;
          console.log("✅ Auction updated successfully:", response);
          this.toastr.success('Auction updated successfully');
  
          // this.submitFile(this.auctionForm.get('auctionReport')?.value, 'auctionReport', auctionId);
          // this.submitFile(this.auctionForm.get('paymentProof')?.value, 'paymentProof', auctionId);
          // this.submitFile(this.auctionForm.get('salesContract')?.value, 'salesContract', auctionId);
          if (additionalFilesArray && additionalFilesArray.length > 0) {
            this.submitAdditionalFile(auctionId);
          }
          this.router.navigate(['/vehicle-management/auction-reports']);
          // this.auctionForm.reset()
        },
        error: error => {
          console.error("❌ Error updating auction:", error);
          console.log("🛑 Error Response:", JSON.stringify(error, null, 2));
          this.toastr.error(error?.error?.message?.join(", ") || 'Error updating auction');
        }
      });
    } else {
      this.toastr.error('Please fill in all fields to submit', 'Error');
    }
  }
  
  goBack(): void {
    this.location.back();
  }

  getUserData(): void {
    const data = localStorage.getItem('localUserData');
    if (data) {
      const user = JSON.parse(data)?.data?.user;
      this.userId = user?.id;
      this.institution = user?.institution?.name;
      this.institutionId = user?.institution?.id;
      console.log("User ID:", this.userId);
    }
  }
}