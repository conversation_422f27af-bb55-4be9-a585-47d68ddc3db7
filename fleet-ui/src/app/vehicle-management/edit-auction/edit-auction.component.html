<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <div class="header d-flex flex-row justify-content-between w-50">
            <button class="btn go-back-btn" (click)="goBack()">
                <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
            </button>
            <h1>Edit Auction Report</h1>
        </div>

        <div class="card">
            <p>Modify details for the vehicle auctioning</p>
            <div class="row form-container">
                <form [formGroup]="auctionForm" class="text-center" (ngSubmit)="onSubmit()" enctype="multipart/form-data">
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="buyer_idNumber">Buyer Id Number</label>
                            <input type="number" class="form-control" formControlName="buyer_idNumber" id="buyer_idNumber">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="buyer_FirstName">Buyer Firstname</label>
                            <input type="text" class="form-control" formControlName="buyer_FirstName" id="buyer_FirstName">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="buyer_LastName">Buyer Lastname</label>
                            <input type="text" class="form-control" formControlName="buyer_LastName" id="buyer_LastName">
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="buyer_tinNumber">Buyer Tin Number</label>
                            <input type="number" class="form-control" formControlName="buyer_tinNumber" id="buyer_tinNumber">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="sale_amount">Sale Amount</label>
                            <input type="number" class="form-control" formControlName="sale_amount" id="sale_amount">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="valuation_amount">Valuation Amount</label>
                            <input type="number" class="form-control" formControlName="valuation_amount" id="valuation_amount">
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="description">Description</label>
                            <textarea class="form-control" formControlName="description" id="description" rows="4"></textarea>
                        </div>
                    </div>

                    <h2>File Upload Section</h2>
                    <!-- <div class="row mt-3" *ngIf="documents.length > 0">
                        <p>These are the existing documents</p>
                      
                        <div class="col-md-6" *ngFor="let document of documents; let i = index">
                          <div class="document">
                            <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                            <a style="cursor: pointer;" (click)="fetchBase64Data(document.fileName)" class="file-link">
                              {{ document.fileName || 'N/A' }}
                            </a>
                          </div>
                        </div>
                    </div> -->
                      
                    <div class="row" *ngIf="documents.length > 0">
                        <div class="col-md-12">
                          <p>These are the existing documents</p>
                          <div class="document-container" *ngFor="let document of documents; let i = index">
                            <!-- Group documents two by two -->
                            <div class="row" *ngIf="i % 2 === 0">
                              <div class="col-md-6" *ngIf="documents[i]">
                                <div class="document">
                                  <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                                  <a style="cursor: pointer;" (click)="fetchBase64Data(documents[i].fileName)" class="file-link">{{ documents[i].fileName || 'N/A' }}</a>
                                </div>
                              </div>
                              
                              <!-- Check if the next document exists and is part of the pair -->
                              <div class="col-md-6" *ngIf="documents[i + 1]">
                                <div class="document">
                                  <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                                  <a style="cursor: pointer;" (click)="fetchBase64Data(documents[i + 1].fileName)" class="file-link">{{ documents[i + 1].fileName || 'N/A' }}</a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                    <!-- Dynamic File Upload -->
                    <div class="row" formArrayName="files">
                        <h2 class="text-warning">Need to upload other files?</h2>

                                    <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                                        <div class="form-group col-md-6">
                                          <input type="file" (change)="onFileChangee($event, i)" formControlName="file" class="form-control" />
                                        </div>
                                        <div class="form-group col-md-6">
                                          <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                                        </div>
            
                                        <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                                          <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                                      </button>
                                      </div>
                                      <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                                        <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add New File
                                      </button>
                      </div>

                    <div class="row mt-4">
                        <div class="col-md-8"></div>
                        <div class="col-md-4 buttons">
                            <button type="submit" class="btn submit-btn col-md-9 float-end">Update Auction</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>