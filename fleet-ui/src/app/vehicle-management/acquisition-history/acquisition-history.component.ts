// AcquisitionHistoryComponent.ts

import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-acquisition-history',
  templateUrl: './acquisition-history.component.html',
  styleUrls: ['./acquisition-history.component.scss'],
})
export class AcquisitionHistoryComponent implements OnInit {
  acquisitionId = '';
  acquisitionHistory: any[] = [];
  userDetails: { [key: string]: any } = {}; // Store user details by userId
  acquisitionDetails: any;
  acquisitions: any;
  acquisitionStatus = 'Pending';
  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private toastr: ToastrService,
    private modalService: NgbModal
  ) {}

  ngOnInit() {
 
    this.route.paramMap.subscribe((params) => {
      const acquisitionId = params.get('acquisitionId');
      if (acquisitionId) {
        this.acquisitionId = acquisitionId;
        this.fetchAcquisitionHistory(acquisitionId);
        // this.fetchRequestDetails(acquisitionId);
      }
    });
  }
 
  fetchAcquisitionHistory(acquisitionId: string) {
    const url = `${environment.baseUrl}/approval/getAllHistoryByActionId?actionId=${acquisitionId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.acquisitionHistory = response;
        console.log(response)

        // Fetch user details for each userId in the history
        this.acquisitionHistory.forEach((item) => {
          const userId = item.userId;
          if (userId && !this.userDetails[userId]) {
            this.fetchUserDetails(userId); // Fetch if not already fetched
          }
        });
      },
      (error) => {
        console.error('Error fetching acquisition history:', error);
      }
    );
  }

  fetchUserDetails(userId: string) {
    if (!this.userDetails[userId]) {
      const userUrl = `${environment.baseUrl}/auth/getUserByUser?userId=${userId}`;
      this.http.get<any>(userUrl).subscribe(
        (user) => {
          this.userDetails[userId] = user;
        },
        (error) => {
          console.error('Error fetching user details:', error);
        }
      );
    }
  }

  // fetchRequestDetails(acquisitionId: string) {
  //   const url = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;
  //   this.http.get<any>(url).subscribe(
  //     (response) => {
  //       if (response && response.acquisition) {
  //         this.acquisitionDetails = response.acquisition;
  //         this.acquisitionId = response.acquisition.id; // Assign acquisition ID
  //         this.acquisitions = response.vehicles; // Assign vehicles

  //         this.acquisitionStatus = this.acquisitionDetails.statusType?.name || 'Pending';

  //         const userId = this.acquisitionDetails.userId;
  //         if (userId) {
  //           this.fetchUserDetails(userId); // Fetch user details
  //         }
  //       } else {
  //         console.error('Unexpected response structure:', response);
  //         this.acquisitions = []; // Fallback to an empty array if needed
  //       }
  //     },
  //     (error) => {
  //       console.error('Error fetching acquisition details:', error);
  //     }
  //   );
  // }
 
  history = {
    activityPerformed: 'APPROVED' 
  };


}

