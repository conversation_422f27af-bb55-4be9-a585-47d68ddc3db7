<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="vehicle-container ">
        <div class="top w-100 " >
            <h1>Request Vehicle </h1>
        </div>
            <div class="card w-100 custom-height-container">
                <div class="progress-container d-flex flex-row justify-content-center align-items-center ">
                    <div class="form-1">
                        <div class="progress-label d-flex flex-row justify-content-center align-items-center m-2" [ngClass]="{ 'active': currentStep === 1, 'inactive': currentStep !== 1 }"> 1 </div>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 100%;" [ngClass]="{ 'active': currentStep === 1, 'inactive': currentStep !== 1 }">
                            </div>
                          </div>
                    </div>
                    <div class="form-2">
                        <div class="progress ">
                            <div class="progress-bar" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 100%;" [ngClass]="{ 'active': currentStep === 2, 'inactive': currentStep !== 2 }">
                            </div>
                            
                          </div>
                        <div class="progress-label d-flex flex-row justify-content-center align-items-center m-2"[ngClass]="{ 'active': currentStep === 2, 'inactive': currentStep !== 2 }">2</div>
                    </div>
                </div>

                <div class="form-container">
                    <form [formGroup]="requestForm" method="post" class="part-1" *ngIf="!currentStep || currentStep === 1">
                        <h2> Vehicle Details</h2>
                        <div class="row mt-4">
                            <div class="form-group col-md-6">
                                <label for="institution">Institution</label>
                                <select id="institution" class="form-control" formControlName="institution">
                                    <option *ngFor="let institution of institutions" [value]="institution">{{ institution }}</option>
                                </select>
                                <div *ngIf="requestForm.get('institution')?.hasError('required') && requestForm.get('institution')?.touched" class="text-danger">Institution is required</div>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="ownership">Ownership</label>
                                <select id="ownership" class="form-control" formControlName="ownership">
                                    <option *ngFor="let ownership of ownerships" [value]="ownership">{{ ownership }}</option>
                                 </select>
                                <div *ngIf="requestForm.get('ownership')?.hasError('required') && requestForm.get('ownership')?.touched" class="text-danger">Ownership is required</div>
                            </div>
                        </div>
                    
                        <div class="row mt-4">
                            <div class="form-group col-md-6">
                                <label for="requestType">Request Type</label>
                                <select id="requestType" class="form-control" formControlName="requestType">
                                    <option *ngFor="let requestType of requestTypes" [value]="requestType">{{ requestType }}</option>
                                </select>
                                <div *ngIf="requestForm.get('requestType')?.hasError('required') && requestForm.get('requestType')?.touched" class="text-danger">Request Type is required</div>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="vehicleType">Vehicle Type</label>
                                <select id="vehicleType" class="form-control" formControlName="vehicleType">
                                    <option *ngFor="let vehicleType of vehicleTypes" [value]="vehicleType">{{ vehicleType }}</option>
                                </select>
                                <div *ngIf="requestForm.get('vehicleType')?.hasError('required') && requestForm.get('vehicleType')?.touched" class="text-danger">Vehicle Type is required</div>
                            </div>
                        </div>
                    
                        <div class="row mt-4 array" formArrayName="vehicleDetails">
                            <div class="row d-flex flex-row mb-3" *ngFor="let detail of vehicleDetails?.controls; let i = index">
                                <div class="form-group col-md-6">
                                    <label class="mr-2" for="vehicleModel{{ i }}">Vehicle Model</label>
                                    <input type="text" class="form-control" id="vehicleModel{{ i }}" formControlName="vehicleModel{{ i }}">
                                </div>
                        
                                <div class="form-group col-md-6">
                                    <label class="mr-2" for="numberOfVehicles{{ i }}">Number of Vehicles</label>
                                    <input type="number" class="form-control" id="numberOfVehicles{{ i }}" formControlName="numberOfVehicles{{ i }}">
                                </div>
                                <p class="remove-btn mt-2" *ngIf="i > 0" (click)="removeVehicleDetail(i)"> Delete record</p>
                            </div>
                        </div>                        
                        <button type="button" class="btn add-btn" (click)="addVehicleDetail()">Add another model</button>
                    
                        <div class="row mt-4">
                            <div class="form-group col-md-6">
                                <label for="beneficiaryAgency">Beneficiary Agency</label>
                                <div class="input-group">
                                    <input type="text" id="beneficiaryAgency" formControlName="beneficiaryAgency" class="form-control">
                                </div>
                            </div>

                            <div class="form-group col-md-6">
                                <label for="intendedUsage">Intended Usage</label>
                                <input type="text" name="intendedUsage" id="intendedUsage" formControlName="intendedUsage" class="form-control"/>
                                <div *ngIf="requestForm.get('intendedUsage')?.hasError('required') && requestForm.get('intendedUsage')?.touched" class="text-danger">Intended Usage is required</div>
                            </div>
                        </div>
                    
                        <div class="row mt-4">
                            <div class="col-md-6 d-flex w-100 mt-2" style="justify-content: center;">
                                <button type="button" class="btn btn-primary w-25" (click)="requestForm.get('ownership')?.value === 'Government Project (GP)' ? togglePart2() : submitOther()">
                                    {{ requestForm.get('ownership')?.value === 'Government Project (GP)' ? 'Next' : 'Submit' }}
                                </button>
                                <app-toastr></app-toastr>
                            </div>
                        </div>
                    </form>

                    <form (ngSubmit)="onSubmit()" [formGroup]="projectForm" method="post" class="part-2" *ngIf="currentStep === 2">
                        <h2> Project Details</h2>
                        <div class="row mt-4">
                            <div class="form-group col-md-6">
                            <label for="projectName">Project Name</label>
                            <input type="text" name="projectName" formControlName="projectName" class="form-control"/>
                            <div *ngIf="projectForm.get('projectName')?.hasError('required') && (projectForm.get('projectName')?.dirty)" class="text-danger">Project Name is required</div>
                            </div>
        
                            <div class="form-group col-md-6">
                            <label for="projectDescription">Project Description</label>
                            <input type="text" name="projectDescription" formControlName="projectDescription" class="form-control"/>
                            <div *ngIf="projectForm.get('projectDescription')?.hasError('required') && (projectForm.get('projectDescription')?.dirty)" class="text-danger">Project Description is required</div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="form-group col-md-6">
                            <label for="projectStartDate">Project Start Date</label>
                            <input type="date" name="projectStartDate" formControlName="projectStartDate" class="form-control"/>
                            <div *ngIf="projectForm.get('projectStartDate')?.hasError('required') && (projectForm.get('projectStartDate')?.dirty)" class="text-danger">Project Start Date is required</div>
                            </div>
                            
                            <div class="form-group col-md-6">
                            <label for="projectEndDate">Project End Date</label>
                            <input type="date" name="projectEndDate" formControlName="projectEndDate" class="form-control"/>
                            <div *ngIf="projectForm.get('projectEndDate')?.hasError('required') && (projectForm.get('projectEndDate')?.dirty)" class="text-danger">Project End Date is required</div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="form-group col-md-6">
                            <label for="document">Document</label>
                            <input type="file" name="document" formControlName="document" class="form-control"/>
                            </div>

                            <!-- <div class="custom-file">
                                <input type="file" class="custom-file-input" id="customFile">
                                <label class="custom-file-label" for="customFile">Choose file</label>
                              </div> -->
                        </div>
                        <div class="row mt-4 buttons d-flex justify-content-between p-3">
                            <button type="button" class="btn back p-1" (click)="goBackToPart1()"> Go Back </button>
                            <button type="submit" class="btn btn-primary  w-25">Submit</button>
                            <app-toastr></app-toastr>
                        </div>
                    </form>
                </div>
            </div>
    </div>
</div>