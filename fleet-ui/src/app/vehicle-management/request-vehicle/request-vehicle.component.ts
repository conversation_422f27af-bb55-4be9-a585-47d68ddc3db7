import { Component, OnInit, ViewChild} from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';

export enum Ownership {
  GR = 'Government Car (GoR)',
  GP = 'Government Project (GP)',
  Coowned = 'Co-owned',
}
export enum RequestType {
  Purchase = 'Purchase New Car',
  Available = 'Available Car'
}
export enum VehicleType {
  Car = 'Car',
  Motorcycle = 'Motorcycle',
}

@Component({
  selector: 'app-vehicles',
  templateUrl: './request-vehicle.component.html',
  styleUrl: './request-vehicle.component.scss'
})
export class RequestVehicleComponent implements OnInit{
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  passwordVisible: boolean = false;
  requestForm: FormGroup;
  projectForm: FormGroup;
  ownerships: string[] = Object.values(Ownership);
  institutions: any[] = [];
  requestTypes: string[] = Object.values(RequestType);
  vehicleTypes: string[] = Object.values(VehicleType);
  showToastr: boolean = false;
  toastrMessage: string = '';
  toastrSuccess: boolean = false;

  currentStep: number = 1;
  formValues: any = {};
  ownership: string = '';
  showSecondForm: boolean = true;

  constructor(private formBuilder: FormBuilder, private http: HttpClient, private toastr: ToastrService) {
    this.requestForm = this.formBuilder.group({
      institution: ['', Validators.required],
      ownership: ['', Validators.required],
      requestType: ['', Validators.required],
      vehicleType: ['', Validators.required],
      beneficiaryAgency: [''],
      intendedUsage: ['', Validators.required],
      vehicleModel: ['', Validators.required],
      numberOfVehicles: ['', Validators.required],
      vehicleDetails: this.formBuilder.array([this.createVehicleDetail()])
    });

    this.projectForm = this.formBuilder.group({
      projectName: ['', Validators.required],
      projectDescription: ['', Validators.required],
      projectStartDate: ['', Validators.required],
      projectEndDate: ['', Validators.required],
      document: ['']
    })
  }

  ngOnInit(): void {
    this.fetchInstitutions();
    this.requestForm.get('ownership')?.valueChanges.subscribe((value) => {
      if (value !== Ownership.GP) {
        this.currentStep = 1;
      } 
    });
  }

  fetchInstitutions(): void {
    this.http.get<any[]>(`${environment.baseUrl}/user-management/institutions`).subscribe(
      (response) => {
        this.institutions = response.map(institution => institution.name); 
      },
      (error) => {
        console.error('Error fetching institutions:', error);
      }
    );
  }

  createVehicleDetail(): FormGroup {
    return this.formBuilder.group({
      vehicleModel: ['', Validators.required],
      numberOfVehicles: ['', Validators.required]
    });
  }

  get vehicleDetails(): FormArray | null {
    const control = this.requestForm.get('vehicleDetails');
    if (control instanceof FormArray) {
      return control;
    }
    return null;
  }

  addVehicleDetail(): void {
    const vehicleDetails = this.vehicleDetails; // Get the vehicle details array

    if (vehicleDetails) {
      const formValue = this.requestForm.get('vehicleDetails')?.value; // Get current form values
      vehicleDetails.push({ vehicleModel: formValue.vehicleModel, numberOfVehicles: formValue.numberOfVehicles }); // Push data to storage array
      this.requestForm.get('vehicleDetails')?.reset(); // Reset the vehicle details form
    }
  }

  removeVehicleDetail(index: number): void {
    const vehicleDetailsFormArray = this.requestForm.get('vehicleDetails') as FormArray; // Get the FormArray instance
  
    if (vehicleDetailsFormArray) {
      vehicleDetailsFormArray.removeAt(index); // Remove element at index using removeAt
    }
  }
  

  togglePart2() {
    if (this.requestForm.valid) {
      
      this.formValues['step' + this.currentStep] = { ...this.requestForm.value };
      this.currentStep++;
      console.log('Part 1 Form Values:', this.requestForm.value);

    } else {
      console.error('Form Errors:');
      for (const controlName in this.requestForm.controls) {
        if (this.requestForm.get(controlName)?.errors) {
          console.error(`  * ${controlName}:`, this.requestForm.get(controlName)?.errors);
        }
      }
      this.toastrComponent.showToastrMessage('Please fill in all fields properly', true);
    }
  }

  goBackToPart1() {
    
    const previousStep = 'step' + (this.currentStep - 1);
    if (this.formValues[previousStep]) {
      this.requestForm.patchValue(this.formValues[previousStep]);
    }
    this.currentStep--;
  }

  submitOther(): void {
    if (this.requestForm.valid) {
      const requestData = { ...this.requestForm.value }; // Get form data
  
      const vehicleDetails = this.requestForm.get('vehicleDetails')?.value as { vehicleModel: string, numberOfVehicles: number }[]; // Get vehicle details
  
      // Combine requestData and vehicleDetails
      requestData.vehicleDetails = vehicleDetails;  
  
      console.log('Form Values:', requestData);
  
      this.toastrComponent.showToastrMessage('Vehicle Requested!', false);
      this.requestForm.reset();
    } else {
      console.error('Form Errors:');
      for (const controlName in this.projectForm.controls) {
        if (this.requestForm.get(controlName)?.errors) {
          console.error(`  * ${controlName}:`, this.projectForm.get(controlName)?.errors);
    }
  }
}
  }
  

  onSubmit(): void {
    if (this.projectForm.valid) {
      console.log(this.projectForm.value);
      const requestData = { ...this.requestForm.value, ...this.projectForm.value };
      console.log('Merged Form Values:', requestData);
      // Now you can send `requestData` to your API
      this.toastrComponent.showToastrMessage('Vehicle Requested!', false);
      this.requestForm.reset();
    } else {
      console.error('Form Errors:');
      for (const controlName in this.projectForm.controls) {
        if (this.requestForm.get(controlName)?.errors) {
          console.error(`  * ${controlName}:`, this.projectForm.get(controlName)?.errors);
        }
      }
      this.toastrComponent.showToastrMessage('Please fill in all fields properly', true);
    }
  }
  
}


