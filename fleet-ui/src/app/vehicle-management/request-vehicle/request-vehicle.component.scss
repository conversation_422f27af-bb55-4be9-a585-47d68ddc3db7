.vehicle-container{
    background-color:#D9D9D94D;
    width: 83%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
}
h1{
    font-size: 20px;
    color: #28A4E2;
    font-weight: 500;
}
h2{
    font-size: 16px;
    color: #28A4E2;
    font-weight: 500;
}
.card{
    padding: 15px;
    border: none;
}
input, select, textarea {
    border: 1px solid #eaeaf5;
    font-size: 12px;
    border-radius: 8px;
}
label{
    color: #000000AB;
    font-size: 14px;
    padding-bottom: 8px;
}
.form-1, .form-2{
    width: 15%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}
.progress{
    width: 100%;
    height: 10px;
}
.progress-bar.active{
    background-color: #28A4E2;
}
.progress-bar.inactive{
    background-color: #D9D9D926;
}
.progress-label{
    height: 25px;
    width: 25px;
    border-radius: 50%;
    font-size: 13px;
    padding: 10px;
    cursor: pointer;
}
.progress-label.active {
    background-color: #28A4E2;
    color: #ffffff;
}

.progress-label.inactive {
    background-color: #D9D9D926;
    color: #757575;
}
form{
    padding: 15px;
}
.back {
    border: 1px solid #28A4E2;
    color: #28A4E2;
    width: 100px;
    font-size: 14px;
}
.buttons{
    display: flex;
    padding: 2px 10px;
}
.remove-btn{
    // border: 1px solid rgb(212, 112, 112);
    color: rgb(231, 159, 26);
    font-size: 12px;
    cursor: pointer;
    // width: 10%;
    // margin-left: 15px;
}
.add-btn{
    border: 1px solid #28A4E2;
    color: #28A4E2;
    font-size: 13px;  
}