import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import html2pdf from 'html2pdf.js';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-disposal-letter',
  templateUrl: './disposal-letter.component.html',
  styleUrl: './disposal-letter.component.scss'
})
export class DisposalLetterComponent {
  @ViewChild('container')
  containerRef!: ElementRef;
  acquisitions: any[] = [];
  acquisitionDetails: any = {};
  userDetails: any = {};
  acquisitionData: any = {};
  disposal: any = {};
  disposalDetails: any = {}; // Store tahe new disposal details

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
    }

    this.route.paramMap.subscribe(params => {
      const disposalId = params.get('disposalId');
      if (disposalId) { 
        this.fetchDisposalDetails(disposalId); // Use the new method to fetch details
      }
    });
  }

  fetchUserDetails(userId: string) {
    const userUrl = `${environment.baseUrl}/auth/getUserByUser?userId=${userId}`;
    this.http.get<any>(userUrl).subscribe(
      (user) => {
        this.userDetails = user;
      },
      (error) => {
        console.error('Error fetching user details:', error);
      }
    );
  }

  fetchDisposalDetails(disposalId: string) {
    const url = `${environment.otherUrl}/disposal/disposal/${disposalId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.disposalDetails = response; // Store the disposal data
        console.log("Disposal Data:", response);

        // Fetch user details if available in the response
        const userId = response.vehicle.vehicleRegRequest?.userId;
        if (userId) {
          this.fetchUserDetails(userId);
        }

        // Set acquisitions to vehicle data for display purposes
        if (response.vehicle) {
          this.acquisitions = [response.vehicle]; // Wrapping in array to use with *ngFor
        }
      },
      (error) => {
        console.error('Error fetching disposal details:', error);
      }
    );
  }

  downloadPdf() {
    const element = document.getElementById('letter-container'); // Use ViewChild reference
    const opt = {
      filename: 'authorization_letter.pdf',
      html2canvas: { scale: 2 }, // Higher scale for better quality
      jsPDF: {
        format: 'letter', // Use 'letter' format
        orientation: 'portrait',
        unit: 'in',
        margin: [0.5, 0.5, 0.5, 0.5] // Set margins (top, left, bottom, right)
      },
      pagebreak: { mode: 'avoid-all', avoid: ['.pagebreak'] } // Avoid page breaks
    };

    html2pdf().set(opt).from(element).save();
  }

  getTotalNumberOfVehicles(): string {
    return this.acquisitions.length.toString();
  }

  generateQrLink(): string {
    const baseLink = `${environment.domainName}/vehicle-management/disposal-details/${this.disposalDetails?.id || 'default-id'}/authorization-letter`;
    return baseLink;
  }
}
