<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <div class="header">
            <h1>Assign Returned Vehicle</h1>
        </div>

        <div class="card">
            <p>Fill in new details of the vehicle to assign to a new institution</p>
            <div class="row form-container">
                <form id="returnedVehicleForm" [formGroup]="editReturnedVehicleForm" class="text-center" method="post" (ngSubmit)="updateVehicle()">
                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="beneficaryAgencyId">Beneficiary Agency</label>
                            <select id="beneficiaryAgencyId" class="form-control" formControlName="beneficiaryAgencyId">
                                <option *ngFor="let institution of beneficiaryAgencies" [value]="institution.id">{{ institution.name }}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="ownershipType">Ownership Type</label>
                            <select id="ownershipType" class="form-control" formControlName="ownershipType">
                                <option *ngFor="let ownership of ownershipTypes" [value]="ownership.id">{{ ownership.name }}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="pickCardNumber">Pink Card Number</label>
                            <input type="text" class="form-control" id="pickCardNumber" formControlName="pickCardNumber">
                        </div>
                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="plateNumber">Plate Number</label>
                            <input type="text" class="form-control" id="plateNumber" formControlName="plateNumber">
                        </div>
                        <div class="form-group col-md-4"  *ngIf="editReturnedVehicleForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">
                            <label for="projectName">Project Name</label>
                            <input type="text" class="form-control" id="projectName" formControlName="projectName" readonly>
                        </div>
                        <div class="form-group col-md-4" *ngIf="editReturnedVehicleForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">
                            <label for="projectDescription">Project Description</label>
                            <input type="text" class="form-control" id="projectDescription" formControlName="projectDescription" readonly >
                        </div>
            
                    </div>

                    <div class="row mt-4"  *ngIf="editReturnedVehicleForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">

                        <div class="form-group col-md-4">
                            <label for="projectStartDate">Project Start Date</label>
                            <input type="date" class="form-control" id="projectStartDate" formControlName="projectStartDate" readonly >
                        </div>
                        <div class="form-group col-md-4">
                            <label for="projectEndDate">Project End Date</label>
                            <input type="date" class="form-control" id="projectEndDate" formControlName="projectEndDate" readonly>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12 text-center" >
                            <button type="button" class="btn show-details-btn" (click)="toggleVehicleDetails()">
                                {{ isVehicleDetailsVisible ? 'Hide Vehicle Details' : 'Show Vehicle Details' }}
                            </button>

                            <div *ngIf="isVehicleDetailsVisible"
                                    class="vehicle-details"
                                    style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin-top: 20px; margin-bottom: 10px;">
                                <h2>Returned Vehicle Information</h2>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label>Chassis Number</label>
                                        <p>{{ vehicle?.chassisNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Engine Number</label>
                                        <p>{{ vehicle?.engineNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Transmission Type</label>
                                        <p>{{ vehicle?.transmissionType || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Manufacturing Year</label>
                                        <p>{{ vehicle?.manufacturingYear || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Vehicle Type</label>
                                        <p>{{ vehicle?.vehicleType?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Ownership Type</label>
                                        <p>{{ vehicle?.ownershipType?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Vehicle Manufacture</label>
                                        <p>{{ vehicle?.vehicleManufacture?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Beneficiary Agency</label>
                                        <p>{{ vehicle?.beneficiaryAgency || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Plate Number</label>
                                        <p>{{ vehicle?.plateNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Pink Card Number</label>
                                        <p>{{ vehicle?.pickCardNumber|| 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-8"></div> 
                        <div class="col-md-4 buttons">
                            <button type="submit" class="btn submit-btn col-md-9 float-end">Assign Vehicle</button>
                          </div>
                    </div>                    
                </form>
            </div>
        </div>
    </div>
</div>
