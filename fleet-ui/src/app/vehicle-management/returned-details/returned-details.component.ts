import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-returned-details',
  templateUrl: './returned-details.component.html',
  styleUrl: './returned-details.component.scss'
})
export class ReturnedDetailsComponent {
  editReturnedVehicleForm !: FormGroup;
  vehicle: any = null;
  acquisition: any = null;
  beneficiaryAgencies: any[] = [];
  ownershipTypes: any[] = [];
  pickCardNumber: any;
  plateNumber: any;
  projectName: any;
  projectStartDate: any;
  projectEndDate: any;
  vehicleId: any;
  ownershipType: any;
  acquisitionId: any;
  isVehicleDetailsVisible: boolean = false;
  isGP: boolean = false;
  isGR: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router
  ){
    this.editReturnedVehicleForm = this.formBuilder.group({
      acquisitionId: ['', Validators.required],
      beneficiaryAgencyId: ['', Validators.required],
      beneficiaryAgency: ['', Validators.required],
      ownershipType: ['', Validators.required],
      pickCardNumber: ['', Validators.required],
      plateNumber: ['', Validators.required],
      projectName: [''],
      projectStartDate: [''],
      projectDescription: [''],
      projectEndDate: ['']
    })
  }

  ngOnInit(){
    this.fetchBeneficiaryAgencies();
    this.fetchOwnershipTypes();
    this.getRouteParams();
  }

  fetchBeneficiaryAgencies(): void {
    const apiUrl = `${environment.baseUrl}/user-management/institutions`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.beneficiaryAgencies = response.map(institution => ({ id: institution.id, name: institution.name }));
      },
      (error) => {
        console.error('Error fetching beneficiary agencies:', error);
      }
    );
  }

  fetchOwnershipTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/ownershipTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.ownershipTypes = response.map(ownership => ({ id: ownership.id, name: ownership.name }));
      },
      (error) => {
        console.error('Error fetching ownershipTypes:', error)
      }
    );
  }

  getOwnershipTypeName(ownershipTypeId: number): string | null {
    const ownershipType = this.ownershipTypes.find(type => type.id === ownershipTypeId);
    return ownershipType ? ownershipType.name : null;
  }
  getRouteParams(): void {
    this.route.params.subscribe(params => {
      if (params['vehicleId']) {
        this.vehicleId = params['vehicleId'];
        this.fetchVehicleDetails(this.vehicleId);
      }
      if (params['acquisitionId']) {
        this.acquisitionId = params['acquisitionId'];
        this.editReturnedVehicleForm.patchValue({
          acquisitionId: this.acquisitionId
        })
      }
      if (this.vehicleId && this.acquisitionId) {
        this.fetchExistingData(this.vehicleId, this.acquisitionId);
      }
    });
  }

  fetchVehicleDetails(vehicleId: string) {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        if(response.ownershipType.id === '0adf1b63-9a6a-4d17-9d4e-e8ee29f0e4fc'){
          this.isGR = true; 
          console.log("Vehicle is GR", this.isGR);
        } else if(response.ownershipType.id === '639bf044-1cbb-4954-b170-9843347073ca'){
          this.isGP = true;
          console.log("Vehicle is GP", this.isGP);
        }
        this.vehicle = response;
      },
      (error) => {
        console.error('Error fetching vehicle details:', error);
      }
    );
  }

  fetchAcquisistionDetails(acquisitionId: string){
    const url = `${environment.otherUrl}/AcquisitionsById?id=${acquisitionId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.acquisition = response;
      },
      (error) => {
        console.error('Error ')
      }
    )
  }

  fetchExistingData(vehicleId: string, acquisitionId: string): void {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    const url1 = `${environment.otherUrl}/AcquisitionsById?id=${acquisitionId}`;
    const url2 = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;

    this.http.get<any>(url).subscribe(
      (response) => {
        console.log("Vehicle details: ", response);

        this.editReturnedVehicleForm.patchValue({
          pickCardNumber: response.pickCardNumber,
          plateNumber: response.plateNumber,
        });
      },
      (error) => {
        console.error('Error fetching vehicle details:', error);
      }
    );

    this.http.get<any[]>(url1).subscribe(
      (response) => {
        console.log("Acquisition details: ", response);

        if (response && response.length > 0) {
          const acquisition = response[0];
          this.ownershipType = acquisition.ownershipType.id;

          this.editReturnedVehicleForm.patchValue({
            beneficiaryAgencyId: acquisition.institutionId,
            beneficiaryAgency: acquisition.institution,
            ownershipType: acquisition.ownershipType.id,
          });
        } else {
          console.error('Invalid response structure:', response);
        }
      },
      (error) => {
        console.error('Error fetching acquisition details:', error);
      }
    );

    this.http.get<any[]>(url2).subscribe(
      (response:any) => {
        console.log("Acquisition Vehicles", response);

        const vehicles = response.vehicles;
        if(vehicles && vehicles.length > 0){
          const vehicle = vehicles[0];

          this.editReturnedVehicleForm.patchValue({
            projectName: vehicle.projectName,
            projectDescription: vehicle.projectDescription,
            projectStartDate: vehicle.projectStartDate,
            projectEndDate: vehicle.projectEndDate
          })

          console.log("Theee vehicle: ", vehicle)
        }
      }
    )
  }

  updateVehicle(){
    const returnedVehicleDetails = {...this.editReturnedVehicleForm.value}

    console.log("Submitted Data: ", returnedVehicleDetails);
    
    this.http.patch(`${environment.otherUrl}/vehicleAllocation/${this.vehicleId}`, returnedVehicleDetails).subscribe(
      (response) => {
        console.log('Vehicle Updated Successfully: ', response);
        this.toastr.success('Vehicle Assigned Successfully');
        this.router.navigate([`/vehicle-management/all-registered`])
      },
      (error) => {
        console.error('Error Updating Vehicle details: ', error);
        this.toastr.error('An error occured while Assigning vehicle', 'Error');
      }
    )
  }

  toggleVehicleDetails() {
    this.isVehicleDetailsVisible = !this.isVehicleDetailsVisible;
  }
}
