import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faSearch, faCaretLeft, faCaretRight, faCar, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-all-disposals',
  templateUrl: './all-disposals.component.html',
  styleUrls: ['./all-disposals.component.scss']
})
export class AllDisposalsComponent implements OnInit {
  disposals: any[] = [];
  filteredDisposals: any[] = [];
  displayedDisposals: any[] = [];
  pageSize: number = 30;
  currentPage: number = 1;
  totalPages: number = 0;
  institutionId: string = '';
  userRole: string = '';
  searchText: string = '';
  faSearch = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  currentFilter: string = 'all';
  backwardIcon = faArrowLeft;


  constructor(private http: HttpClient, private router: Router, private location: Location) {}

  ngOnInit() {
    this.getUserDetails();
    this.fetchAllDisposals();
  }

  goBack(){
    this.location.back();
  }
  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id;
      this.userRole = parsedObj.data.user.role.name;
    }
  }

  fetchAllDisposals() {
    let url: string;

    // Get institution ID from localStorage
    const institutionId = this.institutionId;

    // Determine which API to use based on user role
    switch (this.userRole) {
      case 'Institutional logistics':
      case 'Institutional CBM':
        if (institutionId) {
          url = `${environment.baseUrl}/disposal/disposalByInstutionID/${institutionId}`;
        } else {
          console.error('Institution ID not found in localStorage');
          return;
        }
        break;

      case 'Fleet Mgt Senior Engineer':
      case 'DG Transport':
      case 'Permanent Secretary':
      case 'Minister':
        url = `${environment.baseUrl}/disposal/allVehicleDisposal`;
        break;

      default:
        console.error('Unrecognized user role');
        return;
    }

    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        this.disposals = response.filter(
          disposal => !(disposal.isActionReportsubmitted === true
            && disposal.requestStatus.name === 'APPROVED'
            && disposal.disposalTypes.name === 'Public Auction')
        );
        this.filterDisposalsByRole();
        this.applyCurrentFilter();
        this.updateDisplayedDisposals();
      },
      (error) => {
        console.error('Error fetching disposals:', error);
      }
    );
  }


  filterDisposalsByRole() {
    console.log('User Role:', this.userRole);
    console.log('User Institution ID:', this.institutionId);

    // Roles that should see all disposals, regardless of institution
    const unrestrictedRoles = [
        'Fleet Mgt Senior Engineer',
        'DG Transport',
        'Permanent Secretary',
        'Minister'
    ];

    if (unrestrictedRoles.includes(this.userRole)) {
        // These roles see all requests from all institutions
        this.filteredDisposals = [...this.disposals];
    } else {
        // Other roles only see disposals from their institution
        this.filteredDisposals = this.disposals.filter(vehicle => {
            console.log('Vehicle ID:', vehicle.id, 'Beneficiary Agency:', vehicle.beneficiaryAgencyId);
            return vehicle.beneficiaryAgencyId === this.institutionId;
        });
    }

    // Apply additional role-based filtering (except for unrestricted roles)
    if (!unrestrictedRoles.includes(this.userRole)) {
        switch (this.userRole) {
            case 'Institutional CBM':
                this.filteredDisposals = this.filteredDisposals.filter(
                    vehicle => vehicle.approvalLevel?.name !== 'Institutional logistics'
                );
                break;

            default:
                break;
        }
    }

    console.log('Filtered Disposals:', this.filteredDisposals);
    this.updateDisplayedDisposals(); // Update displayed vehicles based on the current page
}

  applyCurrentFilter() {
    this.filteredDisposals = [...this.disposals];

    switch (this.currentFilter) {
      case 'all':
        break;
      case 'pending-progress':
        this.filteredDisposals = this.filteredDisposals.filter(
          (item) => item.requestStatus?.name === 'PENDING' || item.requestStatus?.name === 'PROGRESS' || item.requestStatus?.name === 'RFAC'
        );
        break;
      case 'completed':
        this.filteredDisposals = this.filteredDisposals.filter(
          (item) => item.requestStatus?.name === 'APPROVED'
        );
        break;
      case 'denied':
        this.filteredDisposals = this.filteredDisposals.filter(
          (item) => item.requestStatus?.name === 'DENIED'
        );
        break;
        case 'pending':
          this.filteredDisposals = this.filteredDisposals.filter(
            (item) => item.requestStatus?.name === 'PENDING'&&item.approvalLevel?.name === this.userRole ||item.requestStatus?.name === 'PROGRESS' && item.approvalLevel?.name === this.userRole
          );
          break;
      default:
        console.error('Invalid filter type:', this.currentFilter);
    }

    this.currentPage = 1;
    this.updateDisplayedDisposals();
  }

  setFilter(filterType: string) {
    this.currentFilter = filterType;
    this.applyCurrentFilter();
  }
  editDisposal(disposalId: string): void {
    console.log("DisposalId:", disposalId);
    this.router.navigateByUrl(`/vehicle-management/edit-disposal/${disposalId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
}
  updateDisplayedDisposals() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredDisposals.length);

    this.displayedDisposals = this.filteredDisposals.slice(startIndex, endIndex); // Update based on current page

    this.totalPages = Math.ceil(this.filteredDisposals.length / this.pageSize); // Recalculate total pages
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedDisposals();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedDisposals();
    }
  }

  getPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedDisposals();
    }
  }
  getFirstEntryIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.filteredDisposals.length);
  }
  sortable(event: any) {
    const selectedField: string = event.target.value;

    this.filteredDisposals.sort((a, b) => {
      const fieldA = a[selectedField];
      const fieldB = b[selectedField];
      if (typeof fieldA === 'string' && typeof fieldB === 'string') {
        return fieldA.localeCompare(fieldB);
      }
      return 0;
    });

    this.updateDisplayedDisposals();
  }
  getDisposalStatusClass(requestStatus: any): string {
    if (requestStatus) {
      switch (requestStatus.name.toLowerCase()) {
        case 'approved':
          return 'status-registered';
        case 'pending':
          return 'status-pending';
        case 'denied':
          return 'status-denied';
        default:
          return '';
      }
    }
    return '';
  }

  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'bg-success'; // Corresponds to green
        case 'pending':
            return 'bg-secondary'; // Corresponds to yellow/orange
        case 'denied':
            return 'bg-danger'; // Corresponds to red
        case 'rfac':
            return 'bg-info'; // Corresponds to blue
        case 'progress':
            return 'bg-warning'; // Corresponds to blue
        default:
            return ''; // Default or fallback class
    }
}
getProgressWidth(status: string): string {
  switch (status.toLowerCase()) {
    case 'approved':
      return '100%'; // Corresponds to green
    case 'pending':
      return '70%'; // Corresponds to yellow/orange
    case 'denied':
      return '100%'; // Corresponds to red
    case 'rfac':
      return '60%'; // Corresponds to blue
    case 'progress':
      return '80%'; // Corresponds to blue
    default:
      return '';
  }
}
  viewDisposal(disposalId: string): void {
    console.log('Disposal ID:', disposalId);
    this.router.navigateByUrl(`/vehicle-management/disposal-details/${disposalId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  viewAuction(disposalId: string): void {
    this.router.navigateByUrl(`/vehicle-management/auction/${disposalId}`).then(success => {
      if(success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating: ', error);
    });
  }
  searchDisposals() {
    const searchTextLower = this.searchText.toLowerCase();

    // Apply search only to the already filtered disposals
    const filteredBySearch = this.filteredDisposals.filter(item => {
      return (
        (item.description || '').toLowerCase().includes(searchTextLower) ||
        (item.disposalTypes?.name || '').toLowerCase().includes(searchTextLower) ||
        (item.disposalReasons?.name || '').toLowerCase().includes(searchTextLower) ||
        (item.approvalLevel?.name || '').toLowerCase().includes(searchTextLower)
      );
    });

    // Update displayed disposals based on the search result
    this.displayedDisposals = filteredBySearch.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
    this.totalPages = Math.ceil(filteredBySearch.length / this.pageSize);
  }

}
