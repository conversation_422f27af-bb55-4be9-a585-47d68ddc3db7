<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicles-container">
      <div class="header d-flex flex-row justify-content-between">
          <button class="btn go-back-btn" (click)="goBack()">
              <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
            </button>
          <h2 class="table-title">All Vehicle Disposals</h2>

          <div class="search-group d-flex">
              <fa-icon [icon]="faSearch" class="icon"></fa-icon>
              <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchDisposals()">
          </div>
      </div>

      <div class="card">
          <div class="table-header d-flex flex-row justify-content-between">
              <!-- Table Filter -->
              <div class="d-flex flex-row justify-content-start align-items-center statuses">
                  <button class="btn btn-outline position-relative" (click)="setFilter('all')" [class.active]="currentFilter === 'all'">All  <span *ngIf="currentFilter === 'all'" class="badge badge-circle">{{ filteredDisposals.length }}</span></button>
                  <button class="btn btn-outline position-relative" (click)="setFilter('pending-progress')" [class.active]="currentFilter === 'pending-progress'">Pending/Progress  <span *ngIf="currentFilter === 'pending-progress'" class="badge badge-circle">{{ filteredDisposals.length }}</span></button>
                  <button *ngIf="userRole != 'Institutional logistics' && userRole != 'Institutional CBM'"class="btn btn-danger text-white position-relative pending-btn"[class.active]="currentFilter === 'pending'"(click)="setFilter('pending')">Pending Your Approval<span *ngIf="currentFilter === 'pending'" class="badge badge-circle">{{ filteredDisposals.length }}</span></button>
                  <button class="btn btn-outline position-relative" (click)="setFilter('completed')" [class.active]="currentFilter === 'completed'">Completed<span *ngIf="currentFilter === 'completed'" class="badge badge-circle">{{ filteredDisposals.length }}</span> </button>
                  <button class="btn btn-outline position-relative" (click)="setFilter('denied')" [class.active]="currentFilter === 'denied'">Denied  <span *ngIf="currentFilter === 'denied'" class="badge badge-circle">{{ filteredDisposals.length }}</span></button>
              </div>

              <div class="sorting-group d-flex flex-row p-2">
                  <label for="sort-field" class="text-muted p-1">Sort by:</label>
                  <select id="sort-field" class="select" (change)="sortable($event)">
                      <option value="">Select Field</option>
                      <option value="createddate">Created Date</option>
                      <!-- <option value="description">Description</option> -->
                      <option value="disposalTypeId">Disposal Type</option>
                      <option value="disposalReasonId">Disposal Reason</option>
                      <option value="requestStatusId">Status</option>
                  </select>
              </div>
          </div>

          <div class="table-only p-3">
              <table class="table table-stripped">
                  <thead>
                      <tr>
                          <!-- <th>Disposal Description</th> -->
                          <th>Disposal Type</th>
                          <th>Disposal Reason</th>
                          <th>Approval Level</th>
                          <th>Status</th>
                          <th>Actions</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr *ngIf="displayedDisposals.length <= 0">
                          <td colspan="9" class="no-vehicles-message">No Disposal Requests!!!</td>
                      </tr>
                      <tr *ngFor="let disposal of displayedDisposals">
                          <td>{{ disposal.disposalTypes?.name }}</td>
                          <td>{{ disposal.disposalReasons?.name }}</td>
                          <td>{{ disposal.approvalLevel?.name }}</td>
                          <td>
                              <div class="progress">
                                  <div class="progress-bar"
                                       [ngClass]="getStatusButtonClass(disposal.requestStatus?.name)"
                                       role="progressbar"
                                       [style.width]="getProgressWidth(disposal.requestStatus?.name)"
                                       aria-valuemin="0"
                                       aria-valuemax="100">
                                       {{ disposal.requestStatus?.name }}
                                  </div>
                              </div>
                          </td>
                          <!-- <td [ngClass]="getDisposalStatusClass(disposal.requestStatus)">
                              {{ disposal.requestStatus?.name }}
                          </td> -->
                          <td class="d-flex flex-row">
                              <button class="btn view-btn me-2" (click)="viewDisposal(disposal.id)"> View </button>
                              <button class="btn auction-btn" *ngIf="disposal.disposalTypes?.name == 'Public Auction' && userRole === 'Institutional logistics' && disposal.isActionReportsubmitted === false && disposal.requestStatus.name === 'APPROVED'" (click)="viewAuction(disposal.id)" style="margin: 0px 14px;"> Auction </button>
                              <button class="btn edit-btn " *ngIf="disposal.requestStatus?.name === 'RFAC' || userRole == 'Institutional logistics'&& disposal.requestStatus?.name === 'APPROVED' || userRole == 'Institutional CBM'&& disposal.requestStatus?.name === 'APPROVED' || userRole == 'Fleet Mgt Senior Engineer'&& disposal.requestStatus?.name === 'APPROVED'" (click)="editDisposal(disposal.id)"> Edit </button>
                          </td>
                      </tr>
                  </tbody>
              </table>
          </div>

          <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
              <div class="pagination-info">
                  Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{  filteredDisposals.length }} entries
              </div>
              <ul class="pagination justify-content-center">
                  <li class="page-item">
                      <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                          <fa-icon [icon]="caretLeft"></fa-icon>
                      </button>
                  </li>
                  <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                      <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                          {{ pageNumber }}
                      </button>
                  </li>
                  <li class="page-item">
                      <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                          <fa-icon [icon]="caretRight"></fa-icon>
                      </button>
                  </li>
              </ul>
          </nav>
      </div>
  </div>
</div>
