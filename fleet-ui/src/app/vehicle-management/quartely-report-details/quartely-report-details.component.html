<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
  
    <div class="vehicle-container">
      <div class="card m-3 p-3">
        <button class="btn go-back-btn" (click)="goBack()">
          <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
        </button>
        <div class="row">
          <div class="col-md-6">
            <div class="vehicle-details" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Vehicle Information</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label>Chassis Number</label>
                  <p>{{ report.vehicle.chassisNumber || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Transmission Type</label>
                  <p>{{ report.vehicle.transmissionType || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Manufacturing Year</label>
                  <p>{{ report.vehicle.manufacturingYear || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Beneficiary Agency</label>
                  <p>{{ report.vehicle.beneficiaryAgency || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Approval Level</label>
                  <p>{{ report.approvalLevel?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Report Status</label>
                  <p> {{ report.reportStatus?.name }} </p>
                </div>
              </div>
              
            </div>
                
            <!-- Additional Information -->
            <div class="additional-info mt-3" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Report Information</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label>Vehicle Status</label>
                  <p>{{ report.vehicleStatus?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Is Vehicle Active</label>
                  <p>{{ report.isVehicleActive === true ? 'Active' : 'Inactive' || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Description</label>
                  <p>{{ report.description || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Reported Date</label>
                  <p>{{ report.reported_date || 'N/A' }}</p>
                </div>
              </div>
            </div>
         
               </div>
          
  
          <!-- Right Column: Additional Information and Project Details -->
          <div class="col-md-6">
            <app-registration-history></app-registration-history>
          </div>
  
      <!-- Action Buttons -->
      <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canSeeActionButtons()">
        <button class="btn btn-success" *ngIf="canApprove()" (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
          <fa-icon [icon]="approveIcon" class="pe-1"></fa-icon>Approve
        </button>
        <button class="btn btn-success" *ngIf="!canApprove()" (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
          <fa-icon [icon]="submitIcon" class="pe-1"></fa-icon>Submit To Next Level
        </button>  <button class="btn btn-danger" *ngIf="canDeny()" (click)="setAction('DENIED')" data-bs-toggle="modal" data-bs-target="#actionModal"><fa-icon [icon]="denyIcon" class="pe-1"></fa-icon> Deny</button>
        <button class="btn btn-secondary" (click)="setAction('RFAC')" data-bs-toggle="modal" data-bs-target="#actionModal"><fa-icon [icon]="moreIcon" class="pe-1"></fa-icon> More Actions</button>
      </div>
        
        <!-- Display the Edit button only if canEditVehicle() returns true -->
        <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canEditVehicle()">
          <!-- <button class="btn btn-info" (click)="editVehicle(vehicle.id)">Edit</button> -->
        </div>

    <!-- Modal for Action Confirmation -->
    
     <div class="modal fade" id="actionModal" aria-labelledby="actionModalLabel" aria-hidden="true">
      <div class="modal-dialog w-75">
        <div class="modal-content animate__animated animate__fadeInUp">
          <!-- Modal Header -->
          <div class="modal-header border-0 bg-info text-white position-relative">
            <h4 class="modal-title w-100 text-center text-white" id="actionModalLabel">Action Confirmation</h4>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
              aria-label="Close"></button>
          </div>
          <div *ngIf="isLoading" class="loader-wrapper d-flex justify-content-center align-items-center flex-column">
            <app-spinner></app-spinner>
            <p class="loading-text">Loading...</p>
          </div>
          
          <!-- Success Message -->
          <div *ngIf="!isLoading && actionSuccessful" class="success-wrapper text-center">
            <div class="form-card">
              <h2 class="text-success">Success</h2>
              <div class="row justify-content-center">
                <div class="col-md-4">
                  <img src="https://img.icons8.com/color/96/000000/ok--v2.png" class="fit-image" alt="Success Icon">
                </div>
              </div>
              <br><br>
            </div>
          </div>
           
              <div class="modal-body" *ngIf="!isLoading && !actionSuccessful">
                <p class="instruction-text">Add a comment if needed, then click "Confirm" to proceed.</p>
                <textarea class="form-control comment-box" [(ngModel)]="actionComment" placeholder="Add your comments here..."></textarea>
              </div>
              <div class="modal-footer" *ngIf="!isLoading && !actionSuccessful">
                <!-- <button type="button" class="btn btn-secondary cancel-btn" data-bs-dismiss="modal">Cancel</button>
                <button *ngIf="!isLoading && !actionSuccessful" type="button" class="btn btn-info confirm-btn" (click)="confirmAction()">Confirm</button> -->
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-info" (click)="confirmAction()">Confirm</button>
              </div>
            </div>
          </div>
        </div>
      </div>
</div>
</div>
</div>