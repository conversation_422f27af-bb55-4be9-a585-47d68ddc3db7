@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');

* {
    font-family: 'Poppins', sans-serif;

}

.vehicle-container {
    background-color: #D9D9D94D;
    height: 90%;
    width: 83%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 3px;
}

.card {
    height: 94%;
    overflow-y: scroll;
}

.card::-webkit-scrollbar {
    width: 0;
}

.card-header {
    background-color: #28A4E2;
    color: white;
    position: fixed;
    z-index: 1;
    width: 80%;
}

h4 {
    color: #28A4E2;
    font-family: 'Poppins', sans-serif;
    font-weight: bold;
}

label {
    // color: #757575;
    color: rgb(146, 146, 146);
    font-weight: bold;
}

span,
p {
    // color: #757575;
    color: black;
}

.buttons {
    right: 0;
}

.separator-line {
    border-top: 2px solid rgb(146, 146, 146);
}

.action,
.btn {
    margin: 10px;
    color: white;

}

.btn-success {
    background-color: white;
    color: green;
    border: 1px solid green;
}

.btn-danger {
    background-color: white;
    color: red;
    border: 1px solid red;
}

.btn-secondary {
    background-color: white;
    color: grey;
    border: 1px solid grey;
}

.btn-warning {
    background-color: white;
    color: yellow;
    border: 1px solid yellow;

}

.btn-primary,
.btn-info {
    background-color: white;
    color: #28A4E2;
    border: 1px sold #28A4E2;
}

.notification{
    height: 30px;
    width: 30px;
    border-radius: 50%;
   

}
.instruction-text {
  font-size: 16px;
  margin-bottom: 15px;
  color: #6c757d;
}

.comment-box {
  width: 100%;
  height: 100px;
  margin-bottom: 15px;
  resize: vertical;
  border-radius: 5px;
  border: 1px solid #ced4da;
  padding: 10px;
  font-size: 14px;
  color: #495057;
}
.bellIcon {
    color: #28A4E2;
    font-size: large;
}

.btn-approve {
    background-color: rgb(48, 178, 48);
    color: white;
}

.btn-deny {
    color: white;
    background-color: rgb(153, 20, 20);
}

.modal-header {
    justify-content: center;
    /* This centers the content within the modal header */
}

/* Change the color of the modal title */
.modal-title {
    color: #28A4E2;
    /* Set the title color to the desired value */
}

.go-back-btn {
    border: 1px solid #28A4E2;
    color: #28A4E2;
    width: 130px;
    font-size: 14px;
    font-weight: 500;

}
 
  .spinner {
    width: 50px;
    height: 50px;
    border: 8px solid rgba(0, 0, 0, 0.1);
    border-top: 8px solid #007bff; /* Blue color */
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .loading-text {
    font-size: 16px;
    color: #007bff; /* Blue color matching the spinner */
  }
  