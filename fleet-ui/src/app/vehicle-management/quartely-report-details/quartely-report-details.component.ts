import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { faArrowLeft, faThumbsUp, faThumbsDown, faQuestion, faPaperPlane } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-quartely-report-details',
  templateUrl: './quartely-report-details.component.html',
  styleUrls: ['./quartely-report-details.component.scss']
})
export class QuartelyReportDetailsComponent implements OnInit {
  report: any = {};
  actionComment = '';
  selectedAction = '';
  userRole = '';
  backwardIcon = faArrowLeft;
  approveIcon = faThumbsUp;
  denyIcon = faThumbsDown;
  moreIcon = faQuestion;
  submitIcon = faPaperPlane;
  loggedInUser = '';
  isLoading: boolean = false;
  actionSuccessful = false;
  vehicleActivities: any[] = [];
  maintenanceActivityIds: any[] = [];

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private router: Router,
    private modalService: NgbModal,
    private location: Location
  ) {}

  ngOnInit(): void {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
      this.userRole = parseObj?.data?.user?.role?.name; // Store the user's role
      this.loggedInUser = parseObj.data.user.id;
    }
  
    this.route.paramMap.subscribe((params) => {
      const reportId = params.get('reportId');
      if (reportId) {
        this.fetchReportDetails(reportId);
        this.fetchActivities(reportId);
      }
    });
  }

  isFleetMgtSeniorEngineer(): boolean {
    return this.userRole === 'Fleet Mgt Senior Engineer' && this.selectedAction === 'APPROVED'; // Check if user is Fleet Mgt Senior Engineer
  }

  fetchReportDetails(reportId: string) {
    const url = `${environment.otherUrl}/quarterlyByReportId?id=${reportId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.report = response; 
        console.log(reportId)
        console.log("Report Data:", this.report);
      },
      (error) => {
        console.error('Error fetching report details:', error); // Handle errors
      }
    );
  }

  fetchActivities(reportId: string) {
    const url = `${environment.otherUrl}/AllQuarterlyActivitiesReportId?id=${reportId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.vehicleActivities = response; 
        console.log("Activities:", this.vehicleActivities);
        console.log(reportId)
      },
      (error) => {
        console.error('Error fetching activities:', error); // Handle errors
      }
    );
  }

  setAction(action: string) {
    this.selectedAction = action;
  }

  confirmAction() {
    if (!this.report || !this.report.id || !this.selectedAction) {
      console.error('Required data is missing: report ID or action');
      this.toastr.error('Required data is missing');
      return;
    }
  
    const approvalData = {
      userId: this.loggedInUser,
      quarterlyReportId: this.report.id,
      comments: this.actionComment, 
      decision: this.selectedAction.toUpperCase(), 
    };
  
    console.log("Approval Data:", approvalData);
    this.isLoading = true;
    this.actionSuccessful = false; 
  
    this.http.post(`${environment.baseUrl}/approval/quarterlyReportApproval`, approvalData).subscribe(
      (response) => {
        setTimeout(() => {
          this.isLoading = false; 
          this.actionSuccessful = true; 
          this.toastr.success("Action confirmed successfully");
          console.log("Final response:", response);
  
          setTimeout(() => {
            window.location.reload();
          }, 3000); 
        }, 3000); 
      },
      (error) => {
        this.isLoading = false; 
        this.toastr.error("Error sending approval");
        console.error("Error during approval:", error);
      }
    );  
  }

  approveVehicle() {
    this.setAction('APPROVED');
  }

  denyVehicle() {
    this.setAction('DENIED');
  }

  moreAction() {
    this.setAction('RFAC');
  }

  canSeeActionButtons(): boolean {
    const approvalLevel = this.report?.approvalLevel?.name?.trim(); // Get the approval level from vehicle data
    return this.userRole && approvalLevel && this.userRole === approvalLevel;
  }

  canEditVehicle(): boolean {
    // Check if the status is 'RFAC', user role is 'Institutional Logistics', and approval level is also 'Institutional Logistics'
    return (
      this.report?.status === 'RFAC' &&
      this.userRole === 'Institutional Logistics' &&
      this.report?.approvalLevel?.name === 'Institutional Logistics'
    );
  }

  goBack() {
    this.location.back();
  }

  canApprove(): boolean {
    return this.userRole === "Permanent Secretart";
  }

  canDeny(): boolean {
    return this.userRole === 'Permanent Secretary'; 
  }

  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'bg-success'; // Corresponds to green
        case 'pending':
            return 'bg-secondary'; // Corresponds to yellow/orange
        case 'denied':
            return 'bg-danger'; // Corresponds to red
        case 'rfac':
            return 'bg-info'; // Corresponds to blue
        case 'progress':
            return 'bg-warning'; // Corresponds to blue
        default:
            return ''; // Default or fallback class
    }
  }

  getProgressWidth(status: string): string {
    switch (status.toLowerCase()) {
      case 'approved':
        return '100%'; // Corresponds to green
      case 'pending':
        return '70%'; // Corresponds to yellow/orange
      case 'denied':
        return '100%'; // Corresponds to red
      case 'rfac':
        return '60%'; // Corresponds to blue
      case 'progress':
        return '80%'; // Corresponds to blue
      default:
        return '';
    }
  }
}
