.vehicles-container{
    background-color:#D9D9D94D;
    width: 83%;
    height: 90%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
}
.search-group{
    background-color: #ffffff;
    border-radius: 10px;
    width: 20%;
    height: 35px;
    border: 1px solid #edebf0;
}
.icon{
    background-color: #ffffff;
    color: #28A4E2;
    font-size: 13px;
    border-radius: 10px;
    padding: 9px 0px 7px 9px !important;
    text-align: center;
}
.global-input{
    color: #A098AE;
    background-color: #fff;
    border-radius: 10px;
    font-size: 13px;
}
.sorting-group{
    font-size: 12px;
    background-color: #F9FBFF;
    width: 17%;
    border-radius: 10px;
}
.select{
    font-size: 12px;
    font-weight: bold;
    width: 60%;
    border: none !important;
    background-color: #F9FBFF;
    outline: none !important;
}
.table-title{
    font-size: 18px;
    color: #28A4E2;
    font-weight: 500;
    width: fit-content;
}
.card{
    border: none;
}
th{
    font-size: 12px;
    color: #B5B7C0 !important;
    font-weight: 500;
}
.status-active{
    color: #23DF04;
    font-size: 12px;
    font-weight: 500;

}
.status-inactive{
    color: #DF0404BF;
    font-size: 12px;
    font-weight: 500;
}
.table-header{
    padding: 15px 15px 0px 15px !important;
}
.table-only{
    padding-bottom: 0px !important;
}
.nav{
    padding: 0px 20px;
}
.nav li .pages{
    height: 30px;
    width: 30px;
    border-radius: 50%;
    font-size: 12px;
    padding: 5px;
}
.nav li{
    margin: 0px 5px;
}
.caret{
    background-color: #ffffff;
    border: none;
    font-size: 20px;
    color: #A098AE;
    padding: 5px 0px;
}
.pagination-info{
    font-size: 14px;
    color: #B5B7C0;
}
td{
    font-size: 12px;
    color: #757575;
}
.request-btn{
    background-color: #28A4E2;
    color: #F9FBFF;
    font-weight: 500;
    font-size: 14px;
    height: 10%;
}
.view-btn{
    border: 1px solid #28A4E2;
    color:#28A4E2;
    font-size: 12px;
    font-weight: 500;
}

.edit-btn{
    border: 1px solid rgb(20, 160, 27);
    color: rgb(20, 160, 27);
    font-size: 12px;
    font-weight: 500;
}
.dispose-btn{
    border: 1px solid rgb(224, 158, 34);
    color: rgb(224, 158, 34);
    font-size: 12px;
    font-weight: 500;
}
.report-btn{
    border: 1px solid rgb(224, 34, 164);
    color: rgb(224, 34, 164);
    font-size: 10px;
    font-weight: 600;
}
  
  .status-button {
    border: 2px solid transparent;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 12px;
  
    &.bg-success {
      color: green;
      border-color: green;
    }
  
    &.bg-warning {
      color: orange;
      border-color: orange;
    }
  
    &.bg-danger {
      color: red;
      border-color: red;
    }
  
    &.bg-secondary {
      color: gray;
      border-color: gray;
    }
  
    &.bg-info {
      color: blue;
      border-color: blue;
    }
  }
  .status-registered {
    color: green; 
  }
  .statuses .btn{
    border: none;
    border-radius: 0px;
    font-size: 15px;
    color: #757575;
}
.statuses .btn.active{
    color: #28A4E2;
    font-weight: 500;
    border-bottom: 3px solid #28A4E2;
    transition: border-bottom 0.2s ease-in-out;
}
.no-vehicles-message {
    text-align: center;
    font-size: 24px;
    // color: #dc3545; /* Bootstrap's text-danger color */
    font-weight: bold;
    padding: 40px 0;
  }
  