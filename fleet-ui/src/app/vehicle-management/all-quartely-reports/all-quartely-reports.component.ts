import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faSearch, faCaretLeft, faCaretRight, faCar, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-all-quartely-reports',
  templateUrl: './all-quartely-reports.component.html',
  styleUrls: ['./all-quartely-reports.component.scss']
})
export class AllQuartelyReportsComponent implements OnInit {
  allQuartelyReports: any[] = [];
  filteredQuarterlyReports: any[] = [];
  pageSize: number = 10;
  currentPage: number = 1;
  totalPages: number = 0;
  institutionId: string = '';
  userRole: string = '';
  searchText: string = '';
  faSearch = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  carIcon = faCar;
  displayedReports: any[] = [];
  currentFilter = 'all';
  backwardIcon = faArrowLeft;


  constructor(private http: HttpClient, private router: Router, private location:Location) {}

  ngOnInit() {
    this.getUserDetails();
    this.fetchAllQuartelyReports();
  }

  goBack(){
    this.location.back();
  }

  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id; 
      this.userRole = parsedObj.data.user.role.name; 

      console.log("inst: ", this.institutionId)
      console.log("User: ", this.userRole)
    }
  }

  fetchAllQuartelyReports() {
    const url = `${environment.otherUrl}/allQuarterly`;

    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log(response);

        this.allQuartelyReports = response;
        this.fetchReportsByRole();
        this.applyCurrentFilter();
        this.updateDisplayedReports();
      },
      (error) => {
        console.error('Error fetching quarterly reports:', error);
      }
    );
  }

  fetchReportsByRole() {
    this.filteredQuarterlyReports = [...this.allQuartelyReports];

    switch(this.userRole) {
      case 'Institutional logistics':
        this.filteredQuarterlyReports = this.filteredQuarterlyReports.filter(
          report => report.vehicle.beneficiaryAgencyId === this.institutionId
        );
        break;
    
      case 'Fleet Mgt Senior Engineer' :
        this.filteredQuarterlyReports = this.filteredQuarterlyReports.filter(
          report => report.approvalLevel?.name !== 'Institutional logistics'
        );
        break;
    }
  }

  applyCurrentFilter() {
    switch (this.currentFilter) {
      case 'all':
        this.filteredQuarterlyReports = this.allQuartelyReports;
        break;
      case 'progress':
        this.filteredQuarterlyReports = this.allQuartelyReports.filter(
          (item) => item.reportStatus?.name === 'PENDING' || item.reportStatus?.name === 'PROGRESS' || item.reportStatus?.name === 'RFAC'
        );
        break;
      case 'approved':
        if(this.userRole === 'Institutional logistics') {
          this.filteredQuarterlyReports = this.allQuartelyReports.filter(
            (item) => (item.reportStatus?.name === 'APPROVED' || item.reportStatus?.name === 'DENIED') && item.vehicle.beneficiaryAgencyId === this.institutionId
          );
        } else {
          this.filteredQuarterlyReports = this.allQuartelyReports.filter(
            (item) => item.reportStatus?.name === 'APPROVED'
          );
        }
        break;
      default:
        console.error('Invalid filter type: ', this.currentFilter);
        this.filteredQuarterlyReports = this.allQuartelyReports;
    }

    this.currentPage = 1;
    this.updateDisplayedReports();
  }

  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'bg-success'; // Corresponds to green
        case 'pending':
            return 'bg-secondary'; // Corresponds to yellow/orange
        case 'denied':
            return 'bg-danger'; // Corresponds to red
        case 'rfac':
            return 'bg-info'; // Corresponds to blue
        case 'progress':
            return 'bg-warning'; // Corresponds to blue
        default:
            return ''; // Default or fallback class
    }
}
getProgressWidth(status: string): string {
  switch (status.toLowerCase()) {
    case 'approved':
      return '100%'; // Corresponds to green
    case 'pending':
      return '70%'; // Corresponds to yellow/orange
    case 'denied':
      return '100%'; // Corresponds to red
    case 'rfac':
      return '60%'; // Corresponds to blue
    case 'progress':
      return '80%'; // Corresponds to blue
    default:
      return '';
  }
}

  setFilter(filterType: string) {
    this.currentFilter = filterType;
    this.applyCurrentFilter();
  }

  updateDisplayedReports() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredQuarterlyReports.length);
  
    // Create a separate array for the displayed vehicles based on the filtered data
    this.displayedReports = this.filteredQuarterlyReports.slice(startIndex, endIndex); // Slice within the filtered list
  
    // Recalculate the total number of pages based on the filtered vehicles
    this.totalPages = Math.ceil(this.filteredQuarterlyReports.length / this.pageSize);
  }

  getFirstEntryIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.filteredQuarterlyReports.length);
  }

  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedReports();
    }
  }

  getPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedReports();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedReports();
    }
  }

  sortable(event: any) {
    const selectedField: string = event.target.value;

    if (!selectedField) {
      this.filteredQuarterlyReports.sort((a, b) => a.beneficiaryAgency.localeCompare(b.beneficiaryAgency));
    } else {
      this.filteredQuarterlyReports.sort((a, b) => {
        const fieldA = a[selectedField];
        const fieldB = b[selectedField];
        if (typeof fieldA === 'string' && typeof fieldB === 'string') {
          return fieldA.localeCompare(fieldB);
        }
        return 0;
      });
    }
    this.updateDisplayedReports();
  }

  isLogistics(): boolean {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }

  viewReport(reportId: string): void {
    console.log('Report ID: ', reportId);
    this.router.navigateByUrl(`/vehicle-management/quartely-report-details/${reportId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
}
