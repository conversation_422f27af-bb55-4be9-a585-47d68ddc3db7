<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicles-container">
    <app-breadcrumb></app-breadcrumb>
    <div class="header d-flex flex-row justify-content-between">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
        <h2 class="table-title">All Quartely Reports</h2>

        <ng-container *ngIf="!isLogistics()">
          <div class="search-group d-flex">
            <fa-icon [icon]="faSearch" class="icon"></fa-icon>
            <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText">
          </div>
        </ng-container>

    </div>


    <div class="card">
      <div class="table-header d-flex flex-row justify-content-between">

          <!-- Table Filter -->
          <div class="d-flex flex-row justify-content-start align-items-center statuses">
            <button class="btn btn-outline" (click)="setFilter('all')" [class.active]="currentFilter === 'all'">All</button>
            <button class="btn btn-outline" (click)="setFilter('progress')" [class.active]="currentFilter === 'progress'">Pending Requests</button>
            <button class="btn btn-outline" (click)="setFilter('approved')" [class.active]="currentFilter === 'approved'">Approved</button>
            <!-- <button *ngIf="showReportingVehiclesTab" class="btn btn-outline" (click)="setFilter('reporting')" [class.active]="currentFilter === 'reporting'">Reporting Vehicles</button>         -->
           </div>
          <div class="sorting-group d-flex flex-row p-2">
              <label for="sort-field" class="text-muted p-1">Sort by:</label>
              <select id="sort-field" class="select" (change)="sortable($event)">
                  <option value="">Select Field</option>
                  <option value="isVehicleActive" >Vehicle Active</option>
                  <option value="vehicleStatus">Vehicle Status</option>
                </select>
          </div>
      </div>

      <div class="table-only p-3">

          <table class="table table-stripped">
            <thead>
              <tr>
                <th>Chassis Number</th>
                <th>Transmission Type</th>
                <th>Beneficiary Agency</th>
                <th>Vehicle Status</th>
                <th>Approval Level</th>
                <th>Report Status</th>
                <th>Vehicle Active</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngIf="displayedReports.length <= 0">
                <td colspan="9" class="no-vehicles-message">No Vehicles!!!</td>
              </tr>
              <tr *ngFor="let report of displayedReports">
                <td>{{ report.vehicle.chassisNumber }}</td>
                <td>{{ report.vehicle.transmissionType }}</td>
                <td>{{ report.vehicle.beneficiaryAgency }}</td>
                <td>{{ report.vehicleStatus?.name }}</td>
                <td>{{ report.approvalLevel?.name }}</td>
                <!-- <td>{{ report.reportStatus?.name}}</td> -->
                <td>
                  <div class="progress">
                      <div class="progress-bar"
                           [ngClass]="getStatusButtonClass(report.reportStatus?.name)"
                           role="progressbar"
                           [style.width]="getProgressWidth(report.reportStatus?.name)"
                           aria-valuemin="0"
                           aria-valuemax="100">
                          {{ report.reportStatus?.name}}
                      </div>
                  </div>
              </td>
                <td>
                   <p [ngClass]="{
                    'status-active':report.isVehicleActive,
                    'status-inactive': !report.isVehicleActive
                   }">
                    {{ report.isVehicleActive === true ? 'Active' : 'Inactive'}}
                  </p>
                  </td>

                <td class="d-flex flex-row">
                  <button class="btn view-btn" (click)="viewReport(report.id)">View</button>
                  <!-- <button *ngIf="report.registrationStatus.name == 'RFAC'|| userRole == 'Institutional logistics' || userRole == 'Institutional CBM' || userRole == 'Fleet Mgt Senior Engineer'" type="button" class="btn edit-btn" style="margin: 0px 14px;" (click)="editRegisteredreport(report.id)"> Edit </button> -->
                  <!-- <button *ngIf="userRole == 'Institutional logistics' && report.registrationStatus.name == 'APPROVED'&& report.isDisposalRequestSubmitted === false" class="btn dispose-btn" style="margin: 0px 14px;" (click)="disposereport(report.id)"> Dispose </button> -->
                  <!-- <button *ngIf="userRole == 'Institutional logistics' && report.registrationStatus.name == 'APPROVED'&& report.isDisposalRequestSubmitted === false" class="btn report-btn" style="margin: 0px;" (click) = "reportreport(report.id)">Quarterly Report</button> -->
                </td>
              </tr>
            </tbody>
          </table>
        </div>

      <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
        <div class="pagination-info">
          Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredQuarterlyReports.length }} entries
        </div>
        <ul class="pagination justify-content-center">
          <li class="page-item">
            <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
              <fa-icon [icon]="caretLeft"></fa-icon>
            </button>
          </li>
          <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
            <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
              {{ pageNumber }}
            </button>
          </li>
          <li class="page-item">
            <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
              <fa-icon [icon]="caretRight"></fa-icon>
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
