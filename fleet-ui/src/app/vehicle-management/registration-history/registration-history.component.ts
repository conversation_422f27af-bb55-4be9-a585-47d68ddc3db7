import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-registration-history',
  templateUrl: './registration-history.component.html',
  styleUrl: './registration-history.component.scss'
})
export class RegistrationHistoryComponent {
  actionId = '';
  actionHistory: any[] = [];
  userDetails: { [key: string]: any } = {};
  acquisitionDetails: any;
  acquisitions: any;
  acquisitionStatus = 'Pending';

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private toastr: ToastrService
  ) {}

  ngOnInit() {
    this.route.paramMap.subscribe((params) => {
      const vehicleId = params.get('vehicleId');
      const acquisitionId = params.get('acquisitionId');
      if (vehicleId) {
        this.actionId = vehicleId ;

        this.fetchAcquisitionHistory(vehicleId);
        // this.fetchRequestDetails(vehicleId);
      }
    });
  }

  fetchAcquisitionHistory(actionId: string) {
    const url = `${environment.baseUrl}/approval/getAllHistoryByActionId?actionId=${actionId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.actionHistory = response;
        console.log('regHistory',this.actionHistory)
      },
      (error) => {
        console.error('Error fetching acquisition history:', error);
      });
    }
    
  history = {
    activityPerformed: 'APPROVED' // This should be dynamically assigned
  };



}


