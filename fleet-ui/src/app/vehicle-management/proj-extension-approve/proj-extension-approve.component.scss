// @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');

// * {
//     font-family: 'Poppins', sans-serif;
// }

// .project-extension-container {
//     background-color: #D9D9D94D;
//     height: 90%;
//     width: 83%;
//     right: 0;
//     top: 10%;
//     position: absolute;
//     padding: 3px;
// }

// .card {
//     height: 94%;
//     overflow-y: scroll;
// }

// .card::-webkit-scrollbar {
//     width: 0;
// }

// h4 {
//     color: #28A4E2;
//     font-family: 'Poppins', sans-serif;
//     font-weight: bold;
// }

// label {
//     color: rgb(146, 146, 146);
//     font-weight: bold;
// }

// p {
//     color: black;
// }

// .action-buttons .btn {
//     margin: 10px;
//     color: white;
// }

// .btn-success {
//     background-color: #4BB543;
//     border-color: #4BB543;
// }

// .btn-danger {
//     background-color: #FF4136;
//     border-color: #FF4136;
// }

// .modal-header {
//     border-bottom: none;
// }

// .modal-body {
//     padding: 2rem;
// }

// .instruction-text {
//     color: #666;
//     font-size: 14px;
// }

// .comment-box {
//     height: 150px;
//     margin-top: 10px;
// }

// .loader-wrapper {
//     height: 100px;
// }

// .spinner {
//     border: 8px solid #f3f3f3;
//     border-top: 8px solid #3498db;
//     border-radius: 50%;
//     width: 60px;
//     height: 60px;
//     animation: spin 2s linear infinite;
// }

// @keyframes spin {
//     0% { transform: rotate(0deg); }
//     100% { transform: rotate(360deg); }
// }

// .loading-text {
//     margin-top: 10px;
//     color: #3498db;
//     font-size: 16px;
// }

// .success-wrapper {
//     padding: 2rem;
// }

// .success-wrapper .form-card {
//     margin: 0;
// }

// .fit-image {
//     width: 100%;
//     height: auto;
// }
// .go-back-btn{
//     border: 1px solid #28A4E2;
//     color: #28A4E2;
//     width: 130px;
//     font-size: 14px;
//     font-weight: 500;
  
//   }
//   .project-extension-container {
//     padding-top: 20px;
//   }
  
//   .card {
//     border: 1px solid #ddd;
//     border-radius: 10px;
//   }
  
//   .go-back-btn {
//     background-color: #f0f0f0;
//     color: #000;
//   }
  
//   .action-buttons {
//     gap: 1rem;
//   }
  
//   .btn-success {
//     background-color: #4BB543;
//     border-color: #4BB543;
//   }
  
//   .btn-danger {
//     background-color: #FF4136;
//     border-color: #FF4136;
//   }
  
//   .modal-header {
//     border-bottom: none;
//   }
  
//   .modal-body {
//     padding: 2rem;
//   }
  
//   .instruction-text {
//     color: #666;
//     font-size: 14px;
//   }
  
//   .comment-box {
//     height: 150px;
//     margin-top: 10px;
//   }
  
//   .loader-wrapper {
//     height: 100px;
//   }
  
//   .spinner {
//     border: 8px solid #f3f3f3;
//     border-top: 8px solid #3498db;
//     border-radius: 50%;
//     width: 60px;
//     height: 60px;
//     animation: spin 2s linear infinite;
//   }
  
//   @keyframes spin {
//     0% { transform: rotate(0deg); }
//     100% { transform: rotate(360deg); }
//   }
  
//   .loading-text {
//     margin-top: 10px;
//     color: #3498db;
//     font-size: 16px;
//   }
  
//   .success-wrapper {
//     padding: 2rem;
//   }
  
//   .success-wrapper .form-card {
//     margin: 0;
//   }
  
//   .fit-image {
//     width: 100%;
//     height: auto;
//   }
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');

*{
    font-family: 'Poppins', sans-serif;
   
  }
.project-extension-container{
    background-color: #D9D9D94D;
    height: 90%;
    width: 83%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 3px;
}
.card{
    height: 94%;
    overflow-y: scroll;
}
.card::-webkit-scrollbar {
    width: 0;  
}
.card-header{
    background-color: #28A4E2;
    color:  white;
    position: fixed;
    z-index: 1;
    width: 80%;
}
h4{
    color: #28A4E2;
    font-family: 'Poppins', sans-serif;
    font-weight: bold;
}
label{
    // color: #757575;
    color: rgb(146, 146, 146);
    font-weight: bold;
}
span,p{
    // color: #757575;
    color: black;
}
.buttons{
    right: 0;
}
.separator-line {
    border-top: 2px solid rgb(146, 146, 146);
  }
.action, .btn {
    margin: 10px;
    color: white;
   
}
.btn-success{
    background-color: white;
    color: green;
    border: 1px solid green;
}
.btn-danger{
    background-color: white;
    color: red;
    border: 1px solid red;
}
.btn-secondary{
    background-color: white;
    color: grey;
    border: 1px solid grey;
}
.btn-warning{
    background-color: white;
    color: yellow;
    border: 1px solid yellow;

}
.btn-primary,.btn-info{
    background-color: white;
    color: #28A4E2;
    border: 1px sold #28A4E2;
}
.notification{
    height: 30px;
    width: 30px;
    border-radius: 50%;
   

}

.bellIcon{
    color: #28A4E2;
    font-size: large;
}
.btn-approve{
    background-color: rgb(48, 178, 48);
    color: white;
}
.btn-deny{
    color: white;
    background-color: rgb(153, 20, 20);
}
.modal-header {
    justify-content: center; /* This centers the content within the modal header */
  }
  
  /* Change the color of the modal title */
  .modal-title {
    color: #28A4E2; /* Set the title color to the desired value */
  }
  .go-back-btn{
    border: 1px solid #28A4E2;
    color: #28A4E2;
    width: 130px;
    font-size: 14px;
    font-weight: 500;
  
  }