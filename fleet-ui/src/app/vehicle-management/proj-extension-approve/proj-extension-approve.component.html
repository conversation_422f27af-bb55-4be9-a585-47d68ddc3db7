<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
  
    <div class="project-extension-container">
      <div class="card m-3 p-3">
        <button class="btn go-back-btn" (click)="goBack()">
          <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
        </button>
        <div class="row">
          <div class="col-md-6">
            <div class="project-extension-details" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Project Extension Information</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label>New Project End Date</label>
                  <p>{{ projectExtension.newProjectEndDate || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Description</label>
                  <p>{{ projectExtension.description || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Requested Date</label>
                  <p>{{ projectExtension.requested_date || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Approval Level</label>
                  <p>{{ projectExtension.approvalLevel?.name || 'N/A' }}</p>
                </div>
              </div>
            </div>
            
            <div class="vehicle-details mt-3" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Vehicle Information</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label>Chassis Number</label>
                  <p>{{ projectExtension.vehicle?.chassisNumber || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Beneficiary</label>
                  <p>{{ projectExtension.vehicle?.beneficiaryAgency || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Engine Number</label>
                  <p>{{ projectExtension.vehicle?.engineNumber || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Transmission</label>
                  <p>{{ projectExtension.vehicle?.transmissionType || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Year</label>
                  <p>{{ projectExtension.vehicle?.manufacturingYear || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>License Plate</label>
                  <p>{{ projectExtension.vehicle?.plateNumber || 'N/A' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- Action Buttons -->
        <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canSeeActionButtons()">
          <button class="btn btn-success" (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
            <fa-icon [icon]="approveIcon" class="pe-1"></fa-icon>Approve
          </button>
          <button class="btn btn-danger" (click)="setAction('DENIED')" data-bs-toggle="modal" data-bs-target="#actionModal">
            <fa-icon [icon]="denyIcon" class="pe-1"></fa-icon> Deny
          </button>
        </div>
      </div>
    </div>
  
    <!-- Modal for Action Confirmation -->
    <div class="modal fade" id="actionModal" aria-labelledby="actionModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div *ngIf="isLoading" class="loader-wrapper d-flex justify-content-center align-items-center flex-column m-3">
            <app-spinner></app-spinner>
            <h2 class="loading-text">Loading...</h2>
          </div>
  
          <!-- Success Message -->
          <div *ngIf="!isLoading && actionSuccessful" class="success-wrapper text-center">
            <div class="form-card">
              <h2 class="text-success">Success</h2>
              <div class="row justify-content-center">
                <div class="col-md-4">
                  <img src="https://img.icons8.com/color/96/000000/ok--v2.png" class="fit-image" alt="Success Icon">
                </div>
              </div>
              <br><br>
            </div>
          </div>
  
          <div class="modal-body" *ngIf="!isLoading && !actionSuccessful">
            <p class="instruction-text">Add a comment if needed, then click "Confirm" to proceed.</p>
            <textarea class="form-control comment-box" [(ngModel)]="actionComment" placeholder="Add your comments here..."></textarea>
          </div>
          <div class="modal-footer" *ngIf="!isLoading && !actionSuccessful">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-info" (click)="confirmAction()">Confirm</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  