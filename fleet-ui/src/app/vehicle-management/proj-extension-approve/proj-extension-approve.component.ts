import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { faArrowLeft, faThumbsUp, faThumbsDown, faQuestion ,faPaperPlane} from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-proj-extension-approve',
  templateUrl: './proj-extension-approve.component.html',
  styleUrls: ['./proj-extension-approve.component.scss']
})
export class ProjExtensionApproveComponent implements OnInit {
  projectExtension: any = {};
  actionComment = '';
  selectedAction = '';
  userRole = '';
  loggedInUser = '';
  backwardIcon = faArrowLeft;
approveIcon = faThumbsUp;
denyIcon = faThumbsDown;
moreIcon = faQuestion;
submitIcon=faPaperPlane
  isLoading: boolean = false;
  actionSuccessful = false;

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private location: Location
  ) {}

  ngOnInit() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
      this.userRole = parseObj?.data?.user?.role?.name;
      this.loggedInUser = parseObj.data.user.id;
    }

    this.route.paramMap.subscribe((params) => {
      const reportId = params.get('reportId');
      if (reportId) {
        this.fetchProjectExtensionDetails(reportId);
      } else {
        console.error('No report ID found in route parameters');
        this.toastr.error('No report ID found in route parameters');
      }
    });
  }

  fetchProjectExtensionDetails(reportId: string) {
    const url = `${environment.otherUrl}/ProjectExtensionById?id=${reportId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.projectExtension = response;
        console.log("Project Extension Data:", response);
      },
      (error) => {
        console.error('Error fetching project extension details:', error);
        this.toastr.error('Error fetching project extension details');
      }
    );
  }

  setAction(action: string) {
    this.selectedAction = action;
  }

  confirmAction() {
    if (!this.projectExtension || !this.projectExtension.id || !this.selectedAction) {
      console.error('Required data is missing: project extension ID or action');
      this.toastr.error('Required data is missing');
      return;
    }

    const approvalData = {
      userId: this.loggedInUser,
      projectExtensionId: this.projectExtension.id,
      decision: this.selectedAction.toUpperCase(),
      comments: this.actionComment
    };

    console.log("Approval Data:", approvalData);
    this.isLoading = true;
    this.actionSuccessful = false;

    this.http.post(`${environment.baseUrl}/approval/ProjectExtensionApproval`, approvalData).subscribe(
      (response) => {
        setTimeout(() => {
          this.isLoading = false;
          this.actionSuccessful = true;
          this.toastr.success("Action confirmed successfully");
          console.log("Response:", response);

          setTimeout(() => {
            window.location.reload();
          }, 3000);
        }, 3000);
      },
      (error) => {
        this.isLoading = false;
        this.toastr.error("Error sending approval");
        console.error("Error during approval:", error);
      }
    );
  }

  goBack() {
    this.location.back();
  }

  canSeeActionButtons(): boolean {
    const approvalLevel = this.projectExtension?.approvalLevel?.name?.trim();
    return this.userRole && approvalLevel && this.userRole === approvalLevel;
  }
}


