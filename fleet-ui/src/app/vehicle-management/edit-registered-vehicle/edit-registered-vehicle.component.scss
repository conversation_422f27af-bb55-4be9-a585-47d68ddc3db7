.page{
    background-color:#D9D9D94D;
    width: 83% !important;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
}
.form-container::-webkit-scrollbar{
    width: 0;
}
h1{
    font-size: 20px;
    color: #28A4E2;
    font-weight: 500;
    margin-bottom: 5px;
}
h2{
    font-size: 16px;
    color: #28A4E2;
    font-weight: 500;
}
p{
    font-size: 13px;
    color: #999797;
}
.card {
    z-index: 0;
    border: none;
    border-radius: 0.5rem;
    position: relative;
    padding: 15px;
}
label{
    color: #000000AB;
    font-size: 13px;
    padding-bottom: 8px;
}
input, select, textarea {
    border: 1px solid #eaeaf5;
    font-size: 13px;
    border-radius: 8px;
}
.submit-btn{
    background-color: #28A4E2;
    color: #ffffff;
}
.document-container {
    margin-bottom: 10px; 
  }
  
  .document {
    display: flex; 
    align-items: center; 
    padding: 10px 5px; 
    border-radius: 5px; 
    background-color: #f5f5f5; 
  }
  
  .file-icon {
    color: #007bff; 
    margin-right: 10px; 
  }
  
  .file-link {
    color: #007bff; 
    text-decoration: none; 
  }
  
  .file-link:hover {
    text-decoration: underline; 
  }