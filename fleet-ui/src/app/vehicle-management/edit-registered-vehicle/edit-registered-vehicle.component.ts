import { Component } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl, FormArray } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import {faArrowLeft, faFileArchive, faMinus, faPlus } from '@fortawesome/free-solid-svg-icons'

@Component({
  selector: 'app-edit-registered-vehicle',
  templateUrl: './edit-registered-vehicle.component.html',
  styleUrl: './edit-registered-vehicle.component.scss'
})
export class EditRegisteredVehicleComponent {
  editRegisteredVehicleForm !: FormGroup;
  vehicleModels: any[] = [];
  vehicleManufactures: any[] =[];
  beneficiaryAgencies: any[] = [];
  dropdownConfig: any = {
    displayKey: "name",   // Display the name of the institution
    valueField: "id",
    search: true,
    height: '300px', // set the height
    placeholder: 'Select', // placeholder text
    customComparator: () => {}, // optional, for custom sorting
    limitTo: 0, // number of items to display in the dropdown. 0 = all
    moreText: 'more', // text for more
    noResultsFound: 'No results found!', // text for no results
    searchPlaceholder: 'Search...',
    clearOnSelection: true,
    inputDirection: 'ltr',
};


  ownerships: any[] = [];
  vehicleTypes: any[] = [];
  vehicleStatuses: any[] = [];
  vehicleId: any;
  ownershipType: any;
  vehicleType: any;
  backwardIcon = faArrowLeft;
  years: number[] = [];
  files!: FormArray;
      selectedFileName: any;
      selectedFiles: { [key: string]: File } = {};
      documents: any[] = [];
      fileIcon = faFileArchive;
      plusIcon = faPlus;
      minusIcon = faMinus;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ){
    this.editRegisteredVehicleForm = this.formBuilder.group({
      beneficiaryAgencyId: ['', Validators.required],
      beneficiaryAgency:[''],
      ownershipType: ['', Validators.required],
      vehicleType: ['', Validators.required],
      fuelType: ['', Validators.required],
      plateNumber: ['', Validators.required],
      vehicleManufacture: ['', Validators.required],
      vehicleModel: [''],
      chassisNumber: ['', [Validators.required, Validators.maxLength(50)]],
      engineNumber: ['', Validators.maxLength(50)],
      transmissionType: ['', Validators.required],
      manufactureYear: ['', Validators.required],
      odometerReading: ['', [ Validators.maxLength(50)]],
      acquisitionDate: ['', Validators.required],
      invoiceNumber: ['', [Validators.maxLength(50)]],
      invoiceDate: [''],
      customsDeclarationNumber: ['', [Validators.maxLength(50)]],
      customsDeclarationDate: [''],
      declaredAmount: ['', [Validators.maxLength(50), Validators.required]],
      projectName: ['', Validators.maxLength(50)],
      projectDescription: [''],
      projectStartDate: [''],
      projectEndDate: [''],
      vehicleStatus: ['', Validators.required],
      isVehicleActive: [true, Validators.required],
      files: this.formBuilder.array([this.createFileFormGroup()])
    });
    this.files = this.editRegisteredVehicleForm.get('files') as FormArray;
  }

  goBack(){
    this.location.back();
  }

  isFieldRequired(fieldName: string): boolean {
    const control = this.editRegisteredVehicleForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }
  ngOnInit() {
    this.fetchVehicleModels();
    this.fetchVehicleManufactures();
    // this.fetchAcquisitionId();
    this.fetchBeneficiaryAgencies();
    this.fetchOwnershipTypes();
    this.fetchVehicleTypes();
    this.getVehicleId();
    this.fetchVehicleStatus();
    this.populateYears();
    this.fetchDocuments(this.vehicleId);

    this.editRegisteredVehicleForm.get('manufactureYear')?.valueChanges.subscribe((newYear) => {
      this.onManufactureYearChanged(newYear);
    });
  }

  fetchDocuments(vehicleId: string) {
    const url = `${environment.fileUrl}/${vehicleId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.documents = response;
        console.log(this.documents)// Assuming 'documents' is an array property in your component
      },
      (error) => {
        console.error('Error fetching documents:', error);
      }
    );
  }

  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }

  removeFile(index: number): void {
    this.files.removeAt(index);
  }

  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: [''],
      applicationId: ['']
    });
  }

  fetchBase64Data(fileName: string) {
    const url = `${environment.fileUrl}/${fileName}/base64`;
    this.http.get<any>(url).subscribe(
        (response) => {
            // Decode the Base64 data and display it in a new window
            const binaryData = atob(response.base64Data);
            const arrayBuffer = new ArrayBuffer(binaryData.length);
            const uint8Array = new Uint8Array(arrayBuffer);
            for (let i = 0; i < binaryData.length; i++) {
                uint8Array[i] = binaryData.charCodeAt(i);
            }
            const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            window.open(url);
        },
        (error) => {
            console.error('Error fetching Base64 data:', error);
        }
    );
  }

  onFileChangee(event: any, index: number): void {
    event.preventDefault();
    const file = event.target.files[0];
    if (file) {
      const fileControl = this.files.at(index).get('file');
      if (file.type !== 'application/pdf') {
        this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
        if (fileControl) {
          fileControl.setValue(null);
        }
        event.target.value = '';
        return;
      }
      if (fileControl) {
        fileControl.setValue(file);
      }
      this.selectedFileName = file.name;
    }
  }

  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitAdditionalFile(auctionId: any): void {
    const fileUploadDataFormArray = this.editRegisteredVehicleForm.get('files') as FormArray;
    fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
      if (control instanceof FormGroup) {
        const fileItem = control.value;
        if (fileItem.file) {
          const fileFormData = new FormData();
          const file = fileItem.file as File;

          console.log('File name:', file.name);
          console.log('File size:', file.size);
          console.log('File type:', file.type)

          if (this.validateFile(file)) {
            fileFormData.append('file', file);
            fileFormData.append('documentDescription', fileItem.documentDescription);
            fileFormData.append('applicationId', auctionId);

            this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
              (response) => {
                console.log('API Response:', response);
                console.log("File uploaded successfully");
                this.editRegisteredVehicleForm.reset();
              },
              (error) => {
                let errorMessage = 'An unexpected error occurred.';
                // let errorMessage = 'Please fill in all fields to submit';

                if (error && error.error && error.error.message) {
                  errorMessage = error.error.message;
                }
                console.error('API Error:', errorMessage);
                // this.toastr.error("An error occurred!!", 'Error');
                this.toastr.error("An error occurred!!", 'Error');
              }
            );
          } else {
            console.warn('File size or type is invalid.');
          }
        } else {
          console.log('No file selected...');
        }
      }
    });
  }

  populateYears() {
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= currentYear -25; year--) {

      this.years.push(year);
    }
  }

  getFullDate() {
    const year = this.editRegisteredVehicleForm.get('manufactureYear')?.value;
    if (year) {
      const fullDate = `${year}-01-01`;
      console.log('Full Date:', fullDate);
      return fullDate;
    }
    return null;
  }

  fetchVehicleStatus(): void {
    const url = `${environment.otherUrl}/vehicle-management/vehicleStatus`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        const filteredResponse = response.filter(
          (type) => type.name === 'GOOD CONDITION' || type.name === 'BAD CONDITION'
        );
        this.vehicleStatuses = filteredResponse.map((type) => ({ id: type.id, name: type.name }));
      }
    );
  }

  fetchVehicleModels(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleModels`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleModels = response.map(vehicleModel => ({ id: vehicleModel.id, name: vehicleModel.name }));
      },
      (error) => {
        console.error('Error fetching vehicle models:', error)
      }
    );
  }

  fetchVehicleManufactures(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleManufacture`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleManufactures = response.map(vehicleManufacture => ({ id: vehicleManufacture.id, name: vehicleManufacture.name }));
      },
      (error) => {
        console.error('Error fetching vehicle manufactures:', error)
      }
    );
  }

  fetchBeneficiaryAgencies(): void {
    const apiUrl = `${environment.baseUrl}/user-management/institutions`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.beneficiaryAgencies = response.map(institution => ({ id: institution.id, name: institution.name }));
      },
      (error) => {
        console.error('Error fetching beneficiary agencies:', error);
      }
    );
  }


  fetchOwnershipTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/ownershipTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.ownerships = response.map(ownership => ({ id: ownership.id, name: ownership.name }));
      },
      (error) => {
        console.error('Error fetching ownershipTypes:', error)
      }
    );
  }
  getVehicleTypeName(vehicleTypeId: number): string | null {
    const vehicleType = this.vehicleTypes.find(type => type.id === vehicleTypeId);
    return vehicleType ? vehicleType.name : null;
  }
  getOwnershipTypeName(ownershipTypeId: number): string | null {
    const ownershipType = this.ownerships.find(type => type.id === ownershipTypeId);
    return ownershipType ? ownershipType.name : null;
  }

  fetchVehicleTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleTypes = response.map(vehicleType => ({ id: vehicleType.id, name: vehicleType.name }));
      },
      (error) => {
        console.error('Error fetching vehicleTypes:', error)
      }
    );
  }

  getVehicleId(): void{
    this.route.params.subscribe(params => {
      if (params['vehicleId']) {
        this.vehicleId = params['vehicleId'];
        this.fetchExistingData(this.vehicleId);
      }
    })
  }

  fetchExistingData(vehicleId: string): void {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    this.http.get<any>(url).subscribe(response => {


      console.log(response);

      this.ownershipType = response.ownershipType?.id;
      this.vehicleType = response.vehicleType?.id;

      this.editRegisteredVehicleForm.patchValue({
        beneficiaryAgencyId: response.beneficiaryAgencyId,
        beneficiaryAgency: response.beneficiaryAgency,
        ownershipType: response.ownershipType?.id,
        plateNumber: response.plateNumber,
        fuelType: response.fuelType,
        vehicleType: response.vehicleType?.id,
        vehicleManufacture: response.vehicleManufacture?.id,
        vehicleModel: response.vehicleModel?.id,
        chassisNumber: response.chassisNumber,
        engineNumber: response.engineNumber,
        transmissionType: response.transmissionType,
        manufactureYear: response.manufacturingYear,
        odometerReading: response.odometerReading,
        acquisitionDate: response.acquisitionDate,
        invoiceNumber: response.invoiceNumber,
        invoiceDate: response.invoiceDate,
        customsDeclarationNumber: response.customsDeclarationNumber,
        customsDeclarationDate: response.customsDeclarationDate,
        declaredAmount: response.declaredAmount,
        vehicleStatus: response.vehicleStatus?.id,
        isVehicleActive: response.isVehicleActive ? 'true' : 'false',
        projectName: response.projectName,
        projectDescription: response.projectDescription,
        projectStartDate: response.projectStartDate,
        projectEndDate: response.projectEndDate
      });

      console.log("Form values after patching:", this.editRegisteredVehicleForm.value);
    }, error => {
      console.error("Error fetching vehicle data:", error);
    });
  }
  onManufactureYearChanged(event: any) {
    if (event && event.target) {
      const selectedYear = event.target.value;
      console.log('Selected Year:', selectedYear);
    } else {
      console.warn('Manufacture year event is null or undefined.');
    }
  }

  updateVehicle(){

    if (this.editRegisteredVehicleForm.valid) {

      const additionalFilesArray = this.editRegisteredVehicleForm.get('files') as FormArray;

      const manufactureYearFormatted = this.getFullDate();
      this.editRegisteredVehicleForm.patchValue({
        manufactureYear: manufactureYearFormatted // Format the year as a full date
      });
      const vehicleDetails = {...this.editRegisteredVehicleForm.value};

      console.log(JSON.stringify(vehicleDetails));

      this.http.patch(`${environment.otherUrl}/updateRegistredVehicle/${this.vehicleId}`, vehicleDetails).subscribe(
        (response: any) => {
          if (additionalFilesArray && additionalFilesArray.length > 0) {
            this.submitAdditionalFile(this.vehicleId);
          }
          console.log('Vehicle Updated Successfully: ', response);
          this.toastr.success('Vehicle Updated Successfuly');
          this.router.navigate([`/vehicle-management/vehicle-details/${this.vehicleId}`])
        },
        (error) => {
          console.error('Error Updating Vehicle details: ', error);
          // this.toastr.error('An error occured while updating vehicle detail', 'Error');
          this.toastr.error('Please fill in all required fields to submit', 'Error');
        }
      )
    } else {
      this.toastr.error('Please fill in all fields to submit', 'Error');
    }
  }
}
