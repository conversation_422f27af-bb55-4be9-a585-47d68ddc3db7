<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <div class="header d-flex flex-row justify-content-between w-50">
            <button class="btn go-back-btn" (click)="goBack()">
                <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
              </button>
            <h1>Update Registered Vehicle</h1>
        </div>

        <div class="card">
            <p>Fill in vehicle details issued by RRA to get the plate number and pink card.</p>
            <div class="row form-container">
                <form id="registerForm" [formGroup]="editRegisteredVehicleForm" class="text-center" method="post" (ngSubmit)="updateVehicle()">
                    <div class="row mt-4">
                      <div class="form-group col-md-4">
                        <label for="beneficiaryAgencyId" class="required-label">Beneficiary Agency</label>
                        <ngx-select-dropdown
                          [options]="beneficiaryAgencies"
                          [config]="dropdownConfig"
                          formControlName="beneficiaryAgency"
                          class="custom-dropdown w-100">
                        </ngx-select-dropdown>
                      </div>


                        <div class="form-group col-md-4">
                            <label for="ownershipType" [ngClass]="{'required-label': isFieldRequired('ownershipType')}">Ownership Type</label>
                            <select id="ownershipType" class="form-control" formControlName="ownershipType">
                                <option [value]="this.ownershipType">{{ getOwnershipTypeName(this.ownershipType) }}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4" >
                            <label for="vehicleType" [ngClass]="{'required-label': isFieldRequired('vehicleType')}">Vehicle Type</label>
                            <select id="vehicleType" class="form-control" formControlName="vehicleType">
                                <!-- <option [value]="this.vehicleType">{{ getVehicleTypeName(this.vehicleType) }}</option> -->
                                <option *ngFor="let vehicleType of vehicleTypes" [value]="vehicleType.id">{{vehicleType.name}}</option>
                            </select>
                          </div>
                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="vehicleManufacture" [ngClass]="{'required-label': isFieldRequired('vehicleManufacture')}">Vehicle Manufacture</label>
                            <select class="form-control" formControlName="vehicleManufacture" id="vehicleManufacture">
                                <option *ngFor="let vehicleManufacture of vehicleManufactures" [value]="vehicleManufacture.id">{{vehicleManufacture.name}}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="vehicleModel" [ngClass]="{'required-label': isFieldRequired('vehicleModel')}">Vehicle Model</label>
                            <select class="form-control" formControlName="vehicleModel" id="vehicleModel">
                                <option *ngFor="let vehicleModel of vehicleModels" [value]="vehicleModel.id">{{vehicleModel.name}}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="chassisNumber" [ngClass]="{'required-label': isFieldRequired('chassisNumber')}">Chassis Number</label>
                            <input type="text" class="form-control" id="chassisNumber" formControlName="chassisNumber">
                        </div>

                    </div>

                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="plateNumber" [ngClass]="{'required-label': isFieldRequired('plateNumber')}">Plate Number</label>
                            <input type="text" class="form-control" id="plateNumber" formControlName="plateNumber">
                        </div>

                        <div class="form-group col-md-4">
                            <label for="fuelType" [ngClass]="{'required-label': isFieldRequired('fuelType')}">Fuel Type</label>
                            <select id="fuelType" formControlName="fuelType" class="form-control">
                                <option value="Diesel">Diesel</option>
                                <option value="Gasoline">Gasoline</option>
                                <option value="LPG">LPG</option>
                                <option value="Gasoline and electric">Gasoline and electric</option>
                            </select>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="vehicleStatus" [ngClass]="{'required-label': isFieldRequired('vehicleStatus')}">Vehicle Status</label>
                            <select id="vehicleStatus" class="form-control" formControlName="vehicleStatus">
                              <option *ngFor="let status of vehicleStatuses" [value]="status.id"> {{ status.name }} </option>
                            </select>

                        </div>


                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="engineNumber">Engine Number</label>
                            <input type="text" class="form-control" id="engineNumber" formControlName="engineNumber">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="transmissionType" [ngClass]="{'required-label': isFieldRequired('transmissionType')}">Transmission Type</label>
                            <input type="text" class="form-control" id="transmissionType" formControlName="transmissionType">
                        </div>
                        <div class="form-group col-md-4">
                          <label for="manufactureYear" [ngClass]="{'required-label': isFieldRequired('manufactureYear')}">
                            Manufacture Year
                          </label>
                          <select class="form-control" id="manufactureYear" formControlName="manufactureYear" (change)="onManufactureYearChanged($event)">
                            <option *ngFor="let year of years" [value]="year">{{ year }}</option>
                          </select>
                        </div>



                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="odometerReading" [ngClass]="{'required-label': isFieldRequired('odometerReading')}">Odometer Reading</label>
                            <input type="text" class="form-control" id="odometerReading" formControlName="odometerReading">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="acquisitionDate" [ngClass]="{'required-label': isFieldRequired('acquisitionDate')}">Acquisiton Date</label>
                            <input type="date" class="form-control" id="acquisitionDate" formControlName="acquisitionDate">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="invoiceNumber" [ngClass]="{'required-label': isFieldRequired('invoiceNumber')}">Invoice Number</label>
                            <input type="text" class="form-control" id="invoiceNumber" formControlName="invoiceNumber">
                        </div>

                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="invoiceDate" [ngClass]="{'required-label': isFieldRequired('invoiceDate')}">Invoice Date</label>
                            <input type="date" class="form-control" id="invoiceDate" formControlName="invoiceDate">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="customsDeclarationNumber" [ngClass]="{'required-label': isFieldRequired('customsDeclarationNumber')}">Customs Declaration Number</label>
                            <input type="text" class="form-control" id="customsDeclarationNumber" formControlName="customsDeclarationNumber">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="customsDeclarationDate" [ngClass]="{'required-label': isFieldRequired('customsDeclarationDate')}">Customs Declaration Date</label>
                            <input type="date" class="form-control" id="customsDeclarationDate" formControlName="customsDeclarationDate">
                        </div>

                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="declaredAmount" [ngClass]="{'required-label': isFieldRequired('declaredAmount')}">Declared Amount</label>
                            <input type="number" class="form-control" id="declaredAmount" formControlName="declaredAmount">
                        </div>

                        <div class="form-group col-md-4">
                          <label for="isVehicleActive" [ngClass]="{'required-label': isFieldRequired('isVehicleActive')}">Is the vehicle still in use?</label>
                          <div class="form-check form-check-inline">
                              <input class="form-check-input" type="radio" id="isVehicleActiveYes" value="true" formControlName="isVehicleActive">
                              <label class="form-check-label" for="isVehicleActiveYes">Yes</label>
                          </div>
                          <div class="form-check form-check-inline">
                              <input class="form-check-input" type="radio" id="isVehicleActiveNo" value="false" formControlName="isVehicleActive">
                              <label class="form-check-label" for="isVehicleActiveNo">No</label>
                          </div>
                      </div>


                        <div class="form-group col-md-4"  *ngIf="editRegisteredVehicleForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">
                            <label for="projectName">Project Name</label>
                            <input type="text" class="form-control" id="projectName" formControlName="projectName" readonly>
                        </div>

                    </div>

                    <div class="row mt-4"  *ngIf="editRegisteredVehicleForm.controls['ownershipType'].value === '639bf044-1cbb-4954-b170-9843347073ca'">
                        <div class="form-group col-md-4">
                            <label for="projectDescription">Project Description</label>
                            <input type="text" class="form-control" id="projectDescription" formControlName="projectDescription" readonly>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="projectStartDate">Project Start Date</label>
                            <input type="date" class="form-control" id="projectStartDate" formControlName="projectStartDate" readonly>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="projectEndDate">Project End Date</label>
                            <input type="date" class="form-control" id="projectEndDate" formControlName="projectEndDate" readonly>
                        </div>

                    </div>

                    <!-- <h2>File Upload Section</h2> -->
                    <!-- <div class="row" *ngIf="documents.length > 0">
                        <div class="col-md-12">
                          <p>These are the existing documents</p>
                          <div class="document-container" *ngFor="let document of documents; let i = index">

                            <div class="row" *ngIf="i % 2 === 0">
                              <div class="col-md-6" *ngIf="documents[i]">
                                <div class="document">
                                  <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                                  <a style="cursor: pointer;" (click)="fetchBase64Data(documents[i].fileName)" class="file-link">{{ documents[i].fileName || 'N/A' }}</a>
                                </div>
                              </div>


                              <div class="col-md-6" *ngIf="documents[i + 1]">
                                <div class="document">
                                  <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                                  <a style="cursor: pointer;" (click)="fetchBase64Data(documents[i + 1].fileName)" class="file-link">{{ documents[i + 1].fileName || 'N/A' }}</a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div> -->

                    <!-- Dynamic File Upload -->
                    <!-- <div class="row" formArrayName="files">
                        <h2 class="text-warning">Need to upload other files?</h2>

                                    <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                                        <div class="form-group col-md-6">
                                          <input type="file" (change)="onFileChangee($event, i)" formControlName="file" class="form-control" />
                                        </div>
                                        <div class="form-group col-md-6">
                                          <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                                        </div>

                                        <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)">
                                          <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                                      </button>
                                      </div>
                                      <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                                        <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add New File
                                      </button>
                      </div> -->
                    <div class="row mt-4">
                        <div class="col-md-8"></div>
                        <div class="col-md-4 buttons">
                            <button type="submit" class="btn submit-btn col-md-9 float-end">Update Vehicle</button>
                          </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
