<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="operations-container">
    <div class="header d-flex flex-row justify-content-between w-50">
      <button class="btn go-back-btn" (click)="backToFirstTable()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
      <h2 class="table-title">OPERATIONS</h2>
    </div>

    <div class="card">
      <div class="table-header">
        <div class="main-container">
          <div class="row ">
            <div class="date-range-filter">
              <div class="form-group col-md-6">
                <p for="startDate">Start Date</p>
                <input type="date" id="startDate" class="form-control" [(ngModel)]="startDate">
              </div>
              <div class="form-group col-md-6">
                <p for="endDate">End Date</p>
                <input type="date" id="endDate" class="form-control" [(ngModel)]="endDate">
              </div>
              <button class="btn go-back-btn mt-5" (click)="filterByDateRange()">Filter</button>
            </div>

            <!-- First Table: Summary of Operations -->
            <div class="table-header d-flex flex-row p-3" >

              <div class="row d-flex ms-auto">
                <div class="btn-group">
                  <div class="dropdown">
                    <button class="btn view-btn dropdown-toggle" type="button" id="dropdownMenuButton"
                      data-bs-toggle="dropdown" aria-expanded="false">Download Report</button> 
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                      <li><a class="dropdown-item" (click)="exportVehiclesToExcel('csv')">Download CSV</a></li>
                      <li><a class="dropdown-item" (click)="exportVehiclesToExcel('pdf')">Download PDF</a></li>
                    </ul>
                  </div>

                  <!-- <button class="btn edit-btn d-flex align-items-center ml-3" (click)="printVehicles()">Print <fa-icon
                      [icon]="printIcon" style="margin-left:2px"></fa-icon></button> -->
                </div>
              </div>
            </div>
            <div  *ngIf="showFirstTable" >

              <table class="table" id="printableArea" *ngIf="showFirstTable && !isLogisticCbm()">
                <thead>
                  <tr>
                    <th>Institution</th>
                    <th>Maintenance Activity</th>
                    <th>Total Number of Vehicles</th>
                    <th>Total Cost</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let operation of displayedOperations" (click)="showDetails(operation)">
                    <td>{{ operation.institutionName }}</td>
                    <td>
                      <ul>
                        <li *ngFor="let activity of operation.activities; let i = index">
                          {{ activity.activityName }}<span *ngIf="i < operation.activities.length - 1">, </span>
                        </li>
                      </ul>
                    </td>
                    <td class="text-center">{{ operation.totalVehicles }}</td>
                    <td>{{ operation.totalCost }}</td>
                  </tr>
                </tbody>
                
              </table>
              <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                <div class="pagination-info">
                    Showing {{ getFirstEntryIndexFirstTable() }} - {{ getLastEntryIndexFirstTable() }} of {{ allOperations.length }} entries
                </div>
                <ul class="pagination justify-content-center">
                    <li class="page-item">
                        <button class="caret" (click)="previousPageFirstTable()" [disabled]="currentPageFirstTable === 1">
                            <fa-icon [icon]="caretLeft"></fa-icon>
                        </button>
                    </li>
                    <li class="page-item" *ngFor="let pageNumber of getPageNumbersFirstTable()">
                        <button class="page-link pages" [class.active]="currentPageFirstTable === pageNumber" (click)="goToPageFirstTable(pageNumber)">
                            {{ pageNumber }}
                        </button>
                    </li>
                    <li class="page-item">
                        <button class="caret" (click)="nextPageFirstTable()" [disabled]="currentPageFirstTable === totalPagesFirstTable">
                            <fa-icon [icon]="caretRight"></fa-icon>
                        </button>
                    </li>
                </ul>
            </nav>
            
            </div>

            <!-- Second Table: Summarized Beneficiary Report -->
            <div *ngIf="!showFirstTable && !selectedVehicleOperation">
              <div class="table-header d-flex flex-row p-3">
                <h2 class="table-title">Summarized {{selectedAgency}} Report</h2>
                <button *ngIf="!isLogisticCbm()" class="btn go-back-btn ms-auto" (click)="backToFirstTable()">
                  <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
                </button>
              </div>

              <table class="table" id="printableArea">
                <thead>
                  <tr>
                    <th>Plate Number</th>
                    <th>Maintenance Activity</th>
                    <th>Total Cost</th>
                    
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let operation of displayedGroupedOperations" (click)="showVehicleReport(operation)">
                    <td>{{ operation.PlateNumber }}</td>
                    <td>
                      <ul>
                        <li *ngFor="let activity of operation.activities; let i = index">
                          {{ activity.activityName }}<span *ngIf="i < operation.activities.length - 1">, </span>
                        </li>
                      </ul>
                    </td>
                    <td class="text-center">{{ operation.totalCost }}</td>
                  </tr>
                </tbody>
              </table>
              <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                <div class="pagination-info">
                  Showing {{ getFirstEntryIndexSecondTable() }} - {{ getLastEntryIndexSecondTable() }} of {{ groupedOperations.length }} entries
              </div>
                <ul class="pagination justify-content-center">
                    <li class="page-item">
                        <button class="caret" (click)="goToPageSecondTable(currentPageSecondTable - 1)" [disabled]="currentPageSecondTable === 1">
                            <fa-icon [icon]="caretLeft"></fa-icon>
                        </button>
                    </li>
                    <li class="page-item" *ngFor="let pageNumber of [].constructor(totalPagesSecondTable); let i = index">
                        <button class="page-link pages" [class.active]="currentPageSecondTable === i + 1" (click)="goToPageSecondTable(i + 1)">
                            {{ i + 1 }}
                        </button>
                    </li>
                    <li class="page-item">
                        <button class="caret" (click)="goToPageSecondTable(currentPageSecondTable + 1)" [disabled]="currentPageSecondTable === totalPagesThirdTable">
                            <fa-icon [icon]="caretRight"></fa-icon>
                        </button>
                    </li>
                </ul>
              </nav>
            </div>

            <!-- Third Table: Vehicle Report Table -->
            <div *ngIf="selectedVehicleOperation && selectedVehicleOperation.length > 0">
              <div class="table-header d-flex flex-row p-3">
                <h2 class="table-title">Vehicle Report for {{ selectedPlateNumber || 'N/A' }}</h2>
                <button class="btn go-back-btn ms-auto" (click)="backToBeneficiaryReport()"><fa-icon
                    [icon]="backwardIcon" class="pe-1"></fa-icon> Back</button>
              </div>

              <table class="table mt-3 table-bordered" id="printableArea">
                <thead>
                  <tr>
                    <th>Maintenance Activity</th>
                    <th>Total Cost</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let activity of displayedVehicleOperations">
                    <td>{{ activity.activityName }}</td>
                    <td>{{ activity.totalCost }}</td>
                  </tr>
                 
                </tbody>
              </table>
              <!-- <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                <div class="pagination-info">
                  Showing {{ getFirstEntryIndexThirdTable() }} - {{ getLastEntryIndexThirdTable() }} of {{ selectedVehicleOperation.length }} entries
              </div>
                <ul class="pagination justify-content-center">
                    <li class="page-item">
                        <button class="caret" (click)="goToPageThirdTable(currentPageThirdTable - 1)" [disabled]="currentPageThirdTable === 1">
                            <fa-icon [icon]="caretLeft"></fa-icon>
                        </button>
                    </li>
                    <li class="page-item" *ngFor="let pageNumber of [].constructor(totalPagesThirdTable); let i = index">
                        <button class="page-link pages" [class.active]="currentPageThirdTable === i + 1" (click)="goToPageThirdTable(i + 1)">
                            {{ i + 1 }}
                        </button>
                    </li>
                    <li class="page-item">
                        <button class="caret" (click)="goToPageThirdTable(currentPageThirdTable + 1)" [disabled]="currentPageThirdTable === totalPagesThirdTable">
                            <fa-icon [icon]="caretRight"></fa-icon>
                        </button>
                    </li>
                </ul>
              </nav> -->
            </div>

            <!-- Message when no data is available -->
            <div *ngIf="selectedVehicleOperation && selectedVehicleOperation.length === 0">
              <p>No vehicle data available for the selected operation.</p>
            </div>
            
            
          </div>
        </div>
      </div>
    </div>
  </div>
</div>