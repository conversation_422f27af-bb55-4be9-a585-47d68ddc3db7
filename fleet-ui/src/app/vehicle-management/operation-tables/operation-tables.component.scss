th{
    font-size: 12px;
    color: #252525 !important;
    line-height: 21px;
    font-weight: 600;
}
.table-container td{
    color: #757575 !important;
    font-size: small;
}

.table tbody tr:hover td {
  background-color: #dadada !important; 
  color: #ffffff !important; 
  cursor: pointer;
}
.operations-container {
    background-color: #D9D9D94D;
    width: 83%;
    height: 100%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 20px;
  }
  
  .table-title {
    font-size: 18px;
    color: #28A4E2;
    font-weight: 500;
    width: fit-content;
  }
  
  .card {
    border: none;
  }
  
  .table-header {
    padding: 15px 15px 0px 15px !important;
  }
  
  .table-only {
    padding-bottom: 0px !important;
  }
  
  th {
    font-size: 13px;
    color: #B5B7C0 !important;
    font-weight: 500;
  }
  
  td {
    font-size: 13px;
    color: #757575;
  }
  
  .search-group {
    background-color: #ffffff;
    border-radius: 10px;
    width: 20%;
    height: 35px;
    border: 1px solid #edebf0;
  }
  
  .icon {
    background-color: #ffffff;
    color: #28A4E2;
    font-size: 13px;
    border-radius: 10px;
    padding: 9px 0px 7px 9px !important;
    text-align: center;
  }
  
  .global-input {
    color: #A098AE;
    background-color: #fff;
    border-radius: 10px;
    font-size: 13px;
  }
  
  .pagination-info {
    font-size: 14px;
    color: #B5B7C0;
  }
  
  .nav li .pages {
    height: 30px;
    width: 30px;
    border-radius: 50%;
    font-size: 12px;
    padding: 5px;
  }
  
  .nav li {
    margin: 0px 5px;
  }
  
  .caret {
    background-color: #ffffff;
    border: none;
    font-size: 20px;
    color: #A098AE;
    padding: 5px 0px;
  }
  .date-range-filter {
    margin-top: 20px;
    background-color: #ffffff; 
    border-radius: 6px; 
    padding: 8px 12px; 
    
    display: flex; 
    gap: 10px; 
    align-items: center; 
    max-width: 500px; 
   
  }
  
  .date-range-filter .form-group {
    margin: 0; 
    flex: 1; 
  }
  
  .date-range-filter .form-group label {
    display: none; 
  }
  
  .date-range-filter .form-control {
    border-radius: 4px; 
    border: 1px solid #d1d1d1; 
    font-size: 14px; 
  }
  
  .date-range-filter .btn-primary {
    background-color: #007bff; 
    border-color: #007bff;
    border-radius: 4px; 
    padding: 6px 10px; 
    font-size: 14px; 
    color: #ffffff; 
    text-transform: uppercase; 
  }
  
  .date-range-filter .btn-primary:hover {
    background-color: #0056b3; 
    border-color: #0056b3;
  }
  
  .date-range-filter .btn-primary:focus {
    box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5); 
  }
  .view-btn{
    border: 1px solid #28A4E2;
    color:#28A4E2;
    font-size: 13px;
    font-weight: 500;
}

.edit-btn{
    border: 1px solid rgb(20, 160, 27);
    color: rgb(20, 160, 27);
    font-size: 13px;
    font-weight: 500;
}
.nav{
  padding: 0px 20px;
}
.nav li .pages{
  height: 30px;
  width: 30px;
  border-radius: 50%;
  font-size: 12px;
  padding: 5px;
}
.nav li{
  margin: 0px 5px;
}
.caret{
  background-color: #ffffff;
  border: none;
  font-size: 20px;
  color: #A098AE;
  padding: 5px 0px;
}
.pagination-info{
  font-size: 14px;
  color: #B5B7C0;
}
