import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { faBackward, faCaretLeft, faCaretRight, faDownload, faPrint, faSearch } from '@fortawesome/free-solid-svg-icons';
import html2pdf from 'html2pdf.js';
import * as XLSX from 'xlsx';
import { debounceTime, distinctUntilChanged } from 'rxjs';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-operation-tables',
  templateUrl: './operation-tables.component.html',
  styleUrls: ['./operation-tables.component.scss']
})
export class OperationTablesComponent implements OnInit {
  allOperations: any[] = [];
  groupedOperations: any[] = [];
  selectedVehicleOperation: any = null;
  searchIcon = faSearch;
  backwardIcon = faBackward;
  searchText: string = '';
  startDate: string = '';  
  endDate: string = '';  
  filteredOperations: any[] = []; 
  downloadIcon=faDownload;
  printIcon=faPrint;
  showFirstTable: boolean = true; // Controls visibility of first and second tables
  selectedPlateNumber: any;
  selectedAgency: any;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  pageSize: number = 5; // Number of items per page
  userRole: string='';
  
  currentPageFirstTable: number = 1;
  totalPagesFirstTable: number = 0;
  displayedOperations: any[] = []; // For the first table
  
  currentPageSecondTable: number = 1;
  totalPagesSecondTable: number = 0;
  displayedGroupedOperations: any[] = []; // For the second table
  
  currentPageThirdTable: number = 1;
  totalPagesThirdTable: number = 0;
  displayedVehicleOperations: any[] = []; // For the third table
  institutionId: any;

  constructor(private http: HttpClient) {}

  ngOnInit() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parseObj = JSON.parse(data);
      this.userRole = parseObj?.data?.user?.role?.name;
      this.institutionId = parseObj?.data?.user?.institution.id;
    }

    if (this.isLogisticCbm()) {
      this.showFirstTable = false;
      this.getAllOperationCostsByBeneficiary(this.institutionId);
    } else {
      this.getAllOperationCosts();
    }
  }

  isLogisticCbm(): boolean {
    return this.userRole === 'Institutional logistics' || this.userRole === 'Institutional CBM';
  }
  
  // Fetch all operations for the first table
  getAllOperationCosts() {
    const url = `${environment.otherUrl}/allOperationalCost`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.allOperations = response.TotalCostPerInstitution || [];
        console.log('allopertions', this.allOperations)
        this.updateDisplayedOperationsFirstTable();
      },
      (error) => {
        console.error('Error fetching operation costs:', error);
      }
    );
   
  }

  // Fetch data for selected beneficiary
  getAllOperationCostsByBeneficiary(beneficiaryAgencyId: string) {
    const url = `${environment.otherUrl}/allOperationalCostByBeneficiaryAngencyId?beneficiaryAgencyId=${beneficiaryAgencyId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.groupedOperations = this.groupByPlateNumber(response.TotalCostVehicle || []);
        this.updateDisplayedOperationsSecondTable();
      },
      (error) => {
        console.error('Error fetching operation costs by beneficiary:', error);
      }
    );
  }

  // Show detailed operations for a selected vehicle
  showVehicleReport(operation: any) {
    this.getAllOperationCostsByVehicle(operation.VehiclesID);
    this.selectedPlateNumber = operation.PlateNumber; // Fetch data for the vehicle
  }

  // Fetch detailed operation data for a selected vehicle
  getAllOperationCostsByVehicle(vehicleId: string) {
    const url = `${environment.otherUrl}/allOperationalCostByVehicleId?VehicleID=${vehicleId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        console.log('Vehicle Operation Response:', response);  // Add this to inspect the API response
        this.selectedVehicleOperation = response.TotalCostPerActivity || [];
        this.updateDisplayedOperationsThirdTable();
      },
      (error) => {
        console.error('Error fetching operation costs by vehicle:', error);
      }
    );
  }
  

  // Group operations by plate number
  groupByPlateNumber(operations: any[]): any[] {
    return operations.reduce((acc: any[], operation: any) => {
      const found = acc.find(item => item.PlateNumber === operation.PlateNumber);
      if (found) {
        found.activities.push(...operation.activities);
      } else {
        acc.push({
          PlateNumber: operation.PlateNumber,
          activities: [...operation.activities],
          totalCost: operation.totalCost,
          VehiclesID: operation.VehiclesID
        });
      }
      return acc;
    }, []);
  }

  filterByDateRange() {
    if (!this.startDate || !this.endDate) {
      console.error('Both start and end dates are required.');
      return;
    }

    const requestBody = {
      startDate: this.startDate,
      endDate: this.endDate
    };

    const url = `${environment.otherUrl}/allOperationalCostByDateRange`;
    this.http.post<any>(url, requestBody).subscribe(
      (response) => {
        this.filteredOperations = response.TotalCostPerInstitution || [];
      },
      (error) => {
        console.error('Error fetching filtered operation costs:', error);
      }
    );
  }

  showDetails(operation: any) {
    if (!this.isLogisticCbm()) {
      this.getAllOperationCostsByBeneficiary(operation.beneficiaryAgencyId);
      this.showFirstTable = false;
      this.selectedVehicleOperation = null;
      this.selectedAgency = operation.institutionName;
    }
  }

  backToFirstTable() {
    if (!this.isLogisticCbm()) {
      this.showFirstTable = true;
      this.selectedVehicleOperation = null;
    }
  }

  // Go back to the second table (beneficiary report)
  backToBeneficiaryReport() {
    this.selectedVehicleOperation = null;
  }
  printVehicles(): void {
    const printContents = document.getElementById('printableArea')?.innerHTML;
    if (printContents) {
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents;
      window.print();
      document.body.innerHTML = originalContents;
      location.reload(); // To reload the original page content
    }
  }

  exportVehiclesToExcel(format: string): void {
    const cleanedData = this.groupedOperations.map(vehicle => {
      const cleanedVehicle: { [key: string]: any } = {};
      Object.keys(vehicle).forEach(key => {
        if (vehicle[key] !== null && vehicle[key] !== undefined && vehicle[key] !== '') {
          cleanedVehicle[key] = vehicle[key];
        }
      });
      return cleanedVehicle;
    });
  
    // Set the cleaned data for the template to render
    this.groupedOperations = cleanedData;
  
    const currentDate = new Date();
    const dateString = currentDate.toISOString().split('T')[0];
    const uniqueId = currentDate.getTime();
  
    if (format === 'csv') {
      this.exportToCSV(`report_${dateString}_${uniqueId}.csv`);    } 
    else if (format === 'pdf') {
      this.exportToPDF(`report_${dateString}_${uniqueId}.pdf`);
    }
  }
  
  exportToCSV(fileName: string): void {
    const table = document.getElementById('printableArea') as HTMLTableElement; // The table in your HTML
  
    if (!table) {
      console.error('Table not found');
      return;
    }
  
    const headers: string[] = [];
    const data: string[][] = [];
  
    // Get table headers (assuming the first row is the header row)
    const headerRow = table.querySelector('thead tr');
    if (headerRow) {
      headerRow.querySelectorAll('th').forEach((th) => {
        headers.push(th.innerText.trim());
      });
    }
  
    // Get table rows data
    const bodyRows = table.querySelectorAll('tbody tr');
    bodyRows.forEach((row) => {
      const rowData: string[] = [];
      row.querySelectorAll('td').forEach((td) => {
        let cellData = td.innerText.trim();
        
        // If the cell contains activities, join them with a line break (\n)
        if (td.querySelector('ul')) {
          const activities = Array.from(td.querySelectorAll('li')).map((li) => li.innerText.trim());
          cellData = activities.join('\n'); // Adds line breaks between activities
        }
        
        rowData.push(cellData);
      });
      data.push(rowData);
    });
  
    // Generate CSV data
    const csvContent = [
      headers.join(','),  // Add header row
      ...data.map((row) => row.map((cell) => `"${cell}"`).join(',')) // Add rows with quotes around cells to allow line breaks
    ].join('\n');
  
    // Download the CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.click();
  }
  exportToPDF(fileName: string): void {
    const element = document.getElementById('printableArea');
    
    const options = {
      margin: 1,
      filename: fileName,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2 },
      jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
    };
  
    if (element) {
      html2pdf().from(element).set(options).save();
    } else {
      console.error('Printable area not found');
    }
  }
  
  updateDisplayedOperationsFirstTable() {
    const startIndex = (this.currentPageFirstTable - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.allOperations.length);
    this.displayedOperations = this.allOperations.slice(startIndex, endIndex);
    this.totalPagesFirstTable = Math.ceil(this.allOperations.length / this.pageSize);
  }

  // Update displayed operations for the second table
  updateDisplayedOperationsSecondTable() {
    const startIndex = (this.currentPageSecondTable - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.groupedOperations.length);
    this.displayedGroupedOperations = this.groupedOperations.slice(startIndex, endIndex);
    this.totalPagesSecondTable = Math.ceil(this.groupedOperations.length / this.pageSize);
  }

  // Update displayed operations for the third table
  updateDisplayedOperationsThirdTable() {
    const startIndex = (this.currentPageThirdTable - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.selectedVehicleOperation.length);
    this.displayedVehicleOperations = this.selectedVehicleOperation.slice(startIndex, endIndex);
    this.totalPagesThirdTable = Math.ceil(this.selectedVehicleOperation.length / this.pageSize);
  }
 
  getFirstEntryIndexFirstTable(): number {
    return (this.currentPageFirstTable - 1) * this.pageSize + 1;
  }

  getLastEntryIndexFirstTable(): number {
    const lastIndex = this.currentPageFirstTable * this.pageSize;
    return lastIndex > this.allOperations.length ? this.allOperations.length : lastIndex;
  }

  getFirstEntryIndexSecondTable(): number {
    return (this.currentPageSecondTable - 1) * this.pageSize + 1;
  }

  getLastEntryIndexSecondTable(): number {
    const lastIndex = this.currentPageSecondTable * this.pageSize;
    return lastIndex > this.displayedGroupedOperations.length ? this.displayedGroupedOperations.length : lastIndex;
  }

  getFirstEntryIndexThirdTable(): number {
    return (this.currentPageThirdTable - 1) * this.pageSize + 1;
  }

  getLastEntryIndexThirdTable(): number {
    const lastIndex = this.currentPageThirdTable * this.pageSize;
    return lastIndex > this.selectedVehicleOperation.length ? this.selectedVehicleOperation.length : lastIndex;
  }
  // Go to a specific page for the first table
  goToPageFirstTable(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPagesFirstTable) {
      this.currentPageFirstTable = pageNumber;
      this.updateDisplayedOperationsFirstTable();
    }
  }

  // Pagination methods for the second and third tables
  goToPageSecondTable(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPagesSecondTable) {
      this.currentPageSecondTable = pageNumber;
      this.updateDisplayedOperationsSecondTable();
    }
  }

  goToPageThirdTable(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPagesThirdTable) {
      this.currentPageThirdTable = pageNumber;
      this.updateDisplayedOperationsThirdTable();
    }
  }



getPageNumbersFirstTable(): number[] {
    return Array.from({ length: this.totalPagesFirstTable }, (_, i) => i + 1); // Simplified
}
getPageNumbersSecondTable(): number[] {
  return Array.from({ length: this.totalPagesSecondTable }, (_, i) => i + 1); // Simplified
}
getPageNumbersThirdTable(): number[] {
  return Array.from({ length: this.totalPagesThirdTable }, (_, i) => i + 1); // Simplified
}

nextPageFirstTable() {
  if (this.currentPageFirstTable < this.totalPagesFirstTable) {
    this.currentPageFirstTable++;
    this.updateDisplayedOperationsFirstTable();
  }
}

nextPageSecondTable() {
  if (this.currentPageSecondTable < this.totalPagesSecondTable) {
    this.currentPageSecondTable++;
    this.updateDisplayedOperationsSecondTable();
  }
}

nextPageThirdTable() {
  if (this.currentPageThirdTable < this.totalPagesThirdTable) {
    this.currentPageThirdTable++;
    this.updateDisplayedOperationsThirdTable();
  }
}

// Go to the previous page
previousPageFirstTable() {
  if (this.currentPageFirstTable > 1) {
    this.currentPageFirstTable--;
    this.updateDisplayedOperationsFirstTable();
  }
}

previousPageSecondTable() {
  if (this.currentPageSecondTable > 1) {
    this.currentPageSecondTable--;
    this.updateDisplayedOperationsSecondTable();
  }
}

previousPageThirdTable() {
  if (this.currentPageThirdTable > 1) {
    this.currentPageThirdTable--;
    this.updateDisplayedOperationsThirdTable();
  }
}

}
