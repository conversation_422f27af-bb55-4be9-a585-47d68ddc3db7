import { Component, ViewChild } from '@angular/core';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { faArrowLeft, faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';


@Component({
  selector: 'app-register-vehicle',
  templateUrl: './register-vehicle.component.html',
  styleUrl: './register-vehicle.component.scss'
})
export class RegisterVehicleComponent {
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  registerVehicleForm !: FormGroup;
  files !: FormArray;
  vehicleModels: any[] = [];
  vehicleManufactures: any[] =[];
  beneficiaryAgencies: any[] = [];
  dropdownConfig: any = {
    displayKey: "name",   // Display the name of the institution
    valueField: "id",
    search: true, 
    height: '300px', // set the height
    placeholder: 'Select', // placeholder text
    customComparator: () => {}, // optional, for custom sorting
    limitTo: 0, // number of items to display in the dropdown. 0 = all
    moreText: 'more', // text for more
    noResultsFound: 'No results found!', // text for no results
    searchPlaceholder: 'Search...', 
    clearOnSelection: false, 
    inputDirection: 'ltr',
  };
  ownerships: any[] = [];
  vehicleTypes: any[] = [];
  acquisitionId: any;
  vehicleRegRequest: any;
  totalNumberOfVehicles:number = 0;
  currentFormIndex = 0;
  formData: any = {};
  acquisitions: any[] = [];
  acquisitionDetails: any = {};
  acquisition: any;
  vehicleForm: any;
  allFormsSubmitted: boolean = false;
  canSubmitNextForm: boolean = false;
  prePopulatedVehicleData: any[] = [];
  currentVehicleType: any;
  currentVehicleTypeName: any;
  vehicleId: any;
  plusIcon = faPlus;
  minusIcon = faMinus;
  selectedFileName: any;
  today: string;
  // acquisitionDate: any;
  submitted = false;
  years: number[] = [];
  backwardIcon = faArrowLeft;


  constructor(
    private formBuilder: FormBuilder, 
    private http: HttpClient, 
    private toastr: ToastrService, 
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {
    this.registerVehicleForm = this.formBuilder.group({
      vehicleRegRequest: [''],
      beneficiaryAgencyId: ['', Validators.required],
      beneficiaryAgency:['', Validators.required],
      ownershipType: ['', Validators.required],
      ownershipTypeName: [''],
      vehicleType: ['', Validators.required],
      fuelType: ['', Validators.required],
      vehicleManufacture: ['', Validators.required],
      vehicleModel: ['', Validators.required],
      chassisNumber: ['', [Validators.required, Validators.maxLength(50)]],
      engineNumber: ['', [Validators.maxLength(50), Validators.required]],
      transmissionType: ['', Validators.required],
      manufactureYear: ['', Validators.required],
      odometerReading: ['', [Validators.required, Validators.maxLength(50)]],
      acquisitionDate: ['', Validators.required],
      invoiceNumber: ['', [Validators.required, Validators.maxLength(50)]],
      invoiceDate: ['', Validators.required],
      customsDeclarationNumber: ['', [Validators.required, Validators.maxLength(50)]],
      customsDeclarationDate: ['', Validators.required],
      declaredAmount: ['', [Validators.required, Validators.min(0)]],
      projectName: ['', Validators.maxLength(50)],
      projectDescription: [''],
      projectStartDate: [''],
      projectEndDate: [''],
      vehiclePhysicalFile: [null, Validators.required],
      taxPaymentProof: [null, Validators.required],
      vehicleRegistrationFees: [null, Validators.required],
      file: [''],
      frontView: ['', Validators.required],
      backView: ['', Validators.required],
      files: this.formBuilder.array([this.createFileFormGroup()])
    });
    this.files = this.registerVehicleForm.get('files') as FormArray;
    this.registerVehicleForm.get('beneficiaryAgencyId')?.valueChanges.subscribe((value) => {
      const selectedAgency = this.beneficiaryAgencies.find(agency => agency.id === value);
      this.registerVehicleForm.patchValue({
        beneficiaryAgency: selectedAgency ? selectedAgency.name : null
      });
    });

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const day = currentDate.getDate().toString().padStart(2, '0');
    this.today = `${year}-${month}-${day}`;
  }

  goBack(){
    this.location.back();
  }

  isFieldRequired(fieldName: string): boolean {
    const control = this.registerVehicleForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }

  ngOnInit(): void {
    this.fetchVehicleModels();
    this.fetchVehicleManufactures();
    this.fetchAcquisitionId();
    this.fetchBeneficiaryAgencies();
    this.fetchOwnershipTypes();
    this.fetchVehicleTypes();
    this.fetchData();
    this.populateYears();
    // this.fetchExistingData(this.acquisitionId);
  }

  populateYears() {
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= currentYear -25; year--) {
      this.years.push(year);
    }
  }

  getFullDate() {
    const year = this.registerVehicleForm.get('manufactureYear')?.value;
    if (year) {
      const fullDate = `${year}-01-01`; // Format as YYYY-01-01
      console.log('Full Date:', fullDate);
      return fullDate;
    }
    return null;
  }
  fetchVehicleModels(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleModels`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleModels = response.map(vehicleModel => ({ id: vehicleModel.id, name: vehicleModel.name }));
      },
      (error) => {
        console.error('Error fetching vehicle models:', error)
      }
    );
  }

  fetchVehicleManufactures(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleManufacture`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleManufactures = response.map(vehicleManufacture => ({ id: vehicleManufacture.id, name: vehicleManufacture.name }));
      },
      (error) => {
        console.error('Error fetching vehicle manufactures:', error)
      }
    );
  }

  fetchBeneficiaryAgencies(): void {
    const apiUrl = `${environment.baseUrl}/user-management/institutions`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.beneficiaryAgencies = response.map(institution => ({ id: institution.id, name: institution.name }));
      },
      (error) => {
        console.error('Error fetching beneficiary agencies:', error);
      }
    );
  }

  fetchOwnershipTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/ownershipTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.ownerships = response.map(ownership => ({ id: ownership.id, name: ownership.name }));
      },
      (error) => {
        console.error('Error fetching ownershipTypes:', error)
      }
    );
  }

  fetchVehicleTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/vehicleTypes`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        this.vehicleTypes = response.map(vehicleType => ({ id: vehicleType.id, name: vehicleType.name }));
      },
      (error) => {
        console.error('Error fetching vehicleTypes:', error)
      }
    );
  }

  getVehicleTypeName(vehicleTypeId: number): string | null {
    const vehicleType = this.vehicleTypes.find(type => type.id === vehicleTypeId);
    return vehicleType ? vehicleType.name : null;
  }
  getOwnershipTypeName(ownershipTypeId: number): string | null {
    const ownershipType = this.ownerships.find(type => type.id === ownershipTypeId);
    return ownershipType ? ownershipType.name : null;
  }
  getBeneficiaryAgencyName(beneficiaryAgencyId: number): string | null {
    const beneficiaryAgency = this.beneficiaryAgencies.find(type => type.id === beneficiaryAgencyId);
    return beneficiaryAgency.name;
  }

  fetchAcquisitionId(): void{
    this.route.params.subscribe(params => {
      if (params['acquisitionId']) {
        this.acquisitionId = params['acquisitionId'];
        
      }
    });
  }

  fetchData(): void {
    this.fetchAcquisitionId(); 
    if (this.acquisitionId) { 
      this.fetchExistingData(this.acquisitionId);
    }
  }

  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }
  
  removeFile(index: number): void {
    this.files.removeAt(index);
  }
  
  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: [''],
      applicationId: ['']
    });
  }
  
  // onFileChange(event: any, index: number): void {
  //   event.preventDefault();
  //   const file = event.target.files[0];
  //   if (file) {
  //     const fileControl = this.files.at(index).get('file');
  //     if (fileControl) { 
  //       fileControl.setValue(file);
  //     }
  //     this.selectedFileName = file.name;
  //   }
  // }
  onFileChange(event: any, fileType: string): void {
    event.preventDefault(); 
    const file = event.target.files[0];
    
    if (file) {
      if (file.type !== 'application/pdf') {
        this.toastrComponent.showToastrMessage(
          'Wrong file type. File should be PDF only.',
          false
        );
        
        event.target.value = ''; 
        
        this.registerVehicleForm.patchValue({ [fileType]: null });
        
        return; 
      }
  
      this.registerVehicleForm.patchValue({ [fileType]: file });
      
    }
  }

  onImageChange(event: any, fileType: string): void {
    event.preventDefault();
    const file = event.target.files[0];
  
    if (file) {
      const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml', 'image/tiff'];
  
      if (!validImageTypes.includes(file.type)) {
        this.toastrComponent.showToastrMessage(
          'Invalid file type. Please upload an image file (JPEG, PNG, GIF, BMP, etc.).',
          false
        );
  
        event.target.value = '';
        this.registerVehicleForm.patchValue({ [fileType]: null });
  
        return;
      }
  
      // Patch the valid image file to the form
      this.registerVehicleForm.patchValue({ [fileType]: file });
    }
  }
  
  
  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB
  
    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  // submitFile(vehicleId: any): void {
  //   const fileUploadDataFormArray = this.registerVehicleForm.get('files') as FormArray;
  //   fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
  //     if (control instanceof FormGroup) {
  //       const fileItem = control.value;
  //       if (fileItem.file) {
  //         const fileFormData = new FormData();
  //         const file = fileItem.file as File;
  
  //         console.log('File name:', file.name);
  //         console.log('File size:', file.size);
  //         console.log('File type:', file.type)
  
  //         if (this.validateFile(file)) {
  //           fileFormData.append('file', file);
  //           fileFormData.append('documentDescription', fileItem.documentDescription);
  //           fileFormData.append('applicationId', vehicleId);
  
  //           this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
  //             (response) => {
  //               console.log('API Response:', response);
  //               console.log("File submitted successfully")
  //               this.registerVehicleForm.reset();
  //             },
  //             (error) => {
  //               let errorMessage = 'An unexpected error occurred.';
  //               if (error && error.error && error.error.message) {
  //                 errorMessage = error.error.message;
  //               }
  //               console.error('API Error:', errorMessage);
  //               this.toastr.error("An error occurred!!", 'Error');
  //             }
  //           );
  //         } else {
  //           console.warn('File size or type is invalid.');
  //         }
  //       } else {
  //         console.log('No file selected...');
  //       }
  //     }
  //   });
  // }
  // submitFile(file: File, fileType: string, vehicleId: string): void {
  //   if (file) {
  //     const fileFormData = new FormData();

  //     if (this.validateFile(file)) {
  //       fileFormData.append('file', file);
  //       fileFormData.append('documentDescription', fileType);
  //       fileFormData.append('applicationId', vehicleId);
  //       this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
  //         (response) => {
  //           console.log(`${fileType} submitted successfully`, response);
  //         },
  //         (error) => {
  //           console.error(`Error submitting ${fileType}`, error);
  //           this.toastr.error(`Error submitting ${fileType}`, 'Error');
  //         }
  //       );
  //     } else {
  //       console.warn(`${fileType} size or type is invalid.`);
  //     }
  //   }
  // }

  submitFile(file: File, fileType: string, vehicleId: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (file) {
        const fileFormData = new FormData();
  
        if (this.validateFile(file)) {
          fileFormData.append('file', file);
          fileFormData.append('documentDescription', fileType);
          fileFormData.append('applicationId', vehicleId);
  
          this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
            (response) => {
              console.log(`${fileType} submitted successfully`, response);
              resolve();  
            },
            (error) => {
              console.error(`Error submitting ${fileType}`, error);
              reject();  
            }
          );
        } else {
          const validationError = `${fileType} size or type is invalid.`;
          console.warn(validationError);
          this.toastr.error(validationError, 'Error');
          reject();
        }
      } else {
        reject();
      }
    });
  }
  

  submitAllFiles(vehicleId: string, files: any[], fileTypes: string[], additional?: File): void {
    let allFilesSubmitted = true;
    let errorMessage = '';
  
    // Submit each file
    files.forEach((file, index) => {
      if (file) {
        this.submitFile(file, fileTypes[index], vehicleId)
          .then(() => {
            this.toastr.success(`${fileTypes[index]} uploaded successfully!`, 'Success');
          })
          .catch((error) => {
            allFilesSubmitted = false;
            errorMessage += `Error submitting ${fileTypes[index]}.\n`;
            this.toastr.error(`Error uploading ${fileTypes[index]}. Please try again.`, 'File Upload Error');
          });
      }
    });
  
    // Submit additional file if present
    if (additional && this.validateFile(additional)) {
      this.submitFile(additional, 'additionalFile', vehicleId)
        .then(() => {
          this.toastr.success('Additional file uploaded successfully!', 'Success');
        })
        .catch((error) => {
          allFilesSubmitted = false;
          errorMessage += 'Error submitting additional file.\n';
          this.toastr.error('Error uploading additional file. Please try again.', 'File Upload Error');
        });
    }
  
    // If any file submission fails, notify the user
    if (!allFilesSubmitted) {
      this.toastr.error(errorMessage.trim(), 'File Submission Error');
    }
  }
  
  
  
  fetchExistingData(acquisitionId: string): void {
    const url = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;
    this.http.get<any>(url).subscribe(response => {
      console.log(response);
      if (response && response.vehicles && Array.isArray(response.vehicles)) {
        this.acquisitions = response.vehicles;
        this.acquisitionDetails = response.acquisition;
        // this.acquisitionDate = response.acquisition.submittedDate;

        // console.log("Acquisition Date: ", this.acquisitionDate)
  
        this.acquisitions.forEach(acquisition => {
          if (acquisition.numberOfVehicles && typeof acquisition.numberOfVehicles === 'number') {
            this.totalNumberOfVehicles += acquisition.numberOfVehicles;
          }
        });
  
        const vehicleRequestValues = {
          userId: this.acquisitionDetails.userId,
          institution: this.acquisitionDetails.institution,
          institutionId: this.acquisitionDetails.institutionId,
          vehicleAcquisition:this.acquisitionId
        }
  
        this.http.post(`${environment.otherUrl}/vehicle-reg-request`, vehicleRequestValues).subscribe(
          (response: any) =>{
            this.vehicleRegRequest = response.VehicleRegRequestId;
            console.log("id: ", this.vehicleRegRequest)
  
            // this.registerVehicleForm.patchValue({
            //   vehicleRegRequest: this.vehicleRegRequest
            // });

            this.acquisitions.forEach(vehicleDetail => {
              this.registerVehicleForm.patchValue({
                vehicleRegRequest: this.vehicleRegRequest,
                // beneficiaryAgencyId: vehicleDetail.beneficiaryAgencyId,
                // beneficiaryAgency: vehicleDetail.beneficiaryAgency,
                ownershipType: this.acquisitionDetails.ownershipType.id,
                // ownershipTypeName: this.acquisitionDetails.ownershipType.name,
                vehicleType: vehicleDetail.vehicleType,
                // acquisitionDate: this.acquisitionDate,
                projectName: vehicleDetail.projectName, 
                projectDescription: vehicleDetail.projectDescription, 
                projectStartDate: vehicleDetail.projectStartDate, 
                projectEndDate: vehicleDetail.projectEndDate, 

              })
            })
            this.prePopulatedVehicleData = [];
      
            this.acquisitions.forEach((acquisition, index) => {
              
              if (acquisition.numberOfVehicles > 0) {
                for (let j = 0; j < acquisition.numberOfVehicles; j++) {
                  try {
                    const prePopulatedData = {
                      vehicleRegRequest: this.vehicleRegRequest, 
                      // beneficiaryAgencyId: acquisition.beneficiaryAgencyId,
                      // beneficiaryAgency: acquisition.beneficiaryAgency,
                      ownershipType: this.acquisitionDetails.ownershipType.id,
                      vehicleType: acquisition.vehicleType,
                      projectName: acquisition.projectName,
                      projectDescription: acquisition.projectDescription,
                      projectStartDate: acquisition.projectStartDate,
                      projectEndDate: acquisition.projectEndDate,
                    };
                    console.log("prePopulatedData: ", JSON.stringify(prePopulatedData));
                    this.prePopulatedVehicleData.push(prePopulatedData);
                    console.log("prePopulatedVehicleData length: ", this.prePopulatedVehicleData.length);
                    console.log("prePopulatedData:", JSON.stringify(this.prePopulatedVehicleData));
                    console.log("currentIndex", this.currentFormIndex)
                  } catch (error) {
                    console.error("Error in loop:", error);
                  }
                }
              }
              // this.onSubmit();
            });

          })
        }
  
    });
  }
  
  submitVehicleForm(formData: any): void {

    // Get the file values from the form
    const physicalFile = this.registerVehicleForm.get('vehiclePhysicalFile')?.value;
    const taxProof = this.registerVehicleForm.get('taxPaymentProof')?.value;
    const registrationFees = this.registerVehicleForm.get('vehicleRegistrationFees')?.value;
    const frontView = this.registerVehicleForm.get('frontView')?.value;
    const backView = this.registerVehicleForm.get('backView')?.value;
    const additional = this.registerVehicleForm.get('file')?.value;
  
    // List of files and their types for validation
    const files = [physicalFile, taxProof, registrationFees, frontView, backView];
    const fileTypes = ['vehiclePhysicalFile', 'taxPaymentProof', 'vehicleRegistrationFees', 'frontView', 'backView'];
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
  
    let allFilesValid = true;
    let errorMessage = '';
  
    // Validate each file
    files.forEach((file, index) => {
      if (file) {
        if (!allowedTypes.includes(file.type)) {
          allFilesValid = false;
          errorMessage += `Invalid file type for ${fileTypes[index]}. Only jpg, jpeg, png, or pdf are allowed.\n`;
        }
      } else {
        allFilesValid = false;
        errorMessage += `${fileTypes[index]} is required.\n`;
      }
    });
  
    // If any file validation fails, display the error and prevent form submission
    if (!allFilesValid) {
      this.toastr.error(errorMessage.trim(), 'File Validation Error');
      return;  // Stop further execution if files are invalid
    }
  
    console.log("Thee submitted Data......", JSON.stringify(formData))
    // Proceed with form submission if validation passes
    this.http.post(`${environment.otherUrl}/vehicle-registration`, formData)
      .subscribe(
        (response: any) => {
          const vehicleId = response.id;
          console.log("Vehicle ID:", vehicleId);
          this.toastr.success('Vehicle registration successful!', 'Success');
  
          // Submit files after successful form submission, tracking the results
          this.submitAllFiles(vehicleId, files, fileTypes, additional);
        },
        (error) => {
          console.error('Error registering vehicle:', error);
          this.toastr.error('An error occurred while registering the vehicle. Please try again.', 'Error');
        }
      );
  }
  
  
    onSubmit(): void {
      this.submitted = true;
      console.log(this.registerVehicleForm.value);
      const selectedAgency = this.registerVehicleForm.get('beneficiaryAgencyId')?.value;
      this.registerVehicleForm.patchValue({
        beneficiaryAgency: selectedAgency.name,
        beneficiaryAgencyId: selectedAgency.id,
      });
      if (this.registerVehicleForm.invalid) {
        const errorMessages = []; 
    
        for (const controlName in this.registerVehicleForm.controls) {
          if (this.registerVehicleForm.get(controlName)?.invalid) {
            const label = this.getFormControlLabel(controlName); 
            errorMessages.push(`${label} is required`);
          }
        }
        if (errorMessages.length > 0) {
          this.toastr.error(`Please fix the following errors: <br>  * ${errorMessages.join('<br>  * ')}`, 'Error');
        } else {
          this.toastr.error('An error occurred while validating the form. Please check all fields.', 'Error');
        }
        return;
      }
    
      const currentVehicleIndex = this.currentFormIndex;
      
      if (currentVehicleIndex < this.prePopulatedVehicleData.length) {
        const prePopulatedData = this.prePopulatedVehicleData[currentVehicleIndex];
        const vehicleType = prePopulatedData.vehicleType;
        const data = localStorage.getItem('localUserData');
        let loggedInInstitutionId = null;

        if (data !== null) {
          const parseObj = JSON.parse(data);
          loggedInInstitutionId = parseObj?.data?.user?.institution?.id; 
        }

        // const selectedAgency = this.registerVehicleForm.get('beneficiaryAgencyId')?.value;
        console.log("beenneee.......", selectedAgency);
        this.registerVehicleForm.patchValue({
          manufactureYear: this.getFullDate(),
          // beneficiaryAgency: selectedAgency.name,
          // beneficiaryAgencyId: selectedAgency.id,
        });

        // const data = localStorage.getItem('localUserData');
        // let loggedInInstitutionId = null;

        // if (data !== null) {
        //   const parseObj = JSON.parse(data);
        //   loggedInInstitutionId = parseObj?.data?.user?.institution?.id; 
        // }

        // const selectedAgency = this.registerVehicleForm.get('beneficiaryAgencyId')?.value;

        // if (selectedAgency?.id !== loggedInInstitutionId) {
        //   this.registerVehicleForm.patchValue({
        //     beneficiaryAgency: selectedAgency.name,
        //     beneficiaryAgencyId: selectedAgency.id,
        //   });
        // } else {
        //   console.log('Selected agency matches the logged-in user institution.');
        // }
        const uniqueData = this.registerVehicleForm.value;
        const combinedData = {
          ...prePopulatedData,
          ...uniqueData,
        };
    
      console.log("Combo Data: ", combinedData);
      this.submitVehicleForm(combinedData);
    
      this.registerVehicleForm.reset();
      console.log("index: ",this.currentFormIndex);
      console.log("data length: ",this.prePopulatedVehicleData.length);
      this.currentFormIndex++;
      this.fetchExistingData(this.acquisitionId);
      
      if (this.currentFormIndex >= this.prePopulatedVehicleData.length) {
        this.allFormsSubmitted = true;
        if(this.allFormsSubmitted === true){
          // this.updateAcquisitionRegisterStatus(this.acquisitionId);
          this.router.navigate(['/vehicle-management/all-registered']);  
          return
        }
      }
      }else{
        console.error('Error: prePopulatedVehicleData index out of bounds.');
      }
    }
    
    getFormControlLabel(controlName: string): string {
      const labels: { [key: string]: string } = {
        beneficiaryAgencyId: 'Beneficiary Agency',
        vehicleRegRequest: 'Vehicle Registration Request',
        ownershipType: 'Ownership Type',
        vehicleType: 'Vehicle Type',
        chassisNumber: 'Chassis Number',
        engineNumber: 'Engine Number',
        transmissionType: 'Transmission Type',
        manufactureYear: 'Manufacture Year',
        odometerReading: 'Odometer Reading',
        acquisitionDate: 'Acquisition Date',
        invoiceNumber: 'Invoice Number',
        invoiceDate: 'Invoice Date',
        customsDeclarationNumber: 'Customs Declaration Number',
        customsDeclarationDate: 'Customs Declaration Date',
        declaredAmount: 'Declared Amount',
        projectName: 'Project Name',
        projectDescription: 'Project Description',
        projectStartDate: 'Project Start Date',
        projectEndDate: 'Project End Date',
      };
      
      return (labels as { [key: string]: string })[controlName] || controlName;

    }
}