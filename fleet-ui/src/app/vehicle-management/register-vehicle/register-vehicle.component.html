<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <div class="header d-flex flex-row justify-content-between w-50">
            <button class="btn go-back-btn" (click)="goBack()">
                <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
              </button>
            <h1>Vehicle Registration</h1>
        </div>

        <div class="card" *ngIf="!acquisitionDetails?.id || !allFormsSubmitted">
            <p>Fill in vehicle details issued by RRA to get the plate number and pink card.</p>
            <div class="row form-container">
                <form id="registerForm" [formGroup]="registerVehicleForm" class="text-center" method="post" (ngSubmit)="onSubmit()" enctype="multipart/form-data">
                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="beneficiaryAgencyId" [ngClass]="{'required-label': isFieldRequired('beneficiaryAgencyId')}">Beneficiary Agency</label>
                            <!-- <select id="beneficiaryAgencyId" class="form-control" formControlName="beneficiaryAgencyId">
                                <option *ngFor="let institution of beneficiaryAgencies" [value]="institution.id">{{ institution.name }}</option>
                            </select> -->
                            <ngx-select-dropdown 
                                [options]="beneficiaryAgencies" 
                                [config]="dropdownConfig"
                                formControlName="beneficiaryAgencyId"
                                class="custom-dropdown">
                            </ngx-select-dropdown>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="ownershipType" [ngClass]="{'required-label': isFieldRequired('ownershipType')}">Ownership Type</label>
                            <input type="text" class="form-control" id="ownershipTypeName" formControlName="ownershipTypeName" [placeholder]="acquisitionDetails?.ownershipType?.name" readonly>
                            <input type="text" class="form-control" id="ownershipType" [value]="acquisitionDetails?.ownershipType?.id" placeholder="acquisitionDetails?.ownershipType?.id" style="display: none;">

                            <!-- <input type="text" class="form-control" id="ownershipType" formControlName="ownershipType" style="display: none;"> -->
                            <!-- <select id="ownershipType" class="form-control" formControlName="ownershipType">
                                <option [value]="acquisitionDetails?.ownershipType?.id">{{ acquisitionDetails?.ownershipType?.name }}</option>
                            </select> -->
                        </div>
                        <!-- <div *ngIf="prePopulatedVehicleData.length > 0"> -->
                            <div class="form-group col-md-4">
                                <label for="vehicleType" [ngClass]="{'required-label': isFieldRequired('vehicleType')}">Vehicle Type</label>
                                <!-- <input type="text" class="form-control" formControlName="vehicleTypeName" [placeholder]="getVehicleTypeName(prePopulatedVehicleData[currentFormIndex]?.vehicleType?.id)" readonly>
                                <input type="text" class="form-control" id="vehicleType" [value]="prePopulatedVehicleData[currentFormIndex]?.vehicleType?.id" style="display: none;"> -->
                            
                            <select id="vehicleType" class="form-control" formControlName="vehicleType">
                                <option [value]="prePopulatedVehicleData[currentFormIndex]?.vehicleType?.id">
                                    {{ getVehicleTypeName(prePopulatedVehicleData[currentFormIndex]?.vehicleType?.id) }}
                                </option>                                
                            </select>
                            <!-- <div *ngIf="registerVehicleForm?.get('vehicleType')?.invalid && (registerVehicleForm?.get('vehicleType')?.dirty || registerVehicleForm?.get('vehicleType')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('vehicleType')?.errors?.['required']">
                                    Vehicle Type is required.
                                </div>
                            </div> -->
                            <!-- <input type="text" class="form-control" id="vehicleType" readonly [value]="getVehicleTypeName(prePopulatedVehicleData[currentFormIndex].vehicleType)"> -->
                          </div>
                          <!-- </div> -->

                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="vehicleManufacture" [ngClass]="{'required-label': isFieldRequired('vehicleManufacture')}">Vehicle Manufacture/Brand</label>
                            <select class="form-control" formControlName="vehicleManufacture" id="vehicleManufacture">
                                <option *ngFor="let vehicleManufacture of vehicleManufactures" [value]="vehicleManufacture.id">{{vehicleManufacture.name}}</option>
                            </select>
                            <!-- <ngx-select-dropdown 
                                [options]="vehicleManufactures" 
                                [config]="dropdownConfig"
                                formControlName="vehicleManufacture"
                                class="custom-dropdown">
                            </ngx-select-dropdown> -->
                            <!-- <div *ngIf="registerVehicleForm?.get('vehicleManufacture')?.invalid && (registerVehicleForm?.get('vehicleManufacture')?.dirty || registerVehicleForm?.get('vehicleManufacture')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('vehicleManufacture')?.errors?.['required']">
                                    Vehicle Manufacture is required.
                                </div>
                            </div> -->
                        </div>
                        <div class="form-group col-md-4">
                            <label for="vehicleModel" [ngClass]="{'required-label': isFieldRequired('vehicleModel')}">Vehicle Model</label>
                            <select class="form-control" formControlName="vehicleModel" id="vehicleModel">
                                <option *ngFor="let vehicleModel of vehicleModels" [value]="vehicleModel.id">{{vehicleModel.name}}</option>
                            </select>
                            <!-- <ngx-select-dropdown 
                                [options]="vehicleModels" 
                                [config]="dropdownConfig"
                                formControlName="vehicleModel"
                                class="custom-dropdown">
                            </ngx-select-dropdown> -->
                            <!-- <div *ngIf="registerVehicleForm?.get('vehicleModel')?.invalid && (registerVehicleForm?.get('vehicleModel')?.dirty || registerVehicleForm?.get('vehicleModel')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('vehicleModel')?.errors?.['required']">
                                    Vehicle Model is required.
                                </div>
                            </div> -->
                        </div>
                        <div class="form-group col-md-4">
                            <label for="chassisNumber" [ngClass]="{'required-label': isFieldRequired('chassisNumber')}">Chassis Number</label>
                            <input type="text" class="form-control" id="chassisNumber" formControlName="chassisNumber">
                            <!-- <div *ngIf="registerVehicleForm?.get('chassisNumber')?.invalid && (registerVehicleForm?.get('chassisNumber')?.dirty || registerVehicleForm?.get('chassisNumber')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('chassisNumber')?.errors?.['required']">
                                    Chassis Number is required.
                                </div>
                            </div> -->
                        </div>

                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="engineNumber" [ngClass]="{'required-label': isFieldRequired('engineNumber')}">Engine Number</label>
                            <input type="text" class="form-control" id="engineNumber" formControlName="engineNumber">
                        </div>

                        <div class="form-group col-md-4">
                            <label for="transmissionType" [ngClass]="{'required-label': isFieldRequired('transmissionType')}">Transmission Type</label>
                            <select id="transmissionType" formControlName="transmissionType" class="form-control">
                                <option value="Manual">Manual</option>
                                <option value="Automatic">Automatic</option>
                                <option value="Continuously Variable Transmission">Continuously Variable Transmission</option>
                                <option value="Semi-automatic transmission">Semi-automatic transmission</option>
                                <option value="Tiptronic transmission">Tiptronic transmission</option>
                                <option value="Dual Clutch Transmission">Dual Clutch Transmission</option>
                            </select>
                            <!-- <div *ngIf="registerVehicleForm?.get('transmissionType')?.invalid && (registerVehicleForm?.get('transmissionType')?.dirty || registerVehicleForm?.get('transmissionType')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('transmissionType')?.errors?.['required']">
                                    Transmission Type is required.
                                </div>
                            </div> -->
                        </div>
                        
                        <div class="form-group col-md-4">
                            <label for="fuelType" [ngClass]="{'required-label': isFieldRequired('fuelType')}">Fuel Type</label>
                            <select id="fuelType" formControlName="fuelType" class="form-control">
                                <option value="Diesel">Diesel</option>
                                <option value="Gasoline">Gasoline</option>
                                <option value="LPG">LPG</option>
                                <option value="Gasoline and electric">Gasoline and electric</option>
                                <option value="Full Electric">Full Electric</option>
                            </select>
                        </div>
                        
                    </div>

                    <div class="row mt-4">


                        <div class="form-group col-md-4">
                            <label for="manufactureYear" [ngClass]="{'required-label': isFieldRequired('manufactureYear')}">Manufacture Year</label>
                            <select class="form-control" id="manufactureYear" formControlName="manufactureYear">
                                <option *ngFor="let year of years" [value]="year">{{ year }}</option>
                            </select>
                            <!-- <div *ngIf="registerVehicleForm?.get('manufactureYear')?.invalid && (registerVehicleForm?.get('manufactureYear')?.dirty || registerVehicleForm?.get('manufactureYear')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('manufactureYear')?.errors?.['required']">
                                    Manufacture Year is required.
                                </div>
                            </div> -->
                        </div>

                        <div class="form-group col-md-4">
                            <label for="odometerReading" [ngClass]="{'required-label': isFieldRequired('odometerReading')}">Odometer Reading</label>
                            <input type="text" class="form-control" id="odometerReading" formControlName="odometerReading">
                            <!-- <div *ngIf="registerVehicleForm?.get('odometerReading')?.invalid && (registerVehicleForm?.get('odometerReading')?.dirty || registerVehicleForm?.get('odometerReading')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('odometerReading')?.errors?.['required']">
                                    Odometer Reading is required.
                                </div>
                            </div> -->
                        </div>
                        <div class="form-group col-md-4">
                            <label for="acquisitionDate" [ngClass]="{'required-label': isFieldRequired('acquisitionDate')}">Acquisiton Date</label>
                            <input type="date" class="form-control" id="acquisitionDate" formControlName="acquisitionDate" value="acquisitionDate" [max]="today">
                        </div>
                        
                        
                    </div>

                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="invoiceNumber" [ngClass]="{'required-label': isFieldRequired('invoiceNumber')}">Invoice Number</label>
                            <input type="text" class="form-control" id="invoiceNumber" formControlName="invoiceNumber">
                            <!-- <div *ngIf="registerVehicleForm?.get('invoiceNumber')?.invalid && (registerVehicleForm?.get('invoiceNumber')?.dirty || registerVehicleForm?.get('invoiceNumber')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('invoiceNumber')?.errors?.['required']">
                                    Invoice Number is required.
                                </div>
                            </div> -->
                        </div>

                        <div class="form-group col-md-4">
                            <label for="invoiceDate" [ngClass]="{'required-label': isFieldRequired('invoiceDate')}">Invoice Date</label>
                            <input type="date" class="form-control" id="invoiceDate" formControlName="invoiceDate" [max]="today">
                            <!-- <div *ngIf="registerVehicleForm?.get('invoiceDate')?.invalid && (registerVehicleForm?.get('invoiceDate')?.dirty || registerVehicleForm?.get('invoiceDate')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('invoiceDate')?.errors?.['required']">
                                    Invoice Date is required.
                                </div>
                            </div> -->
                        </div>
                        <div class="form-group col-md-4">
                            <label for="customsDeclarationNumber" [ngClass]="{'required-label': isFieldRequired('customsDeclarationNumber')}">Customs Declaration Number</label>
                            <input type="text" class="form-control" id="customsDeclarationNumber" formControlName="customsDeclarationNumber">
                            <!-- <div *ngIf="registerVehicleForm?.get('customsDeclarationNumber')?.invalid && (registerVehicleForm?.get('customsDeclarationNumber')?.dirty || registerVehicleForm?.get('customsDeclarationNumber')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('customsDeclarationNumber')?.errors?.['required']">
                                    Customs Declaration Number is required.
                                </div>
                            </div> -->
                        </div>
                        
                    </div>

                    <div class="row mt-4">

                        <div class="form-group col-md-4">
                            <label for="customsDeclarationDate" [ngClass]="{'required-label': isFieldRequired('customsDeclarationDate')}">Customs Declaration Date</label>
                            <input type="date" class="form-control" id="customsDeclarationDate" formControlName="customsDeclarationDate" [max]="today">
                            <!-- <div *ngIf="registerVehicleForm?.get('customsDeclarationDate')?.invalid && (registerVehicleForm?.get('customsDeclarationDate')?.dirty || registerVehicleForm?.get('customsDeclarationDate')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('customsDeclarationDate')?.errors?.['required']">
                                    Customs Declaration Date is required.
                                </div>
                            </div> -->
                        </div>

                        <div class="form-group col-md-4">
                            <label for="declaredAmount" [ngClass]="{'required-label': isFieldRequired('declaredAmount')}">Purchase Amount</label>
                            <input type="number" class="form-control" id="declaredAmount" formControlName="declaredAmount">
                            <!-- <div *ngIf="registerVehicleForm?.get('declaredAmount')?.invalid && (registerVehicleForm?.get('declaredAmount')?.dirty || registerVehicleForm?.get('declaredAmount')?.touched || submitted)" class="text-danger">
                                <div *ngIf="registerVehicleForm?.get('declaredAmount')?.errors?.['required']">
                                    Purchase Amount is required.
                                </div>
                                <span *ngIf="registerVehicleForm.get('declaredAmount')?.errors?.['min']">Purchase must be a positive number.</span>
                            </div> -->
                        </div>

                        <div class="form-group col-md-4" *ngIf="acquisitionDetails.ownershipType.name == 'Project Vehicles'">
                            <label for="projectName">Project Name</label>
                            <input type="text" class="form-control" id="projectName" formControlName="projectName" readonly>
                        </div>

                    </div>

                    <div class="row mt-4"  *ngIf="acquisitionDetails.ownershipType.name == 'Project Vehicles'">
                        <div class="form-group col-md-4">
                            <label for="projectDescription">Project Description</label>
                            <textarea class="form-control" id="projectDescription" formControlName="projectDescription" rows="4" readonly></textarea>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="projectStartDate">Project Start Date</label>
                            <input type="date" class="form-control" id="projectStartDate" formControlName="projectStartDate" readonly>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="projectEndDate">Project End Date</label>
                            <input type="date" class="form-control" id="projectEndDate" formControlName="projectEndDate" readonly>
                        </div>
                        
                    </div>
                    
                    <h2 class="mt-4">File Upload Section </h2>
                    <!-- <div class="row" formArrayName="files">
                        <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                            <div class="form-group col-md-6">
                              <input type="file" (change)="onFileChange($event, i)" formControlName="file" class="form-control" />
                            </div>
                            <div class="form-group col-md-6">
                              <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                            </div>

                            <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                              <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                          </button>
                          </div>
                          <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                            <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add another file
                          </button>
                    </div> -->

                <div class="row mt-4">
                        <div class="form-group col-md-6">
                            <label for="vehiclePhysicalFile" [ngClass]="{'required-label': isFieldRequired('vehiclePhysicalFile')}">1. Vehicle Physical File</label>
                            <input type="file" (change)="onFileChange($event, 'vehiclePhysicalFile')" class="form-control" id="vehiclePhysicalFile">
                            <div *ngIf="registerVehicleForm.get('vehiclePhysicalFile')?.errors && registerVehicleForm.get('vehiclePhysicalFile')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('vehiclePhysicalFile')?.errors?.['required']">Vehicle Physical File is required.</span>
                            </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label for="taxPaymentProof" [ngClass]="{'required-label': isFieldRequired('taxPaymentProof')}">2. Proof of Tax Payment</label>
                            <input type="file" (change)="onFileChange($event, 'taxPaymentProof')" class="form-control" id="taxPaymentProof">
                            <div *ngIf="registerVehicleForm.get('taxPaymentProof')?.errors && registerVehicleForm.get('taxPaymentProof')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('taxPaymentProof')?.errors?.['required']">Tax Payment Proof is required.</span>
                            </div>
                        </div>    
                    </div>

                    <div class="row mt-4">
                        <div class="form-group col-md-6">
                            <!-- <label for="vehicleRegistrationFees" [ngClass]="{'required-label': isFieldRequired('vehicleRegistrationFees')}">3. Payment Motor Vehicle Registration Fees</label> -->
                            <label for="vehicleRegistrationFees" [ngClass]="{'required-label': isFieldRequired('vehicleRegistrationFees')}">3. Proof of Motor Payment </label>
                            <input type="file" (change)="onFileChange($event, 'vehicleRegistrationFees')" class="form-control" id="vehicleRegistrationFees">
                            <div *ngIf="registerVehicleForm.get('vehicleRegistrationFees')?.errors && registerVehicleForm.get('vehicleRegistrationFees')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('vehicleRegistrationFees')?.errors?.['required']">Payment Motor Vehicle Registration Fees is required.</span>
                            </div>
                        </div>
                    </div>

                    <h2 class="mt-3">Picture Upload Section</h2>
                    <p>Upload at least 2 pictures of the vehicle to register it</p>
                    <div class="row mt-4">
                        <div class="form-group col-md-6">
                            <label for="frontView" [ngClass]="{'required-label': isFieldRequired('frontView')}">Front View of the Vehicle</label>
                            <input type="file" (change)="onImageChange($event, 'frontView')" class="form-control" id="frontView">
                            <div *ngIf="registerVehicleForm.get('frontView')?.errors && registerVehicleForm.get('frontView')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('frontView')?.errors?.['required']">Vehicle front view is required.</span>
                              </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label for="backView" [ngClass]="{'required-label': isFieldRequired('backView')}">Back View of the Vehicle</label>
                            <input type="file" (change)="onImageChange($event, 'backView')" class="form-control" id="backView">
                            <div *ngIf="registerVehicleForm.get('backView')?.errors && registerVehicleForm.get('backView')?.touched" class="text-danger">
                                <span *ngIf="registerVehicleForm.get('backView')?.errors?.['required']">Vehicle behind view is required.</span>
                              </div>
                        </div>

                    </div>

                    <!-- <div class="row mt-5">
                        <div class="form-group col-md-6">
                            <label for="additional file">Any Additional Document?</label>
                            <input type="file" name="additionalFile" id="additionalFile" (change) = "onFileChange($event, 'additonalFile')" class="form-control">
                        </div>

                        <div class="form-group col-md-6">
                            <label for="description">Description</label>
                            <textarea name="description" id="description" rows="4" class="form-control" placeholder="Description of the Additional Document"></textarea>
                        </div>
                    </div> -->

                    <div class="row mt-4">
                        <div class="col-md-8"></div> 
                        <div class="col-md-4 buttons">
                            <!-- <button type="submit" class="btn col-md-9 float-end" (click)="onSubmitAndNext()">Register Vehicle</button> -->
                            <button type="submit" class="btn submit-btn col-md-9 float-end">Register Vehicle {{currentFormIndex +1}}</button>
                          </div>
                    </div>                    
                </form>
            </div>
        </div>
    </div>
</div>
<app-toastr></app-toastr>