@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');

* {
    font-family: 'Poppins', sans-serif;

}

.vehicle-container {
    background-color: #D9D9D94D;
    height: 90%;
    width: 83%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 3px;
}

.card {
    height: 94%;
    overflow-y: scroll;
}

.card::-webkit-scrollbar {
    width: 0;
}

.card-header {
    background-color: #28A4E2;
    color: white;
    position: fixed;
    z-index: 1;
    width: 80%;
}

h4 {
    color: #28A4E2;
    font-family: 'Poppins', sans-serif;
    font-weight: bold;
}

label {
    // color: #757575;
    color: rgb(146, 146, 146);
    font-weight: bold;
}

span,
p {
    // color: #757575;
    color: black;
}

.buttons {
    right: 0;
}

.separator-line {
    border-top: 2px solid rgb(146, 146, 146);
}

.action,
.btn {
    margin: 10px;
    color: white;

}

.btn-success {
    background-color: white;
    color: green;
    border: 1px solid green;
}

.btn-danger {
    background-color: white;
    color: red;
    border: 1px solid red;
}

.btn-secondary {
    background-color: white;
    color: grey;
    border: 1px solid grey;
}

.btn-warning {
    background-color: white;
    color: yellow;
    border: 1px solid yellow;

}

.btn-primary,
.btn-info {
    background-color: white;
    color: #28A4E2;
    border: 1px sold #28A4E2;
}

.notification{
    height: 30px;
    width: 30px;
    border-radius: 50%;
   

}
.instruction-text {
  font-size: 16px;
  margin-bottom: 15px;
  color: #6c757d;
}

.comment-box {
  width: 100%;
  height: 100px;
  margin-bottom: 15px;
  resize: vertical;
  border-radius: 5px;
  border: 1px solid #ced4da;
  padding: 10px;
  font-size: 14px;
  color: #495057;
}
.bellIcon {
    color: #28A4E2;
    font-size: large;
}

.btn-approve {
    background-color: rgb(48, 178, 48);
    color: white;
}

.btn-deny {
    color: white;
    background-color: rgb(153, 20, 20);
}

.modal-header {
    justify-content: center;
    /* This centers the content within the modal header */
}

/* Change the color of the modal title */
.modal-title {
    color: #28A4E2;
    /* Set the title color to the desired value */
}

.go-back-btn {
    border: 1px solid #28A4E2;
    color: #28A4E2;
    width: 130px;
    font-size: 14px;
    font-weight: 500;
}
.approval-message {
    background-color: rgba(204, 111, 158, 0.5); /* Greenish color with transparency */
    border: 1px solid #6c9; /* Darker border color */
    border-radius: 5px;
    padding: 15px;
  }
  
  .approval-message p {
    margin-bottom: 10px;
  }
  
  .approval-message button {
    margin-top: 10px;
    background-color: #e93c73; /* Green button color */
    color: white; /* Text color */
    border: none; /* Remove border */
    border-radius: 5px; /* Rounded corners */
    padding: 10px 20px; /* Padding */
    cursor: pointer; /* Cursor on hover */
    transition: background-color 0.3s; /* Smooth transition */
  }
  
  .approval-message button:hover {
    background-color:#e93c73; /* Darker green on hover */
  }
  .spinner {
    width: 50px;
    height: 50px;
    border: 8px solid rgba(0, 0, 0, 0.1);
    border-top: 8px solid #007bff; /* Blue color */
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .loading-text {
    font-size: 16px;
    color: #007bff; /* Blue color matching the spinner */
  }
  .image-section {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    margin-bottom: 20px;
    transition: all 0.3s ease-in-out;
  }
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    border-bottom: 2px solid #ddd;
    padding-bottom: 8px;
    transition: color 0.3s ease;
  }
  
  .section-title:hover {
    color: #007bff;
  }
  
  .section-subtitle {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 15px;
    color: #555;
  }
  
  .document-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #f9f9f9;
  }
  
  .document {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
    cursor: pointer;
  }
  
  .document:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-4px);
  }
  
  .file-icon {
    font-size: 1.2rem;
    color: #007bff;
  }
  
  .file-link {
    text-decoration: none;
    color: #007bff;
    font-weight: 500;
    transition: color 0.3s ease;
    cursor: pointer;
  }
  
  .file-link:hover {
    color: #0056b3;
  }
  
  .image-preview {
    margin-top: 20px;
    border-top: 2px solid #ddd;
    padding-top: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
    
  }
  
  .preview-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
  }
  
  .image-wrapper {
    position: relative;
    overflow: hidden;
  }
  .preview-image {
    opacity: 1;
    transition: opacity 0.5s ease-in-out; /* Add transition effect */
  }
  
  .preview-image.hidden {
    opacity: 0; /* When hidden, the opacity becomes 0 */
  }
  
  
  // .preview-image {
  //   width: 100%;
  //   height: auto;
  //   border: 1px solid #ddd;
  //   border-radius: 12px;
  //   padding: 10px;
  //   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  //   background-color: #fff;
  //   transition: opacity 0.5s ease-in-out;
  //   position: absolute;
  // }
  
  .image-wrapper img {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
  }
  
  .image-wrapper img.hidden {
    opacity: 0;
  }
  