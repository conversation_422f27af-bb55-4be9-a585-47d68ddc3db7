<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>

    <div class="vehicle-container">
      <div class="card m-3 p-3">
        <button class="btn go-back-btn" (click)="goBack()">
          <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
        </button>
        <div class="row">
          <div class="col-md-6">
            <!-- Left Column: Vehicle and Additional Details -->
            <!-- Vehicle Details -->
            <div class="vehicle-details" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Vehicle Information</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label>Chassis Number</label>
                  <p>{{ vehicle.chassisNumber || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Engine Number</label>
                  <p>{{ vehicle.engineNumber || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Transmission Type</label>
                  <p>{{ vehicle.transmissionType || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Manufacturing Year</label>
                  <p>{{ vehicle.manufacturingYear || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Vehicle Type</label>
                  <p>{{ vehicle.vehicleType?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Vehicle Model</label>
                  <p>{{ vehicle.vehicleModel?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Ownership Type</label>
                  <p>{{ vehicle.ownershipType?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Vehicle Manufacture</label>
                  <p>{{ vehicle.vehicleManufacture?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Beneficiary Agency</label>
                  <p>{{ vehicle.beneficiaryAgency || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Approval Level</label>
                  <p>{{ vehicle.approvalLevel?.name || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">

                  <label>Status</label>
                  <p
                  [ngClass]="{
                    'status-registered fw-bold': vehicle.registrationStatus?.name === 'APPROVED',
                    'status-unregistered fw-bold' : vehicle.registrationStatus?.name === 'PROGRESS' || vehicle.registrationStatus?.name === 'DENIED' || vehicle.registrationStatus?.name === 'PENDING'
                  }"
                >
                  {{
                      vehicle.registrationStatus?.name === 'APPROVED' ? 'REGISTERED' : 'Unregistered'

                  }}
                </p>
                </div>
              </div>

            </div>

            <!-- Additional Information -->
            <div class="additional-info mt-3" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Additional Information</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label>Customs Declaration Number</label>
                  <p>{{ vehicle.customsDeclarationNumber || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Customs Declaration Date</label>
                  <p>{{ vehicle.customsDeclarationDate || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Invoice Number</label>
                  <p>{{ vehicle.invoiceNumber || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Invoice Date</label>
                  <p>{{ vehicle.invoiceDate || 'N/A' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Acquisition Date</label>
                  <p>{{ vehicle.acquisitionDate || 'N/A' }}</p>
                </div>
                <div class="col-md-3 mb-3" *ngIf="vehicle?.plateNumber">
                  <label>Plate Number</label>
                  <p>{{ vehicle?.plateNumber }}</p>
              </div>
              </div>
                <div class="col-md-4 mb-3" *ngIf="vehicle?.pickCardNumber">
                    <!-- <label>Pink Card Number </label> -->
                    <label>Pink Card Number </label>
                    <p>{{ vehicle?.pickCardNumber }}</p>
                </div>
               <div class="row" *ngIf="documentsPdf.length > 0">
                  <div class="col-md-12">
                    <h5>PDF Documents</h5>
                    <div class="document-container">
                      <div class="document" *ngFor="let document of documentsPdf">
                        <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                        <a (click)="fetchBase64Data(document.fileName)" class="file-link">{{ document.fileName || 'N/A' }}</a>
                      </div>
                    </div>
                  </div>
                </div>
            </div>

            <div *ngIf="documentsImages.length > 0" class="image-section additional-info mt-3" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Images</h4>
              <div class="row">
                <div class="col-md-12">
                  <h3>Image Documents</h3>
                  <div class="document-container">
                    <div class="document" *ngFor="let document of documentsImages">
                      <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                      <a (click)="fetchImageBase64(document.fileName)" class="file-link">
                        {{ document.documentDescription || 'N/A' }}
                      </a>
                    </div>
                  </div>

                  <!-- Display the selected image in the UI -->
                  <div *ngIf="selectedImageBase64" class="image-preview mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                      <h3>Selected Image</h3>
                      <button (click)="closeImagePreview()" class="btn btn-danger mt-2">Close Preview</button>
                    </div>
                    <img [src]="selectedImageBase64" alt="Selected Document Image" class="img-fluid" style="max-width: 100%; height: auto; border: 1px solid #ccc; border-radius: 10px;" />
                  </div>
                  </div>
              </div>
            </div>




            <!-- Project Details -->
            <div *ngIf="vehicle.projectName" class="project-details" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
              <h4>Project Information</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label>Project Name</label>
                  <p>{{ vehicle.projectName || 'Not specified' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Project Start Date</label>
                  <p>{{ vehicle.projectStartDate || 'Not specified' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Project End Date</label>
                  <p>{{ vehicle.projectExtensionDate || 'Not specified' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                  <label>Project Description</label>
                  <p>{{ vehicle.projectDescription || 'Not specified' }}</p>
                </div>
              </div>
            </div>
               </div>


          <!-- Right Column: Additional Information and Project Details -->
          <div class="col-md-6">
            <div *ngIf="vehicle.registrationStatus.name === 'APPROVED'" class="mb-5 p-4 approval-message">
              <p>This vehicle has been succesfully registered. Click below to view the aPink Card.</p>
              <button class="btn btn-info" (click)="redirectToPinkCard()">View Pink Card</button>
            </div>
            <app-registration-history></app-registration-history>
            <!-- <div *ngIf="vehicle.registrationStatus.name === 'APPROVED'"><app-device-location></app-device-location> </div> -->
          </div>

      <!-- Action Buttons -->
      <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canSeeActionButtons()">
        <button class="btn btn-success" *ngIf="canApprove()" (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
          <fa-icon [icon]="approveIcon" class="pe-1"></fa-icon>Approve
        </button>
        <button class="btn btn-success" *ngIf="!canApprove()" (click)="setAction('APPROVED')" data-bs-toggle="modal" data-bs-target="#actionModal">
          <fa-icon [icon]="submitIcon" class="pe-1"></fa-icon>Submit To Next Level
        </button>  <button class="btn btn-danger" *ngIf="canDeny()" (click)="setAction('DENIED')" data-bs-toggle="modal" data-bs-target="#actionModal"><fa-icon [icon]="denyIcon" class="pe-1"></fa-icon> Deny</button>
        <button class="btn btn-secondary" (click)="setAction('RFAC')" data-bs-toggle="modal" data-bs-target="#actionModal"><fa-icon [icon]="moreIcon" class="pe-1"></fa-icon> More Actions</button>
      </div>

        <!-- Display the Edit button only if canEditVehicle() returns true -->
        <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canEditVehicle()">
          <button class="btn btn-info" (click)="editVehicle(vehicle.id)">Edit</button>
        </div>


      <!-- Modal for Action Confirmation -->
      <div class="modal fade" id="actionModal" aria-labelledby="actionModalLabel" aria-hidden="true">
        <div class="modal-dialog w-75">
          <div class="modal-content animate__animated animate__fadeInUp">
            <!-- Modal Header -->
            <div class="modal-header border-0 bg-info text-white position-relative">
              <h4 class="modal-title w-100 text-center text-white" id="actionModalLabel">Action Confirmation</h4>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                aria-label="Close"></button>
            </div>
          <div *ngIf="isLoading" class="loader-wrapper d-flex justify-content-center align-items-center flex-column">
            <app-spinner></app-spinner>
            <p class="loading-text">Loading...</p>
          </div>

          <!-- Success Message -->
          <div *ngIf="!isLoading && actionSuccessful" class="success-wrapper text-center">
            <div class="form-card">
              <h2 class="text-success">Success</h2>
              <div class="row justify-content-center">
                   <div class="col-md-4">
                  <img src="https://img.icons8.com/color/96/000000/ok--v2.png" class="fit-image" alt="Success Icon">
                </div>
              </div>
              <br><br>
            </div>
          </div>

              <div class="modal-body" *ngIf="!isLoading && !actionSuccessful">
                <p class="instruction-text">Add a comment if needed, then click "Confirm" to proceed.</p>
                <div *ngIf="isFleetMgtSeniorEngineer()" >
                  <!-- <label>Plate Number</label> -->
                  <input type="text" class="form-control mb-3" required [(ngModel)]="plateNumber" placeholder="Assign Plate Number"  #plateNumberInput="ngModel"/>

                  <!-- <label>Pick Card Number</label> -->
                  <input type="text" class="form-control mb-3" required [(ngModel)]="pickCardNumber" placeholder="Assign Pink Card Number"  #pickCardNumberInput="ngModel"/>

                </div>
                <textarea class="form-control comment-box" [(ngModel)]="actionComment" placeholder="Add your comments here..."></textarea>
              </div>
              <div class="modal-footer" *ngIf="!isLoading && !actionSuccessful">
                <!-- <button type="button" class="btn btn-secondary cancel-btn" data-bs-dismiss="modal">Cancel</button>
                <button *ngIf="!isLoading && !actionSuccessful" type="button" class="btn btn-info confirm-btn" (click)="confirmAction()">Confirm</button> -->
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-info" (click)="confirmAction()">Confirm</button>
              </div>
            </div>
          </div>
        </div>
      </div>
</div>
</div>
</div>
