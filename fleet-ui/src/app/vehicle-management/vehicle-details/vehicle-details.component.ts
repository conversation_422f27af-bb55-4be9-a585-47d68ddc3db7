import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { faArrowLeft,faThumbsUp, faThumbsDown,faQuestion, faPaperPlane, faFile, faCameraAlt, faCamera } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';
import { SafeUrl, DomSanitizer } from '@angular/platform-browser';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-vehicle-details',
  templateUrl: './vehicle-details.component.html',
  styleUrls: ['./vehicle-details.component.scss']
})
export class VehicleDetailsComponent implements OnInit {
  vehicle: any = {}; // Store the vehicle details
  actionComment = ''; // For modal comments
  selectedAction = ''; // To track the action selected
  plateNumber = ''; // New field for plate number
  pickCardNumber = ''; 
  userRole='';
  backwardIcon = faArrowLeft;
  approveIcon = faThumbsUp;
  denyIcon = faThumbsDown;
  moreIcon = faQuestion;
  submitIcon=faPaperPlane;
  loggedInUser='';
  isLoading: boolean = false;
  actionSuccessful = false; 
  fetchedVehicleId=''
  documentsPdf: any[] = [];
  documentsImages: any[] = [];
  fileIcon = faFile;
  ImageIcon = faCamera
  imageSource: SafeUrl | null = null;
  selectedImageBase64: string| null = null;

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private router: Router,
    private modalService: NgbModal,
    private location: Location,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit() {
  
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
      this.userRole = parseObj?.data?.user?.role?.name; // Store the user's role
      this.loggedInUser = parseObj.data.user.id;

      // console.log("User Role in ngOnInit:", this.userRole);
    }
  
    this.route.paramMap.subscribe((params) => {
      const vehicleId = params.get('vehicleId');
      if (vehicleId) {
        this.fetchedVehicleId = vehicleId
        this.fetchVehicleDetails(vehicleId);
        this.fetchDocuments(vehicleId);
        
      }
    });
  }
  
  isFleetMgtSeniorEngineer(): boolean {
    return this.userRole === 'Fleet Mgt Senior Engineer' && this.selectedAction=== 'APPROVED' // Check if user is Fleet Mgt Senior Engineer
  }
  fetchVehicleDetails(vehicleId: string) {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.vehicle = response; // Store the vehicle data
        console.log("Vehicle Data:", response);
        // console.log("Approval Level in Vehicle:", this.vehicle?.approvalLevel?.name); // Check approval level
      },
      (error) => {
        console.error('Error fetching vehicle details:', error); // Handle errors
      }
    );
  }
  

  setAction(action: string) {
    this.selectedAction = action;
  }

  confirmAction() {
    if (!this.vehicle || !this.vehicle.id || !this.selectedAction) {
      console.error('Required data is missing: vehicle ID or action');
      this.toastr.error('Required data is missing');
      return;
    }
  
    let approvalData: any;
    let apiUrl: string;
  
    if (this.vehicle.isrecordedFromOldVehicle) {
      // Prepare approval data for vehicles recorded from old vehicles
      apiUrl = `${environment.baseUrl}/approval/ExistingVehicleRegApproval`;
      approvalData = {
        userId: this.loggedInUser,
        vehicleId: this.vehicle.id,
        decision: this.selectedAction.toUpperCase(),
        comments: this.actionComment,
        isVehicleActive: this.vehicle.isVehicleActive,
        vehicleStatusId: this.vehicle.vehicleStatus?.id
      };
    } else {
      // Prepare approval data for new vehicles
      apiUrl = `${environment.baseUrl}/approval/VehicleRegApproval`;
      approvalData = {
        userId: this.loggedInUser,
        vehicleId: this.vehicle.id,
        decision: this.selectedAction.toUpperCase(),
        comments: this.actionComment,
        plateNumber: this.plateNumber,
        pickCardNumber: this.pickCardNumber
      };
    }
  
    console.log("Approval Data:", approvalData);
    this.isLoading = true;
    this.actionSuccessful = false;
  
    this.http.post(apiUrl, approvalData).subscribe(
      (response) => {
        setTimeout(() => {
          this.isLoading = false;
          this.actionSuccessful = true;
          this.toastr.success("Action confirmed successfully");
          console.log("final body", response);
  
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        }, 3000);
      },
      (error) => {
        this.isLoading = false;
        this.toastr.error("Error sending approval");
        console.error("Error during approval:", error);
        console.log("Error details:", error.error);
      }
    );
  }
  

  approveVehicle() {
    this.setAction('APPROVED');
  }

  denyVehicle() {
    this.setAction('DENIED');
  }

  moreAction() {
    this.setAction('RFAC');
  }

  editVehicle(vehicleId: string) {
    this.router.navigateByUrl(`/vehicle-management/edit-vehicle/${vehicleId}`).then((success) => {
      if (!success) {
        console.error('Navigation to edit page failed');
      }
    });
  }
  canSeeActionButtons(): boolean {
    const approvalLevel = this.vehicle?.approvalLevel.name.trim(); // Get the approval level from vehicle data
    // console.log("User Role:", this.userRole);
    // console.log("Vehicle Approval Level:", approvalLevel);
    
    return this.userRole && approvalLevel && this.userRole === approvalLevel;
  }
  canEditVehicle(): boolean {
    // Check if the status is 'RFAC', user role is 'Institutional Logistics', and approval level is also 'Institutional Logistics'
    return (
      this.vehicle?.status === 'RFAC' &&
      this.userRole === 'Institutional Logistics' &&
      this.vehicle?.approvalLevel?.name === 'Institutional Logistics'
    );
  }
  redirectToPinkCard() {
    const vehicleId = this.fetchedVehicleId; // Assuming you have access to the vehicle ID
    if (vehicleId) {
      this.router.navigate([`/vehicle-management/vehicle-details/${vehicleId}/pinkCard`]);
    } else {
      console.error('No vehicle ID available.');
    }

  }
  goBack(){
    this.location.back();
  }
  canApprove(): boolean {
    return this.userRole === "Minister"||this.userRole === 'Permanent Secretary';
  }
  canDeny():boolean{
    return this.userRole ==='Minister' ||this.userRole === 'Institutional CBM'||this.userRole === 'Permanent Secretary' ;
  }
  fetchDocuments(vehicleId: string) {
    const url = `${environment.fileUrl}/${vehicleId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        // Filter documents into PDFs and Images
        this.documentsPdf = response.filter(doc => doc.fileName?.toLowerCase().endsWith('.pdf'));
        this.documentsImages = response.filter(doc => /\.(jpeg|jpg|png)$/i.test(doc.fileName));
  
        // Console logs to verify the content
        console.log('Full response:', response); // Logs the full response
        console.log('PDF Documents:', this.documentsPdf); // Logs PDF documents
        console.log('Image Documents:', this.documentsImages); // Logs image documents
  
        // Check if the images array has elements
        if (this.documentsImages.length === 0) {
          console.warn('No images found in the response.');
        }
      },
      (error) => {
        console.error('Error fetching documents:', error);
      }
    );
  }


  fetchBase64Data(fileName: string) {
    const url = `${environment.fileUrl}/${fileName}/base64`;
    this.http.get<any>(url).subscribe(
      (response) => {
        const binaryData = atob(response.base64Data);
        const arrayBuffer = new ArrayBuffer(binaryData.length);
        const uint8Array = new Uint8Array(arrayBuffer);
        for (let i = 0; i < binaryData.length; i++) {
          uint8Array[i] = binaryData.charCodeAt(i);
        }
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        window.open(url);
      },
      (error) => {
        console.error('Error fetching Base64 data:', error);
      }
    );
  }
  // fetchImageBase64(fileName: string) {
  //   const url = `${environment.fileUrl}/${fileName}/base64`;
  //   this.http.get<any>(url).subscribe(
  //     (response) => {
  //       const binaryData = atob(response.base64Data);
  //       const arrayBuffer = new ArrayBuffer(binaryData.length);
  //       const uint8Array = new Uint8Array(arrayBuffer);
  //       for (let i = 0; i < binaryData.length; i++) {
  //         uint8Array[i] = binaryData.charCodeAt(i);
  //       }
  //       const blob = new Blob([arrayBuffer], { type: 'image/jpeg' }); // Change type accordingly
  //       const blobUrl = window.URL.createObjectURL(blob);
  //       window.open(blobUrl); // Open the image in a new tab
  //     },
  //     (error) => {
  //       console.error('Error fetching image Base64 data:', error);
  //     }
  //   );
  // }
  
  fetchImageBase64(fileName: string) {
    const url = `${environment.fileUrl}/${fileName}/base64`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.selectedImageBase64 = 'data:image/jpeg;base64,' + response.base64Data;
        // You can also handle different image types by setting the correct MIME type based on file extension
      },
      (error) => {
        console.error('Error fetching image Base64 data:', error);
        this.selectedImageBase64 = null; // Clear the image if an error occurs
      }
    );
  }
  closeImagePreview() {
    this.selectedImageBase64 = null; // Clear the selected image
  }
  

  displayImage(imageSrc: string) {
    this.imageSource = this.sanitizer.bypassSecurityTrustUrl(imageSrc);
  }
}

