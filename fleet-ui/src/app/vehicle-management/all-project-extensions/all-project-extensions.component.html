<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicles-container">
    <app-breadcrumb></app-breadcrumb>
    <div class="header d-flex flex-row justify-content-between">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
        <h2 class="table-title">All Project Extensions </h2>

        <ng-container *ngIf="!isLogistics()">
          <div class="search-group d-flex mt-2">
            <fa-icon [icon]="searchIcon" class="icon"></fa-icon>
            <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchVehicles()">
        </div>
        </ng-container>

    </div>


    <div class="card">
      <div class="table-header d-flex flex-row justify-content-between">

          <!-- Table Filter -->
          <div class="d-flex flex-row justify-content-start align-items-center statuses">
            <button class="btn btn-outline" (click)="setFilter('all')" [class.active]="currentFilter === 'all'">All</button>
            <button class="btn btn-outline" (click)="setFilter('progress')" [class.active]="currentFilter === 'progress'">Pending Requests</button>
            <button class="btn btn-outline" (click)="setFilter('approved')" [class.active]="currentFilter === 'approved'">Approved</button>
            <!-- <button *ngIf="showReportingVehiclesTab" class="btn btn-outline" (click)="setFilter('reporting')" [class.active]="currentFilter === 'reporting'">Reporting Vehicles</button>         -->
           </div>
          <div class="sorting-group d-flex flex-row p-2">
              <label for="sort-field" class="text-muted p-1">Sort by:</label>
              <select id="sort-field" class="select" (change)="sortable($event)">
                  <option value="">Select Field</option>
                  <option value="institution">Institution</option>
                  <option value="requestType">Request Type</option>
                  <option value="ownershipType">Ownership Type</option>
                  <option value="status">Status</option>
                </select>
          </div>
      </div>

      <div class="table-only p-3">

          <table class="table table-stripped">
            <thead>
              <tr>
                <th>Chassis Number</th>
                <th>Beneficiary Agency</th>
                <th>Project Name </th>
                <th>New Project End Date</th>
                <th>Project on-going</th>
                <th>Vehicle Active</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngIf="displayedExtensions.length <= 0">
                <td colspan="9" class="no-vehicles-message">No Extension Requests!!!</td>
              </tr>
              <tr *ngFor="let extension of displayedExtensions" >
                <td>{{ extension.vehicle.chassisNumber }}</td>
                <td>{{ extension.vehicle.beneficiaryAgency }}</td>
                <td>{{ extension.vehicle.projectName}}</td>
                <td>{{ extension.newProjectEndDate}}</td>
                <td>
                  <p [ngClass]="{
                   'status-active':extension.vehicle.isProjectOnGoing,
                   'status-inactive': !extension.vehicle.isProjectOnGoing
                  }">
                   {{ extension.vehicle.isProjectOnGoing == true ? 'Yes' : 'No' }}
                 </p>
                 </td>
                  <td>
                      <p [ngClass]="{
                       'status-active':extension.isVehicleActive,
                       'status-inactive': !extension.isVehicleActive
                      }">
                       {{ extension.isVehicleActive === true ? 'Active' : 'Inactive'}}
                     </p>
                     </td>

                <td class="d-flex flex-row">
                  <button class="btn view-btn" (click)="viewExtension(extension.id)">View</button>
                  <!-- <button *ngIf="extension.registrationStatus.name == 'RFAC'|| userRole == 'Institutional logistics' || userRole == 'Institutional CBM' || userRole == 'Fleet Mgt Senior Engineer'" type="button" class="btn edit-btn" style="margin: 0px 14px;" (click)="editRegisteredextension(extension.id)"> Edit </button> -->
                  <!-- <button *ngIf="userRole == 'Institutional logistics' && extension.registrationStatus.name == 'APPROVED'&& extension.isDisposalRequestSubmitted === false" class="btn dispose-btn" style="margin: 0px 14px;" (click)="disposeextension(extension.id)"> Dispose </button> -->
                  <!-- <button *ngIf="userRole == 'Institutional logistics' && extension.registrationStatus.name == 'APPROVED'&& extension.isDisposalRequestSubmitted === false" class="btn extension-btn" style="margin: 0px;" (click) = "extensionextension(extension.id)">Quarterly extension</button> -->
                </td>
              </tr>
            </tbody>
          </table>
        </div>

      <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
        <div class="pagination-info">
          Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredProjectExtensions.length }} entries
        </div>
        <ul class="pagination justify-content-center">
          <li class="page-item">
            <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
              <fa-icon [icon]="caretLeft"></fa-icon>
            </button>
          </li>
          <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
            <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
              {{ pageNumber }}
            </button>
          </li>
          <li class="page-item">
            <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
              <fa-icon [icon]="caretRight"></fa-icon>
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
