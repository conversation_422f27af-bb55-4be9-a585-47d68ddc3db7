import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faSearch, faCaretLeft, faCaretRight, faCar, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-all-project-extensions',
  templateUrl: './all-project-extensions.component.html',
  styleUrl: './all-project-extensions.component.scss'
})
export class AllProjectExtensionsComponent {
  allProjectExtensions: any[] = [];
  filteredProjectExtensions: any[] = [];
  pageSize: number = 10;
  currentPage: number = 1;
  totalPages: number = 0;
  institutionId: string = '';
  userRole: string = '';
  searchText: string = '';
  searchIcon = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  carIcon = faCar;
  displayedExtensions: any[] = [];
  currentFilter = 'all';
  backwardIcon = faArrowLeft;


  constructor(private http: HttpClient, private router: Router, private location: Location) {}

  ngOnInit() {
    this.getUserDetails()
    this.fetchAllProjectExtensions()
  }

  goBack(){
    this.location.back();
  }

  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id; // Store institution ID
      this.userRole = parsedObj.data.user.role.name; // Store user role
    }
  }

  fetchAllProjectExtensions() {
    const url = `${environment.otherUrl}/allProjectExtension`;

    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
      
        console.log(response);

        this.allProjectExtensions = response;
        this.fetchExtensionsByRole();
        this.applyCurrentFilter();
        this.updateDisplayedExtensions()
      },
      (error) => {
        console.error('Error fetching all project extensions:', error);
      }
    );
  }

  fetchExtensionsByRole() {
    this.filteredProjectExtensions = [...this.allProjectExtensions];

    switch(this.userRole) {
      case 'Institutional logistics':
        this.filteredProjectExtensions = this.filteredProjectExtensions.filter(
          extension => extension.vehicle.beneficiaryAgencyId === this.institutionId
        );
        break;
    
      case 'Fleet Mgt Senior Engineer' :
        this.filteredProjectExtensions = this.filteredProjectExtensions.filter(
          report => report.approvalLevel?.name !== 'Institutional logistics'
        );
        break;
    }
  }

  applyCurrentFilter() {
    switch (this.currentFilter) {
      case 'all':
        this.filteredProjectExtensions = this.allProjectExtensions;
        break;
      case 'progress':
        this.filteredProjectExtensions = this.allProjectExtensions.filter(
          (item) => item.requestStatus?.name === 'PENDING' || item.requestStatus?.name === 'PROGRESS' || item.requestStatus?.name === 'RFAC'
        );
        break;
      case 'approved':
        if(this.userRole === 'Institutional logistics') {
          this.filteredProjectExtensions= this.allProjectExtensions.filter(
            (item) => (item.requestStatus?.name === 'APPROVED' || item.requestStatus?.name === 'DENIED') && item.vehicle.beneficiaryAgencyId === this.institutionId
          );
        } else {
          this.filteredProjectExtensions = this.allProjectExtensions.filter(
            (item) => item.requestStatus?.name === 'APPROVED'
          );
        }
        break;
      default:
        console.error('Invalid filter type: ', this.currentFilter);
        this.filteredProjectExtensions = this.allProjectExtensions;
    }

    this.currentPage = 1;
    this.updateDisplayedExtensions();
  }
  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'bg-success'; // Corresponds to green
        case 'pending':
            return 'bg-secondary'; // Corresponds to yellow/orange
        case 'denied':
            return 'bg-danger'; // Corresponds to red
        case 'rfac':
            return 'bg-info'; // Corresponds to blue
        case 'progress':
            return 'bg-warning'; // Corresponds to blue
        default:
            return ''; // Default or fallback class
    }
}
getProgressWidth(status: string): string {
  switch (status.toLowerCase()) {
    case 'approved':
      return '100%'; // Corresponds to green
    case 'pending':
      return '70%'; // Corresponds to yellow/orange
    case 'denied':
      return '100%'; // Corresponds to red
    case 'rfac':
      return '60%'; // Corresponds to blue
    case 'progress':
      return '80%'; // Corresponds to blue
    default:
      return '';
  }
}

  setFilter(filterType: string) {
    this.currentFilter = filterType;
    this.applyCurrentFilter();
  }
  updateDisplayedExtensions() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredProjectExtensions.length);
  
    // Create a separate array for the displayed vehicles based on the filtered data
    this.displayedExtensions = this.filteredProjectExtensions.slice(startIndex, endIndex); // Slice within the filtered list
  
    // Recalculate the total number of pages based on the filtered vehicles
    this.totalPages = Math.ceil(this.filteredProjectExtensions.length / this.pageSize);
  }

  getFirstEntryIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.filteredProjectExtensions.length);
  }

  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedExtensions();
    }
  }

  getPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedExtensions();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedExtensions();
    }
  }

  sortable(event: any) {
    const selectedField: string = event.target.value;

    if (!selectedField) {
      this.filteredProjectExtensions.sort((a, b) => a.beneficiaryAgency.localeCompare(b.beneficiaryAgency));
    } else {
      this.filteredProjectExtensions.sort((a, b) => {
        const fieldA = a[selectedField];
        const fieldB = b[selectedField];
        if (typeof fieldA === 'string' && typeof fieldB === 'string') {
          return fieldA.localeCompare(fieldB);
        }
        return 0;
      });
    }
    this.updateDisplayedExtensions();
  }

  isLogistics(): boolean {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }

  viewExtension(extentionId: string): void {
    console.log('Report ID: ', extentionId);
    this.router.navigateByUrl(`/vehicle-management/project-extension-details/${extentionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
  searchVehicles() {
    const searchTextLower = this.searchText.toLowerCase();
    
    // Apply search only to the already filtered project extensions
    const filteredBySearch = this.filteredProjectExtensions.filter(item => {
      return (
        (item.chassisNumber || '').toLowerCase().includes(searchTextLower) ||
        (item.beneficiaryAgency || '').toLowerCase().includes(searchTextLower) ||
        (item.projectName || '').toLowerCase().includes(searchTextLower) ||
        (item.newProjectEndDate || '').toLowerCase().includes(searchTextLower) ||
        (item.isVehicleActive ? 'active' : 'inactive').includes(searchTextLower)
      );
    });
  
    // Update displayed project extensions based on the search result
    this.displayedExtensions = filteredBySearch.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
    this.totalPages = Math.ceil(filteredBySearch.length / this.pageSize);
  }
  
}

