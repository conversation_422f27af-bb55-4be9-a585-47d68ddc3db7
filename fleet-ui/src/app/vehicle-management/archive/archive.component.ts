import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { LoaderService } from '../../shared/loader.service';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-archive',
  templateUrl: './archive.component.html',
  styleUrl: './archive.component.scss'
})
export class ArchiveComponent {
  userId: string = ''; 
  institutionName: any;
  role: any;
  approvalHistory: any={};
  toastr: any;
  constructor(private http:HttpClient,private router: Router,public loaderService: LoaderService,) { }
  ngOnInit() {
    this.getUserData();
    this.fetchApprovalHistory()
  }
  getUserData(){
    const data = localStorage.getItem('localUserData');
    if(data !== null){
      const parseObj = JSON.parse(data);
      this.userId = parseObj.data.user.id;
      console.log(this.userId)
      this.role = parseObj.data.user.role.name;
     // this.beneficiaryAgencyId = parseObj.data.user.beneficiaryAgency.id;
    }
  }
  fetchApprovalHistory() {
    const url = `${environment.baseUrl}/approval/getAllHistoryByUserId?userId=${this.userId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.approvalHistory = response; // Store the approval history data
        console.log("Approval History Data:", response);
      },
      (error) => {
        console.error('Error fetching approval history:', error);
        this.toastr.error('Error fetching approval history.');
      }
    );
  }
  
}

