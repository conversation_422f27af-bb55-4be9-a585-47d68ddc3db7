<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="archive-container">
        <h2>Archive</h2>
        <div style="background-color: white;" class="p-5">
          <div *ngIf="approvalHistory && approvalHistory.length > 0">
          <div *ngFor="let history of approvalHistory">
        
            <div class="activity-card p-10" style="border: 1px solid rgb(243, 243, 243);" *ngFor="let activity of history.activities">
              <div class="activity-header">
                <h4>Date: {{ activity.DateOfActivity }}</h4>
                <p>Activity Performed: {{ activity.activityPerformed }}</p>
              </div>
              
              <div class="activity-details">
                <p><strong>Comments:</strong> {{ activity.comments }}</p>
                <p><strong>Approval Level:</strong> {{ activity.approvalLevel.name }} ({{ activity.approvalLevel.code }})</p>
              </div>
            </div>
          </div>
        </div>
        
        <div *ngIf="!approvalHistory || approvalHistory.length === 0">
          <p>No Archive available.</p>
        </div>
      </div> 
        </div>
       
      
</div>
