@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');

*{
    font-family: 'Poppins', sans-serif;
   
  }
.auction-container{
    background-color: #D9D9D94D;
    height: 90%;
    width: 83%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 3px;
}
.card{
    height: 94%;
    overflow-y: scroll;
}
.card::-webkit-scrollbar {
    width: 0;  
}
.card-header{
    background-color: #28A4E2;
    color:  white;
    position: fixed;
    z-index: 1;
    width: 80%;
}
h4{
    color: #28A4E2;
    font-family: 'Poppins', sans-serif;
    font-weight: bold;
}
label{
    // color: #757575;
    color: rgb(146, 146, 146);
    font-weight: bold;
}
span,p{
    // color: #757575;
    color: black;
}
.buttons{
    right: 0;
}
.separator-line {
    border-top: 2px solid rgb(146, 146, 146);
  }
.action, .btn {
    margin: 10px;
    color: white;
   
}
.btn-success{
    background-color: white;
    color: green;
    border: 1px solid green;
}
.btn-danger{
    background-color: white;
    color: red;
    border: 1px solid red;
}
.btn-secondary{
    background-color: white;
    color: grey;
    border: 1px solid grey;
}
.btn-warning{
    background-color: white;
    color: yellow;
    border: 1px solid yellow;

}
.btn-primary,.btn-info{
    background-color: white;
    color: #28A4E2;
    border: 1px sold #28A4E2;
}
.notification{
    height: 30px;
    width: 30px;
    border-radius: 50%;
}

.bellIcon{
    color: #28A4E2;
    font-size: large;
}
.btn-approve{
    background-color: rgb(48, 178, 48);
    color: white;
}
.btn-deny{
    color: white;
    background-color: rgb(153, 20, 20);
}
.modal-header {
    justify-content: center; 
  }
  
  
  .modal-title {
    color: #28A4E2; 
  }
  .go-back-btn{
    border: 1px solid #28A4E2;
    color: #28A4E2;
    width: 130px;
    font-size: 14px;
    font-weight: 500;
  
  }
  .approval-message {
    background-color: rgba(144, 238, 144, 0.5); /* Greenish color with transparency */
    border: 1px solid #6c9; /* Darker border color */
    border-radius: 5px;
    padding: 15px;
  }
  
  .approval-message p {
    margin-bottom: 10px;
  }
  
  .approval-message button {
    margin-top: 10px;
    background-color: #4caf50; /* Green button color */
    color: white; /* Text color */
    border: none; /* Remove border */
    border-radius: 5px; /* Rounded corners */
    padding: 10px 20px; /* Padding */
    cursor: pointer; /* Cursor on hover */
    transition: background-color 0.3s; /* Smooth transition */
  }
  
  .approval-message button:hover {
    background-color: #388e3c; /* Darker green on hover */
  }
  .document-container {
    margin-bottom: 10px; 
  }
  
  .document {
    display: flex; 
    align-items: center; 
    padding: 5px; 
    border-radius: 5px; 
    background-color: #f5f5f5; 
  }
  
  .file-icon {
    color: #007bff; 
    margin-right: 10px; 
  }
  
  .file-link {
    color: #007bff; 
    text-decoration: none; 
  }
  
  .file-link:hover {
    text-decoration: underline; 
  }