<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="auction-container">
    <div class="card m-3 p-3">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
      <div class="row">
        <div class="col-md-6">
          <!-- Auction Details -->
          <div class="auction-details" style="border: 1px solid #ddd; border-radius: 10px; padding: 15px;">
            <h4>Auction Information</h4>
            <div class="row">
              
              <div class="col-md-6 mb-3">
                <label>Buyer ID Number</label>
                <p>{{ auction.buyer_idNumber }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Buyer Name</label>
                <p>{{ auction.buyer_FirstName }} {{ auction.buyer_LastName }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Buyer TIN Number</label>
                <p>{{ auction.buyer_tinNumber }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Sale Amount</label>
                <p>{{ auction.sale_amount }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Valuation Amount</label>
                <p>{{ auction.valuation_amount }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Description</label>
                <p>{{ auction.description }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Created Date</label>
                <p>{{ auction.createddate }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Submitted Date</label>
                <p>{{ auction.submittedDate || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Approved Date</label>
                <p>{{ auction.approveddate || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Request Status</label>
                <p>{{ auction.requestStatus.name }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label>Approval Level</label>
                <p>{{ auction.approvalLevel.name }}</p>
              </div>
              
              <div class="row" *ngIf="documents.length > 0">
                <div class="col-md-6">
                  <label>Documents</label>
                  <div class="document-container" *ngFor="let document of documents">
                    <div class="document">
                      <fa-icon [icon]="fileIcon" class="file-icon"></fa-icon>
                      <!-- Display the Font Awesome file icon -->
                      <a style="cursor: pointer;" (click)="fetchBase64Data(document.fileName)" class="file-link">{{
                        document.fileName || 'N/A' }}</a>
                      <!-- <a href="{{ document.fileUrl }}" target="_blank" class="file-link">{{ document.fileName }}</a> -->
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
        <!-- Right Column: Additional Information and Project Details -->
        <div class="col-md-6">
          <app-auction-history></app-auction-history>
         <!-- <div *ngIf="vehicle.registrationStatus.name === 'APPROVED'"><app-device-location></app-device-location> </div> -->
      </div>

      <!-- Action Buttons -->
      <div class="col-md-12 action-buttons mt-3 d-flex" *ngIf="canSeeActionButtons()">
        <button class="btn btn-success" (click)="setAction('APPROVED')" data-bs-toggle="modal"
          data-bs-target="#actionModal"><fa-icon [icon]="approveIcon" class="pe-1"></fa-icon>Approve</button>
        <button class="btn btn-danger" (click)="setAction('DENIED')" data-bs-toggle="modal"
          data-bs-target="#actionModal"><fa-icon [icon]="denyIcon" class="pe-1"></fa-icon> Deny</button>
        <button class="btn btn-secondary" (click)="setAction('RFAC')" data-bs-toggle="modal"
          data-bs-target="#actionModal"><fa-icon [icon]="moreIcon" class="pe-1"></fa-icon> More Actions</button>
      </div>
     
       <!-- Modal for Action Confirmation -->
       <div class="modal fade" id="actionModal" aria-labelledby="actionModalLabel" aria-hidden="true">
        <div class="modal-dialog w-75">
          <div class="modal-content animate__animated animate__fadeInUp">
            <!-- Modal Header -->
            <div class="modal-header border-0 bg-info text-white position-relative">
              <h4 class="modal-title w-100 text-center text-white" id="actionModalLabel">Action Confirmation</h4>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                aria-label="Close"></button>
            </div>
            <div *ngIf="isLoading" class="loader-wrapper d-flex justify-content-center align-items-center flex-column">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="loading-text">Loading...</p>
            </div>

            <!-- Success Message -->
            <div *ngIf="!isLoading && actionSuccessful" class="success-wrapper text-center">
              <div class="form-card">
                <h2 class="text-success">Success</h2>
              <div class="row justify-content-center">
                  <div class="col-md-4">
                    <img src="https://img.icons8.com/color/96/000000/ok--v2.png" class="fit-image" alt="Success Icon">
                  </div>
                </div>
                <br><br>
              </div>
            </div>

            <div class="modal-body" *ngIf="!isLoading &&!actionSuccessful">
              <p>Add a comment if needed, then click "Confirm" to proceed.</p>
              <textarea class="form-control" [(ngModel)]="actionComment"
                placeholder="Add your comments here..."></textarea>
            </div>
            <div class="modal-footer" *ngIf="!isLoading &&!actionSuccessful">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-info" (click)="confirmAction()">Confirm</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>