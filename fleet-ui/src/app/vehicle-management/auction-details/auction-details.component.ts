// auction-details.component.ts
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { faArrowLeft, faThumbsUp, faThumbsDown, faQuestion, faFileArchive } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-auction-details',
  templateUrl: './auction-details.component.html',
  styleUrls: ['./auction-details.component.scss']
})
export class AuctionDetailsComponent implements OnInit {
  auction: any = {}; // Store the auction details
  actionComment = ''; // For modal comments
  selectedAction = ''; // To track the action selected
  backwardIcon = faArrowLeft;
  approveIcon = faThumbsUp;
  denyIcon = faThumbsDown;
  fileIcon = faFileArchive;
  moreIcon = faQuestion;
  isLoading: boolean = false;
  actionSuccessful = false; // Tracks success status
  loggedInUser: any;
  userRole: any;
  documents: any[] = [];
  base64Data: any;
  fetchedAuctionId=''

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private router: Router,
    private modalService: NgbModal
  ) {}

  ngOnInit() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
      this.userRole = parseObj?.data?.user?.role?.name; // Store the user's role
      this.loggedInUser = parseObj.data.user.id;

      // console.log("User Role in ngOnInit:", this.userRole);
    }

    this.route.paramMap.subscribe((params) => {
      const auctionId = params.get('auctionId');
      if (auctionId) {
        this.fetchedAuctionId = auctionId;
        this.fetchAuctionDetails(auctionId);
        this.fetchDocuments(auctionId);
      }
    });
  }

  fetchAuctionDetails(auctionId: string) {
    const url = `${environment.baseUrl}/disposal/auctionReport/${auctionId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.auction = response;
        console.log("Auction Data:", response);
      },
      (error) => {
        console.error('Error fetching auction details:', error);
        this.toastr.error('Error fetching auction details.');
      }
    );
  }
  fetchDocuments(auctionId: string) {
    const url = `${environment.fileUrl}/${auctionId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.documents = response;
        console.log(this.documents)// Assuming 'documents' is an array property in your component
      },
      (error) => {
        console.error('Error fetching documents:', error);
      }
    );
  }

  fetchBase64Data(fileName: string) {
    const url = `${environment.fileUrl}/${fileName}/base64`;
    this.http.get<any>(url).subscribe(
        (response) => {
            // Decode the Base64 data and display it in a new window
            const binaryData = atob(response.base64Data);
            const arrayBuffer = new ArrayBuffer(binaryData.length);
            const uint8Array = new Uint8Array(arrayBuffer);
            for (let i = 0; i < binaryData.length; i++) {
                uint8Array[i] = binaryData.charCodeAt(i);
            }
            const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            window.open(url);
        },
        (error) => {
            console.error('Error fetching Base64 data:', error);
        }
    );
}

  setAction(action: string) {
    this.selectedAction = action;
    console.log('Decision: ', this.selectedAction)
  }

  // confirmAction() {
  //   // Implement your logic for confirming the action
  //   console.log("Acquisition ID before submission:", this.auction.id); 
  
  //   // Start loading
  //   this.isLoading = true;
  //   this.actionSuccessful = false; // Reset action success state
    
  //   const approvalData = {
  //     userId:  this.loggedInUser,
  //     auctionReportId:  this.auction.id,
  //     comments:  this.actionComment || ' ', // Include comments, if any
  //     decision:  this.selectedAction.toUpperCase() // Decision based on the selected action
  //   };

    
  //   console.log(JSON.stringify(approvalData));

  //   this.http.post(`${environment.baseUrl}/approval/ActionReportApproval`, approvalData).subscribe(
  //     (response) => {
  //       // Ensure the loader is shown for at least 3 seconds
  //       setTimeout(() => {
  //         this.isLoading = false; // End loading
  //         this.actionSuccessful = true; // Mark action as successful
  //         this.toastr.success("Action confirmed successfully");
  //         console.log("final body", response);
  
  //         // After showing the success message for 3 seconds, reload the page
  //         setTimeout(() => {
  //           // window.location.reload();
  //         }, 3000); // Wait 3 seconds before reloading
  //       }, 3000); // Minimum loading time
  //     },
  //     (error) => {
  //       this.isLoading = false; // End loading on error
  //       this.toastr.error("Error sending approval");
  //       console.error("Error during approval:", error);
  //       console.log("Error details:", error.error);
  //     }
  //   );
  // }

  confirmAction() {
    console.log("Auction ID before submission:", this.auction.id); 
  
    
    this.isLoading = true;
    this.actionSuccessful = false; 
    
    const approvalData = {
      userId: this.loggedInUser,
      auctionReportId: this.auction.id,
      comments: this.actionComment || '',
      decision: this.selectedAction.toUpperCase()
    };
    console.log("Auction Report ID:", this.auction.id);
    console.log("Approval Data:", approvalData);

  
    console.log("Body being sent", approvalData); 
  
    this.http.post(`${environment.baseUrl}/approval/ActionReportApproval`, approvalData).subscribe(
      (response) => {

        
        setTimeout(() => {
          this.isLoading = false; 
          this.actionSuccessful = true; 
          this.toastr.success("Action confirmed successfully");
          console.log("final body", response);
  
          
          setTimeout(() => {
            window.location.reload();
          }, 3000); 
        }, 3000); 
      },
      (error) => {
        this.isLoading = false; 
        this.toastr.error("Error sending approval");
        console.error("Error during approval:", error);
        console.log("Error details:", error.error);
      }
    );
  }
  canSeeActionButtons(): boolean {
    const approvalLevel = this.auction?.approvalLevel?.name.trim();  // Auction approval level
    const status = this.auction?.requestStatus?.name;  // Auction status
    const userRole = this.userRole;  // User's role

    // Ensure the buttons are visible when:
    // - The user's role matches the approval level (except for 'Minister')
    // - The auction status is not 'APPROVED'
    return userRole === approvalLevel && userRole !== 'Minister' && status !== 'APPROVED';
  }

  redirectToAuthorizationLetter() {
        const auctionId = this.fetchedAuctionId;
        if (auctionId) {
          this.router.navigate([`/vehicle-management/auction-details/${auctionId}/authorization-letter`]);
        } else {
          console.error('No auction ID available.');
        }
      }

  goBack(){
    this.router.navigate([`/vehicle-management/auction-reports`]);
  }
}
