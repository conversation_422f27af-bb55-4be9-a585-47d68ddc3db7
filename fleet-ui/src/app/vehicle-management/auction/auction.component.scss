.page{
    background-color:#D9D9D94D;
    width: 83% !important;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
    height: 90%;
}
.form-container::-webkit-scrollbar{
    width: 0;
}
h1{
    font-size: 20px;
    color: #28A4E2;
    font-weight: 500;
    margin-bottom: 5px;
}
h2{
    font-size: 16px;
    color: #28A4E2;
    font-weight: 500;
}
p{
    font-size: 12px;
    color: #999797;
}
.card {
    z-index: 0;
    border: none;
    border-radius: 0.5rem;
    position: relative;
    padding: 15px;
}
label{
    color: #000000AB;
    font-size: 13px;
    padding-bottom: 8px;
}
input, select, textarea {
    border: 1px solid #eaeaf5;
    font-size: 13px;
    border-radius: 8px;
}
.submit-btn{
    background-color: #28A4E2;
    color: #ffffff;
}

.btn-secondary {
    background-color: white;
    color: grey;
    border: 1px solid grey;
}

.vehicle-details {
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    margin-bottom: 10px;
}

.form-container {
    text-align: center;
}

.card {
    padding: 15px;
    border-radius: 10px;
}

.card h4 {
    color: #28A4E2;
}

.card label {
    color: #757575;
}
