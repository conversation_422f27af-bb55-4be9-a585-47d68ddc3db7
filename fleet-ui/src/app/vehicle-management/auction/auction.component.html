<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>
  <div class="page">
    <div class="header d-flex flex-row justify-content-between w-50">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
      <h1>Vehicle Auctioning</h1>
    </div>

    <div class="card">
      <p>Fill in all details for vehicle auctioning</p>
      <div class="row form-container">
        <form [formGroup]="auctionForm" class="text-center" (ngSubmit)="onSubmit()" enctype="multipart/form-data">
          <div class="row mt-4">
            <div class="form-group col-md-4">
              <label for="buyer_idNumber" [ngClass]="{'required-label': isFieldRequired('buyer_idNumber')}">Buyer Id Number</label>
              <input type="number" class="form-control" formControlName="buyer_idNumber" id="buyer_idNumber">
              <div *ngIf="auctionForm.get('buyer_idNumber')?.errors && auctionForm.get('buyer_idNumber')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('buyer_idNumber')?.errors?.['required']">Id Number is required.</span>
                <span *ngIf="auctionForm.get('buyer_idNumber')?.errors?.['pattern']">Id Number must be 16 digits.</span>
              </div>
            </div>
            <div class="form-group col-md-4">
              <label for="buyer_FirstName" [ngClass]="{'required-label': isFieldRequired('buyer_FirstName')}">Buyer Firstname</label>
              <input type="text" class="form-control" formControlName="buyer_FirstName" id="buyer_FirstName">
              <div *ngIf="auctionForm.get('buyer_FirstName')?.errors && auctionForm.get('buyer_FirstName')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('buyer_FirstName')?.errors?.['required']">First Name is required.</span>
                <span *ngIf="auctionForm.get('buyer_FirstName')?.errors?.['minlength']">First Name must be at least 2 characters long.</span>
                <span *ngIf="auctionForm.get('buyer_FirstName')?.errors?.['maxlength']">First Name cannot be more than 50 characters long.</span>
              </div>
            </div>
            <div class="form-group col-md-4">
              <label for="buyer_LastName" [ngClass]="{'required-label': isFieldRequired('buyer_LastName')}">Buyer Lastname</label>
              <input type="text" class="form-control" formControlName="buyer_LastName" id="buyer_LastName">
              <div *ngIf="auctionForm.get('buyer_LastName')?.errors && auctionForm.get('buyer_LastName')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('buyer_LastName')?.errors?.['required']">Last Name is required.</span>
                <span *ngIf="auctionForm.get('buyer_LastName')?.errors?.['minlength']">Last Name must be at least 2 characters long.</span>
                <span *ngIf="auctionForm.get('buyer_LastName')?.errors?.['maxlength']">Last Name cannot be more than 50 characters long.</span>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <div class="form-group col-md-4">
              <label for="buyer_tinNumber" [ngClass]="{'required-label': isFieldRequired('buyer_tinNumber')}">Buyer Tin Number</label>
              <input type="number" class="form-control" formControlName="buyer_tinNumber" id="buyer_tinNumber">
              <div *ngIf="auctionForm.get('buyer_tinNumber')?.errors && auctionForm.get('buyer_tinNumber')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('buyer_tinNumber')?.errors?.['required']">TIN Number is required.</span>
                <span *ngIf="auctionForm.get('buyer_tinNumber')?.errors?.['pattern']">TIN Number must be 9 digits.</span>
              </div>
            </div>
            <div class="form-group col-md-4">
              <label for="sale_amount" [ngClass]="{'required-label': isFieldRequired('sale_amount')}">Sale Amount</label>
              <input type="number" class="form-control" formControlName="sale_amount" id="sale_amount">
              <div *ngIf="auctionForm.get('sale_amount')?.errors && auctionForm.get('sale_amount')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('sale_amount')?.errors?.['required']">Sale Amount is required.</span>
                <span *ngIf="auctionForm.get('sale_amount')?.errors?.['min']">Sale Amount must be a positive number.</span>
              </div>
            </div>
            <div class="form-group col-md-4">
              <label for="valuation_amount" [ngClass]="{'required-label': isFieldRequired('valuation_amount')}">Valuation Amount</label>
              <input type="number" class="form-control" formControlName="valuation_amount" id="valuation_amount">
              <div *ngIf="auctionForm.get('valuation_amount')?.errors && auctionForm.get('valuation_amount')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('valuation_amount')?.errors?.['required']">Valuation Amount is required.</span>
                <span *ngIf="auctionForm.get('valuation_amount')?.errors?.['min']">Valuation Amount must be a positive number.</span>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <div class="form-group col-md-4">
              <label for="description">Description</label>
              <textarea class="form-control" formControlName="description" id="description" rows="4"></textarea>
              <div *ngIf="auctionForm.get('description')?.errors && auctionForm.get('description')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('description')?.errors?.['maxlength']">Description cannot be more than 500 characters long.</span>
              </div>
            </div>
          </div>

          <h2>File Upload Section</h2>
          <p>The first 2 files are mandatory for vehicle auctioning</p>
          <div class="row mt-4">
            <div class="form-group col-md-6">
              <label for="auctionReport" [ngClass]="{'required-label': isFieldRequired('auctionReport')}">Auction Report</label>
              <input type="file" id="auctionReport" (change)="onFileChange($event, 'auctionReport')" class="form-control">
              <div *ngIf="auctionForm.get('auctionReport')?.errors && auctionForm.get('auctionReport')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('auctionReport')?.errors?.['required']">Auction Report is required.</span>
              </div>
            </div>

            <div class="form-group col-md-6">
              <label for="salesContract" [ngClass]="{'required-label': isFieldRequired('salesContract')}">Sales Contract Attachment</label>
              <input type="file" id="salesContract" (change)="onFileChange($event, 'salesContract')" class="form-control">
              <div *ngIf="auctionForm.get('salesContract')?.errors && auctionForm.get('salesContract')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('salesContract')?.errors?.['required']">Sales Contract is required.</span>
              </div>
            </div>
            
          </div>

          <div class="row" formArrayName="files">
            <p class="mt-4">Upload any additional files and add the description</p>
                        <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                            <div class="form-group col-md-6">
                              <input type="file" (change)="onFileChangee($event, i)" formControlName="file" class="form-control" />
                            </div>
                            <div class="form-group col-md-6">
                              <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="4"></textarea>
                            </div>

                            <button type="button" class=" btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                              <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                          </button>
                          </div>
                          <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                            <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add New File
                          </button>
          </div>
          <!-- <div class="row mt-4">
            <div class="form-group col-md-6">
              <label for="paymentProof" [ngClass]="{'required-label': isFieldRequired('paymentProof')}">RRA Payment Proof</label>
              <input type="file" id="paymentProof" (change)="onFileChange($event, 'paymentProof')" class="form-control">
              <div *ngIf="auctionForm.get('paymentProof')?.errors && auctionForm.get('paymentProof')?.touched" class="text-danger">
                <span *ngIf="auctionForm.get('paymentProof')?.errors?.['required']">Payment Proof is required.</span>
              </div>
            </div>
          </div> -->
          <div class="row mt-4">
            <div class="col-md-8"></div>
            <div class="col-md-4 buttons">
              <button type="submit" class="btn submit-btn col-md-9 float-end">Auction Vehicle</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
