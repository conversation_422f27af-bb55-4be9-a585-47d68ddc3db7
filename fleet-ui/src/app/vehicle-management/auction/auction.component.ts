import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { faArrowLeft, faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';

@Component({
  selector: 'app-auction',
  templateUrl: './auction.component.html',
  styleUrls: ['./auction.component.scss']
})
export class AuctionComponent implements OnInit {
  auctionForm!: FormGroup;
  files !:FormArray;
  disposalId: string = '';
  disposal: any = null;
  plusIcon = faPlus;
  minusIcon = faMinus;
  selectedFileName: any;
  backwardIcon = faArrowLeft;


  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {
    this.auctionForm = this.formBuilder.group({
      disposalId: [''],
      buyer_idNumber: ['', [Validators.required, Validators.pattern(/^\d{16}$/)]],
      buyer_FirstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      buyer_LastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      buyer_tinNumber: ['', [Validators.required, Validators.pattern(/^\d{9}$/)]],
      sale_amount: ['', [Validators.required, Validators.min(0)]],
      valuation_amount: ['', [Validators.required, Validators.min(0)]],
      description: ['', [Validators.maxLength(500)]],
      auctionReport: [null, Validators.required],
      // paymentProof: [null, Validators.required],
      salesContract: [null, Validators.required],
      files: this.formBuilder.array([this.createFileFormGroup()])
    });
    this.files = this.auctionForm.get('files') as FormArray;

  }

  goBack(){
    this.location.back();
  }
  
  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }
  
  removeFile(index: number): void {
    this.files.removeAt(index);
  }
  
  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: [''],
      applicationId: ['']
    });
  }

  // onFileChangee(event: any, index: number): void {
  //   event.preventDefault();
  //   const file = event.target.files[0];
  //   if (file) {
  //     const fileControl = this.files.at(index).get('file');
  //     if (fileControl) { 
  //       fileControl.setValue(file);
  //     }
  //     this.selectedFileName = file.name;
  //   }
  // }

  onFileChangee(event: any, index: number): void {
    event.preventDefault();
    const file = event.target.files[0];
    if (file) {
      const fileControl = this.files.at(index).get('file');
      if (file.type !== 'application/pdf') {
        this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
        if (fileControl) {
          fileControl.setValue(null);
        }
        event.target.value = '';
        return;
      }
      if (fileControl) { 
        fileControl.setValue(file);
      }
      this.selectedFileName = file.name;
    }
  }

  isFieldRequired(fieldName: string): boolean {
    const control = this.auctionForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }

  markAllAsTouched(): void {
    Object.values(this.auctionForm.controls).forEach(control => {
      control.markAsTouched();
    });
  }

  ngOnInit(): void {
    this.fetchDisposalId();
  }

  fetchDisposalId(): void {
    this.route.params.subscribe((params) => {
      if (params['disposalId']) {
        this.disposalId = params['disposalId'];
        this.fetchDisposalDetails(this.disposalId);
      }
    });
  }

  fetchDisposalDetails(disposalId: string): void {
    const url = `${environment.baseUrl}/disposal/disposal/${disposalId}`;
    this.http.get<any>(url).subscribe(
      (response: any) => {
        this.disposal = response;
      },
      (error) => {
        console.error('Error fetching disposal details: ', error);
      }
    );
  }

  onFileChange(event: any, fileType: string): void {
    const file = event.target.files[0];
    if (file) {
      this.auctionForm.patchValue({ [fileType]: file });
    }
  }

  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitFile(file: File, fileType: string, auctionId: string): void {
    if (file) {
      const fileFormData = new FormData();

      if (this.validateFile(file)) {
        fileFormData.append('file', file);
        fileFormData.append('documentDescription', fileType);
        fileFormData.append('applicationId', auctionId);

        this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
          (response) => {
            console.log(`${fileType} submitted successfully`, response);
          },
          (error) => {
            console.error(`Error submitting ${fileType}`, error);
            this.toastr.error(`Error submitting ${fileType}`, 'Error');
          }
        );
      } else {
        console.warn(`${fileType} size or type is invalid.`);
        this.toastr.warning(`Invalid file type or size. Only jpeg, png, and pdf files are allowed.`);
      }
    }
  }

  submitAdditionalFile(auctionId: any): void {
    const fileUploadDataFormArray = this.auctionForm.get('files') as FormArray;
    fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
      if (control instanceof FormGroup) {
        const fileItem = control.value;
        if (fileItem.file) {
          const fileFormData = new FormData();
          const file = fileItem.file as File;
  
          console.log('File name:', file.name);
          console.log('File size:', file.size);
          console.log('File type:', file.type)
  
          if (this.validateFile(file)) {
            fileFormData.append('file', file);
            fileFormData.append('documentDescription', fileItem.documentDescription);
            fileFormData.append('applicationId', auctionId);
  
            this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
              (response) => {
                console.log('API Response:', response);
                console.log("File uploaded successfully");
                this.auctionForm.reset();
              },
              (error) => {
                let errorMessage = 'An unexpected error occurred.';
                if (error && error.error && error.error.message) {
                  errorMessage = error.error.message;
                }
                console.error('API Error:', errorMessage);
                this.toastr.error("An error occurred!!", 'Error');
              }
            );
          } else {
            console.warn('File size or type is invalid.');
          }
        } else {
          console.log('No file selected...');
        }
      }
    });
  }

  onSubmit(): void {
    this.markAllAsTouched();

    const additionalFilesArray = this.auctionForm.get('files') as FormArray;

    if (this.auctionForm.valid) {
      const hasUploadedFiles = this.auctionForm.get('auctionReport')?.value &&
        // this.auctionForm.get('paymentProof')?.value &&
        this.auctionForm.get('salesContract')?.value;

      if (hasUploadedFiles) {
        this.auctionForm.patchValue({ disposalId: this.disposalId });

        const requestData = { ...this.auctionForm.value };
        console.log("Form Data being sent: ", JSON.stringify(requestData, null, 2));



        this.http.post(`${environment.baseUrl}/disposal/auction-report`, requestData).subscribe(
          (response: any) => {
            const auctionId = response.id;

            console.log("Auction Report Response: ", response);
            this.submitFile(this.auctionForm.get('auctionReport')?.value, 'auctionReport', auctionId);
            // this.submitFile(this.auctionForm.get('paymentProof')?.value, 'paymentProof', auctionId);
            this.submitFile(this.auctionForm.get('salesContract')?.value, 'salesContract', auctionId);

            if (additionalFilesArray && additionalFilesArray.length > 0) {
              this.submitAdditionalFile(auctionId);
            }
            console.log('Auction details: ', response);
            // this.updateDisposalAuctionStatus(this.disposalId);
            this.toastr.success('Vehicle Auctioning Successful');
            this.auctionForm.reset();
            this.router.navigate(['/vehicle-management/auction-reports']);
          },
          (error) => {
            this.toastr.error('Error auctioning vehicle', 'Error');
          }
        );
      } else {
        this.toastr.error('All 2 files must be uploaded', 'Error');
      }
    } else {
      this.toastr.error('Please fill in all fields to submit', 'Error');
    }
  }
}
