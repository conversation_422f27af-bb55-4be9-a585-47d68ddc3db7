<div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px;">
    <h4>Approval History</h4>
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Date</th>
          <th>Approval Level</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>  
        <tr *ngFor="let history of actionHistory">
          <td>{{ history.DateOfActivity }}</td>
          <td>{{ history.approvalLevel?.name }}</td>
          <td>
            <span
            [ngClass]="{
              'text-success': history.activityPerformed === 'APPROVED' && history.approvalLevel?.name === 'Minister', 
              'text-warning': history.activityPerformed === 'APPROVED' && (history.approvalLevel?.name === 'Institutional CBM' || history.approvalLevel?.name === 'DG Transport' || history.approvalLevel?.name === 'Fleet Mgt Senior Engineer'|| history.approvalLevel?.name === 'Permanent Secretary'),
              'text-danger': history.activityPerformed === 'DENIED',
              'text-secondary': history.activityPerformed === 'WAITING' || !history.activityPerformed
            }"
          >
            {{
              (history.activityPerformed === 'APPROVED' && history.approvalLevel?.name === 'Minister')
                ? 'APPROVED'
                : (history.activityPerformed === 'APPROVED' && (history.approvalLevel?.name === 'Institutional CBM' || history.approvalLevel?.name === 'DG Transport' || history.approvalLevel?.name === 'Fleet Mgt Senior Engineer'|| history.approvalLevel?.name === 'Permanent Secretary'))
                  ? 'SUBMITTED'
                  : history.activityPerformed || 'WAITING'
            }}
          </span>
          </td>
        </tr>
      </tbody>
    </table>
    <h4>Comment History</h4>
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Date</th>
          <th>Comment</th>
          <th>Approval Level</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let history of actionHistory">
          <tr *ngIf="history.comments">
            <td>{{ history.DateOfActivity }}</td>
            <td>{{ history.comments || 'No comment provided' }}</td>
            <td>
              {{ history.approvalLevel?.name }}
              <ng-template #loading>Loading...</ng-template>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
  