import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-disposal-history',
  templateUrl: './disposal-history.component.html',
  styleUrl: './disposal-history.component.scss'
})
export class DisposalHistoryComponent {
  actionId = ''; // ID for fetching the history
  actionHistory: any[] = []; // Stores the fetched history
  userDetails: { [key: string]: any } = {}; 
  acquisitionDetails: any;
  acquisitions: any;
  acquisitionStatus = 'Pending';

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private toastr: ToastrService
  ) {}

  ngOnInit() {
    // Extract the disposalId from the route parameters
    this.route.paramMap.subscribe((params) => {
      const disposalId = params.get('disposalId');
      console.log('Disposal ID:', disposalId);  // Check if disposalId is passed
      if (disposalId) {
        this.actionId = disposalId;
        this.fetchDisposalHistory(disposalId);  // Fetch history using disposalId
      } else {
        console.error('Disposal ID is missing!');
      }
    });
  }

  // Fetch disposal history using the disposalId (actionId)
  fetchDisposalHistory(actionId: string) {
    const url = `${environment.baseUrl}/approval/getAllHistoryByActionId?actionId=${actionId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.actionHistory = response;
        console.log('Disposal History:', this.actionHistory); // Log the fetched history
      },
      (error) => {
        console.error('Error fetching disposal history:', error);
      }
    );
  }
}
