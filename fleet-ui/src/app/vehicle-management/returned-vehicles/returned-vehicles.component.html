<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    
    <div class="vehicles-container">
        <div class="header d-flex flex-row justify-content-between">
            <h2 class="table-title">List Of Returned Vehicles</h2>
        </div>
        <div class="card">
            <div class="table-header d-flex flex-row justify-content-between">
              <p>This is a list of returned vehicles, select one to assign to acquisition</p>
                <div class="sorting-group d-flex flex-row p-2">
                    <label for="sort-field" class="text-muted p-1">Sort by:</label>
                    <select id="sort-field" class="select" (change)="sortable($event)">
                        <option value="">Select Field</option>
                        <option value="institutio">Institution</option>
                        <option value="requestType">Request Type</option>
                        <option value="ownershipType">Ownership Type</option>
                        <option value="status">Status</option>
                      </select>
                </div>
            </div>
    
            <div class="table-only p-3">
                <table class="table table-stripped">
                    <thead>
                        <tr>
                            <th>Vehicle Type</th>
                            <th sortable = "institution">Vehicle Manufacture</th>
                            <th sortable="requestType">Vehicle Model</th>
                            <th sortable ="ownershipType">Plate Number</th>
                            <th sortable = "status">Transmission Type</th>
                            <th sortable="approvalLevel">Pink Card</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let item of returnedVehicles">
                        <td>{{ item.vehicleType.name }}</td>
                        <td>{{ item.vehicleManufacture.name}}</td>
                        <td>{{ item.vehicleModel.name }}</td>
                        <td>{{ item.plateNumber }}</td>
                        <td>{{ item.transmissionType}} </td>
                        <td>{{item.pickCardNumber}}</td>
                        <td class="d-flex flex-row"> 
                          <button class="btn view-btn" (click)="viewVehicle(item.id)">View </button>
                          <button class="btn assign-btn" (click)="assignVehicle(item.id)">Assign Vehicle</button>
                        </td>
                      </tr>                          
                    </tbody>
                </table>
            </div>
            
            <nav aria-label="Page navigation" class="nav d-flex flex-row justify-content-between">
                <div class="pagination-info">
                  Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredAcquisitions.length }} entries
                </div>
                <ul class="pagination justify-content-center">
                  <li class="page-item">
                    <button class="caret" (click)="previousPage()" [disabled]="currentPage === 1">
                      <fa-icon [icon]="caretLeft"></fa-icon>
                    </button>
                  </li>
                  <li class="page-item" *ngFor="let pageNumber of getPageNumbers()">
                    <button class="page-link pages" [class.active]="currentPage === pageNumber" (click)="goToPage(pageNumber)">
                      {{ pageNumber }}
                    </button>
                  </li>
                  <li class="page-item">
                    <button class="caret" (click)="nextPage()" [disabled]="currentPage === totalPages">
                      <fa-icon [icon]="caretRight"></fa-icon>
                    </button>
                  </li>
                </ul>
              </nav>
              
        </div>
    </div>
</div>