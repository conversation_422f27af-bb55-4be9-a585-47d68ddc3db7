import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component } from '@angular/core';
import { faSearch, faCaretLeft, faCaretRight, faCar, faCheck } from '@fortawesome/free-solid-svg-icons';
import { User } from '../../models/user.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-returned-vehicles',
  templateUrl: './returned-vehicles.component.html',
  styleUrl: './returned-vehicles.component.scss'
})
export class ReturnedVehiclesComponent {
  allFormsSubmitted = false;
  searchIcon = faSearch;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  carIcon = faCar;
  checkIcon = faCheck;
  searchText: string = '';
  returnedVehicles: any[] = [];
  filteredAcquisitions: any[] = [];
  displayedAcquisitions: any[] = []; 
  currentFilter: string = 'all'; 
  pageSize = 7; 
  currentPage = 1;
  totalPages: number = 0;
  acquisitionId: any;
  
  institutionId: string = ''; 
  userRole: string='';
  
  constructor(
    private http: HttpClient, 
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router, 
    private cd: ChangeDetectorRef
  ) { }
   
  ngOnInit() {
      this.getUserDetails();
      this.fetchReturnedVehicles();
      this.isFsem();
      this.getAcquisitionId();
  }

  isFsem():boolean{
    const data = localStorage.getItem('localUserData');
    if(data !=null){
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Fleet Mgt Senior Engineer';
    }
    return false;
  }

  getAcquisitionId(): void{
    this.route.params.subscribe(params => {
      if (params['acquisitionId']) {
        this.acquisitionId = params['acquisitionId'];
        this.getAcquisitionDetails(this.acquisitionId);
      }
    })
  }

  getAcquisitionDetails(acquisitionId: string){
    const url = `${environment.otherUrl}/AllVehiclesPerAcquisition?acquisitionId=${acquisitionId}`;

    this.http.get<any[]>(url).subscribe(
      (response:any) => {
        console.log("Acquisition details", response);

        const vehicles = response.vehicles;
        if(vehicles && vehicles.length > 0){
          for(let i = 0; i<vehicles.length; i++){
            const vehicle = vehicles[i];
          }
        }
      }
    )
  }

  fetchReturnedVehicles() {
    if (!this.institutionId || !this.userRole) {
      console.error('Institution ID or User Role not found');
      return;
    }

    let url = `${environment.otherUrl}/allReturnedVehicles`;
   
    this.http.get<any[]>(url).subscribe(
      (response:any[]) => {
        this.returnedVehicles = response;
      },
      (error) => {
        console.error('Error fetching data:', error);
      }
    );
  }
  
  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id;
      this.userRole = parsedObj.data.user.role.name;
      console.log('User Role:', this.userRole); 
    }
  }
  
  assignVehicle(vehicleId: string): void {
    console.log('Vehicle ID:', vehicleId);
    this.router.navigateByUrl(`/vehicle-management/returned-details/${this.acquisitionId}/${vehicleId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  viewVehicle(vehicleId: string): void {
    console.log('Vehicle ID:', vehicleId);
    this.router.navigateByUrl(`/vehicle-management/vehicle-details/${vehicleId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
  allPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  }

  updateDisplayedAcquisitions() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredAcquisitions.length);
    
    this.displayedAcquisitions = this.filteredAcquisitions.slice(startIndex, endIndex);
    this.totalPages = Math.ceil(this.filteredAcquisitions.length / this.pageSize);
  }
  
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedAcquisitions();
      this.cd.markForCheck(); 
    }
  }
  
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedAcquisitions();
      this.cd.markForCheck(); // Trigger change detection
    }
  }
  
  goToPage(pageNumber: number) {
    if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedAcquisitions();
      this.cd.markForCheck(); // Trigger change detection
    }
  }
  getPageNumbers(): number[] {
    const pageNumbers = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
      console.log("Page number: ",pageNumbers);
    }
    return pageNumbers;
  }
  
  navigateToRequestVehicle(): void {
    this.router.navigateByUrl('vehicle-management/request-vehicle');
  }

  getFirstEntryIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }
  
  getLastEntryIndex(): number {
    const lastEntryIndex = this.currentPage * this.pageSize;
    return Math.min(lastEntryIndex, this.filteredAcquisitions.length);
  }
  
  sortable(event: any) {
    const selectedField = event.target.value;
  
    if (!selectedField) {
      this.filteredAcquisitions.sort((a, b) => a.institution.localeCompare(b.institution));
    } else {
      this.filteredAcquisitions.sort((a, b) => {
        const fieldA = a[selectedField];
        const fieldB = b[selectedField];
        if (typeof fieldA === 'string' && typeof fieldB === 'string') {
          return fieldA.localeCompare(fieldB);
        }
        return 0;
      });
    }
  
    // Re-apply the displayed data logic after sorting
    this.updateDisplayedAcquisitions();
  }
  
  editAcquisition(acquisitionId: string): void{
    console.log("AcquisitionId: ",acquisitionId);
    this.router.navigateByUrl(`/vehicle-management/edit-requested-vehicle/${acquisitionId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'bg-success'; // Corresponds to green
        case 'pending':
            return 'bg-secondary'; // Corresponds to yellow/orange
        case 'denied':
            return 'bg-danger'; // Corresponds to red
        case 'rfac':
            return 'bg-info'; // Corresponds to blue
        case 'progress':
            return 'bg-warning'; // Corresponds to blue
        default:
            return ''; // Default or fallback class
    }
}
getProgressWidth(status: string): string {
  switch (status.toLowerCase()) {
    case 'approved':
      return '100%'; // Corresponds to green
    case 'pending':
      return '70%'; // Corresponds to yellow/orange
    case 'denied':
      return '100%'; // Corresponds to red
    case 'rfac':
      return '60%'; // Corresponds to blue
    case 'progress':
      return '80%'; // Corresponds to blue
    default:
      return '';
  }
}
registerVehicle(acquisitionId: string):void{
  console.log("Acquisition Id: ", acquisitionId);
  this.router.navigateByUrl(`/vehicle-management/register-vehicle/${acquisitionId}`).then(success =>{
    if(success){
      console.log('Navigation Successful');
    }else{
      console.error('Navigation Failed');
    }
  }).catch(error => {
    console.error('Error navigating: ', error);
  });
}

}
