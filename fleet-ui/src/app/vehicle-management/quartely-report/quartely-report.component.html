<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>
  <div class="page">
      <div class="header">
          <h1>Vehicle Quarterly Report</h1>
      </div>
   <!-- Parent Form -->
<form id="parentForm" [formGroup]="parentFormGroup" (ngSubmit)="onSubmitParentForm()">

  <!-- Nested Form Start -->
  <form [formGroup]="reportForm" (ngSubmit)="onSubmitNestedForm()" enctype="multipart/form-data">
    <div *ngIf="currentStep === 1">
      <div class="card">
        <p>Fill in the fields to send the Quarterly Report</p>
        <div class="row form-container">
          <div class="col-md-12 text-center">
            <button type="button" class="btn show-details-btn" (click)="toggleVehicleDetails()">
              {{ isVehicleDetailsVisible ? 'Hide Vehicle Details' : 'Show Vehicle Details' }}
            </button>

            <div
                *ngIf="isVehicleDetailsVisible"
                class="vehicle-details"
                style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin-top: 20px; margin-bottom: 10px;">
                <h4>Vehicle Information</h4>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label>Chassis Number</label>
                        <p>{{ vehicle?.chassisNumber || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Engine Number</label>
                        <p>{{ vehicle?.engineNumber || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Transmission Type</label>
                        <p>{{ vehicle?.transmissionType || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Manufacturing Year</label>
                        <p>{{ vehicle?.manufacturingYear || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Vehicle Type</label>
                        <p>{{ vehicle?.vehicleType?.name || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Ownership Type</label>
                        <p>{{ vehicle?.ownershipType?.name || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Vehicle Manufacture</label>
                        <p>{{ vehicle?.vehicleManufacture?.name || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Beneficiary Agency</label>
                        <p>{{ vehicle?.beneficiaryAgency || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Plate Number</label>
                        <p>{{ vehicle?.plateNumber || 'N/A' }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>Pink Card Number</label>
                        <p>{{ vehicle?.pickCardNumber || 'N/A' }}</p>
                    </div>
                </div>
            </div>
          </div>

          <div class="row mt-4">
              <h2>Vehicle Condition</h2>
              <div class="form-group col-md-4">
                  <label for="vehicleStatusId" [ngClass]="{'required-label': isFieldRequired('vehicleStatusId')}">Vehicle Status</label>
                  <select class="form-control" formControlName="vehicleStatusId" id="vehicleStatusId">
                      <option *ngFor="let vehicleStatus of vehicleStatuses" [value]="vehicleStatus.id">{{vehicleStatus.name}}</option>
                  </select>
              </div>
              <div class="form-group col-md-4">
                  <label for="description">Description</label>
                  <textarea class="form-control" formControlName="description" id="description" rows="2"></textarea>
              </div>

              <div class="form-group col-md-4">
                  <label for="isVehicleActive">Is the vehicle still in use?</label>
                  <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" id="isVehicleActiveYes" value="true" formControlName="isVehicleActive" name="isVehicleActive"> 
                      <label class="form-check-label" for="isVehicleActiveYes">Yes</label>
                  </div>
                  <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" id="isVehicleActiveNo" value="false" formControlName="isVehicleActive" name="isVehicleActive"> 
                      <label class="form-check-label" for="isVehicleActiveNo">No</label>
                  </div>
              </div>

          </div>

          <div class="row mt-4">
              <h2>File Upload Section </h2>
              <div class="row" formArrayName="files">
                  <div *ngFor="let file of files.controls; let i = index" [formGroupName]="i" class="file-upload row mt-4">
                      <div class="form-group col-md-6">
                          <input type="file" (change)="onFileChange($event, i)" formControlName="file" class="form-control" />
                      </div>
                      <div class="form-group col-md-6">
                          <textarea id="documentDescription" placeholder="File Description" formControlName="documentDescription" class="form-control" rows="3"></textarea>
                      </div>

                      <button type="button" class="btn remove-btn mt-3" *ngIf="i > 0" (click)="removeFile(i)"> 
                        <fa-icon [icon]="minusIcon" class="pe-1"></fa-icon> Delete File
                      </button>
                  </div>
                  <button type="button" (click)="addFile()" class="btn add-btn mt-4" *ngIf="files.length < 2">
                      <fa-icon [icon]="plusIcon" class="pe-1"></fa-icon> Add another file
                  </button>
              </div>
              <div class="row mt-4">
                  <div class="col-md-9"></div> 
                  <div class="col-md-3 buttons">
                      <button type="submit" class="btn submit-btn" (click)="nextStep()">Next</button>
                  </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  </form>
  <!-- Nested Form End -->

  <!-- Other parent form content here -->
  <div class="row" *ngIf="currentStep === 2" style="background-color: white;">
    <button class="btn go-back-btn m-3" (click)="goBack()">
      <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
    </button>
    <div class="row mb-2 mt-2 d-flex justify-content-between align-items-center">
      <div class="col-md-10">
        <h2>Activities Performed On Vehicle</h2>
      </div>
      <div class="col-md-2 text-right">
        <!-- Download and Print Buttons -->
        <div class="btn-group justify-content-end">
          <button class="btn view-btn d-flex align-items-center" (click)="exportVehiclesToExcel()">
            Download <fa-icon [icon]="downloadIcon" style="margin-left: 2px;"></fa-icon>
          </button>
          <button class="btn edit-btn d-flex align-items-center ml-3" (click)="printVehicles()">
            Print <fa-icon [icon]="printIcon" style="margin-left: 2px;"></fa-icon>
          </button>
        </div>
      </div>
    </div>
    <div class="container mt-4">
      <!-- Check if there are any vehicle activities -->
      <ng-container *ngIf="vehicleActivities.length > 0; else noActivities">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>Activity Date</th>
                <th>Category</th>
                <th>Field</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              <!-- Loop through each activity in vehicleActivities -->
              <ng-container *ngFor="let activity of vehicleActivities; let i = index">
                
                <!-- Fuel Section -->
                <ng-container *ngIf="activity.fuelQuantity">
                  <tr>
                    <td rowspan="5">{{ activity.activityDate | date:'yyyy-MM-dd' }}</td>
                    <td rowspan="5"><strong>Fuel</strong></td>
                    <td>Fuel Quantity</td>
                    <td>{{ activity.fuelQuantity }}</td>
                  </tr>
                  <tr>
                    <td>Fuel Mileage</td>
                    <td>{{ activity.mileage }}</td>
                  </tr>
                  <tr>
                    <td>Fuel Cost</td>
                    <td>{{ activity.cost  }}</td>
                  </tr>
                  <tr>
                    <td>Fuel Consumption Date</td>
                    <td>{{ activity.activityDate   | date:'yyyy-MM-dd' }}</td>
                  </tr>
                  <tr>
                    <td>Driver's Name</td>
                    <td>{{ activity.driveName}}</td>
                  </tr>
                </ng-container>
      
                <!-- Insurance Section -->
                <ng-container *ngIf="activity.insuranceType || activity.insuranceCost">
                  <tr>
                    <td rowspan="4"></td>
                    <td rowspan="4"><strong>Insurance</strong></td>
                    <td>Insurance Type</td>
                    <td>{{ activity.insuranceType }}</td>
                  </tr>
                  <tr>
                    <td>Insurance Cost</td>
                    <td>{{ activity.cost }}</td>
                  </tr>
                  <tr>
                    <td>Insurance Acquisition Date</td>
                    <td>{{ activity.activityDate | date:'yyyy-MM-dd' }}</td>
                  </tr>
                  <tr>
                    <td>Insurance Period (months)</td>
                    <td>{{ activity.insurancePeriod }}</td>
                  </tr>
                </ng-container>
      
                <!-- Maintenance Section -->
                <ng-container *ngIf="activity.maintenanceCost || activity.sparePartsRepaired">
                  <tr>
                    <td rowspan="4"></td>
                    <td rowspan="4"><strong>Maintenance</strong></td>
                    <td>Maintenance Cost</td>
                    <td>{{ activity.cost }}</td>
                  </tr>
                  <tr>
                    <td>Maintenance Activity Date</td>
                    <td>{{ activity.activityDate | date:'yyyy-MM-dd' }}</td>
                  </tr>
                  <tr>
                    <td>Spare Parts Repaired</td>
                    <td>{{ activity.sparePartsRepaired }}</td>
                  </tr>
                  <tr>
                    <td>Activity Observation</td>
                    <td>{{ activity.maintenanceActivityObeservation }}</td>
                  </tr>
                </ng-container>
      
                <!-- Oil Service Section -->
                <ng-container *ngIf="activity.oilServiceMileage || activity.oilServiceCost">
                  <tr>
                    <td rowspan="4"></td>
                    <td rowspan="4"><strong>Oil Service</strong></td>
                    <td>Oil Service Mileage</td>
                    <td>{{ activity.mileage }}</td>
                  </tr>
                  <tr>
                    <td>Oil Service Cost</td>
                    <td>{{ activity.cost  }}</td>
                  </tr>
                  <tr>
                    <td>Oil Service Date</td>
                    <td>{{ activity.activityDate | date:'yyyy-MM-dd' }}</td>
                  </tr>
                  <tr>
                    <td>Service Name</td>
                    <td>{{ activity.oilServiceCategoryID }}</td>
                  </tr>
                </ng-container>
      
                <!-- Vehicle Status Section -->
                <tr>
                  
                  <td colspan="3"><strong>Vehicle Active Status</strong></td>
               <td [ngClass]="{'text-success': activity.isVehicleActive, 'text-danger': !activity.isVehicleActive}">
                <strong>  {{ activity.isVehicleActive ? 'Active' : 'Inactive' }}    </strong> 
                </td>
            
                  
                </tr>
      
              </ng-container>
            </tbody>
          </table>
        </div>
      </ng-container>
      <!-- Fallback message if no activities are found -->
   
    </div>
    
    
  
    <div class="col-6 text-right mt-3 mb-4">
      <button (click)="onSubmitParentForm()" type="button" class="btn btn-primary">Submit quarterly Form</button>
    </div>
  </div>
  

  <ng-template #noActivities>
    <div class="alert alert-warning" role="alert">
      No vehicle activities found.
    </div>
  </ng-template>
</form>

  </div>
</div>