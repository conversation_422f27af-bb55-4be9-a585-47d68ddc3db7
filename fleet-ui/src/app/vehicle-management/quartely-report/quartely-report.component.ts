import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { faMinus, faPlus,faCirclePlus, faDownload, faPrint, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import * as XLSX from 'xlsx'; 
interface ReportResponse {
  reportId: string; // Ensure this matches your actual API response
}

interface VehicleActivity {
  vehicleID: string;
  oilServiceCategoryID: string;
  fuelQuantity?: number;
  fuelMileage?: number;
  fuelCost?: number;
  fuelConsumptionDate?: string;
  oilServiceMileage?: number;
  oilServiceCost?: number;
  oilServiceDate?: string;
  isVehicleActive: boolean;
  vehicleStatusId: string;
  maintenanceCost?: number;
  maintenanceActivityDate?: string;
  sparePartsRepaired?: string;
  insurancePeriod?: number;
  insuranceCost?: number;
  insuranceAcquisitionDate?: string;
  insuranceType?: string;
  activityDescription?: string;
  oilServiceCategory: OilServiceCategory;
  activityOnVehicle: ActivityOnVehicle;
  cost?: number;
  activityDate: string;
  activityQuaterSatrtDate?: string;
  activityQuaterEndDateDate?: string;
  mileage?: number;
  vehicleStatus: VehicleStatus;
  createddate: string;
  driveName?: string;
  maintenanceActivityObeservation?: string;
  id: string;
}
interface OilServiceCategory {
  id: string;
  name: string;
  category: string;
}

interface ActivityOnVehicle {
  id: string;
  name: string;
  code: string;
}

interface VehicleStatus {
  id: string;
  name: string;
}

@Component({
  selector: 'app-quartely-report',
  templateUrl: './quartely-report.component.html',
  styleUrls: ['./quartely-report.component.scss']
})

export class QuartelyReportComponent implements OnInit {
  searchTerm: string = '';
  reportForm!: FormGroup;
  vehicleActivities: VehicleActivity[] = [];
  files!: FormArray;
  vehicle: any = null;
  isVehicleDetailsVisible = false;
  vehicleId: any = '';
  reportId:any =''
  vehicleStatuses: any[] = [];
  plusIcon = faPlus;
  minusIcon = faMinus;
  selectedFileName: any;
  parentFormGroup!: FormGroup;
  currentStep: number = 1;
  maintenanceActivityIds: string[] = [];
  vehicleQuarterlyReportId: string | undefined;
  plusIconn = faCirclePlus;
  downloadIcon = faDownload;
  printIcon = faPrint;
  backwardIcon = faArrowLeft;
  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.reportForm = this.formBuilder.group({
      vehicleId: [''],
      vehicleStatusId: ['', Validators.required],
      description: [''],
      isVehicleActive: [false],
      files: this.formBuilder.array([this.createFileFormGroup()])
    });
    this.files = this.reportForm.get('files') as FormArray;
  }

  isFieldRequired(fieldName: string): boolean {
    const control = this.reportForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['vehicleId']) {
        this.vehicleId = params['vehicleId'];
        this.fetchVehicleDetails(this.vehicleId);
        this.fetchAndKeepActivityIds();
      }
    });
    this.fetchVehicleStatus();
  }
  goBack(): void {
    this.currentStep = 1; // Navigate back to the first step
  }
  fetchVehicleDetails(vehicleId: string): void {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.vehicle = response;
      },
      (error) => {
        console.error('Error fetching vehicle details:', error);
      }
    );
  }

  fetchVehicleStatus(): void {
    const url = `${environment.otherUrl}/vehicle-management/vehicleStatus`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        const filteredResponse = response.filter(
          (type) => type.id === 'f41b70f7-8168-400e-a8ba-0c7117e4e372' || type.id === '46977316-67bd-497f-9778-159d8399cff5'
        );
        this.vehicleStatuses = filteredResponse.map((type) => ({ id: type.id, name: type.name }));
      }
    );
  }

  addFile(): void {
    if (this.files.length < 2) {
      this.files.push(this.createFileFormGroup());
    } else {
      console.warn("Maximum number of files (2) reached.");
    }
  }

  removeFile(index: number): void {
    this.files.removeAt(index);
  }

  createFileFormGroup(): FormGroup {
    return this.formBuilder.group({
      file: [null],
      documentDescription: ['']
    });
  }

  nextStep() {
    if (this.currentStep === 1) {
      this.onSubmitNestedForm();  // Attempt to submit nested form
    }
    if (this.currentStep < 2) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  onFileChange(event: any, index: number): void {
    event.preventDefault();
    const file = event.target.files[0];
    if (file) {
      const fileControl = this.files.at(index).get('file');
      if (file.type !== 'application/pdf') {
        this.toastr.error('Wrong file type. File should be PDF only.', 'Error');
        if (fileControl) {
          fileControl.setValue(null);
        }
        event.target.value = '';  // Clear the file input
        return;
      }
      if (fileControl) {
        fileControl.setValue(file);
      }
      this.selectedFileName = file.name;
    }
  }

  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitFile(vehicleId: string): void {
    const fileUploadDataFormArray = this.reportForm.get('files') as FormArray;
    fileUploadDataFormArray.controls.forEach((control: AbstractControl) => {
      if (control instanceof FormGroup) {
        const fileItem = control.value;
        if (fileItem.file) {
          const fileFormData = new FormData();
          const file = fileItem.file as File;

          if (this.validateFile(file)) {
            fileFormData.append('file', file);
            fileFormData.append('documentDescription', fileItem.documentDescription);
            fileFormData.append('applicationId', vehicleId);

            this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
              (response) => {
                console.log('API Response:', response);
                console.log("File submitted successfully");
                this.reportForm.reset();
              },
              (error) => {
                let errorMessage = 'An unexpected error occurred.';
                if (error && error.error && error.error.message) {
                  errorMessage = error.error.message;
                }
                console.error('API Error:', errorMessage);
                this.toastr.error("An error occurred!!", 'Error');
              }
            );
          } else {
            console.warn('File size or type is invalid.');
          }
        } else {
          console.log('No file selected...');
        }
      }
    });
  }

  onSubmitNestedForm(): void {
    if (this.reportForm.valid) {
      const formData = {
        vehicleId: this.vehicleId,
        vehicleStatusId: this.reportForm.get('vehicleStatusId')?.value,
        description: this.reportForm.get('description')?.value,
        isVehicleActive: this.reportForm.get('isVehicleActive')?.value === 'true'
      };
  
      console.log('Submitting form with data:', JSON.stringify(formData));
  
      this.http.post<{ id: string }>(`${environment.otherUrl}/submit-Quarterly-vehicle-report`, formData).subscribe(
        (response) => {
          console.log('Response:', response);
          this.vehicleQuarterlyReportId = response.id; // Store the ID
          console.log('First idd: ', this.vehicleQuarterlyReportId)
          this.toastr.success('Saved Successfully');
          this.currentStep = 2; // Move to the next step
        },
        (error) => {
          console.error('Error response:', error);
          if (error.error && error.error.message) {
            this.toastr.error(`Error: ${error.error.message}`, 'Error');
          } else {
            this.toastr.error('Error submitting quarterly report', 'Error');
          }
        }
      );
    } else {
      this.toastr.error('Please fill out the form correctly before proceeding.', 'Error');
    }
  }
  

  async fetchAndKeepActivityIds(): Promise<void> {
    if (this.vehicleId) {
      try {
        const response = await fetch(`${environment.otherUrl}/activity-performed-by-VehicleD?id=${this.vehicleId}`);
        if (!response.ok) throw new Error(`Error fetching activities: ${response.statusText}`);
    
        const activities: VehicleActivity[] = await response.json();
        console.log(activities);
        this.vehicleActivities = activities;
        this.maintenanceActivityIds = activities.map(activity => activity.id); // Extract IDs
        
      } catch (error) {
        console.error('Error fetching vehicle activities:', error);
        this.vehicleActivities = [];
      }
    }
  }

  async onSubmitParentForm(): Promise<void> {
    if (this.vehicleQuarterlyReportId && this.maintenanceActivityIds.length > 0) {
      let allSuccessful = true;
  
      for (const activityId of this.maintenanceActivityIds) {
        const formData = {
          vehicleQuarterlyReportId: this.vehicleQuarterlyReportId,
          maintenanceActivityId: activityId
        };
        
        console.log('Submitting:', formData);
        
        try {
          const response = await this.http.post(`${environment.otherUrl}/submit-Quarterly-activities`, formData).toPromise();
          console.log(`Activity ${activityId} linked successfully`, response);
        } catch (error) {
          console.error(`Error linking activity ${activityId}:`, error);
          allSuccessful = false;
        }
      }
  
      if (allSuccessful) {
        this.toastr.success('Quartely report submitted successfully');
      } else {
        this.toastr.error('Some activities failed to link', 'Error');
      }
  
      this.router.navigate(['/vehicle-management/all-quartely-reports']); // Navigate as needed
    } else {
      this.toastr.error('Missing report or activities data', 'Error');
      console.error('Missing report or activities data:', {
        reportId: this.vehicleQuarterlyReportId,
        maintenanceActivityIds: this.maintenanceActivityIds
      });
    }
  }
  exportVehiclesToExcel() {
    // Convert the `vehicleActivities` array to a worksheet
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.vehicleActivities);

    // Create a new workbook and append the worksheet
    const workbook: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Activities');

    // Save to file
    XLSX.writeFile(workbook, 'VehicleActivities.xlsx');
  }
  printVehicles(): void {
    const printContents = document.getElementById('printableArea')?.innerHTML;
    if (printContents) {
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents;
      window.print();
      document.body.innerHTML = originalContents;
      location.reload(); // To reload the original page content
    }
  }
  toggleVehicleDetails(): void {
    this.isVehicleDetailsVisible = !this.isVehicleDetailsVisible;
  }
}
