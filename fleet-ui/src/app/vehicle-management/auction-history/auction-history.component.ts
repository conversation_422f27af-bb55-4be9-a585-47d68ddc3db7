import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-auction-history',
  templateUrl: './auction-history.component.html',
  styleUrl: './auction-history.component.scss'
})
export class AuctionHistoryComponent {
  actionId = ''; // ID for fetching the history
  actionHistory: any[] = []; // Stores the fetched history
  userDetails: { [key: string]: any } = {}; 
  acquisitionDetails: any;
  acquisitions: any;
  acquisitionStatus = 'Pending';

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private toastr: ToastrService
  ) {}

  ngOnInit() {
    // Extract the auctionId from the route parameters
    this.route.paramMap.subscribe((params) => {
      const auctionId = params.get('auctionId');
      console.log('Auction ID:', auctionId);  // Check if auctionId is passed
      if (auctionId) {
        this.actionId = auctionId;
        this.fetchAuctionHistory(auctionId);  // Fetch history using auctionId
      } else {
        console.error('Auction ID is missing!');
      }
    });
  }

  // Fetch auction history using the auctionId (actionId)
  fetchAuctionHistory(actionId: string) {
    const url = `${environment.baseUrl}/approval/getAllHistoryByActionId?actionId=${actionId}`;
    this.http.get<any[]>(url).subscribe(
      (response) => {
        this.actionHistory = response;
        console.log('Auction History:', this.actionHistory); // Log the fetched history
      },
      (error) => {
        console.error('Error fetching auction history:', error);
      }
    );
  }
}
