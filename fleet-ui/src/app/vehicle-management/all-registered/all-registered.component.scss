.vehicles-container{
    background-color:#D9D9D94D;
    width: 83%;
    height: 90%;
    right: 0;
    top: 10%;
    position: absolute;
    padding: 15px;
}
.overdue-row > td{
    background-color: #f1adad !important;
  }
  .pending-btn {
    margin-left: 15px;
    padding: 8px 12px;
    font-weight: bold;
    background-color: #ff4d4d;
    color: white;
    border-radius: 5px;
    transition: background-color 0.3s;
  }

  .pending-btn:hover {
    background-color: #d43f3f;
  }


  .statuses button {
    margin-right: 10px;
  }


  .badge-circle {
    position: absolute;
    top: -5px;
    right: -10px;
    background-color: #ff4d4d;
    color: white;
    font-size: 12px;
    padding: 5px 8px;
    border-radius: 50%;
    font-weight: bold;
  }



.overdue{
    color: rgb(196, 34, 34);
    margin: 0px 14px;
    // width: 120px;
    font-weight: bold;
    font-size: 12px;
}
.search-group{
    background-color: #ffffff;
    border-radius: 10px;
    width: 20%;
    height: 35px;
    border: 1px solid #edebf0;
}
.icon{
    background-color: #ffffff;
    color: #28A4E2;
    font-size: 13px;
    border-radius: 10px;
    padding: 9px 0px 7px 9px !important;
    text-align: center;
}
.global-input{
    color: #A098AE;
    background-color: #fff;
    border-radius: 10px;
    font-size: 13px;
}
.sorting-group{
    font-size: 12px;
    background-color: #F9FBFF;
    width: 17%;
    border-radius: 10px;
}
.select{
    font-size: 12px;
    font-weight: bold;
    width: 60%;
    border: none !important;
    background-color: #F9FBFF;
    outline: none !important;
}
.table-title{
    font-size: 18px;
    color: #28A4E2;
    font-weight: 500;
    width: fit-content;

}
.card{
    border: none;
}
th{
    font-size: 12px;
    color: #B5B7C0 !important;
    font-weight: 500;
}
.status-active{
    color: #23DF04;
    font-size: 12px;
    font-weight: 500;

}
.status-inactive{
    color: #DF0404BF;
    font-size: 12px;
    font-weight: 500;
}
.table-header{
    padding: 15px 15px 0px 15px !important;
}
.table-only{
    padding-bottom: 0px !important;
}
.nav{
    padding: 0px 20px;
}
.nav li .pages{
    height: 30px;
    width: 30px;
    border-radius: 50%;
    font-size: 12px;
    padding: 5px;
}
.nav li{
    margin: 0px 5px;
}
.caret{
    background-color: #ffffff;
    border: none;
    font-size: 20px;
    color: #A098AE;
    padding: 5px 0px;
}
.pagination-info{
    font-size: 14px;
    color: #B5B7C0;
}
td{
    font-size: 12px;
    color: #757575;
}
.request-btn{
    background-color: #28A4E2;
    color: #F9FBFF;
    font-weight: 500;
    font-size: 14px;
    height: 10%;
}
.view-btn{
    border: 1px solid #28A4E2;
    color:#28A4E2;
    font-size: 12px;
    font-weight: 500;
}

.edit-btn{
    border: 1px solid rgb(20, 160, 27);
    color: rgb(20, 160, 27);
    font-size: 12px;
    font-weight: 500;
}
.dispose-btn{
    border: 1px solid rgb(224, 158, 34);
    color: rgb(224, 158, 34);
    font-size: 12px;
    font-weight: 500;
}
.report-btn{
    border: 1px solid rgb(224, 34, 164);
    color: rgb(224, 34, 164);
    font-size: 11px;
    font-weight: 600;
}
.ext-btn{
    border: 1px solid rgb(18, 102, 7);
    color: rgb(18, 102, 7);
    font-size: 10px;
    font-weight: 600;
}

  .status-button {
    border: 2px solid transparent;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 12px;

    &.bg-success {
      color: green;
      border-color: green;
    }

    &.bg-warning {
      color: orange;
      border-color: orange;
    }

    &.bg-danger {
      color: red;
      border-color: red;
    }

    &.bg-secondary {
      color: gray;
      border-color: gray;
    }

    &.bg-info {
      color: blue;
      border-color: blue;
    }
  }
  .status-registered {
    color: green;
  }
  .statuses .btn{
    border: none;
    border-radius: 0px;
    font-size: 15px;
    color: #757575;
}
.statuses .btn.active{
    color: #28A4E2;
    font-weight: 500;
    border-bottom: 3px solid #28A4E2;
    transition: border-bottom 0.2s ease-in-out;
}
.no-vehicles-message {
    text-align: center;
    font-size: 24px;
    // color: #dc3545; /* Bootstrap's text-danger color */
    font-weight: bold;
    padding: 40px 0;
  }
/* Add these styles to your component's SCSS file for consistent table rows */

.table-only {
    .table {      
      thead th {
        height: 50px;
        vertical-align: middle;
        padding: 12px 8px;
        font-weight: 500;
        font-size: 13px;
        border-bottom: 2px solid #dee2e6;

        position: sticky;
        top: 0;
        z-index: 10;
      }
  
      tbody tr {
        min-height: 60px; /* Minimum row height */
        
        &:hover {
          background-color: #f8f9fa;
        }
        
        &.overdue-row {
          background-color: #f8d7da;
          
          &:hover {
            background-color: #f1b0b7;
          }
        }
      }
  
      tbody td {
        vertical-align: top; /* Align content to top */
        padding: 12px 8px;
        font-size: 13px;
        line-height: 1.4;
        word-wrap: break-word;
        white-space: normal; /* Allow text wrapping */
        overflow-wrap: break-word; /* Break long words if needed */
        
        /* Last column (Actions) - maintain original styling */
        &:last-child {
          min-width: 200px;
          
         
          
          .overdue {
            color: #dc3545;
            font-weight: 600;
            font-size: 10px;
            display: block;
            margin-top: 2px;
            line-height: 1.2;
          }
        }
      }
  
      /* Column width suggestions (flexible) */
      th:nth-child(1), td:nth-child(1) { min-width: 100px; } /* Plate Number */
      th:nth-child(2), td:nth-child(2) { min-width: 120px; } /* Chassis Number */
      th:nth-child(3), td:nth-child(3) { min-width: 110px; } /* Ownership Type */
      th:nth-child(4), td:nth-child(4) { min-width: 90px; }  /* Vehicle Type */
      th:nth-child(5), td:nth-child(5) { min-width: 100px; } /* Manufacture */
      th:nth-child(6), td:nth-child(6) { min-width: 150px; } /* Beneficiary Agency */
      th:nth-child(7), td:nth-child(7) { min-width: 100px; } /* Approval Level */
      th:nth-child(8), td:nth-child(8) { min-width: 90px; }  /* Status */
      th:nth-child(9), td:nth-child(9) { min-width: 200px; } /* Actions */
    }
  }
  
  /* Keep only the overdue span styling, remove action-buttons wrapper styles */
  .overdue {
    color: #dc3545;
    font-weight: 600;
    font-size: 10px;
    display: block;
    margin-top: 2px;
    line-height: 1.2;
  }
  
  /* Status styling */
  .status-registered {
    color: #28a745;
    font-weight: 600;
  }
  
  /* No vehicles message */
  .no-vehicles-message {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
    height: 100px;
    vertical-align: middle;
  }
  
  /* Responsive adjustments */
  @media (max-width: 1200px) {
    .table-only .table {
      tbody td {
        font-size: 12px;
        padding: 6px;
      }
      
      .action-buttons .btn {
        font-size: 10px;
        padding: 3px 6px;
        min-height: 24px;
      }
    }
  }
  
  @media (max-width: 768px) {
    .table-only {
      overflow-x: auto;
      
      .table {
        min-width: 800px; /* Prevent table from being too compressed */
        
        tbody tr {
          min-height: 55px;
        }
        
        tbody td {
          font-size: 11px;
          padding: 8px 4px;
          
          &:last-child {
            min-width: 180px;
          }
        }
        
        .overdue {
          font-size: 9px;
        }
      }
    }
  }
  
  /* Remove tooltip styling since we're not using truncated text anymore */
