<div class="container">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>

  <div class="vehicles-container">
    <div class="header d-flex flex-row justify-content-between">
      <button class="btn go-back-btn" (click)="goBack()">
        <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
      </button>
      <h2 class="table-title">All Registered Vehicles</h2>

      <ng-container *ngIf="isLogistics()">
        <button class="btn request-btn d-flex flex-row justify-content-center align-items-center pe-4" (click)="navigateToRegisterVehicle()">
          <fa-icon [icon]="carIcon" class="pe-2"></fa-icon>
          Existing Vehicle Recording
        </button>
      </ng-container>
    </div>

    <div class="card">
      <div class="table-header d-flex flex-row justify-content-between">
        <!-- Table Filter -->
        <div class="d-flex flex-row justify-content-start align-items-center statuses">
          <button class="btn btn-outline position-relative"
                  (click)="setFilter('approved')"
                  [class.active]="currentFilter === 'approved'">
            Registered
            <span *ngIf="currentFilter === 'approved'" class="badge badge-circle">{{ filteredRegisteredVehicles.length }}</span>
          </button>
          <button class="btn btn-outline position-relative"
                  (click)="setFilter('pending-progress')"
                  [class.active]="currentFilter === 'pending-progress'">
            Pending Requests
            <span *ngIf="currentFilter === 'pending-progress'" class="badge badge-circle">{{ filteredRegisteredVehicles.length }}</span>
          </button>

          <button *ngIf="userRole != 'Institutional logistics' && userRole != 'Institutional CBM'"
                  class="btn btn-danger text-white position-relative pending-btn"
                  [class.active]="currentFilter === 'pending'"
                  (click)="setFilter('pending')">
            Pending Your Approval
            <span *ngIf="currentFilter === 'pending'" class="badge badge-circle">{{ filteredRegisteredVehicles.length }}</span>
          </button>

          <button class="btn btn-outline position-relative"
                  (click)="setFilter('denied')"
                  [class.active]="currentFilter === 'denied'">
            Denied
            <span *ngIf="currentFilter === 'denied'" class="badge badge-circle">{{ filteredRegisteredVehicles.length }}</span>
          </button>

          <button *ngIf="showReportingVehiclesTab"
                  class="btn btn-outline position-relative"
                  (click)="setFilter('reporting')"
                  [class.active]="currentFilter === 'reporting'">
            Reporting Vehicles
            <span *ngIf="currentFilter === 'reporting'" class="badge badge-circle">{{ filteredRegisteredVehicles.length }}</span>
          </button>
        </div>

        <div class="search-group d-flex mt-2">
          <fa-icon [icon]="searchIcon" class="icon"></fa-icon>
          <input type="search" class="global-input form-control-sm" placeholder="Search Here...." [(ngModel)]="searchText" (keyup)="searchVehicles()">
        </div>
      </div>

<div class="table-only p-3">
  <table class="table table-stripped">
    <thead>
      <tr>
        <th>Plate Number</th>
        <th>Chassis Number</th>
        <th>Ownership Type</th>
        <th>Vehicle Type</th>
        <th>Vehicle Manufacture</th>
        <th>Beneficiary Agency</th>
        <th>Approval Level</th>
        <th>Status</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngIf="displayedVehicles.length <= 0">
        <td colspan="9" class="no-vehicles-message">No Vehicles!!!</td>
      </tr>
      <tr *ngFor="let vehicle of displayedVehicles" 
          [ngClass]="{ 'overdue-row': vehicle.isoverdueQuarterlyReportSubmition || isProjectVehicleOverdue(vehicle)}">
        
        <!-- Plate Number -->
        <td>{{ vehicle.plateNumber }}</td>
        
        <!-- Chassis Number -->
        <td>{{ vehicle.chassisNumber }}</td>
        
        <!-- Ownership Type -->
        <td>{{ vehicle.ownershipType?.name }}</td>
        
        <!-- Vehicle Type -->
        <td>{{ vehicle.vehicleType?.name }}</td>
        
        <!-- Vehicle Manufacture -->
        <td>{{ vehicle.vehicleManufacture?.name }}</td>
        
        <!-- Beneficiary Agency -->
        <td>{{ vehicle.beneficiaryAgency }}</td>
        
        <!-- Approval Level -->
        <td>{{ vehicle.approvalLevel?.name }}</td>
        
        <!-- Status -->
        <td [ngClass]="getVehicleStatusClass(vehicle.registrationStatus)">
          {{ vehicle.registrationStatus?.name === 'APPROVED' ? 'REGISTERED' : 'Unregistered' }}
        </td>
        
        <!-- Actions -->
        <td>
          <div class="action-buttons">
            <!-- View Button -->
            <button *ngIf="!vehicle.isoverdueQuarterlyReportSubmition && vehicle.isQuarterlyReportSubmited" 
                    class="btn view-btn me-2" 
                    (click)="viewVehicle(vehicle.id)"
                    title="View vehicle details">
              View
            </button>
            
            <!-- Edit Button -->
            <button *ngIf="vehicle.registrationStatus.name == 'RFAC' || 
                           userRole == 'Institutional logistics' && vehicle.registrationStatus.name != 'APPROVED' || 
                           userRole == 'Institutional CBM' && vehicle.registrationStatus.name != 'APPROVED' || 
                           userRole == 'Fleet Mgt Senior Engineer' && vehicle.registrationStatus.name != 'APPROVED'"
                    class="btn edit-btn" 
                    (click)="editRegisteredVehicle(vehicle.id)"
                    title="Edit vehicle information">
              Edit
            </button>
            
            <!-- Operation Cost Button -->
            <button *ngIf="userRole == 'Institutional logistics' && 
                           vehicle.registrationStatus.name == 'APPROVED' && 
                           !vehicle.isoverdueQuarterlyReportSubmition && 
                           vehicle.isQuarterlyReportSubmited && 
                           !isProjectVehicleOverdue(vehicle)"
                    class="btn edit-btn" 
                    (click)="reportCostOfMaintenance(vehicle.id)"
                    title="Report operation costs">
              Operation Cost
            </button>
            
            <!-- Dispose Button -->
            <button *ngIf="userRole == 'Institutional logistics' && 
                           vehicle.registrationStatus.name == 'APPROVED' && 
                           vehicle.isDisposalRequestSubmitted === false && 
                           !vehicle.isoverdueQuarterlyReportSubmition && 
                           vehicle.isQuarterlyReportSubmited"
                    class="btn dispose-btn" 
                    (click)="disposeVehicle(vehicle.id)"
                    title="Dispose vehicle">
              Dispose
            </button>
            
            <!-- Quarterly Report Button -->
            <button *ngIf="userRole == 'Institutional logistics' && 
                           vehicle.registrationStatus.name == 'APPROVED' && 
                           !vehicle.isQuarterlyReportSubmited"
                    class="btn report-btn" 
                    (click)="reportVehicle(vehicle.id)"
                    title="Submit quarterly report">
              Waiting For Quarterly Report...
            </button>
            
            <!-- Project Extension Button -->
            <button *ngIf="isProjectVehicleOverdue(vehicle) && 
                           userRole == 'Institutional logistics' && 
                           vehicle.registrationStatus.name == 'APPROVED'"
                    class="btn ext-btn" 
                    (click)="requestProjectExtension(vehicle.id)"
                    title="Request project extension">
              Project Extension
            </button>
            
            <!-- Overdue Messages -->
            <span *ngIf="vehicle.isoverdueQuarterlyReportSubmition" 
                  class="overdue"
                  title="Quarterly report submission is overdue">
              Quarterly Report is overdue!
            </span>
            
            <span *ngIf="isProjectVehicleOverdue(vehicle)" 
                  class="overdue"
                  title="Project end date has passed">
              Project Has Ended
            </span>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<nav aria-label="Page navigation" class="nav d-flex flex-column flex-md-row justify-content-between align-items-center">
  <!-- Pagination info - responsive layout -->
  <div class="pagination-info mb-2 mb-md-0 order-2 order-md-1">
    <span class="d-none d-md-inline">
      Showing {{ getFirstEntryIndex() }} - {{ getLastEntryIndex() }} of {{ filteredRegisteredVehicles.length }} entries
    </span>
    <span class="d-md-none">
      Page {{ getPaginationInfoMobile() }} ({{ filteredRegisteredVehicles.length }} total)
    </span>
  </div>

  <!-- Pagination controls -->
  <div class="pagination-controls order-1 order-md-2">
    <ul class="pagination justify-content-center mb-0">
      <!-- Previous button -->
      <li class="page-item">
        <button class="page-link caret d-flex align-items-center justify-content-center" 
                (click)="previousPage()" 
                [disabled]="currentPage === 1"
                aria-label="Previous page">
          <fa-icon [icon]="caretLeft"></fa-icon>
        </button>
      </li>

      <!-- Page numbers -->
      <li class="page-item" *ngFor="let pageNumber of getVisiblePageNumbers()">
        <button *ngIf="pageNumber !== '...'" 
                class="page-link pages" 
                [class.active]="currentPage === pageNumber" 
                (click)="goToPageOrEllipsis(pageNumber)"
                [attr.aria-label]="'Go to page ' + pageNumber">
          {{ pageNumber }}
        </button>
        <span *ngIf="pageNumber === '...'" 
              class="page-link ellipsis" 
              aria-hidden="true">
          ...
        </span>
      </li>

      <!-- Next button -->
      <li class="page-item">
        <button class="page-link caret d-flex align-items-center justify-content-center" 
                (click)="nextPage()" 
                [disabled]="currentPage === totalPages"
                aria-label="Next page">
          <fa-icon [icon]="caretRight"></fa-icon>
        </button>
      </li>
    </ul>
  </div>
</nav>
    </div>
  </div>
</div>
