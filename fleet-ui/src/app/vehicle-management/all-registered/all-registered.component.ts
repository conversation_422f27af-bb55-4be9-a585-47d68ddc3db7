import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faSearch, faCaretLeft, faCaretRight, faCar, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-all-registered',
  templateUrl: './all-registered.component.html',
  styleUrls: ['./all-registered.component.scss']
})
export class AllRegisteredComponent implements OnInit {
  registeredVehicles: any[] = [];
  filteredRegisteredVehicles: any[] = [];
  pageSize: number = 50; // Number of items per page
  currentPage: number = 1; // Current page
  totalPages: number = 0; // Total number of pages
  institutionId: string = '';
  userRole: string = '';
  searchText: string = '';
  faSearch = faSearch;
  backwardIcon = faArrowLeft;
  caretLeft = faCaretLeft;
  caretRight = faCaretRight;
  carIcon = faCar;
  searchIcon = faSearch;
  currentFilter: string = 'approved';
  displayedVehicles: any[] = [];
  showReportingVehiclesTab: boolean = false;
  reportingVehicles: any[] = [];
  isMainContentDropdownOpen = false;


  toggleMainContentDropdown() {
    this.isMainContentDropdownOpen = !this.isMainContentDropdownOpen;
  }
  constructor(
    private http: HttpClient,
    private router: Router,
    private location: Location
  ) {}

  goBack(){
    this.location.back();
  }

  ngOnInit() {
    this.getUserDetails(); // Load user data
    this.fetchRegisteredVehicles();
    this.fetchReportingVehicles() // Load registered vehicles data
  }

  getUserDetails() {
    const data = localStorage.getItem('localUserData');
    if (data != null) {
      const parsedObj = JSON.parse(data);
      this.institutionId = parsedObj.data.user.institution.id; // Store institution ID
      this.userRole = parsedObj.data.user.role.name; // Store user role
    }
  }

  fetchRegisteredVehicles() {
    if (!this.institutionId || !this.userRole) {
      console.error('Institution ID or User Role not found');
      return;
    }

    let url: string;

    switch (this.userRole) {
      case 'Institutional logistics':
      case 'Institutional CBM':
        // Fetch registered vehicles by institution for Logistics and CBM roles
        // url = `${environment.otherUrl}/allVehicles/${this.institutionId}`;
        url = `${environment.otherUrl}/allVehiclesbybeneficiaryAgencyId/${this.institutionId}`;
        break;

      case 'Fleet Mgt Senior Engineer':
      case 'DG Transport':
      case 'Permanent Secretary':
      case 'Minister':
        // Fetch all registered vehicles for other roles
        url = `${environment.otherUrl}/allVehicles`;
        break;

      default:
        console.error('Unrecognized user role');
        return;
    }

    this.http.get<any[]>(url).subscribe(
      (response: any[]) => {
        console.log(response);

        response.forEach(vehicle => {
          console.log('Vehicle isoverdueQuarterlyReportSubmition:', vehicle.isoverdueQuarterlyReportSubmition);
        });

        this.registeredVehicles = response
        .filter(
          vehicle => !(vehicle.isDisposalRequestSubmitted === true && vehicle.registrationStatus.name === 'APPROVED' )
        );
        this.filterVehiclesByRole();
        this.applyCurrentFilter();
        this.updateDisplayedVehicles();
      },
      (error) => {
        console.error('Error fetching registered vehicles:', error);
      }
    );
}
// Add this method to your component class
getVisiblePageNumbers(): (number | string)[] {
  const totalPages = this.totalPages;
  const currentPage = this.currentPage;
  const visiblePages: (number | string)[] = [];

  // For mobile/small screens, show fewer pages
  const isMobile = window.innerWidth < 768;
  const maxVisiblePages = isMobile ? 3 : 5;

  if (totalPages <= maxVisiblePages + 2) {
    // Show all pages if total is small
    for (let i = 1; i <= totalPages; i++) {
      visiblePages.push(i);
    }
  } else {
    // Always show first page
    visiblePages.push(1);

    // Calculate start and end of visible range
    let start = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
    let end = Math.min(totalPages - 1, currentPage + Math.floor(maxVisiblePages / 2));

    // Adjust if we're near the beginning or end
    if (currentPage <= Math.floor(maxVisiblePages / 2) + 1) {
      end = Math.min(totalPages - 1, maxVisiblePages);
    }
    if (currentPage >= totalPages - Math.floor(maxVisiblePages / 2)) {
      start = Math.max(2, totalPages - maxVisiblePages);
    }

    // Add ellipsis if needed
    if (start > 2) {
      visiblePages.push('...');
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      visiblePages.push(i);
    }

    // Add ellipsis if needed
    if (end < totalPages - 1) {
      visiblePages.push('...');
    }

    // Always show last page
    if (totalPages > 1) {
      visiblePages.push(totalPages);
    }
  }

  return visiblePages;
}

// Add method to handle ellipsis clicks
goToPageOrEllipsis(pageNumber: number | string) {
  if (typeof pageNumber === 'number') {
    this.goToPage(pageNumber);
  }
  // For ellipsis, you could implement a dropdown or do nothing
}

// Add method to get pagination info text for mobile
getPaginationInfoMobile(): string {
  return `${this.currentPage} of ${this.totalPages}`;
}

fetchReportingVehicles() {
  if (!this.institutionId) {
    console.error('Institution ID not found');
    return;
  }

  const url = `${environment.otherUrl}/allVehiclesByRepInstitution/${this.institutionId}`;
  this.http.get<any[]>(url).subscribe(
    (response: any[]) => {
      console.log('Reporting vehicles:', response);
      this.reportingVehicles = response; // Store reporting vehicles data
      this.showReportingVehiclesTab = this.reportingVehicles.length > 0 && (this.userRole === 'Institutional logistics' || this.userRole === 'Institutional CBM');
      console.log('Show reporting vehicles tab:', this.showReportingVehiclesTab);
    },
    (error) => {
      console.error('Error fetching reporting vehicles:', error); // Handle errors
      this.showReportingVehiclesTab = false;
    }
  );
}
filterVehiclesByRole() {
  this.filteredRegisteredVehicles = [...this.registeredVehicles];

  switch (this.userRole) {
      case 'Institutional logistics':
          break;

      case 'Institutional CBM':
          // Exclude vehicles with 'Institutional logistics' approval level
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
                  // vehicle.beneficiaryAgencyId === this.institutionId &&
                  vehicle.approvalLevel?.name !== 'Institutional logistics'
          );
          break;

      case 'Fleet Mgt Senior Engineer':
          // Exclude 'Institutional logistics' and 'Institutional CBM'
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
                  vehicle.approvalLevel?.name !== 'Institutional logistics' &&
                  vehicle.approvalLevel?.name !== 'Institutional CBM'
          );
          break;

      case 'DG Transport':
          // Exclude multiple approval levels
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
                  vehicle.approvalLevel?.name !== 'Institutional logistics' &&
                  vehicle.approvalLevel?.name !== 'Institutional CBM'
          );
          break;

      case 'Permanent Secretary':
          // Exclude additional approval levels
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
                  vehicle.approvalLevel?.name !== 'Institutional logistics' &&
                  vehicle.approvalLevel?.name !== 'Institutional CBM'

          );
          break;

      case 'Minister':
          // Only show vehicles with 'Minister' approval level
          this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
              vehicle =>
                vehicle.approvalLevel?.name !== 'Institutional logistics' &&
                vehicle.approvalLevel?.name !== 'Institutional CBM'
          );
          break;

      default:
          console.error('Unrecognized user role:', this.userRole);
          break;
  }

  this.updateDisplayedVehicles(); // Update the displayed vehicles based on the current page
}

applyCurrentFilter() {
  this.filterVehiclesByRole();

  switch (this.currentFilter) {

    // case 'all':
    // this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
    //   (item) => item.registrationStatus?.name === 'APPROVED' || item.registrationStatus?.name === 'PENDING' || item.registrationStatus?.name === 'PROGRESS'
    // );
    // this.filteredRegisteredVehicles = [...this.reportingVehicles];
    //   break;
    case 'pending-progress':
      this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
        (item) => item.registrationStatus?.name === 'PENDING' || item.registrationStatus?.name === 'PROGRESS'  || item.registrationStatus?.name === 'RFAC'
      );
      break;
      case 'approved':
        this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
          (item) => item.registrationStatus?.name === 'APPROVED'
        );
        break;
      case 'denied' :
        this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
          (item) => item.registrationStatus?.name === 'DENIED'
        );
        break;
        case 'pending':
        this.filteredRegisteredVehicles = this.filteredRegisteredVehicles.filter(
          (item) => item.registrationStatus?.name === 'PENDING'&& item.approvalLevel?.name === this.userRole ||item.registrationStatus?.name === 'PROGRESS' && item.approvalLevel?.name === this.userRole
        );
        break;
      case 'reporting':
        this.filteredRegisteredVehicles = [...this.reportingVehicles];
        break;
    default:
      console.error('Invalid filter type:', this.currentFilter);
  }

  this.currentPage = 1; // Reset to the first page when changing filters
  this.updateDisplayedVehicles(); // Refresh the displayed acquisitions
}

setFilter(filterType: string) {
  this.currentFilter = filterType;
  this.applyCurrentFilter(); // Apply the selected filter
}

updateDisplayedVehicles() {
  const startIndex = (this.currentPage - 1) * this.pageSize;
  const endIndex = Math.min(startIndex + this.pageSize, this.filteredRegisteredVehicles.length);

  // Create a separate array for the displayed vehicles based on the filtered data
  this.displayedVehicles = this.filteredRegisteredVehicles.slice(startIndex, endIndex); // Slice within the filtered list

  // Recalculate the total number of pages based on the filtered vehicles
  this.totalPages = Math.ceil(this.filteredRegisteredVehicles.length / this.pageSize);
}


getFirstEntryIndex(): number {
  return (this.currentPage - 1) * this.pageSize + 1;
}

getLastEntryIndex(): number {
  const lastEntryIndex = this.currentPage * this.pageSize;
  return Math.min(lastEntryIndex, this.filteredRegisteredVehicles.length);
}

goToPage(pageNumber: number) {
  if (pageNumber >= 1 && pageNumber <= this.totalPages) {
      this.currentPage = pageNumber;
      this.updateDisplayedVehicles(); // Refresh the displayed vehicles based on the new page
  }
}

getPageNumbers(): number[] {
  const pageNumbers = [];
  for (let i = 1; i <= this.totalPages; i++) {
      pageNumbers.push(i);
  }
  return pageNumbers;
}

previousPage() {
  if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedVehicles();
  }
}

nextPage() {
  if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updateDisplayedVehicles();
  }
}
  // sortable(event: any) {
  //   const selectedField: string = event.target.value;

  //   if (!selectedField) {
  //     this.filteredRegisteredVehicles.sort((a, b) => a.beneficiaryAgency.localeCompare(b.beneficiaryAgency));
  //   } else {
  //     this.filteredRegisteredVehicles.sort((a, b) => {
  //       const fieldA = a[selectedField];
  //       const fieldB = b[selectedField];
  //       if (typeof fieldA === 'string' && typeof fieldB === 'string') {
  //         return fieldA.localeCompare(fieldB);
  //       }
  //       return 0;
  //     });
  //   }
  //   this.updateDisplayedVehicles();
  // }
  sortDirection = true;

    sortable(event: any) {
        const field = event.target.value;
        if (field) {
            this.sortDirection = !this.sortDirection;
            this.displayedVehicles.sort((a, b) => {
                let aValue = this.getFieldValue(a, field);
                let bValue = this.getFieldValue(b, field);
                if (typeof aValue === 'string') {
                    aValue = aValue.toLowerCase();
                }
                if (typeof bValue === 'string') {
                    bValue = bValue.toLowerCase();
                }
                if (aValue < bValue) {
                    return this.sortDirection ? -1 : 1;
                } else if (aValue > bValue) {
                    return this.sortDirection ? 1 : -1;
                } else {
                    return 0;
                }
            });
        }
    }

    getFieldValue(vehicle: any, field: string) {
        switch (field) {
            case 'chassisNumber':
            case 'engineNumber':
                return vehicle[field];
            case 'ownershipType':
            case 'vehicleType':
            case 'vehicleManufacture':
            case 'approvalLevel':
                return vehicle[field]?.name;
            case 'beneficiaryAgency':
                return vehicle[field];
            case 'status':
                return vehicle.registrationStatus?.name === 'APPROVED' ? 'REGISTERED' : 'Unregistered';
            default:
                return '';
        }
    }

  isLogistics():boolean{
    const data = localStorage.getItem('localUserData');
    if(data !=null){
      const parseObj = JSON.parse(data);
      const roleName = parseObj?.data?.user?.role?.name;
      return roleName === 'Institutional logistics';
    }
    return false;
  }
  viewVehicle(vehicleId: string): void {
    console.log('Vehicle ID:', vehicleId);
    this.router.navigateByUrl(`/vehicle-management/vehicle-details/${vehicleId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }
  navigateToRegisterVehicle(): void {
    this.router.navigateByUrl('vehicle-management/register-existing-vehicle');
  }
  getVehicleStatusClass(registrationStatus: any): string {
    if (registrationStatus && registrationStatus.name === 'APPROVED') {
      return 'status-registered';
    } else {
      return '';
    }
  }
  editRegisteredVehicle(vehicleId: string): void{
    console.log("AcquisitionId: ",vehicleId);
    this.router.navigateByUrl(`/vehicle-management/edit-registered-vehicle/${vehicleId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  disposeVehicle(vehicleId: string):void {
    console.log("Vehicle Id: ",vehicleId);
    this.router.navigateByUrl(`/vehicle-management/vehicle-disposal/${vehicleId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  reportVehicle(vehicleId: string): void {
    this.router.navigateByUrl(`/vehicle-management/quartely-report/${vehicleId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  requestProjectExtension(vehicleId: string): void {
    this.router.navigateByUrl(`/vehicle-management/project-extension-request/${vehicleId}`).then(success => {
      if (success) {
        console.log('Navigation successful');
      } else {
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating:', error);
    });
  }

  reportCostOfMaintenance(vehicleId: string):void{
    this.router.navigateByUrl(`vehicle-management/cost-benefit/${vehicleId}`).then(success => {
      if(success) {
        console.log('Navigation Successful');
      } else{
        console.error('Navigation failed');
      }
    }).catch(error => {
      console.error('Error navigating', error);
    });
  }

  isProjectVehicleOverdue(vehicle: any): boolean {
    if (vehicle.ownershipType.name === 'Project Vehicles') {
      const projectEndDate = new Date(vehicle.projectEndDate);
      const projectExtensionDate = new Date(vehicle.projectExtensionDate);
      const currentDate = new Date();

      // Check if the project extension date is less than the current date
      if (projectExtensionDate < currentDate) {
        return true;
      }
    }

    return false;
  }

 searchVehicles() {
  const searchTextLower = this.searchText.toLowerCase();

  // Apply search only to the already filtered vehicles based on the current tab (filteredRegisteredVehicles)
  const filteredBySearch = this.filteredRegisteredVehicles.filter(item => {
    return (
      (item.plateNumber || '').toLowerCase().includes(searchTextLower) ||
      (item.pickCardNumber || '').toLowerCase().includes(searchTextLower) ||
      (item.vehicleType?.name || '').toLowerCase().includes(searchTextLower) ||
      (item.chassisNumber || '').toLowerCase().includes(searchTextLower) ||
      (item.vehicleModel?.name || '').toLowerCase().includes(searchTextLower) ||
      (item.vehicleManufacture?.name || '').toLowerCase().includes(searchTextLower) ||
      (item.beneficiaryAgency || '').toLowerCase().includes(searchTextLower) ||
      (item.ownershipType?.name || '').toLowerCase().includes(searchTextLower) ||
      (item.isVehicleActive ? 'active' : 'inactive').includes(searchTextLower)
    );
  });

  // Update displayed vehicles based on the search result
  this.displayedVehicles = filteredBySearch.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
  this.totalPages = Math.ceil(filteredBySearch.length / this.pageSize);
}

}
