.pink-card {
  width: 90%;
  padding: 20px;
  background-color: #ffcccb;
  border: 2px solid #000;
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  margin: 20px auto;
  font-family: 'Times New Roman', Times, serif;
  position: relative;
}

.header-center {
  text-align: center;
  font-size: 20px;
  margin-bottom: 20px;
}

.sub-header-center {
  text-align: center;
  margin-bottom: 10px;
  font-size: 18px;
}

.vehicle-table,
.owner-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;

  td,
  tr {
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
  }

  .merged-cell {
    text-align: center;
    padding: 20px;
    background-color: #ffcccb;
  }
}

.left-paragraph,
.right-paragraph {
  width: 45%;
  font-family: 'Times New Roman', Times, serif;
  font-weight: bold;
  font-size: 16px;
  text-align: left;

  &.left-paragraph {
    float: left;
    margin-right: 10px;
  }

  &.right-paragraph {
    float: right;
    font-style: italic;
  }
}

.no-container {
  display: inline-flex;
  align-items: center;

  .no-letter {
    position: relative;
    display: inline-block;
    text-decoration: underline;
    margin-left: 2px;
    font-size: 0.8em;
    top: -0.5em;
  }
}

.button-container {
  text-align: center;
  margin-top: 20px;
}

.download-button {
  padding: 10px 20px;
  font-size: 16px;
  font-family: 'Times New Roman', Times, serif;
  color: #fff;
  background-color: #847d81;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #555;
  }
}

.top-half {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 10px;
}

.qrcode {
  background-color: #ffcccb;
  margin: 10px;
}

.center-image,
.pmid {
  display: block;
  margin: 0 auto;
  text-align: center;
}

.language-selector {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px auto;
  padding: 8px 12px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #ccc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  width: fit-content;

  label {
    margin-right: 6px;
    font-weight: bold;
  }

  select.styled-select {
    padding: 6px 12px;
    font-size: 14px;
    font-family: 'Times New Roman', Times, serif;
    color: #333;
    background-color: #fef2f2;
    border: 1px solid #aaa;
    border-radius: 6px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    // background-image: url("data:image/svg+xml,%3Csvg%20width%3D%2210%22%20height%3D%225%22%20viewBox%3D%220%200%2010%205%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cpath%20d%3D%22M0%200l5%205%205-5z%22%20fill%3D%22%23333%22/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 10px;
    cursor: pointer;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #847d81;
      box-shadow: 0 0 0 2px rgba(132, 125, 129, 0.2);
    }
  }
}

// --- Media Queries ---

@media (max-width: 1279px) {
  .pink-card {
    width: 95%;
    padding: 15px;
  }

  .left-paragraph,
  .right-paragraph {
    width: 48%;
    font-size: 15px;
  }

  .vehicle-table td,
  .owner-table td {
    padding: 6px;
  }
}

@media (max-width: 1024px) {
  .top-half {
    flex-direction: column;
    align-items: center;
  }

  .header-center {
    font-size: 18px;
  }

  .sub-header-center {
    font-size: 16px;
  }

  .left-paragraph,
  .right-paragraph {
    width: 100%;
    float: none;
    margin: 5px 0;
    text-align: left;
  }
}

@media (max-width: 767px) {
  .pink-card {
    width: 100%;
    padding: 10px;
  }

  .header-center,
  .sub-header-center {
    font-size: 16px;
  }

  .left-paragraph,
  .right-paragraph {
    width: 100%;
    float: none;
    margin: 0;
  }

  .vehicle-table td,
  .owner-table td {
    padding: 4px;
  }

  .download-button {
    padding: 8px 15px;
    font-size: 14px;
  }

  .language-selector {
    top: 10px;
    right: 10px;
    padding: 4px 8px;

    label {
      font-size: 13px;
    }

    select.styled-select {
      font-size: 13px;
      padding: 5px 10px;
    }
  }
}

@media (max-width: 634px) {
  .pink-card {
    width: 95%;
    padding: 10px;
  }

  .vehicle-table,
  .owner-table {
    width: 100%;
    overflow-x: auto;
    display: block;

    td {
      padding: 4px;
      font-size: 12px;
    }
  }

  .header-center,
  .sub-header-center {
    font-size: 16px;
  }

  .left-paragraph,
  .right-paragraph {
    width: 100%;
    font-size: 14px;
    margin: 0;
  }

  .button-container {
    margin-top: 10px;
  }

  .download-button {
    padding: 6px 10px;
    font-size: 12px;
  }
}

@media (max-width: 523px) {
  .logo {
    text-align: center;
  }

  .top-half {
    flex-direction: column;
    align-items: center;
  }

  .header-center,
  .sub-header-center {
    font-size: 14px;
  }

  .left-paragraph,
  .right-paragraph {
    font-size: 14px;
    margin: 5px 0;
  }

  .vehicle-table,
  .owner-table {
    font-size: 12px;

    td {
      border: 1px solid #999;
      padding: 8px;
    }

    tr {
      border: none;
    }
  }

  .download-button {
    padding: 6px 10px;
    font-size: 12px;
  }

  .language-selector {
    position: static;
    display: flex;
    justify-content: center;
    margin: 10px auto;

    label {
      font-size: 12px;
    }

    select.styled-select {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}

