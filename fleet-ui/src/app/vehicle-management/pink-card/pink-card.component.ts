import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import html2pdf from 'html2pdf.js';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-pink-card',
  templateUrl: './pink-card.component.html',
  styleUrl: './pink-card.component.scss'
})
export class PinkCardComponent {
  plateNumber = 'RAB 123X';
  ownerName = '<PERSON>';
  vehicleType = 'SUV';
  registrationDate = '01/01/2020';
  engineNumber = 'ABC1234567890';
  vehicle: any = {};
  selectedLanguage: "rw" | "en" = "rw"; // ✅ This says it can be either 'rw' or 'en'



  constructor(
    private http: HttpClient,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
    }

    this.route.paramMap.subscribe(params => {
      const vehicleId = params.get('vehicleId');
      if (vehicleId) {
        this.fetchVehicleDetails(vehicleId);
      }
    });
  }

  fetchVehicleDetails(vehicleId: string) {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        this.vehicle = response;
      },
      (error) => {
        console.error('Error fetching vehicle details:', error);
      }
    );
  }

  downloadCard() {
    const elementId = this.selectedLanguage === 'rw' ? 'pink-card-rw' : 'pink-card-en';
    const element = document.getElementById(elementId);

    const opt = {
      filename: `Pink Card ${this.vehicle.pickCardNumber}.pdf`,
      html2canvas: { scale: 2 },
      jsPDF: {
        format: 'legal',
        orientation: 'landscape',
        unit: 'in',
        margin: [0.5, 0.5, 0.5, 0.5]
      },
      pagebreak: { mode: 'avoid-all', avoid: ['.pagebreak'] }
    };

    html2pdf().set(opt).from(element).save();
  }

  generateQrLink(): string {
    return `${environment.domainName}/vehicle-management/vehicle-details/${this.vehicle?.id || 'default-id'}/pinkCard`;
  }

  hasProjectEndDate(): boolean {
    return this.vehicle?.projectEndDate != null;
  }

  getProjectEndDate(): string | null {
    return this.hasProjectEndDate() ? this.vehicle.projectEndDate : null;
  }
}
