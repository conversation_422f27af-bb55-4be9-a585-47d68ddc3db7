<!-- Language Selector -->
<div class="language-selector">
  <label for="language">Language:</label>
  <select id="language" [(ngModel)]="selectedLanguage" class="styled-select">
    <option value="rw">Kinyarwanda</option>
    <option value="en">English</option>
  </select>
</div>

<!-- Language-based rendering -->
<div [ngSwitch]="selectedLanguage">

  <!-- Kinyarwanda Version -->
  <div *ngSwitchCase="'rw'" class="pink-card" id="pink-card-rw">
    <div class="top-half">
      <div class="logo">
        <h1>REPUBULIKA Y’U RWANDA</h1>
        <img class="center-image" src="../../../assets/img/logo.png" alt="Logo">
        <p class="he">Icyemezo cy'iyandikisha ry'ikinyabiziga</p>
        <p class="pmid">N<span class="no-letter">o</span> {{vehicle.pickCardNumber}}</p>
      </div>
      <div class="qr-code-container">
        <qrcode class="qrcode" [qrdata]="generateQrLink()" [width]="180" [errorCorrectionLevel]="'M'"></qrcode>
      </div>
    </div>

    <h1 class="header-center fw-bold">IBIRANGA IKINYABIZIGA</h1>
    <table class="vehicle-table">
      <tr>
        <td> NIMERO Y'IYANDIKISHA <span class="no-container">N<span class="no-letter"> o</span></span></td>
        <td>UBWOKO BW'IKINYABIZIGA</td>
        <td>IKIRANGO CY'IKINYABIZIGA</td>
        <td></td>
        <td>NIMERO YA MOTERI <span class="no-container">N<span class="no-letter"> o</span></span></td>
        <td>NIMERO YA SHASI<span class="no-container">N<span class="no-letter"> o</span></span></td>
        <td>UMWAKA CYAKOZWEMO</td>
        <td>ITARIKI</td>
      </tr>
      <tr>
        <td>{{ vehicle.plateNumber }}</td>
        <td>{{ vehicle.vehicleType?.name }}</td>
        <td>{{ vehicle.vehicleManufacture?.name }}</td>
        <td></td>
        <td>{{ vehicle.engineNumber }}</td>
        <td>{{ vehicle.chassisNumber }}</td>
        <td>{{ vehicle.manufacturingYear | date:'yyyy' }}</td>
        <td>{{ vehicle.acquisitionDate | date:'dd/MM/yyyy' }}</td>
      </tr>
    </table>

    <h1 class="header-center fw-bold"> IBIRANGA NYIR'IKINYABIZIGA</h1>
    <table class="owner-table">
      <tr>
        <td style="width: 10%; text-align: center; font-weight: bold;">IZINA RYA NYIR'IKINYABIZIGA</td>
        <td style="width: 30%; font-weight: bold;">
          {{ vehicle.projectName ? (vehicle.beneficiaryAgency + ' / ' + vehicle.projectName) : vehicle.beneficiaryAgency }}
        </td>
        <td style="width: 15%; text-align: center; font-weight: bold;">AHO ABARIZWA</td>
        <td style="width: 15%; text-align: center; font-weight: bold;"></td>
        <td *ngIf="hasProjectEndDate()" style="width: 30%; text-align: center; font-style: italic; font-weight: bold;">
          IGIHE ICYI CYEMEZO KIZARANGIRIRA: {{ getProjectEndDate() }}
        </td>
      </tr>
      <tr>
        <td colspan="8" class="merged-cell">
          <h2 class="sub-header-center">ICYITONDERWA</h2>
          <p style="text-align: center;">
            Mu gihe ikinyabiziga kiguzwe, uwakiguze agomba kugarura bidatinze pulaki n’iki cyemezo kuri Minisiteri y’Ibikorwa Remezo ikamuha dosiye y'icyo kinyabiziga. Iyo dosiye ijyanwa mu Kigo
            cy'Igihugu gishinzwe Imisoro n'Amahoro mu biro bishinzwe ibinyabiziga, bakamuha izindi pulaki zitari iza Leta.
          </p>
        </td>
      </tr>
    </table>
  </div>

  <!-- English Version -->
  <div *ngSwitchCase="'en'" class="pink-card" id="pink-card-en">
    <div class="top-half">
      <div class="logo">
        <h1>REPUBLIC OF RWANDA</h1>
        <img class="center-image" src="../../../assets/img/logo.png" alt="Logo">
        <p class="he">Vehicle Registration Certificate</p>
        <p class="pmid">N<span class="no-letter">o</span> {{vehicle.pickCardNumber}}</p>
      </div>
      <div class="qr-code-container">
        <qrcode class="qrcode" [qrdata]="generateQrLink()" [width]="180" [errorCorrectionLevel]="'M'"></qrcode>
      </div>
    </div>

    <h1 class="header-center fw-bold">VEHICLE IDENTIFICATION</h1>
    <table class="vehicle-table">
      <tr>
        <td> REGISTRATION <span class="no-container">N<span class="no-letter"> o</span></span></td>
        <td>MODEL</td>
        <td>BRAND</td>
        <td></td>
        <td>ENGINE <span class="no-container">N<span class="no-letter"> o</span></span></td>
        <td>CHASSIS <span class="no-container">N<span class="no-letter"> o</span></span></td>
        <td>YEAR</td>
        <td>DATE</td>
      </tr>
      <tr>
        <td>{{ vehicle.plateNumber }}</td>
        <td>{{ vehicle.vehicleType?.name }}</td>
        <td>{{ vehicle.vehicleManufacture?.name }}</td>
        <td></td>
        <td>{{ vehicle.engineNumber }}</td>
        <td>{{ vehicle.chassisNumber }}</td>
        <td>{{ vehicle.manufacturingYear | date:'yyyy' }}</td>
        <td>{{ vehicle.acquisitionDate | date:'dd/MM/yyyy' }}</td>
      </tr>
    </table>

    <h1 class="header-center fw-bold">OWNER IDENTIFICATION</h1>
    <table class="owner-table">
      <tr>
        <td style="width: 10%; text-align: center; font-weight: bold;">NAME</td>
        <td style="width: 30%; font-weight: bold;">
          {{ vehicle.projectName ? (vehicle.beneficiaryAgency + ' / ' + vehicle.projectName) : vehicle.beneficiaryAgency }}
        </td>
        <td style="width: 15%; text-align: center; font-weight: bold;">ADDRESS</td>
        <td style="width: 15%; text-align: center; font-weight: bold;"></td>
        <td *ngIf="hasProjectEndDate()" style="width: 30%; text-align: center; font-style: italic; font-weight: bold;">
          CARD VALID TILL: {{ getProjectEndDate() }}
        </td>
      </tr>
      <tr>
        <td colspan="8" class="merged-cell">
          <h2 class="sub-header-center">RECOMMENDATION</h2>
          <p style="text-align: center;">
            In case of sale of the vehicle, the buyer must immediately return the plates and this certificate to the Ministry of Infrastructure, which will provide the file for the subject vehicle.
            This file must be presented to the department responsible for vehicle registration in Rwanda Revenue Authority to obtain private plates.
          </p>
        </td>
      </tr>
    </table>
  </div>

  <!-- Optional fallback -->
  <div *ngSwitchDefault>
    <p>Please select a language to view the Pink Card.</p>
  </div>
</div>

<!-- Download Button -->
<div class="button-container">
  <button (click)="downloadCard()" class="download-button">Download Pink Card</button>
</div>
