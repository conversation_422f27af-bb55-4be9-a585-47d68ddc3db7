<div class="container">
    <app-side-bar></app-side-bar>
    <app-top-nav></app-top-nav>
    <div class="page">
        <div class="header d-flex flex-row justify-content-between w-50">
            <button class="btn go-back-btn" (click)="goBack()">
                <fa-icon [icon]="backwardIcon" class="pe-1"></fa-icon> Go Back
              </button>
            <h1>Vehicle Disposal</h1>
        </div>

        <div class="card">
            <p>Fill in vehicle disposal details</p>
            <div class="row form-container">
                <form id="disposalForm" [formGroup]="disposalForm" class="text-center" (ngSubmit)="onSubmit()">
                    <div class="row mt-4">
                        <div class="form-group col-md-4">
                            <label for="disposalTypeId" [ngClass]="{'required-label': isFieldRequired('disposalTypeId')}">Disposal Type</label>
                            <select class="form-control" formControlName="disposalTypeId" id="disposalTypeId">
                                <option *ngFor="let disposalType of disposalTypes" [value]="disposalType.id">{{disposalType.name}}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="disposalReasonId" [ngClass]="{'required-label': isFieldRequired('disposalReasonId')}">Disposal Reason</label>
                            <select class="form-control" formControlName="disposalReasonId" id="disposalReasonId">
                                <option *ngFor="let disposalReason of disposalReasons" [value]="disposalReason.id">{{disposalReason.name}}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="description">Description</label>
                            <textarea class="form-control" formControlName="description" id="description" rows="4"></textarea>
                        </div>
                    </div>

                    <div class="row mt-4" *ngIf="disposalForm.controls['disposalTypeId'].value === 'b6f69d06-c9b9-4f35-ba96-267a86c2a7de'">
                        <h2>File Upload Section</h2>
                        <p>Please upload the required documents to proceed with your request</p>
                        <div class="form-group col-md-6">
                            <label for="evaluationReportFromCompetitiveGarage" [ngClass]="{'required-label': isFieldRequired('evaluationReportFromCompetitiveGarage')}">Evaluation Report From Competitive Garage <span class="text-danger">*</span>:</label>
                            <input type="file" (change)="onFileChange($event, 'evaluationReportFromCompetitiveGarage')" class="form-control" id="evaluationReportFromCompetitiveGarage">
                            <div *ngIf="disposalForm.get('evaluationReportFromCompetitiveGarage')?.errors && disposalForm.get('evaluationReportFromCompetitiveGarage')?.touched" class="text-danger">
                                <span *ngIf="disposalForm.get('evaluationReportFromCompetitiveGarage')?.errors?.['required']">Evaluation Report From Competitive Garage is required.</span>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12 text-center">
                            <button type="button" class="btn show-details-btn" (click)="toggleVehicleDetails()">
                                {{ isVehicleDetailsVisible ? 'Hide Vehicle Details' : 'Show Vehicle Details' }}
                            </button>

                            <div
                                *ngIf="isVehicleDetailsVisible"
                                class="vehicle-details"
                                style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin-top: 20px; margin-bottom: 10px;">
                                <h4>Vehicle Information</h4>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label>Chassis Number</label>
                                        <p>{{ vehicle?.chassisNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Engine Number</label>
                                        <p>{{ vehicle?.engineNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Transmission Type</label>
                                        <p>{{ vehicle?.transmissionType || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Manufacturing Year</label>
                                        <p>{{ vehicle?.manufacturingYear || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Vehicle Type</label>
                                        <p>{{ vehicle?.vehicleType?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Ownership Type</label>
                                        <p>{{ vehicle?.ownershipType?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Vehicle Manufacture</label>
                                        <p>{{ vehicle?.vehicleManufacture?.name || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Beneficiary Agency</label>
                                        <p>{{ vehicle?.beneficiaryAgency || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Plate Number</label>
                                        <p>{{ vehicle?.plateNumber || 'N/A' }}</p>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label>Pink Card Number</label>
                                        <p>{{ vehicle?.pickCardNumber|| 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-9"></div> 
                            <div class="col-md-3 buttons">
                                <button type="submit" class="btn submit-btn">Dispose Vehicle</button>
                              </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
