import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { Location } from '@angular/common';


@Component({
  selector: 'app-vehicle-disposal',
  templateUrl: './vehicle-disposal.component.html',
  styleUrls: ['./vehicle-disposal.component.scss'],
})
export class VehicleDisposalComponent implements OnInit {
  disposalForm!: FormGroup;
  vehicle: any = null; 
  isVehicleDetailsVisible = false; 
  vehicleId: any = '';
  userId: any = '';
  disposalTypes: any[] = [];
  disposalReasons: any[] = [];
  isGP: boolean = false;
  isGR: boolean = false;
  backwardIcon = faArrowLeft;


  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {
    this.disposalForm = this.formBuilder.group({
      vehicleId: [''],
      UserId: [''],
      disposalTypeId: ['', Validators.required],
      disposalReasonId: ['', Validators.required],
      description: [''],
      evaluationReportFromCompetitiveGarage: [null],
      file: ['']
    });
  }

  goBack(){
    this.location.back();
  }
  
  isFieldRequired(fieldName: string): boolean {
    const control = this.disposalForm.get(fieldName);
    if (control) {
      const validator = control.validator ? control.validator({} as AbstractControl) : null;
      return validator && validator['required'];
    }
    return false;
  }

  ngOnInit(): void {
    this.getUserDetails();
    this.fetchVehicleId();
    this.fetchDisposalReasons();
    this.fetchDisposalTypes();
  }

  getUserDetails() {
    const localUserData = localStorage.getItem('localUserData');
    if (localUserData) {
      const parseObj = JSON.parse(localUserData);
      this.userId = parseObj.data.user.id;
    }
  }

  fetchVehicleId(): void {
    this.route.params.subscribe((params) => {
      if (params['vehicleId']) {
        this.vehicleId = params['vehicleId'];
        this.fetchVehicleDetails(this.vehicleId);
      }
    });
  }

  fetchVehicleDetails(vehicleId: string) {
    const url = `${environment.otherUrl}/vehicle/${vehicleId}`;
    this.http.get<any>(url).subscribe(
      (response) => {
        if(response.ownershipType.name === 'Government Vehicles'){
          this.isGR = true; 
          console.log("Vehicle is GR", this.isGR);
        } else if(response.ownershipType.name === 'Project Vehicles'){
          this.isGP = true;
          console.log("Vehicle is GP", this.isGP);
        }
        this.vehicle = response;
      },
      (error) => {
        console.error('Error fetching vehicle details:', error);
      }
    );
  }

  fetchDisposalTypes(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/disposalType`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        if(this.isGP){
          this.disposalTypes = response.filter(type => type.name === 'Handover')
          .map((type) => ({ id: type.id, name: type.name }));
        }else{
          this.disposalTypes = response.map(type => ({ id: type.id, name: type.name }));
        }
      },
      (error) => {
        console.error('Error fetching disposal types:', error);
      }
    );
  }

  fetchDisposalReasons(): void {
    const apiUrl = `${environment.otherUrl}/vehicle-management/disposalReason`;
    this.http.get<any[]>(apiUrl).subscribe(
      (response) => {
        if(this.isGR === true){
          this.disposalReasons = response.filter(reason => reason.name !== 'Project ended')
          .map((reason) => ({ id: reason.id, name: reason.name }));
        }else{
          this.disposalReasons = response.map((reason) => ({ id: reason.id, name: reason.name }));
        }
      },
      (error) => {
        console.error('Error fetching disposal reasons:', error);
      }
    );
  }

  onFileChange(event: any, fileType: string): void {
    const file = event.target.files[0];
    if (file) {
      this.disposalForm.patchValue({ [fileType]: file });
    }
  }
  
  validateFile(file: File): boolean {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB
  
    return allowedMimeTypes.includes(file.type) && file.size <= maxSize;
  }

  submitFile(file: File, fileType: string, vehicleId: string): void {
    if (file) {
      const fileFormData = new FormData();

      if (this.validateFile(file)) {
        fileFormData.append('file', file);
        fileFormData.append('documentDescription', fileType);
        fileFormData.append('applicationId', vehicleId);
        this.http.post(`${environment.fileUrl}/upload`, fileFormData).subscribe(
          (response) => {
            console.log(`${fileType} submitted successfully`, response);
          },
          (error) => {
            console.error(`Error submitting ${fileType}`, error);
            this.toastr.error(`Error submitting ${fileType}`, 'Error');
          }
        );
      } else {
        console.warn(`${fileType} size or type is invalid.`);
      }
    }
  }

  onSubmit() {
    const evaluationReport = this.disposalForm.get('evaluationReportFromCompetitiveGarage')?.value;
    const disposalType = this.disposalForm.controls['disposalTypeId'].value;
  
    // Log the values for debugging
    console.log('Disposal Type:', disposalType);
    console.log('Evaluation Report File:', evaluationReport);
  
    // Ensure that disposalType is 'Public Auction' and evaluationReport is not uploaded
    if (disposalType === 'b6f69d06-c9b9-4f35-ba96-267a86c2a7de' && (!evaluationReport || evaluationReport.length === 0)) {
      console.error('File is missing! Evaluation report must be uploaded.');
      this.toastr.error('Please upload the evaluation report from a competitive garage before submitting.');
      return; // Stop submission
    }
  
    // Ensure form is valid
    if (this.disposalForm.invalid) {
      console.error('Form is invalid! Please complete all required fields.');
      this.toastr.error('Please fill in all required fields before submitting.', 'Error');
      return;
    }
  
    // Attach necessary data before submission
    this.disposalForm.patchValue({
      vehicleId: this.vehicleId,
      UserId: this.userId,
    });
  
    const requestData = { ...this.disposalForm.value };
    console.log('Request Data:', JSON.stringify(requestData));
  
    // Submit the form data
    this.http.post(`${environment.baseUrl}/disposal/vehicle-disposal`, requestData).subscribe(
      (response: any) => {
        const disposalId = response.id;
  
        // Ensure that evaluationReport is only uploaded when it exists
        if (evaluationReport && evaluationReport instanceof File) {
          console.log('Uploading evaluation report:', evaluationReport);
          this.submitFile(evaluationReport, 'evaluationReportFromCompetitiveGarage', disposalId);
        } else {
          console.warn('No valid file detected for upload.');
        }
  
        this.toastr.success('Vehicle Disposal Successful');
        this.disposalForm.reset();
        this.router.navigate(['/vehicle-management/all-disposals']);
      },
      (error) => {
        console.error('Error submitting vehicle disposal:', error);
        this.toastr.error('Error submitting vehicle disposal', 'Error');
      }
    );
  }
  
  

  toggleVehicleDetails() {
    this.isVehicleDetailsVisible = !this.isVehicleDetailsVisible;
  }

  // updateVehicleDisposalStatus(vehicleId: string) {
  //   const url = `${environment.otherUrl}/updateRegistredVehicle/${vehicleId}`;
  
  //   const updatedVehicleData = {
  //     ...this.vehicle, 
  //     isDisposalRequestSubmitted: true, 
  //   };

  //   console.log(JSON.stringify(updatedVehicleData));
  //   this.http.patch(url, updatedVehicleData).subscribe(
  //     (updatedVehicle) => {
  //       console.log('Vehicle disposal status updated successfully:', updatedVehicle);
  //     },
  //     (error) => {
  //       console.error('Error updating vehicle disposal status:', error);
  //     }
  //   );
  // }
}
