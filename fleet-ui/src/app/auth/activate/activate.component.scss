.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: auto;
  margin: 2% 3%; /* Make the container take up the full height of the viewport */
}

.square-input {
  width: 70px; /* Adjust width as needed */
  height: 70px; /* Adjust height as needed */
  text-align: center;
  font-size: 30px;
  background-color: #aaa6a64d;
  border: 2px solid rgb(174, 174, 174);
  margin: 10px 5px; /* Shortened margin shorthand */
}

img {
  margin-top: 20px;
  max-width: 100%; /* Ensure image stays within container */
}

h1 {
  font-size: 20px;
  font-weight: bold;
  margin-top: 20px;
}

h2 {
  color: #28A4E2;
  font-size: 20px;
  margin-top: 20px;
  font-weight: bold;
}

.p1 {
  margin-top: 0;
  font-size: 14px;
}

label {
  font-size: 12px;
  margin-top: 20px;
}

.activate {
  margin: 10px;
  width: 60%;
}
.under {
  display: flex;
  flex-direction: column;
  margin: 20px;
  justify-content: center;
  align-items: center;
}
.under-input {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.under-input .global-input {
  width: 60%;
}
.back {
  margin-top: 15px;
  font-size: 14px;
  cursor: pointer;
} 
.arrow-icon {
  margin-right: 3px;
}
.back:hover {
  opacity: 0.5;
}
.form-group {
  width: 30%;
}
/* Responsive adjustments */
@media screen and (max-width: 767px) {
  .form-group {
      width: 60%; /* Adjust width for smaller screens */
  }
}

@media screen and (max-width: 450px) {
  .form-group {
      width: 100%; /* Adjust width for even smaller screens */
  }
}

