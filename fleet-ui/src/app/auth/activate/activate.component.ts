import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-activate',
  templateUrl: './activate.component.html',
  styleUrl: './activate.component.scss'
})
export class ActivateAccountComponent implements OnInit{
  activationCode1: string = '';
  activationCode2: string = '';
  activationCode3: string = '';
  activationCode4: string = '';
  activationCode5: string = '';
  activationCode6: string = '';
  activationCode7: string = '';
  emailForResend: string = '';
  showEmailInput: boolean = false;
  emailFromUrl: string = '';
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;



  constructor(private route: ActivatedRoute, private router: Router, private http: HttpClient) {
    console.log('ActivateAccountComponent initialized');
    console.log('Email:', this.route.snapshot.paramMap.get('email'));
    console.log('Code:', this.route.snapshot.paramMap.get('code'));

    
  }
  ngOnInit() {
    this.route.params.subscribe(params => {
      this.emailFromUrl = params['email'];
      // console.log('ActivateAccountComponent initialized');
      // console.log('Email:', this.route.snapshot.paramMap.get('email'));
      // console.log('Code:', this.route.snapshot.paramMap.get('code'));
     
    });
  }

onSubmit(): void {
  const activationCode = `${this.activationCode1}${this.activationCode2}${this.activationCode3}${this.activationCode4}${this.activationCode5}${this.activationCode6}${this.activationCode7}`;
  const token = activationCode;
  const apiUrl = `${environment.baseUrl}/auth/verify/${this.emailFromUrl}/${token}`

  this.http.get(apiUrl).subscribe(
    (response) => {
      console.log(response);
      this.toastrComponent.showToastrMessage('Account activated successfully!', false);
     
      this.clearActivationCodeFields();
      
     
      setTimeout(() => {
        // this.router.navigate(['/auth/login']);
        this.navigateToReset();
      }, 3000); 
    },
    (error) => {
      console.error(error);
      this.toastrComponent.showToastrMessage('Error activating account. Please try again.', true);
    }
  );
}
toggleEmailInput(): void {
  this.showEmailInput = !this.showEmailInput;
}

resendVerification(): void {
  

  const apiUrl = `${environment.baseUrl}/auth/resend-verification/${this.emailFromUrl}`
  

  this.http.get(apiUrl).subscribe(
    (response) => {
      console.log(response);
      this.toastrComponent.showToastrMessage('Verification email has been resent successfully!', false);
      this.showEmailInput = false;
    },
    (error) => {
      console.error(error);
      this.toastrComponent.showToastrMessage('Error resending verification email. Please try again.', true);
    }
  );
}
navigateToReset = () => {
  this.router.navigate([`/auth/change-password/${this.emailFromUrl}`]);
}

clearActivationCodeFields(): void {
  this.activationCode1 = '';
  this.activationCode2 = '';
  this.activationCode3 = '';
  this.activationCode4 = '';
  this.activationCode5 = '';
  this.activationCode6 = '';
  this.activationCode7 = '';
}
}