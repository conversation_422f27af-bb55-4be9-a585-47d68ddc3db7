.container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: 0 auto; /* Center the container horizontally */
    padding: 0 20px; /* Add padding to the sides */
}

img {
    margin-top: 20px;
    max-width: 100%; /* Ensure image stays within container */
}

.container h1 {
    font-size: 15px;
}

.container h2 {
    color: #28A4E2;
    font-size: 20px;
    margin-top: 20px;
}

.p1 {
    margin-top: 0;
    font-size: 14px;
}

label {
    font-size: 12px;
    margin-top: 20px;
}

#submit {
    margin-top: 20px;
    background-color: #28A4E2;
    color: #fff;
}

.back {
    margin-top: 15px;
    font-size: 14px;
    cursor: pointer;
}

.arrow-icon {
    margin-right: 3px;
}

.back:hover {
    opacity: 0.5;
}

.form-group {
    width: 30%;
}

/* Responsive adjustments */
@media screen and (min-width: 767px) {
    .form-group {
        width: 50%; /* Adjust width for smaller screens */
    }
}

@media screen and (max-width: 780px) {
    .form-group {
        width: 100%; /* Adjust width for even smaller screens */
    }
}
