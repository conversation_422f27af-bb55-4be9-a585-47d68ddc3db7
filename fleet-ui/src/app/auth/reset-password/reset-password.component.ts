import { Component, OnInit, ViewChild } from '@angular/core';
import { faLock, faArrowLeft,faKey } from '@fortawesome/free-solid-svg-icons';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit{
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  lockIcon = faLock;
  arrowIcon = faArrowLeft;
  keyIcon = faKey;
  passwordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;
  newPassword: string='';
  token:string='';
  currentPassword:string='';
  emailFromUrl:string=''

  constructor(private route: ActivatedRoute,private router: Router,private http: HttpClient,) {}
  ngOnInit() {
    this.route.params.subscribe(params => {
      this.token = params['token'];
      this.emailFromUrl= params['email'] // or whatever parameter is passed
    });
  }
  navigateToLogin = () => {
    this.router.navigate(['/auth/login']);
  }

  togglePasswordVisibility(field: string) {
    if (field === 'password') {
      this.passwordVisible = !this.passwordVisible;
    } else if (field === 'confirmPassword') {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    }
  }

  onSubmit() {
    console.log('New Password:', this.newPassword);
    console.log('Token:', this.token);
    // You can implement your password reset logic here
    const requestBody = {
      email: this.emailFromUrl,
      newPassword: this.newPassword,
      currentPassword: this.currentPassword
      // newToken: this.token
    };
     const apiUrl = `${environment.baseUrl}/auth/change-password`;
    
    //Check if newPassword and token are provided
    if (!this.newPassword || !this.currentPassword) {
      this.toastrComponent.showToastrMessage('Please provide both current and new passwords', true);
      return;
    }

    

    this.http.post(apiUrl, requestBody).subscribe(
      () => {
        this.toastrComponent.showToastrMessage('Password reset successful', false);
        // Optionally, navigate the user to the login page
         this.navigateToLogin();
      },
      (error) => {
        this.toastrComponent.showToastrMessage('Failed to Change password', true);
      }
    );
}
}
