<div class="login">
    <div class="form-container">
        <div class="image-holder">
            <img src="../../../assets/img/illustration.png">
        </div>
        
        <form (ngSubmit)="onSubmit()" [formGroup]="loginForm" method="post">
            <div class="form-logo">
                <img src="../../../assets/img/logo.png">
                <h1>Ministry of Infrastructure | Fleet Management System</h1>
            </div>
            <h2 class="text-center"><strong>Login To Your Account</strong></h2>
            
            <div class="form-group">
                <label for="email">Email Address</label>
                <div class="input-group"> 
                    
                    <input type="email" formControlName="email" id="email" placeholder="<EMAIL>" class="global-input" type='email' placeholder="<EMAIL>">
                    <fa-icon [icon]="envelopeIcon" class="lock-icon icon"></fa-icon>
                </div>
                <div *ngIf="loginForm.get('email')?.errors && loginForm.get('email')?.touched" class="text-danger">Invalid email</div>
            
            </div>

            <div class="form-group">
                <label for="password"> Password </label>
                <div class="input-group">
                    <input type="password" id="password" formControlName="password" class="global-input" [type]="passwordVisible ? 'text': 'password'"
                        (input)="checkPasswordRequirements()">
                        <fa-icon [icon]="lockIcon" class="lock-icon icon" (click)="togglePasswordVisibility()"></fa-icon>
                </div>
                <div class="password-requirements">
                    <div *ngIf="loginForm.get('password')?.hasError('required') && loginForm.get('password')?.touched" class="text-danger">Password is required</div>
                    <div *ngIf="loginForm.get('password')?.hasError('minlength') && loginForm.get('password')?.hasError('minlength')" class="text-warning">Password must be at least 8 characters long</div>
                    <div *ngIf="!loginForm.get('password')?.hasError('required') && !loginForm.get('password')?.hasError('uppercase') && !passwordContainsUppercase()" class="text-warning">Password should contain at least one uppercase letter</div>
                    <div *ngIf="!loginForm.get('password')?.hasError('required') && !loginForm.get('password')?.hasError('lowercase') && !passwordContainsLowercase()" class="text-warning">Password should contain at least one lowercase letter</div>
                    <div *ngIf="!loginForm.get('password')?.hasError('required') && !loginForm.get('password')?.hasError('number') && !passwordContainsNumber()" class="text-warning">Password should contain at least one number</div>
                    <div *ngIf="!loginForm.get('password')?.hasError('required') && !loginForm.get('password')?.hasError('special') && !passwordContainsSpecialCharacter()" class="text-warning">Password should contain at least one special character</div>
                </div>
            </div>
            
           <a class="forgot-password" (click)="navigateToForgot()">Forgot Password?</a> 
                
                <button class="btn btn-primary btn-block" type="submit">Login</button>
                <app-toastr></app-toastr>  
                
                
        </form>
    </div>
</div>
