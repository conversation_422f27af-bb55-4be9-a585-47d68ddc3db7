import { Component, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import {faLock, faArrowLeft, faEnvelope} from '@fortawesome/free-solid-svg-icons'
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { environment } from '../../../environments/environment';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  lockIcon = faLock;
  envelopeIcon = faEnvelope;
  arrowIcon = faArrowLeft;
  passwordVisible: boolean = false;
  loginForm: FormGroup;
  showToastr: boolean = false;
  toastrMessage: string = '';
  toastrSuccess: boolean = false;

  constructor(private formBuilder: FormBuilder, private toastr: ToastrService, private http: HttpClient, private router:Router) {
    this.loginForm = this.formBuilder.group({
      
      email: ['', [Validators.required, this.emailValidator]],
      password: ['', [
        Validators.required,
        Validators.minLength(8), 
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/)
    ]]
    
    });
   
  }
  
 onSubmit(): void {
  const apiUrl = `${environment.baseUrl}/auth/login`;
  if (this.loginForm.valid) {
    const formData = this.loginForm.value;
    
    this.http.post(apiUrl, formData).subscribe(
      (response: any) => {
        console.log(response); 
        if (response.success && response.data.token) {
          // Successful authentication
          localStorage.setItem('localUserData', JSON.stringify(response));
          localStorage.setItem('accessToken', response.data.token.access_token);
          this.toastrComponent.showToastrMessage('Login successful!', false);
          this.loginForm.reset();
          this.router.navigate(['/user-management/dashboard']);
        } else {
          // Authentication failed
          this.toastrComponent.showToastrMessage('Invalid email or password', true);
        }
      },
      (error) => {
        console.error(error); 
        this.toastrComponent.showToastrMessage('Invalid email or password', true);
      }
    );
  } else {
    this.toastrComponent.showToastrMessage('Invalid email or password', true);
  }
}


checkPasswordRequirements() {
  const passwordControl = this.loginForm.get('password');
  if (passwordControl) {
      if (!passwordControl.hasError('required') && !passwordControl.hasError('minlength')) {
          this.updatePasswordValidity('uppercase', /[A-Z]/.test(passwordControl.value));
          this.updatePasswordValidity('lowercase', /[a-z]/.test(passwordControl.value));
          this.updatePasswordValidity('number', /\d/.test(passwordControl.value));
          this.updatePasswordValidity('specialCharacter', /[\W_]/.test(passwordControl.value));
      }
  }
}

  
  updatePasswordValidity(controlName: string, isValid: boolean) {
    const control = document.querySelector(`.text-warning-${controlName}`);
    if (control) {
      if (isValid) {
        control.classList.remove('text-warning');
        control.classList.add('text-success');
      } else {
        control.classList.remove('text-success');
        control.classList.add('text-warning');
      }
    }
  }
 

  passwordContainsUppercase() {
    const password = this.loginForm.get('password')?.value;
    return /[A-Z]/.test(password);
}

passwordContainsLowercase() {
    const password = this.loginForm.get('password')?.value;
    return /[a-z]/.test(password);
}

passwordContainsNumber() {
    const password = this.loginForm.get('password')?.value;
    return /\d/.test(password);
}

passwordContainsSpecialCharacter() {
    const password = this.loginForm.get('password')?.value;
    // Update to allow any special character
    return /[\W_]/.test(password);  // Matches any non-word character or underscore
}

emailValidator(control: { value: string; }) {
    const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!EMAIL_REGEX.test(control.value)) {
        return { 'invalidEmail': true };
    }
    return null;
}

  
  togglePasswordVisibility(){
    this.passwordVisible= !this.passwordVisible;
  }
  navigateToForgot = () => {
    this.router.navigate(['/auth/forgot-password'])
  }
}