.login {
  width: 100%;
  height: 100vh; 
  overflow: hidden; 
  display: flex;  
  
}

.form-container {
  display: flex;
  height: 100%;
  width: 100%; 
}



.form-group{
  width:50%;
}

.image-holder {
  width: 50%;
  background-color: #D9D9D9;
  
}

.image-holder img {
  width: 80%;
  height: auto; 
  margin: 20% 10%; 
}

.form-logo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 10% ;
  margin-bottom: 5%;
}

.form-logo h1 {
  margin-top: 20px; 
  font-size: 15px;
  color: black;
  text-align: center;
  
}

.form-container form {
  flex: 1; 
  background-color: #ffffff;
  align-items: center;
  display: flex;
  flex-direction: column;
  text-align: center;
  color: #D9D9D9;
 
  
}

.login form h2 {
  font-size: 20px;
  color: #28A4E2;
  margin-bottom: 30px;
  text-align: center; 
  
}

.login form label {
  font-size: 16px;
  color: #6f7a85;
  margin-top: 20px;
  
}


.forgot-password {
  text-align: right; 
  color: #007bff;
  text-decoration: none;
  cursor: pointer;
  
}

.forgot-password:hover {
  text-decoration: underline;
}
  
.login form .btn-primary{
  width: 50%; 
  margin-top: 30px;  
}

.login form .btn-primary:active {
  transform: translateY(1px);
}

.login form .already {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #6f7a85;
  opacity: 0.9;
  text-decoration: none;
  
}

@media screen and (max-width: 767px) {
  .form-group {
    width: 100%; 
  }
h1{
  width: 55%;
}

.global-input{
  width:85%;
}
 
.image-holder {
    display: none;
  }
}

@media screen and (max-width: 1024px) {
  
  .login .form-container {
    width: 100%;
  }

   .form-group {
    width: 100%; 
  }
h1{
  width: 50%;
}
.btn{
  margin-bottom: 100px;
}
.image-holder {
    display: none; 
  }
} 
@media screen and (max-width: 1279px) {
  .login {
    margin: 0;
    padding: 0;
  }

  .login .form-container {
    width: 100%;
    justify-content: center;
    flex-direction: row; 
    align-items: center; 
  }

  .form-group {
    width: 70%; 
  }

  .image-holder {
    height: 100vh;
  }
  .image-holder img{
    margin: 50% 10%;
  }

  .form-group h1 {
    width: 100%;
    text-align: center; 
  }
}



