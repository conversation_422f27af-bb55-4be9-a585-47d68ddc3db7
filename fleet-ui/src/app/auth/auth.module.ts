import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../shared/shared.module';
import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { AuthRoutingModule } from './auth-routing.module';
import { ActivateAccountComponent } from './activate/activate.component';
import { ChangePasswordComponent } from './change-password/change-password.component';

@NgModule({
  declarations: [LoginComponent,RegisterComponent,ForgotPasswordComponent,ResetPasswordComponent, ActivateAccountComponent,ChangePasswordComponent,],
  imports: [
    CommonModule,SharedModule, AuthRoutingModule
  ]
})
export class AuthModule { }
