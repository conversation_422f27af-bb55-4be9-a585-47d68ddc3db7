import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router, GuardResult, MaybeAsync } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})

export class AuthGuard implements CanActivate {
  
  constructor(
    private authService: AuthService, private router: Router
  ){

  }
  canActivate(
    route: ActivatedRouteSnapshot, 
    state: RouterStateSnapshot
  ): boolean {
    const expectedRole = route.data['role'];
    const user = this.authService.getUser();
    if (user && user.role === expectedRole) {
      return true;
    }
    
    this.router.navigate(['not-authorized']);
    return false;
  }
}