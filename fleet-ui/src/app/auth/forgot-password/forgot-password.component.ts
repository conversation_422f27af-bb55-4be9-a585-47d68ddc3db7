import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { faLock, faArrowLeft, faEnvelope } from '@fortawesome/free-solid-svg-icons';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent {
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent; // ViewChild reference to ToastrComponent
  email: string = ''; // Variable to store the entered email
  envelopeIcon = faEnvelope;
  arrowIcon = faArrowLeft;

  constructor(private router: Router, private http: HttpClient) {}

  navigateToLogin() {
    this.router.navigate(['/auth/login']);
  }

  submitEmail() {

    const apiUrl = `${environment.baseUrl}/auth/forgot-password/${this.email}`;
    
    this.http.get(apiUrl, {}).subscribe(
      () => {
        // Handle success, e.g., display a success message
        this.toastrComponent.showToastrMessage('Email sent successfully', false);
      },
      (error) => {
        // Handle error, e.g., display an error message
        this.toastrComponent.showToastrMessage('Invalid Email:', true);
      }
    );
  }
}
