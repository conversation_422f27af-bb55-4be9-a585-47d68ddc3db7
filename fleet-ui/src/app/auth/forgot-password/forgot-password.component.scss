.container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 0 20px; /* Add padding to the container */
}

img {
    margin-top: 20px;
    max-width: 100%; /* Ensure image doesn't exceed container width */
}

.container h1 {
    font-size: 15px;
}

.container h2 {
    color: #28A4E2;
    font-size: 20px;
}

.p1 {
    margin-top: 0;
    font-size: 14px;
}

label {
    font-size: 12px;
    margin-top: 30px;
}

#submit {
    margin-top: 20px;
    background-color: #28A4E2;
    color: #fff;
    padding: 10px 20px; /* Add padding to the button */
}

.back {
    margin-top: 15px;
    font-size: 14px;
    cursor: pointer;
}

.arrow-icon {
    margin-right: 3px;
}

.back:hover {
    opacity: 0.5;
}

.form-group {
    width: 50%;
}


/* Responsive styles */
@media screen and (max-width: 523px) {
*{
    font-size: 12px;
}
    .form-group{
        width: 100%;
        padding: 0px;
    }
    .btn{
        width: 100%;
        padding: 10px;
    }
    .lock-icon{
        display: none;
    }
}
@media screen and (max-width: 767px) {
    .form-group {
        width: 100%; /* Adjust width for smaller screens */
    }
    
}
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .container {
        padding:  50px 40px; /* Adjust padding for medium-sized screens */
    }
    .form-group{
        width: 50%;
    }
}

@media screen and (max-width: 1280px) {
    .container {
        padding: 0 60px; /* Adjust padding for large screens */
    }
    .form-group{
        width: 80%;
    }
    
}
