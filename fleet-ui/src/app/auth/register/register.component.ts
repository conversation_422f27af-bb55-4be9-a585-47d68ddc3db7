import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild  } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ToastrComponent } from '../../shared/toastr/toastr.component';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrl: './register.component.scss'
})
export class RegisterComponent implements OnInit{
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  passwordVisible: boolean = false;
  registerForm: FormGroup;
  roles: any[] = [];
  institutions: any[] = [];
  showToastr: boolean = false;
  toastrMessage: string = '';
  toastrSuccess: boolean = false;
  userRole: any;
  loggedInInstitution: any;

  constructor(private formBuilder: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService) {
      this.registerForm = this.formBuilder.group({
        identification: ['', Validators.required],
        firstName: ['', Validators.required],
        lastName: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        personalEmail: ['', [Validators.required, Validators.email]],
        phoneNumber: ['', Validators.required],
        password: ['', [
          Validators.required,
          Validators.minLength(8),
          Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/)
        ]],
        institution: ['', Validators.required],
        role: ['', Validators.required]
      }); 
  }
  ngOnInit(): void {
    this.fetchRoles(); 
    this.fetchInstitutions(); 
    this.getUserData()
  }
  getUserData(){
    const data = localStorage.getItem('localUserData');
    if(data != null){
      const parseObj = JSON.parse(data);
      this.userRole = parseObj.data.user.role.name;
      this.loggedInInstitution = parseObj.data.user.institution.name;
  
      // Conditional setup based on logged-in user's role
      // if (this.role === 'Admin') {
      //   this.setAdminRoleOptions();
      // } else if (this.role === 'Super Admin') {
      //   this.setSuperAdminOptions();
      // }
    }
  }
  fetchRoles(): void {
    this.http.get<any[]>(`${environment.baseUrl}/user-management/roles`).subscribe(
      (response) => {
        // Check if logged-in user is Admin or Super Admin
        if (this.userRole === 'ADMIN') {
          this.roles = response.filter(role => role.name === 'Institutional logistics' || role.name === 'Institutional CBM');
        } else if (this.userRole === 'SUPER-ADMIN') {
          this.roles = response.map(role => role);  // Super Admin can see all roles
        }
      },
      (error) => {
        console.error('Error fetching roles:', error);
      }
    );
  }
  
  fetchInstitutions(): void {
    this.http.get<any[]>(`${environment.baseUrl}/user-management/institutions`).subscribe(
      (response) => {
        // Admin can only see their own institution
        if (this.userRole === 'ADMIN') {
          this.institutions = response.filter(institution => institution.name === this.loggedInInstitution);
        } else if (this.userRole === 'SUPER-ADMIN') {
          this.institutions = response.map(institution => institution);  // Super Admin can see all institutions
        }
      },
      (error) => {
        console.error('Error fetching institutions:', error);
      }
    );
  }
 checkPasswordRequirements() {
  const passwordControl = this.registerForm.get('password');
  if (passwordControl) {
      if (!passwordControl.hasError('required') && !passwordControl.hasError('minlength')) {
          this.updatePasswordValidity('uppercase', /[A-Z]/.test(passwordControl.value));
          this.updatePasswordValidity('lowercase', /[a-z]/.test(passwordControl.value));
          this.updatePasswordValidity('number', /\d/.test(passwordControl.value));
          this.updatePasswordValidity('specialCharacter', /[\W_]/.test(passwordControl.value));
      }
  }
}

  
  updatePasswordValidity(controlName: string, isValid: boolean) {
    const control = document.querySelector(`.text-warning-${controlName}`);
    if (control) {
      if (isValid) {
        control.classList.remove('text-warning');
        control.classList.add('text-success');
      } else {
        control.classList.remove('text-success');
        control.classList.add('text-warning');
      }
    }
  }
 

  passwordContainsUppercase() {
    const password = this.registerForm.get('password')?.value;
    return /[A-Z]/.test(password);
}

passwordContainsLowercase() {
    const password = this.registerForm.get('password')?.value;
    return /[a-z]/.test(password);
}

passwordContainsNumber() {
    const password = this.registerForm.get('password')?.value;
    return /\d/.test(password);
}

passwordContainsSpecialCharacter() {
    const password = this.registerForm.get('password')?.value;
    // Update to allow any special character
    return /[\W_]/.test(password);  // Matches any non-word character or underscore
}


  onSubmit(): void {
    if (this.registerForm.valid) {
      console.log('sent request', this.registerForm.value)
      this.http.post(`${environment.baseUrl}/auth/register`, this.registerForm.value).subscribe(
          (response) => {
            console.log(response);
            this.toastrComponent.showToastrMessage('User Registered!', false);
            this.registerForm.reset();
          },
          (error) => {
            console.error(error);
            this.toastrComponent.showToastrMessage('Error registering user. Please try again later.', true);
          }
        );
    } else {
      this.toastrComponent.showToastrMessage('Please fill in all fields properly', true);
    }
  }

  emailValidator(control: { value: string; }, emailType: 'work' | 'personal' = 'personal'): { [key: string]: any } | null {
    let emailPattern: RegExp | undefined;
  
    if (emailType === 'work') {
      emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$\.com$/; 
    } else {
      emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$\.com$/;
    }
  
    if (emailPattern && !emailPattern.test(control.value)) {
      return { 'invalidEmail': true };
    }
    return null;
  }

  togglePasswordVisibility(){
    this.passwordVisible= !this.passwordVisible;
  }
}
