<div class="">
  <app-side-bar></app-side-bar>
  <app-top-nav></app-top-nav>
</div>
<div class="page">
  <div class="top w-100">Create New User</div>
  <div class="card w-100 custom-height-container">
    <form (ngSubmit)="onSubmit()" [formGroup]="registerForm" method="post">
      <div class="row mt-3">
        <div class="form-group col-md-6">
          <label for="identification">Identification</label>
          <input type="text" class="form-control" id="identification" placeholder="National Id or Employee Id" formControlName="identification">
        </div>
        <div class="form-group col-md-6">
          <label for="firstName">First Name</label>
          <input type="text" class="form-control" id="firstName" placeholder="First Name" formControlName="firstName">
        </div>
      </div>
  
      <div class="row mt-3">
        <div class="form-group col-md-6">
          <label for="lastName">Last Name</label>
          <input type="text" class="form-control" id="lastName" placeholder="Last Name" formControlName="lastName">
        </div>
        <div class="form-group col-md-6">
          <label for="email">Work Email</label>
          <input type="email" class="form-control" id="email" placeholder="Work email" formControlName="email">
          <div *ngIf="registerForm.get('email')?.errors && (registerForm.get('email')?.dirty || registerForm.get('email')?.touched)">
            <div *ngIf="registerForm.get('email')?.hasError('required')" class="text-danger">work Email is required</div>
            <div *ngIf="registerForm.get('email')?.hasError('invalidEmail')" class="text-danger">Invalid work Email format</div>
          </div>
        </div>
      </div>
  
      <div class="row mt-3">
        <div class="form-group col-md-6">
          <label for="personalEmail">Personal Email</label>
          <input type="email" class="form-control" id="personalEmail" placeholder="Personal email" formControlName="personalEmail">
          <div *ngIf="registerForm.get('personalEmail')?.errors && (registerForm.get('personalEmail')?.dirty || registerForm.get('personalEmail')?.touched)">
            <div *ngIf="registerForm.get('personalEmail')?.hasError('required')" class="text-danger">Personal Email is required</div>
            <div *ngIf="registerForm.get('personalEmail')?.hasError('invalidEmail')" class="text-danger">Invalid Personal Email format</div>
          </div>
        </div>
        <div class="form-group col-md-6">
          <label for="password">Password</label>
          <div class="input-group">
            <input type="password" id="password" formControlName="password" placeholder="Password" class="form-control" [type]="passwordVisible ? 'text': 'password'"
              (input)="checkPasswordRequirements()">
          </div>
          <div class="password-requirements">
            <div *ngIf="registerForm.get('password')?.hasError('required') && registerForm.get('password')?.touched" class="text-danger">Password is required</div>
            <div *ngIf="registerForm.get('password')?.hasError('minlength') && registerForm.get('password')?.hasError('minlength')" class="text-warning">Password must be at least 8 characters long</div>
            <div *ngIf="!registerForm.get('password')?.hasError('required') && !registerForm.get('password')?.hasError('uppercase') && !passwordContainsUppercase()" class="text-warning">Password should contain at least one uppercase letter</div>
            <div *ngIf="!registerForm.get('password')?.hasError('required') && !registerForm.get('password')?.hasError('lowercase') && !passwordContainsLowercase()" class="text-warning">Password should contain at least one lowercase letter</div>
            <div *ngIf="!registerForm.get('password')?.hasError('required') && !registerForm.get('password')?.hasError('number') && !passwordContainsNumber()" class="text-warning">Password should contain at least one number</div>
            <div *ngIf="!registerForm.get('password')?.hasError('required') && !registerForm.get('password')?.hasError('special') && !passwordContainsSpecialCharacter()" class="text-warning">Password should contain at least one special character</div>
          </div>
        </div>
      </div>
  
      <div class="row mt-3">
        <div class="form-group col-md-6">
          <label for="institution">Institution</label>
      
          <!-- Show Admin dropdown if user role is Admin -->
          <select *ngIf="userRole === 'ADMIN'" id="institution" class="form-control" formControlName="institution" [disabled]="true">
            <option [value]="loggedInInstitution">{{ loggedInInstitution }}</option>
          </select>
      
          <!-- Show all institution options if user role is Super Admin -->
          <select *ngIf="userRole === 'SUPER-ADMIN'" id="institution" class="form-control" formControlName="institution">
            <option selected>Choose Institution</option>
            <option *ngFor="let institution of institutions" [value]="institution.name">{{ institution.name }}</option>
          </select>
        </div>
        <div class="form-group col-md-6">
          <label for="phoneNumber">Phone Number</label>
          <input type="text" class="form-control" id="phoneNumber" placeholder="Phone Number" formControlName="phoneNumber">
        </div>
      </div>
  
      <div class="row mt-3">
        <div class="form-group col-md-6">
          <label for="role">Role</label>
      
          <!-- Show Admin dropdown if user role is Admin -->
          <select *ngIf="userRole === 'ADMIN'" id="role" class="form-control" formControlName="role">
            <!-- <option selected>Choose Role</option> -->
            <option *ngFor="let role of roles" [value]="role.name">{{ role.name }}</option>
          </select>
      
          <!-- Show all role options if user role is Super Admin -->
          <select *ngIf="userRole === 'SUPER-ADMIN'" id="role" class="form-control" formControlName="role">
            <!-- <option selected>Choose Role</option> -->
            <option *ngFor="let role of roles" [value]="role.name">{{ role.name}}</option>
          </select>
        </div>
      </div>
  
      <div class="row mt-3 mb-0">
        <div class="col-md-6">
          <button type="submit" class="btn btn-primary mt-1 mb-3 w-50">Create User</button>
          <app-toastr></app-toastr>
        </div>
      </div>
    </form>
  </div>
  
</div>
