.custom-height-container {
    height: 83%;
    border-radius: 0;
  
  }
  .page{
    width: 83%;
    height: 90vh;
    top: 10%;
    right: 0;
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    position: absolute;
    background-color: #D9D9D94D;
  }
  .card{
    border: none;
    height: 95%;
  }
  label{
    color: #000000AB;
    font-size: 14px;
    padding-bottom: 8px;
}
  input{
    border: 1px solid #eaeaf5;
    font-size: 13px;
    border-radius: 8px;
  }
  h1{
    font-size: 20px;
    padding: 0 0 20px 0;
    color: #28A4E2;
    text-align: left !important;
    }
  .top{
    
    height: 50px;
    padding: 10px;
    color: #ffffff;
    font-weight: bold;
    font-size: 18px;
    background-color: #28A4E2;
    border-radius: 20px 20px 0 0 !important;
    border: none;
}
form{
 padding: 20px;
 overflow-y: auto;
 border: none;
}
form::-webkit-scrollbar{
  width: 0;
}
@media screen and (max-width: 991px) {
  .page{
    width: 90%;
    height: 93%;
    right: 0;
    top: 7%;
    z-index: -1;
  }
  label{
    display: none;
  }
}