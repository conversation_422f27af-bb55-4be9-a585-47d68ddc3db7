import { HttpClient } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { faLock, faArrowLeft, faKey } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';
import { ToastrComponent } from '../../shared/toastr/toastr.component';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.scss'
})

export class ChangePasswordComponent {
  @ViewChild(ToastrComponent) toastrComponent!: ToastrComponent;
  lockIcon = faLock;
  arrowIcon = faArrowLeft;
  keyIcon = faKey;
  passwordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;
  newPassword: string='';
  token:string='';
 
 

  constructor(private route: ActivatedRoute,private router: Router,private http: HttpClient,) {}
  ngOnInit() {
    this.route.params.subscribe(params => {
      // this.token = params['token'];
    });
  }
  navigateToLogin = () => {
    this.router.navigate(['/auth/login']);
  }

  togglePasswordVisibility(field: string) {
    if (field === 'password') {
      this.passwordVisible = !this.passwordVisible;
    } 
  }

  onSubmit() {
    console.log('New Password:', this.newPassword);
    console.log('Token:', this.token);
    // You can implement your password reset logic here
    const requestBody = {
      newPassword: this.newPassword,
      newToken: this.token
    };
     const apiUrl = `${environment.baseUrl}/auth/reset-password`;
    
    //Check if newPassword and token are provided
    if (!this.newPassword || !this.token) {
      this.toastrComponent.showToastrMessage('Please provide both token and new password', true);
      return;
    }

    

    this.http.post(apiUrl, requestBody).subscribe(
      () => {
        console.log('successfull', requestBody)
        this.toastrComponent.showToastrMessage('Password reset successful', false);
       
         this.navigateToLogin();
      },
      (error) => {
        this.toastrComponent.showToastrMessage('Failed to reset password', true);
      }
    );
}
}
