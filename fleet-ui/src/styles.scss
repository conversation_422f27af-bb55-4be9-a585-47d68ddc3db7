@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap');

*{
    font-family: 'Poppins', sans-serif;
   
  }
body {
    font-family: 'Poppins', sans-serif;
    color: #555555;
  }

.global-input{
    width: 90%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 5px 0px 0px 5px;
    box-sizing: border-box;
    background-color: #F1F3F6;
    border: none;
    font-size: 12px;
    outline: none;
}
.global-input:focus {
    border-color: #28A4E2; 
}
.form-group{
    display: flex;
    flex-direction: column;
    align-items: baseline;
}
.input-group{
    width: 100%;
}
.icon{

    background-color: #28A4E2;
    padding: 9px 10px;
    border-radius: 0px 5px 5px 0px;
    font-size: 13px;
    color: #fff;
}   
.btn-primary {
    background: #28A4E2;
    border: none;
    border-radius: 4px;
    padding: 11px;
    box-shadow: 0px 4.119999885559082px 24.233430862426758px 0px #4758ED4D;
    text-shadow: none;
    outline: none !important;
    font-size: 14px;
    color: white;
    font-weight: bold;
    width: 30%;
  }
.btn:hover{
    opacity: 0.9;
}
.toastr-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 400px;
    z-index: 1000;
  }
  
  .toastr-message {
    padding: 10px 15px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    
  }
  
  // .success {
  //   background-color: #2ecc71;
  //   color: #fff;
  // }
  
  // .error {
  //   background-color: #e74c3c;
  //   color: #fff;
  // }
  
  .password-requirements {
    margin-top: 5px;
    font-size: 0.9rem;
  }
  
  .text-warning {
    color: orange;
    font-size: 13px;
  }
  .text-danger{
    color: rgb(212, 41, 41);
    font-size: 10px;
    margin-top: 5px;
  }
  .required-label::after {
    content: ' *';
    color: rgb(224, 42, 42);
  }
html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

.remove-btn{
  border: 1px solid rgb(170, 30, 30);
  color: rgb(170, 30, 30);
  font-size: 12px;
  cursor: pointer;
  width: 150px;
  margin-left: 15px;
}
.add-btn{
  border: 1px solid rgb(57, 141, 57);
  color: rgb(57, 141, 57);
  font-size: 13px;
  cursor: pointer;
  font-weight: 500;
  width: 150px;
  margin-left: 15px;
}

.go-back-btn {
  border: 1px solid #28A4E2;
  // border: none;
  outline: none;
  color: #28A4E2;
  width: 120px;
  font-size: 14px;
  font-weight: 500;
  margin: 0px 0px 15px 0px;
}
.go-back-btn:hover{
  opacity: 0.8;
  border: 1px solid rgb(36, 35, 35);
}

.btn:hover{
  opacity: 0.8;
  border: 1px solid rgb(36, 35, 35);
}
.custom-dropdown {
  width: 410px; 
  display: block; 
  border-radius: 25px;
}

.custom-dropdown .ngx-dropdown-list {
  width: 100%; 
}

.custom-dropdown .ngx-dropdown {
  max-height: 200px; 
  overflow-y: auto; 
}

.custom-dropdown .ngx-dropdown-list li {
  text-align: left !important;
}

.custom-dropdown input {
  width: 100%; 
}
