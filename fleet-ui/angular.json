{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"fleet-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/fleet-ui", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/pink-bluegrey.css", "node_modules/ngx-toastr/toastr.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/@popperjs/core/dist/umd/popper.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/html2pdf.js/dist/html2pdf.bundle.min.js", "node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"port": 39495}, "configurations": {"production": {"buildTarget": "fleet-ui:build:production"}, "development": {"buildTarget": "fleet-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "fleet-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/pink-bluegrey.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "96dc4b9f-7d68-4b6a-ab3d-0796af649d3b"}}