// import { AbstractEntity } from '@app/common';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToOne,
  // OneToMany,
  PrimaryGeneratedColumn,
  // OneToMany,
  UpdateDateColumn,
} from 'typeorm';
import { Role } from './role.entity';
// import { CivilStatus } from './civilStatus.entity';
import { UserType } from './userType.entity';
import { Institution } from '../institution/institution.entity';
// import { Exclude } from 'class-transformer';

export enum Gender {
  M = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
}

@Entity()
export class User extends BaseEntity {
  /**
   * this decorator will help to auto generate id for the table.
   */
  @PrimaryGeneratedColumn('uuid')
  public id!: string;
  @Column({ type: 'varchar' })
  identification: string;

  @Column({ type: 'varchar' })
  firstName: string;

  @Column({ type: 'varchar' })
  lastName: string;

  @Column({ type: 'varchar' })
  email: string;
  @Column({ type: 'varchar' })
  personalEmail: string;

  @Column({ default: false })
  isEmailValid: boolean;

  // @Exclude()
  @Column({ type: 'varchar' })
  password: string;

  @Column({ type: 'int' })
  phoneNumber: number;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ default: true })
  isActive: boolean;

  // @Column({ type: 'enum', enum: ['Male', 'Female'] })
  // gender: string;

  // @ManyToOne(() => Role, { cascade: true })
  // @JoinTable()
  // role: Role;

  // @OneToMany(() => Role, (role) => role.user)
  // roles: Role;

  @ManyToOne(() => Role, (role) => role.users, { nullable: true }) // Making role optional
  role: Role;

  @ManyToOne(() => Institution, (institution) => institution.users)
  institution: Institution;

  @ManyToOne(() => UserType, { nullable: true })
  @JoinTable()
  /* The `userType` property in the `User` entity is defining a Many-to-One relationship with the
  `UserType` entity. This means that a `User` can be associated with only one `UserType`, while a
  `UserType` can be associated with multiple `User` entities. */
  userType: UserType;

  // @ManyToOne(() => CivilStatus, { cascade: true })
  // @JoinTable()
  // civilStatus: CivilStatus;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public deleted_at!: Date;
}
