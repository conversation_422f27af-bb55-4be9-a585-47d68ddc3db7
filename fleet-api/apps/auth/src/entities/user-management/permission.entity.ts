// import { Entity, JoinTable, ManyToMany } from 'typeorm';
// import { Role } from './role.entity';
// import { UserActivity } from './userActivity.entity';
// import { AbstractEntity } from '@app/common';

// @Entity()
// export class Permission extends AbstractEntity<Permission> {
//   /**
//    * this decorator will help to auto generate id for the table.
//    */

//   // @OneToMany(() => Role, (role) => role.id)
//   // @JoinColumn()
//   // role: Role;

//   @ManyToMany(() => Role)
//   @JoinTable()
//   role: Role;

//   // @OneToMany(() => UserActivity, (userActivity) => userActivity.id)
//   // @JoinColumn()
//   // userActivity: UserActivity;

//   @ManyToMany(() => UserActivity)
//   @JoinTable()
//   userActivity: UserActivity;

//   // @OneToOne(() => UserActivity)
//   // @JoinColumn()
//   // userActivity: UserActivity;
// }

import {
  Column,
  // Column,
  // DeleteDateColumn,
  Entity,
  JoinColumn,
  // JoinTable,
  // ManyToMany,
  // PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Role } from './role.entity';
// import { UserActivity } from './userActivity.entity';
// import { AbstractEntity } from '@app/common';

@Entity('userActivity_role')
export class Permission {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @PrimaryGeneratedColumn('uuid', { name: 'userActivity_id' })
  userActivityId: string;

  @PrimaryGeneratedColumn('uuid', { name: 'role_id' })
  roleId: string;

  // @DeleteDateColumn()
  // @Column({ name: 'deleted_at', nullable: true })
  // public deletedAt?: Date

  // @ManyToOne(() => UserActivity, (userActivity) => userActivity.roles, {
  //   onDelete: 'NO ACTION',
  //   onUpdate: 'NO ACTION',
  // })
  // @JoinColumn([{ name: 'userActivity_id', referencedColumnName: 'id' }])
  // userActivities: UserActivity[];

  // @ManyToOne(() => Role, (role) => role.userActivities, {
  //   onDelete: 'NO ACTION',
  //   onUpdate: 'NO ACTION',
  // })
  @JoinColumn([{ name: 'role_id', referencedColumnName: 'id' }])
  roles: Role[];

  @Column({ default: false })
  isDeleted: boolean;
}
