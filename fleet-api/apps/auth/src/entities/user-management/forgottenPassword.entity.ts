// import { AbstractEntity } from '@app/common';
import { BaseEntity, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class ForgottenPassword extends BaseEntity {
  // export class ForgottenPassword extends AbstractEntity<ForgottenPassword> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @PrimaryGeneratedColumn('uuid')
  public id!: string;

  @Column({ type: 'varchar' })
  email: string;

  @Column({ type: 'varchar' })
  newPasswordToken: string;

  @Column({ type: 'varchar' })
  timestamp: Date;
}
