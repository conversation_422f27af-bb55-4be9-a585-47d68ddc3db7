import { AbstractEntity } from '@app/common';
import {
  <PERSON>umn,
  Entity,
  // Join<PERSON><PERSON>umn,
  // ManyToMany,
  // ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
// import { UserActivity } from './userActivity.entity';
import { User } from './user.entity';

@Entity()
export class Role extends AbstractEntity<Role> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  // @ManyToMany(() => UserActivity, (userActivity) => userActivity.roles, {
  //   onDelete: 'NO ACTION',
  //   onUpdate: 'NO ACTION',
  // })
  // userActivities?: UserActivity[];

  // @ManyToOne(() => User, (event) => event.roles)
  // @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })
  // public user!: User;

  @OneToMany(() => User, (user) => user.role)
  users: User[];
}
