import { AbstractEntity } from '@app/common';
import { Column, Entity, ManyToMany, OneToMany } from 'typeorm';
import { User } from '../user-management/user.entity';
import { NonBudgetedAgencies } from '../nonBudget-agencies/nonBudget-agencies.entity';
@Entity()
export class Institution extends AbstractEntity<Institution> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;
  @OneToMany(() => User, (user) => user.role)
  users: User[];
  @ManyToMany(
    () => NonBudgetedAgencies,
    (nonBudgetedAgencies) => nonBudgetedAgencies.institution,
  )
  nonBudgetedAgencies: NonBudgetedAgencies[];
  @Column({ default: false }) // Set the default value to false
  isLocationAvailable: boolean;
  @Column({ default: true }) // Set the default value to false
  isbudgeted: boolean;
}
