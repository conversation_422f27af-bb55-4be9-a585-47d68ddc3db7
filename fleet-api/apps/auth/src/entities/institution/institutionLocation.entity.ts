import { Entity, ManyToOne, Unique } from 'typeorm';

import { AbstractEntity } from '@app/common';
import { Institution } from './institution.entity';
import { Province } from '../delimitation/province.entity';
import { District } from '../delimitation/district.entity';
import { Sector } from '../delimitation/sector.entity';
import { Cell } from '../delimitation/cell.entity';
import { Village } from '../delimitation/village.entity';

@Entity()
@Unique(['institution']) // Define the field(s) to be unique
export class InstitutionLocation extends AbstractEntity<InstitutionLocation> {
  /**
   * this decorator will help to auto generate id for the table.
   */
  @ManyToOne(() => Institution)
  institution: Institution;
  @ManyToOne(() => Province)
  province: Province;
  @ManyToOne(() => District)
  district: District;
  @ManyToOne(() => Sector)
  sector: Sector;
  @ManyToOne(() => Cell)
  cell: Cell;

  @ManyToOne(() => Village)
  village: Village;
}
