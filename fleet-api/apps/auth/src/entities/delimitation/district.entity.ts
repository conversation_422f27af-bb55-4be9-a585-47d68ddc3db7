import { Column, Entity, JoinTable, ManyToOne } from 'typeorm';
import { Province } from './province.entity';
import { AbstractEntity } from '@app/common';

@Entity()
export class District extends AbstractEntity<District> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;
  @Column()
  code: string;

  @ManyToOne(() => Province, { cascade: true })
  @JoinTable()
  province: Province;
}
