import {
  <PERSON>,
  Post,
  Body,
  HttpStatus,
  HttpCode,
  Get,
  Param,
  Inject,
  Query,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiTags } from '@nestjs/swagger';
import { IResponse } from '@app/common/interfaces/response.interface';
import { ResponseError, ResponseSuccess } from '@app/common/dto/response.dto';
import { Login } from './interfaces/login.interface';
import { CreateUserDto, UserDto } from './dto/user.dto';
import { ChangePasswordDto, ResetPasswordDto } from './dto/resetPassword.dto';
import { UserManagementService } from './user-management/user-management.service';

import { NOTIFICATIONS_SERVICE } from '@app/common/constants';
import { ClientProxy, MessagePattern } from '@nestjs/microservices';
@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userManagementService: UserManagementService,

    @Inject(NOTIFICATIONS_SERVICE)
    private readonly notificationsService: ClientProxy,
  ) {}

  @MessagePattern({ cmd: 'userExists' })
  async userExists(userId: string): Promise<boolean> {
    return this.authService.userExists(userId);
  }
  @MessagePattern({ cmd: 'userData' })
  async userData(userId: string) {
    return this.authService.userData(userId);
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  public async login(@Body() login: Login): Promise<IResponse> {
    try {
      const response = await this.authService.validateLogin(
        login.email,
        login.password,
      );
      return new ResponseSuccess('LOGIN.SUCCESS', response);
    } catch (error) {
      return new ResponseError('LOGIN.ERROR', error);
    }
  }

  @Post('register')
  @HttpCode(HttpStatus.OK)
  async register(@Body() createUserDto: CreateUserDto): Promise<IResponse> {
    try {
      const planPassword = createUserDto.password;
      const newUser = new UserDto(
        await this.userManagementService.createNewUser(createUserDto),
      );

      await this.authService.createEmailToken(newUser.email);
      console.log(newUser.email);
      const fullName = newUser.firstName + ' ' + newUser.lastName;

      const sent = await this.authService.sendEmailVerification(
        newUser.email,
        newUser.phoneNumber,
        fullName,
        planPassword,
      );

      if (sent) {
        return new ResponseSuccess('User registered successfully');
      } else {
        return new ResponseError('Email not sent ');
      }
    } catch (error) {
      return new ResponseError('Registration Error', error);
    }
  }
  // getting user by user ID

  @Get('getUserByUser')
  public async getUserById(@Query('userId') userId: string) {
    return await this.authService.userDetails(userId);
  }

  @Get('verify/:email/:token')
  public async verifyEmail(@Param() params): Promise<IResponse> {
    try {
      const isEmailVerified = await this.authService.verifyEmail(params.token);
      return new ResponseSuccess('LOGIN.EMAIL_VERIFIED', isEmailVerified);
    } catch (error) {
      return new ResponseError('LOGIN.ERROR', error);
    }
  }

  @Get('resend-verification/:email')
  public async resendEmailVerification(@Param() params): Promise<IResponse> {
    try {
      await this.authService.createEmailToken(params.email);
      const isEmailSent = await this.authService.resendEmailVerification(
        params.email,
      );
      if (isEmailSent) {
        return new ResponseSuccess('Email resent', null);
      } else {
        return new ResponseError('Email not sent');
      }
    } catch (error) {
      return new ResponseError('Error in sending email', error);
    }
  }

  @Get('forgot-password/:email')
  public async sendEmailForgotPassword(@Param() params): Promise<IResponse> {
    try {
      const isEmailSent = await this.authService.sendEmailForgotPassword(
        params.email,
      );
      if (isEmailSent) {
        return new ResponseSuccess('LOGIN.EMAIL_RESENT', null);
      } else {
        return new ResponseError('REGISTRATION.ERROR.MAIL_NOT_SENT');
      }
    } catch (error) {
      return new ResponseError('LOGIN.ERROR.SEND_EMAIL', error);
    }
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  public async setNewPassord(
    @Body() resetPassword: ResetPasswordDto,
  ): Promise<IResponse> {
    try {
      const isEmailVerified = await this.authService.resetForgotenPassowrd(
        resetPassword.newToken,
        resetPassword.newPassword,
      );
      return new ResponseSuccess(
        'REST_PASSWORD SUCCESSFULLY ',
        isEmailVerified,
      );
    } catch (error) {
      return new ResponseError('REST_PASSWORD_ERROR', error);
    }
  }

  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  public async changePassword(
    @Body() changePassword: ChangePasswordDto,
  ): Promise<IResponse> {
    try {
      const isValidPassword = await this.authService.checkPassword(
        changePassword.email,
        changePassword.currentPassword,
      );
      if (isValidPassword) {
        const setNewPassword = await this.userManagementService.setPassword(
          changePassword.email,
          changePassword.newPassword,
        );
        return new ResponseSuccess('Password changed', setNewPassword);
      } else {
        return new ResponseError(
          'Change password error, wrong current password',
        );
      }
    } catch (error) {
      return new ResponseError('Change password error', error);
    }
  }
}
