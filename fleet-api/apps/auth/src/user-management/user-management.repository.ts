import { AbstractRepository } from '@app/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Role } from 'apps/auth/src/entities/user-management/role.entity';
// import { UserActivity } from 'apps/auth/src/entities/user-management/userActivity.entity';
import { UserType } from 'apps/auth/src/entities/user-management/userType.entity';
// import { CivilStatus } from 'apps/auth/src/entities/user-management/civilStatus.entity';
// import { Permission } from 'apps/auth/src/entities/user-management/permission.entity';
// import { Country } from 'apps/auth/src/entities/delimitation/country.entity';
import { Province } from 'apps/auth/src/entities/delimitation/province.entity';
import { District } from 'apps/auth/src/entities/delimitation/district.entity';
import { Sector } from 'apps/auth/src/entities/delimitation/sector.entity';
import { Cell } from 'apps/auth/src/entities/delimitation/cell.entity';
import { Village } from 'apps/auth/src/entities/delimitation/village.entity';
import { Institution } from 'apps/auth/src/entities/institution/institution.entity';
import { InstitutionLocation } from 'apps/auth/src/entities/institution/institutionLocation.entity';
import { Injectable } from '@nestjs/common';
import { EmailVerification } from '../entities/user-management/emailVerification.entity';
import { ForgottenPassword } from '../entities/user-management/forgottenPassword.entity';
import { User } from '../entities/user-management/user.entity';
import { NonBudgetedAgencies } from '../entities/nonBudget-agencies/nonBudget-agencies.entity';

@Injectable()
export class UserRepository extends AbstractRepository<User> {
  constructor(
    @InjectRepository(User)
    userRepository: Repository<User>,
    entityManager: EntityManager,
  ) {
    super(entityManager, userRepository);
  }
}

// @Injectable()
// export class UsersRepository extends AbstractRepository<User> {
//   constructor(
//     @InjectRepository(User)
//     usersRepository: Repository<User>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, usersRepository);
//   }
// }
export class RoleRepository extends AbstractRepository<Role> {
  constructor(
    @InjectRepository(Role)
    roleRepository: Repository<Role>,
    entityManager: EntityManager,
  ) {
    super(entityManager, roleRepository);
  }
}

// export class UserActivityRepository extends AbstractRepository<UserActivity> {
//   constructor(
//     @InjectRepository(UserActivity)
//     userActivityRepository: Repository<UserActivity>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, userActivityRepository);
//   }
// }

export class UserTypeRepository extends AbstractRepository<UserType> {
  constructor(
    @InjectRepository(UserType)
    userTypeRepository: Repository<UserType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, userTypeRepository);
  }
}

// export class CivilStatusRepository extends AbstractRepository<CivilStatus> {
//   constructor(
//     @InjectRepository(CivilStatus)
//     civilStatusRepository: Repository<CivilStatus>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, civilStatusRepository);
//   }
// }

// export class PermissionRepository{
//   constructor(
//     @InjectRepository(Permission)
//     permissionRepository: Repository<Permission>,
//     entityManager: EntityManager,
//   ) {
//     // super(entityManager, permissionRepository);
//   }
// }

// export class PermissionRepository extends AbstractRepository<Permission> {
//   constructor(
//     @InjectRepository(Permission)
//     permissionRepository: Repository<Permission>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, permissionRepository);
//   }
// }

// delimitation
// export class CountryRepository extends AbstractRepository<Country> {
//   constructor(
//     @InjectRepository(Country)
//     countryRepository: Repository<Country>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, countryRepository);
//   }
// }

export class ProvinceRepository extends AbstractRepository<Province> {
  constructor(
    @InjectRepository(Province)
    provinceRepository: Repository<Province>,
    entityManager: EntityManager,
  ) {
    super(entityManager, provinceRepository);
  }
}

export class DistrictRepository extends AbstractRepository<District> {
  constructor(
    @InjectRepository(District)
    districtRepository: Repository<District>,
    entityManager: EntityManager,
  ) {
    super(entityManager, districtRepository);
  }
}

export class SectorRepository extends AbstractRepository<Sector> {
  constructor(
    @InjectRepository(Sector)
    sectorRepository: Repository<Sector>,
    entityManager: EntityManager,
  ) {
    super(entityManager, sectorRepository);
  }
}

export class CellRepository extends AbstractRepository<Cell> {
  constructor(
    @InjectRepository(Cell)
    cellRepository: Repository<Cell>,
    entityManager: EntityManager,
  ) {
    super(entityManager, cellRepository);
  }
}

export class VillageRepository extends AbstractRepository<Village> {
  constructor(
    @InjectRepository(Village)
    villageRepository: Repository<Village>,
    entityManager: EntityManager,
  ) {
    super(entityManager, villageRepository);
  }
}

// Agency

export class InstitutionRepository extends AbstractRepository<Institution> {
  constructor(
    @InjectRepository(Institution)
    institutionRepository: Repository<Institution>,
    entityManager: EntityManager,
  ) {
    super(entityManager, institutionRepository);
  }
}

export class InstitutionLocationRepository extends AbstractRepository<InstitutionLocation> {
  constructor(
    @InjectRepository(InstitutionLocation)
    institutionLocationRepository: Repository<InstitutionLocation>,
    entityManager: EntityManager,
  ) {
    super(entityManager, institutionLocationRepository);
  }
}

export class NonBudgetedAgenciesRepository extends AbstractRepository<NonBudgetedAgencies> {
  constructor(
    @InjectRepository(NonBudgetedAgencies)
    nonBudgetedAgenciesRepository: Repository<NonBudgetedAgencies>,
    entityManager: EntityManager,
  ) {
    super(entityManager, nonBudgetedAgenciesRepository);
  }
}

@Injectable()
export class EmailVerificationRepository extends AbstractRepository<EmailVerification> {
  constructor(
    @InjectRepository(EmailVerification)
    emailVerificationRepository: Repository<EmailVerification>,
    entityManager: EntityManager,
  ) {
    super(entityManager, emailVerificationRepository);
  }
}

@Injectable()
export class ForgottenPasswordRepository extends AbstractRepository<ForgottenPassword> {
  constructor(
    @InjectRepository(ForgottenPassword)
    forgottenPasswordRepository: Repository<ForgottenPassword>,
    entityManager: EntityManager,
  ) {
    super(entityManager, forgottenPasswordRepository);
  }
}
