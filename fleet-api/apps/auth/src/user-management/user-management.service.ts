import * as csv<PERSON><PERSON><PERSON> from 'csvtojson';
import async from 'async';
import { resolve } from 'path';
// import { getConnectionOptions, getConnection } from 'typeorm';
import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Role } from 'apps/auth/src/entities/user-management/role.entity';
// import { UserActivity } from 'apps/auth/src/entities/user-management/userActivity.entity';
import { UserType } from 'apps/auth/src/entities/user-management/userType.entity';
// import { CivilStatus } from 'apps/auth/src/entities/user-management/civilStatus.entity';
import { Permission } from 'apps/auth/src/entities/user-management/permission.entity';
import {
  // UserRepository,
  RoleRepository,
  // UserActivityRepository,
  UserTypeRepository,
  // CivilStatusRepository,
  // PermissionRepository,
  // CountryRepository,
  ProvinceRepository,
  CellRepository,
  DistrictRepository,
  SectorRepository,
  VillageRepository,
  InstitutionRepository,
  InstitutionLocationRepository,
  NonBudgetedAgenciesRepository,
} from './user-management.repository';
import {
  // CivilStatusDto,
  // PermissionDto,
  // PermissionDto,
  RoleDto,
  // UpdateCivilStatusDto,
  // UpdatePermissionDto,
  // UpdatePermissionDto,
  UpdateRoleDto,
  // UpdateUserActivityDto,
  UpdateUserTypeDto,
  // UserActivityDto,
  UserTypeDto,
} from '../dto/user-management.dto';
// import { Country } from 'apps/auth/src/entities/delimitation/country.entity';
import {
  CellDto,
  // CountryDto,
  DistrictDto,
  ProvinceDto,
  SectorDto,
  VillageDto,
} from '../dto/delimitation.dto';
import { Province } from 'apps/auth/src/entities/delimitation/province.entity';
import { District } from 'apps/auth/src/entities/delimitation/district.entity';
import { Sector } from 'apps/auth/src/entities/delimitation/sector.entity';
import { Cell } from 'apps/auth/src/entities/delimitation/cell.entity';
import { Village } from 'apps/auth/src/entities/delimitation/village.entity';
import {
  InstitutionLocationDto,
  InstitutionDto,
  NonBudgetedAgenciesDto,
} from '../dto/institution.dto';
import { Institution } from 'apps/auth/src/entities/institution/institution.entity';
import { InstitutionLocation } from 'apps/auth/src/entities/institution/institutionLocation.entity';
import { Repository, FindOptionsWhere } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateUserDto, UserProfileDto } from '../dto/user.dto';

import * as bcrypt from 'bcryptjs';
import { User } from '../entities/user-management/user.entity';
import { NonBudgetedAgencies } from '../entities/nonBudget-agencies/nonBudget-agencies.entity';
// import { FindOptionsWhere } from 'typeorm';
const saltRounds = 10;
// const csv = require('csvtojson');

@Injectable()
export class UserManagementService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,

    private readonly roleRepository: RoleRepository,

    // @InjectRepository(UserActivity)
    // private readonly userActivityRepository: Repository<UserActivity>,

    // private readonly userActivityRepository: UserActivityRepository,

    private readonly userTypeRepository: UserTypeRepository,
    // private readonly civilStatusRepository: CivilStatusRepository,
    // private readonly permissionRepository: PermissionRepository,

    // private readonly countryRepository: CountryRepository,
    private readonly provinceRepository: ProvinceRepository,
    private readonly districtRepository: DistrictRepository,
    private readonly sectorRepository: SectorRepository,
    private readonly cellRepository: CellRepository,
    private readonly villageRepository: VillageRepository,

    private readonly institutionRepository: InstitutionRepository,
    @InjectRepository(NonBudgetedAgencies)
    private nonBudgetedAgenciesManageRepository: Repository<NonBudgetedAgencies>,
    private readonly nonBudgetedAgenciesRepository: NonBudgetedAgenciesRepository,
    private readonly institutionLocationRepository: InstitutionLocationRepository,
    @InjectRepository(Institution)
    private institutionManagerRepository: Repository<Institution>,
    @InjectRepository(Permission)
    private readonly userActivityRoleRepository: Repository<Permission>,

    @InjectRepository(Province)
    private provinceRepo: Repository<Province>,
    @InjectRepository(District)
    private districtRepo: Repository<District>,
    @InjectRepository(Sector)
    private sectorRepo: Repository<Sector>,
    @InjectRepository(Cell)
    private cellRepo: Repository<Cell>,
    @InjectRepository(Village)
    private villageRepo: Repository<Village>,
  ) {}

  async findAllUsers(): Promise<User[]> {
    return await this.userRepository.find({
      relations: {
        role: true,
        institution: true,
        userType: true,
      },
    });
  }

  async findUserByEmail(email: string): Promise<User> {
    return this.userRepository.findOne({ where: { email } });
  }

  async findUserById(id: string): Promise<User> {
    return this.userRepository.findOne({ where: { id } });
  }

  isValidEmail(email: string) {
    if (email) {
      const re =
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(email);
    } else return false;
  }
  async createNewUser(newUser: CreateUserDto): Promise<User> {
    const dataFromDb = await this.institutionRepository.findOne({
      name: newUser.institution,
    });
    const roleDataFromDb = await this.roleRepository.findOne({
      name: newUser.role,
    });

    console.log(dataFromDb);
    // console.log("ghhhhhhhhfd");
    // console.log(roleDataFromDb);

    if (!dataFromDb)
      throw new HttpException(
        `INSTITUTION_NOT_FOUND with NAME ${newUser.institution}`,
        HttpStatus.NOT_FOUND,
      );
    if (!roleDataFromDb)
      throw new HttpException(
        `ROLE_NOT_FOUND with NAME ${newUser.role}`,
        HttpStatus.NOT_FOUND,
      );

    const userToBeRegistered = await this.validateToBeCreatedUser(newUser);
    if (userToBeRegistered) {
      throw new HttpException(
        'REGISTRATION.USER_ALREADY_REGISTERED',
        HttpStatus.FORBIDDEN,
      );
    } else {
      if (this.isValidEmail(newUser.email)) {
        const userRegistered = await this.findUserByEmail(newUser.email);

        if (!userRegistered) {
          const dataFromDb = await this.institutionRepository.findOne({
            name: newUser.institution,
          });
          if (!dataFromDb)
            throw new HttpException(
              'Institution Data Not Found',
              HttpStatus.NOT_FOUND,
            );

          const createdUser = new User();
          createdUser.firstName = newUser.firstName;
          createdUser.lastName = newUser.lastName;
          createdUser.email = newUser.email;
          createdUser.personalEmail = newUser.personalEmail;
          createdUser.phoneNumber = newUser.phoneNumber;
          createdUser.identification = newUser.identification;
          createdUser.password = newUser.password = await bcrypt.hash(
            newUser.password,
            saltRounds,
          );
          createdUser.institution = { id: dataFromDb.id } as any;
          createdUser.role = { id: roleDataFromDb.id } as any;

          const userToBeCreated = this.userRepository.create(createdUser);
          return await userToBeCreated.save();
        } else if (userRegistered.isEmailValid !== true) {
          return userRegistered;
        } else {
          throw new HttpException(
            'REGISTRATION.USER_ALREADY_REGISTERED',
            HttpStatus.FORBIDDEN,
          );
        }
      } else {
        throw new HttpException(
          'REGISTRATION.MISSING_MANDATORY_PARAMETERS',
          HttpStatus.FORBIDDEN,
        );
      }
    }
  }

  private async validateToBeCreatedUser(
    createUserDto: CreateUserDto,
  ): Promise<boolean> {
    try {
      const userFromDb = await this.userRepository.findOne({
        where: { email: createUserDto.email },
      });
      if (!userFromDb)
        throw new HttpException(
          'LOGIN.EMAIL.ALREADY.EXIST.ERROR',
          HttpStatus.FORBIDDEN,
        );
    } catch (err) {
      return;
    }
    return true;
  }

  // async setPassword(email: string, newPassword: string): Promise<boolean> {
  //   const userFromDb = await this.userRepository.findOne({ where: { email } });
  //   if (!userFromDb)
  //     throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);

  //   userFromDb.password = bcrypt.hash(newPassword, saltRounds);

  //   // await this.userRepository.create(userFromDb);
  //   const savedUser = this.userRepository.create(userFromDb);
  //   await savedUser.save();
  //   return true;
  // }
  async setPassword(email: string, newPassword: string): Promise<boolean> {
    const userFromDb = await this.userRepository.findOne({ where: { email } });
    if (!userFromDb)
      throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);

    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    userFromDb.password = hashedPassword;

    try {
      const savedUser = await this.userRepository.save(userFromDb);
      if (savedUser) {
        return true; // Return true if user is saved successfully
      }
    } catch (error) {
      // Handle any potential error during saving process
      console.error('Error saving user:', error);
    }

    return false; // Return false if user saving fails
  }

  async updateProfile(profileDto: UserProfileDto): Promise<User> {
    const userFromDb = await this.userRepository.findOne({
      // email: profileDto.email,
      where: { email: profileDto.email },
    });
    if (!userFromDb)
      throw new HttpException('COMMON.USER_NOT_FOUND', HttpStatus.NOT_FOUND);

    if (profileDto.profilePicture) {
      const base64Data = profileDto.profilePicture.replace(
        /^data:image\/png;base64,/,
        '',
      );
      const dir = '../public/users/' + userFromDb.email;

      const success = await this.writeFile(dir, 'profilepic.png', base64Data);
      if (success == true) {
        // userFromDb.photos = userFromDb.photos || {
        //   profilePic: new PhotoDto(),
        //   gallery: [],
        // };
        // userFromDb.photos.profilePic =
        //   userFromDb.photos.profilePic || new PhotoDto();
        // userFromDb.photos.profilePic.date = new Date();
        // userFromDb.photos.profilePic.url =
        //   '/public/users/' + userFromDb.email + '/profilepic.png';
      }
    }

    // await userFromDb.save();
    return userFromDb;
  }

  async writeFile(
    dir: string,
    filename: string,
    base64Data: string,
  ): Promise<any> {
    return new Promise(function (resolve, reject) {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const fs = require('fs');
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
      }
      fs.writeFile(dir + '/' + filename, base64Data, 'base64', function (err) {
        if (err) reject(err);
        else resolve(true);
      });
    });
  }

  // // User on admin level
  // async createUserByAdmin(createUserDto: CreateUserDto) {
  //   await this.validateCreateUserDto(createUserDto);
  //   const user = new User({
  //     ...createUserDto,
  //     password: await bcrypt.hash(createUserDto.password, 10),
  //   });
  //   return this.userRepository.create(user);
  // }

  // private async validateCreateUserDto(createUserDto: CreateUserDto) {
  //   try {
  //     await this.userRepository.findOne({ email: createUserDto.email });
  //   } catch (err) {
  //     return;
  //   }
  //   throw new UnprocessableEntityException('Email already exists.');
  // }

  // async verifyUser(email: string, password: string) {
  //   const user = await this.userRepository.findOne({ email });
  //   const passwordIsValid = await bcrypt.compare(password, user.password);
  //   if (!passwordIsValid) {
  //     throw new UnauthorizedException('Credentials are not valid.');
  //   }
  //   return user;
  // }

  // async getUser(getUserDto: UpdateUserDto) {
  //   return this.userRepository.findOne(getUserDto, { role: true });
  // }

  // async getAllUsers() {
  //   return this.userRepository.(User)
  //   .createQueryBuilder("user")
  //   .leftJoinAndSelect("user.role", "role")
  //   .getMany()
  // }

  // async getAllUsers() {
  //   return this.userRepository.find({
  //     relations: {
  //       role: true,
  //     },
  //   });
  // }

  // async getAllUsers() {
  //   return this.userRepository.find({});
  // }

  // async findAllUsers(): Promise<User[]> {
  //   return await this.userRepository.find({});
  // }

  // Roles
  async createRole(roleDto: RoleDto) {
    const role = new Role({
      ...roleDto,
    });

    return this.roleRepository.create(role);
  }

  async findAllRole() {
    return this.roleRepository.find({});
  }

  async findOneRole(id: string) {
    return this.roleRepository.findOne({ id });
  }

  async updateRole(id: string, updateRoleDto: UpdateRoleDto) {
    return this.roleRepository.findOneAndUpdate({ id }, updateRoleDto);
  }

  async removeRole(id: string) {
    return this.roleRepository.findOneAndDelete({ id });
  }

  // // user activities
  // async createUserActivity(userActivityDto: UserActivityDto) {
  //   return this.userActivityRepository.save(userActivityDto);
  // }

  // async findAllUserActivities() {
  //   return this.userActivityRepository.find({});
  // }

  // async findOneUserActivity(id: string) {
  //   return this.userActivityRepository.findOne({ id });
  // }

  // async updateUserActivity(
  //   id: string,
  //   updateUserActivityDto: UpdateUserActivityDto,
  // ) {
  //   return this.userActivityRepository.findOneAndUpdate(
  //     { id },
  //     updateUserActivityDto,
  //   );
  // }

  // async removeUserActivity(id: string) {
  //   return this.userActivityRepository.findOneAndDelete({ id });
  // }

  // user Types
  async createUserType(userTypeDto: UserTypeDto) {
    const userType = new UserType({
      ...userTypeDto,
    });
    return this.userTypeRepository.create(userType);
  }

  async findAllUserTypes() {
    return this.userTypeRepository.find({});
  }

  async findOneUserType(id: string) {
    return this.userTypeRepository.findOne({ id });
  }

  async updateUserType(id: string, updateUserTypeDto: UpdateUserTypeDto) {
    return this.userTypeRepository.findOneAndUpdate({ id }, updateUserTypeDto);
  }

  async removeUserType(id: string) {
    return this.userTypeRepository.findOneAndDelete({ id });
  }

  // // Civil status
  // async createCivilStatus(civilStatusDto: CivilStatusDto) {
  //   const civilStatus = new CivilStatus({
  //     ...civilStatusDto,
  //   });
  //   return this.civilStatusRepository.create(civilStatus);
  // }

  // async findAllCivilStatus() {
  //   return this.civilStatusRepository.find({});
  // }

  // async findOneCivilStatus(id: string) {
  //   return this.civilStatusRepository.findOne({ id });
  // }

  // async updateCivilStatus(
  //   id: string,
  //   updateCivilStatusDto: UpdateCivilStatusDto,
  // ) {
  //   return this.civilStatusRepository.findOneAndUpdate(
  //     { id },
  //     updateCivilStatusDto,
  //   );
  // }

  // async removeCivilStatus(id: string) {
  //   return this.civilStatusRepository.findOneAndDelete({ id });
  // }

  async createUserActivityRole(createUserActivityRole: {
    userActivityId: string;
    roleId: string;
  }): Promise<void> {
    // const userActivity = await this.userActivityRepository.find({
    //   where: { id: createUserActivityRole.userActivityId },
    // });
    // if (!userActivity) {
    //   throw new NotFoundException();
    // }

    await this.userActivityRoleRepository.save(createUserActivityRole);
  }

  // // Permissions
  // async createPermission(permissionDto: PermissionDto) {
  //   const permission = new Permission({
  //     ...permissionDto,
  //   });
  //   return this.permissionRepository.create(permission);
  // }

  async findAllPermissions() {
    return this.userActivityRoleRepository.find({
      relations: {
        roles: true,
        // userActivities: true,
      },
    });
  }

  async findOnePermission(id: string) {
    const permissionByRoles = await this.userActivityRoleRepository
      .createQueryBuilder('permission')
      .leftJoin('permission.roles', 'role')
      .where('permission.roleId = :roleId', { roleId: id })
      .getOne();
    return permissionByRoles;
  }

  async findPermissionByRoleId(id: string) {
    const permissionByRoles = await this.userActivityRoleRepository
      .createQueryBuilder('permission')
      .leftJoinAndSelect('permission.roles', 'role')
      .leftJoinAndSelect('permission.userActivities', 'userActivity')
      .where('permission.roleId = :roleId', { roleId: id })
      .getMany();
    return permissionByRoles;
  }

  // async updatePermission(id: string, updatePermissionDto: UpdatePermissionDto) {
  //   return this.permissionRepository.findOneAndUpdate(
  //     { id },
  //     updatePermissionDto,
  //   );
  // }

  async updatePermission(updateUserActivityRole: {
    userActivityId: string;
    roleId: string;
    isDeleted: boolean;
  }) {
    // const userActivity = await this.userActivityRepository.find({
    //   where: { id: updateUserActivityRole.userActivityId },
    // });
    // if (!userActivity) {
    //   throw new NotFoundException();
    // }

    await this.userActivityRoleRepository.save(updateUserActivityRole);
  }

  async removePermission(id: string) {
    const question = await this.userActivityRoleRepository.findOne({
      relations: {
        roles: true,
      },
      where: { roleId: id },
    });

    await this.userActivityRoleRepository.remove(question);
  }

  async removePermissionByRoleAndUserActivity(
    role: string,
    userActivity: string,
  ) {
    const question = await this.userActivityRoleRepository.findOne({
      relations: {
        roles: true,
        // userActivities: true,
      },
      where: { roleId: role } && { userActivityId: userActivity },
    });

    await this.userActivityRoleRepository.remove(question);
  }

  // Delimitation
  // Country
  // async createCountry(countryDto: CountryDto) {
  //   const country = new Country({
  //     ...countryDto,
  //   });
  //   return this.countryRepository.create(country);
  // }

  // async findAllCountries() {
  //   return this.countryRepository.find({});
  // }

  // async removeCountry(id: string) {
  //   return this.countryRepository.findOneAndDelete({ id });
  // }

  // Province
  async createProvince(provinceDto: ProvinceDto) {
    const province = new Province({
      ...provinceDto,
    });
    return this.provinceRepository.create(province);
  }
  // async findAllCountries() {
  //   return this.countryRepository.find({});
  // }

  async findAllProvinces() {
    return this.provinceRepository.find({});
  }

  // async findAllProvinces() {
  //   return this.provinceRepository.findAll({
  //     relations: {
  //       country: true,
  //     },
  //   });
  // }

  async removeProvince(id: string) {
    return this.provinceRepository.findOneAndDelete({ id });
  }

  // District
  async createDistrict(districtDto: DistrictDto) {
    const district = new District({
      ...districtDto,
    });
    return this.districtRepository.create(district);
  }

  async findAllDistricts() {
    return this.districtRepository.findAll({
      relations: {
        province: true,
      },
    });
  }

  // retriving all districts by provinceid and retrive id,name and code
  async findAllDistrictsByProvinceId(provinceId: string): Promise<District[]> {
    const where: FindOptionsWhere<District> = {
      province: { id: provinceId }, // Assuming the province ID is stored in the 'id' property of the 'province' relation
    };

    return this.districtRepository.findAllRelations(where);
  }
  // Sector
  async createSector(sectorDto: SectorDto) {
    const sector = new Sector({
      ...sectorDto,
    });
    return this.sectorRepository.create(sector);
  }

  async findAllSectors() {
    return this.sectorRepository.findAll({
      relations: {
        district: true,
      },
    });
  }
  // retriving sectors by ditrict id
  async findAllSectorsByDistrictId(districtId: string): Promise<Sector[]> {
    const where: FindOptionsWhere<Sector> = {
      district: { id: districtId }, // Assuming the district ID is stored in the 'id' property of the 'district' relation
    };

    return this.sectorRepository.findAllRelations(where);
  }

  // Cell
  async createCell(cellDto: CellDto) {
    const cell = new Cell({
      ...cellDto,
    });
    return this.cellRepository.create(cell);
  }

  async findAllCells() {
    return this.cellRepository.findAll({
      relations: {
        sector: true,
      },
    });
  }

  // retrive all cells by sector id
  async findAllCellsBySectorId(sectorId: string): Promise<Cell[]> {
    const where: FindOptionsWhere<Cell> = {
      sector: { id: sectorId }, // Assuming the sector ID is stored in the 'id' property of the'sector' relation
    };

    return this.cellRepository.findAllRelations(where);
  }
  // Village
  async createVillage(villageDto: VillageDto) {
    const village = new Village({
      ...villageDto,
    });
    return this.villageRepository.create(village);
  }

  async findAllVillages() {
    return this.villageRepository.findAll({
      relations: {
        cell: true,
      },
    });
  }

  // retrieve  villages by cell id

  async findAllVillagesByCellId(cellId: string): Promise<Village[]> {
    const where: FindOptionsWhere<Village> = {
      cell: { id: cellId }, // Assuming the cell ID is stored in the 'id' property of the 'cell' relation
    };

    return this.villageRepository.findAllRelations(where);
  }

  // Agency
  async createInstitution(institutionDto: InstitutionDto) {
    const institution = new Institution({
      ...institutionDto,
    });
    return this.institutionRepository.create(institution);
  }

  async findAllAgencies() {
    return this.institutionRepository.find({});
  }

  // find institution by id
  async findInstitutionById(id: string): Promise<Institution> {
    try {
      const institution = await this.institutionRepository.findOne({ id });
      return institution;
    } catch (error) {
      throw new NotFoundException('Institution not found');
    }
  }
  // update institution

  async removeAgency(id: string) {
    return this.institutionRepository.findOneAndDelete({ id });
  }

  // create BeneficiaryAgencies
  // async createBeneficiaryAgencies(
  //   nonBudgetedAgenciesDto: NonBudgetedAgenciesDto,
  // ) {
  //   const nonBudgetedAgencies = new NonBudgetedAgencies({
  //     ...nonBudgetedAgenciesDto,
  //   });
  //   return this.nonBudgetedAgenciesRepository.create(nonBudgetedAgencies);
  // }
  // async createBeneficiaryAgencies(
  //   nonBudgetedAgenciesDto: NonBudgetedAgenciesDto,
  // ) {
  //   const nonBudgetedAgencies = new NonBudgetedAgencies({
  //     ...nonBudgetedAgenciesDto,
  //   });
  //   return this.nonBudgetedAgenciesRepository.create(nonBudgetedAgencies);
  // }
  async createBeneficiaryAgencies(
    nonBudgetedAgenciesDto: NonBudgetedAgenciesDto,
  ) {
    const institutionId = nonBudgetedAgenciesDto.institution; // Corrected variable name
    const institution = await this.institutionManagerRepository
      .createQueryBuilder('institution')
      .where('institution.id = :institutionId', { institutionId }) // Fixed parameter name
      .getOne();
    console.log(institutionId);
    if (!institution) {
      throw new NotFoundException('Institution not found');
    }
    const nonBudgetedAgencieDto = new NonBudgetedAgencies({
      ...nonBudgetedAgenciesDto,
      institution: institutionId,
      agency: nonBudgetedAgenciesDto.agency,

      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });
    console.log(nonBudgetedAgencieDto);
    return this.nonBudgetedAgenciesManageRepository.save(nonBudgetedAgencieDto);
  }

  // find all beneficialiciaryAgencies
  async findAllBeneficiaryAgencies() {
    return this.nonBudgetedAgenciesRepository.find({});
  }

  // find beneficiaryAgencies by id
  async findBeneficiaryAgenciesById(id: string): Promise<NonBudgetedAgencies> {
    try {
      const beneficiaryAgencies =
        await this.nonBudgetedAgenciesRepository.findOne({ id });
      return beneficiaryAgencies;
    } catch (error) {
      throw new NotFoundException('BeneficiaryAgencies not found');
    }
  }

  // removing beneficiary angecy
  async removeBeneficiaryAgencies(id: string) {
    return this.nonBudgetedAgenciesRepository.findOneAndDelete({ id });
  }

  // BeneficiaryAgencies
  // async createBeneficiaryAgencies(
  //   beneficiaryAgenciesDto: BeneficiaryAgenciesDto,
  // ) {
  //   const beneficiaryAgencies = new BeneficiaryAgencies({
  //    ...beneficiaryAgenciesDto,
  //   });
  //   return this.beneficiaryAgenciesRepository.create(beneficiaryAgencies);
  // }

  // Agency district
  async createInstitutionLocation(
    institutionLocationDto: InstitutionLocationDto,
  ) {
    const dataveInstitution = await this.institutionRepository.findOne({
      id: institutionLocationDto.institution,
    });
    if (!dataveInstitution) {
      throw new HttpException('Institution', HttpStatus.BAD_REQUEST);
    }
    const dataveProvice = await this.provinceRepository.findOne({
      id: institutionLocationDto.province,
    });
    if (!dataveProvice) {
      throw new HttpException('province', HttpStatus.BAD_REQUEST);
    }
    const dataveDistrict = await this.districtRepository.findOne({
      id: institutionLocationDto.district,
    });
    if (!dataveDistrict) {
      throw new HttpException('district', HttpStatus.BAD_REQUEST);
    }
    const dataveSector = await this.sectorRepository.findOne({
      id: institutionLocationDto.sector,
    });
    if (!dataveSector) {
      throw new HttpException('sector', HttpStatus.BAD_REQUEST);
    }
    const dataveCell = await this.cellRepository.findOne({
      id: institutionLocationDto.cell,
    });
    if (!dataveCell) {
      throw new HttpException('cell', HttpStatus.BAD_REQUEST);
    }
    const datavevillage = await this.villageRepository.findOne({
      id: institutionLocationDto.village,
    });
    if (!datavevillage) {
      throw new HttpException('village', HttpStatus.BAD_REQUEST);
    }
    const newInstitutionLocationDto = new InstitutionLocation({
      ...institutionLocationDto,
      institution: dataveInstitution, // Assign the StatusType object
      province: dataveProvice,
      district: dataveDistrict,
      sector: dataveSector,
      village: datavevillage,
      cell: dataveCell, // Assign the ApprovalLevel object
      // Assign the RequestType object
    });
    // return dataveCell;
    const savedInstitutionLocation =
      await this.institutionLocationRepository.create(
        newInstitutionLocationDto,
      );

    if (!savedInstitutionLocation) {
      throw new HttpException('data not saved', HttpStatus.BAD_REQUEST);
    }

    dataveInstitution.isLocationAvailable = true;
    const upadtedInstitution =
      await this.institutionManagerRepository.save(dataveInstitution);
    if (!upadtedInstitution) {
      throw new HttpException(
        'institution not updated',
        HttpStatus.BAD_REQUEST,
      );
    }
    return savedInstitutionLocation;
  }

  // async findAllAgencyDistricts() {
  //   return this.institutionLocationRepository.findAll({
  //     relations: {
  //       insttiution: true,
  //       village: true,
  //     },
  //   });
  // }

  async removeAgencyDistrict(id: string) {
    return this.institutionLocationRepository.findOneAndDelete({ id });
  }
  //========================================= sychronizing delimitation data =================================
  async synchronize() {
    const dirName = resolve(__dirname, '../../../resources/codification.csv');
    try {
      const fileData = await csvtojson().fromFile(dirName);

      async.eachOfSeries(fileData, async (row: Record<string, any>) => {
        let province = await this.provinceRepo.findOne({
          where: {
            code: row.province_code,
          },
        });

        if (!province) {
          const record = this.provinceRepo.create({
            code: row.province_code,
            name: row.province_name,
          });

          province = await this.provinceRepo.save(record);
        }

        let district = await this.districtRepo.findOne({
          where: {
            code: row.district_code,
          },
        });

        if (!district) {
          const record = this.districtRepo.create({
            name: row.district_name,
            code: row.district_code,
            province,
          });

          district = await this.districtRepo.save(record);
        }

        let sector = await this.sectorRepo.findOne({
          where: {
            code: row.sector_code,
          },
        });

        if (!sector) {
          const record = this.sectorRepo.create({
            district,
            code: row.sector_code,
            name: row.sector_name,
          });

          sector = await this.sectorRepo.save(record);
        }

        let cell = await this.cellRepo.findOne({
          where: {
            code: row.cell_code,
          },
        });

        if (!cell) {
          const record = this.cellRepo.create({
            sector,
            code: row.cell_code,
            name: row.cell_name,
          });

          cell = await this.cellRepo.save(record);
        }

        let village = await this.villageRepo.findOne({
          where: {
            code: row.village_code,
          },
        });

        if (!village) {
          const record = this.villageRepo.create({
            cell,
            code: row.village_code,
            name: row.village_name,
          });

          village = await this.villageRepo.save(record);
        }
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  // getting all users by institution id
  async findAllUsersByInstitutionId(institutionId: string) {
    return await this.userRepository
      .createQueryBuilder('User')
      .where('User.institution.id = :institutionId', {
        institutionId,
      })
      .leftJoinAndSelect('User.role', 'role')
      .getMany();
  }
}
