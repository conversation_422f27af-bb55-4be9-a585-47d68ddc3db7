import * as jwt from 'jsonwebtoken';
// import { default as config } from '../config';
import { Injectable } from '@nestjs/common';
import { User } from './entities/user-management/user.entity';
import config from '../config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class JWTService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}
  async createToken(
    email,
    id,
    firstName,
    lastName,
    institution,
    // userActivities,
  ) {
    const expiresIn = config.jwt.expiresIn,
      secretOrKey = config.jwt.secretOrKey;
    const userInfo = {
      Email: email,
      UserId: id,
      FirstName: firstName,
      LastName: lastName,
      institution: institution,
      // userActivities: userActivities,
    };
    const token = jwt.sign(userInfo, secretOrKey, { expiresIn });
    return {
      expires_in: expiresIn,
      access_token: token,
    };
  }

  async validateUser(signedUser): Promise<User> {
    const userFromDb = await this.userRepository.findOne({
      where: { email: signedUser.email },
    });
    if (userFromDb) {
      return userFromDb;
    }
    return null;
  }
}
