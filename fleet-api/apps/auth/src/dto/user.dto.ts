import {
  // IsE<PERSON>,
  <PERSON><PERSON>otEmpt<PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Matches,
  MinLength,
} from 'class-validator';
import { RoleDto } from './user-management.dto';
import { ApiProperty } from '@nestjs/swagger';

const passwordRegEx = /((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/;

export class CreateUserDto {
  @ApiProperty({ description: 'The field identification is required' })
  @IsString()
  @MinLength(2, { message: 'identification must have at least 2 characters.' })
  @IsNotEmpty()
  identification: string;
  @ApiProperty({ description: 'The field first name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'The field last name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty()
  // @IsEmail(null, { message: 'Please provide valid Email.' })
  email: string;

  @ApiProperty({ description: 'The field personnel email is required' })
  @IsNotEmpty()
  personalEmail: string;

  @ApiProperty({ description: 'The field phone number is required' })
  @IsNotEmpty()
  phoneNumber: number;

  @ApiProperty({ description: 'The field password is required' })
  @IsNotEmpty()
  @Matches(passwordRegEx, {
    message: `Password must contain Minimum 8 and maximum 20 characters, 
      at least one uppercase letter, 
      one lowercase letter, 
      one number and 
      one special character`,
  })
  password: string;

  @IsNotEmpty({ message: 'The field institutionId cannot be empty' })
  @ApiProperty()
  institution: string;

  @IsNotEmpty({ message: 'The field role cannot be empty' })
  @ApiProperty()
  role: string;
}

export class UpdateUserDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty()
  email: string;
  @ApiProperty({ description: 'The field personnel name is required' })
  @IsNotEmpty()
  personalEmail: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty()
  phoneNumber: number;
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty()
  role: RoleDto;
  @IsNotEmpty({ message: 'The field institutionId cannot be empty' })
  @ApiProperty()
  institutionId: string;

  @IsNotEmpty({ message: 'The field useTypeId cannot be empty' })
  @ApiProperty()
  useTypeId: string;
}

export class UserProfileDto {
  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'The field file is required' })
  @IsString()
  @IsNotEmpty()
  profilePicture: string;
}

export class UserDto {
  constructor(object: any) {
    this.firstName = object.firstName;
    this.lastName = object.lastName;
    this.email = object.email;
    this.phoneNumber = object.phoneNumber;
    this.role = object.role;
  }
  readonly firstName: string;
  readonly lastName: string;
  readonly email: string;
  readonly phoneNumber: string;
  readonly role: string;
}

export class GetUserDto {
  constructor(object: any) {
    this.firstName = object.firstName;
    this.lastName = object.lastName;
    this.email = object.email;
    this.phoneNumber = object.phoneNumber;
    this.role = object.role;
  }
  readonly firstName: string;
  readonly lastName: string;
  readonly email: string;
  readonly phoneNumber: string;
  role: {
    id: string;
    name: string;
  };
}
