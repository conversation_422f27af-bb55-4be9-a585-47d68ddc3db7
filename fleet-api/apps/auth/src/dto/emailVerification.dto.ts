import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsOptional } from 'class-validator';

export class ResetPasswordDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty({ message: 'The field email cannot be empty' })
  email: string;

  @ApiProperty({ description: 'The field emailToken is required' })
  @IsNotEmpty({ message: 'The field emailToken cannot be empty' })
  emailToken: string;

  @ApiProperty({ description: 'The field timestamp is required' })
  @IsOptional()
  @IsDateString()
  timestamp: Date;
}
