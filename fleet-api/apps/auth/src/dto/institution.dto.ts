// import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional } from 'class-validator';
// import { VillageDto } from './delimitation.dto';
import { ApiProperty } from '@nestjs/swagger';

export class InstitutionDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({
    description: 'Indicates if the institution is budgeted',
    default: true,
  })
  @IsNotEmpty({ message: 'The field isbudgeted cannot be empty' })
  @IsBoolean({ message: 'The field isbudgeted must be a boolean' })
  isbudgeted: boolean = true;
}

export class InstitutionLocationDto {
  @ApiProperty({ description: 'The field institutionId is required' })
  @IsNotEmpty({ message: 'The field institutionId cannot be empty' })
  institution: string;
  @ApiProperty({ description: 'The field provinceId is required' })
  @IsNotEmpty({ message: 'The field provinceId cannot be empty' })
  province: string;
  @ApiProperty({ description: 'The field districtId is required' })
  @IsNotEmpty({ message: 'The field districtId cannot be empty' })
  district: string;
  @ApiProperty({ description: 'The field sectorId is required' })
  @IsNotEmpty({ message: 'The field sectorId cannot be empty' })
  sector: string;
  @ApiProperty({ description: 'The field cellId is required' })
  @IsNotEmpty({ message: 'The field cellId cannot be empty' })
  cell: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field villageId cannot be empty' })
  village: string;
}

export class NonBudgetedAgenciesDto {
  @ApiProperty({ description: 'The field institution Id is required' })
  @IsNotEmpty({ message: 'The field institution Id cannot be empty' })
  institution: string;
  @ApiProperty({ description: 'The field beneficiaryAgency is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency cannot be empty' })
  agency: string;
}
