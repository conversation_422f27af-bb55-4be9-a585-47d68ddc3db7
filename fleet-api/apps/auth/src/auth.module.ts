import { Module } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UserManagementModule } from './user-management/user-management.module';
import { ConfigModule } from '@nestjs/config';
import { JWTService } from './jwt.service';

import { JwtStrategy } from './passport/jwt.strategy';
import { User } from './entities/user-management/user.entity';
import { EmailVerification } from './entities/user-management/emailVerification.entity';
import { ForgottenPassword } from './entities/user-management/forgottenPassword.entity';
import {
  UserRepository,
  EmailVerificationRepository,
  ForgottenPasswordRepository,
  InstitutionLocationRepository,
} from './user-management/user-management.repository';
import { DatabaseModule } from '@app/common';

import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { NOTIFICATIONS_SERVICE } from '@app/common/constants';
import { InstitutionLocation } from './entities/institution/institutionLocation.entity';
@Module({
  imports: [
    UserManagementModule,
    DatabaseModule,
    DatabaseModule.forFeature([
      User,
      EmailVerification,
      ForgottenPassword,
      InstitutionLocation,
    ]),

    ConfigModule.forRoot({
      isGlobal: true,
    }),

    ClientsModule.registerAsync([
      {
        name: NOTIFICATIONS_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('NOTIFICATIONS_HOST'),
            port: configService.get('NOTIFICATIONS_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JWTService,
    JwtStrategy,
    UserRepository,
    EmailVerificationRepository,
    ForgottenPasswordRepository,
    InstitutionLocationRepository,
  ],
})
export class AuthModule {}
