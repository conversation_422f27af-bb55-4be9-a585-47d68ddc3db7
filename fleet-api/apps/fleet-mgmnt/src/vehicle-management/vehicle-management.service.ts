import { Injectable } from '@nestjs/common';
import {
  ActivityOnVehicleRepository,
  ApprovalLevelRepository,
  DisposalReasonsRepository,
  DisposalTypesRepository,
  OilServiceCategoryRepository,
  // CommentsRepository,
  OwnershipTypeRepository,
  RequesttbRepository,
  RequestTypeRepository,
  StatusTypeRepository,
  VehicleManufactureRepository,
  VehicleModelRepository,
  VehicleStatusRepository,
  VehicleTypeRepository,
} from './vehicle-management.repository';
// import { Repository } from 'typeorm';
import { VehicleType } from '../entities/vehicle-management /vehicle-types.entity';
// import { Repository } from 'typeorm';
// import { InjectRepository } from '@nestjs/typeorm';
import { OwnershipType } from '../entities/vehicle-management /vehicle-ownership.entity';
import { StatusType } from '../entities/vehicle-management /status.entity';
import { Requesttb } from '../entities/vehicle-management /request.entity';
import { RequestType } from '../entities/vehicle-management /requestType.entity';
// import { Comments } from '../entities/vehicle-management /comments.entity';
import { ApprovalLevel } from '../entities/vehicle-management /approvel-level.entity';
import {
  CreateVehicleTypeDto,
  UpdateVehicleTypeDto,
} from '../dtos/vehicle-management/vehicle-type.dto';
import {
  StatusTypeDto,
  UpdateStatusTypeDto,
} from '../dtos/vehicle-management/status-type.dto';
import { UpdateOwnershipTypeDto } from '../dtos/vehicle-management/ownership-type.dto';
import {
  CreateRequestTypeDto,
  UpdateRequestTypeDto,
} from '../dtos/vehicle-management/request-type.dto';
import {
  CreateRequestDto,
  UpdateRequestDto,
} from '../dtos/vehicle-management/request.dto';
import {
  CreateApprovalLevelDto,
  UpdateApprovalLevelDto,
} from '../dtos/vehicle-management/approval-levels.dto';
import { CreateVehicleModelDto } from '../dtos/vehicle-management/vehicle-model.dto';
import { VehicleModel } from '../entities/vehicle-management /vehicle-model.entity';
import { CreateVehicleManufactureDto } from '../dtos/vehicle-management/vehicle-manufacture.dto';
import { CreateVehicleStatusDto } from '../dtos/vehicle-management/vehicle-status.dto';
import { VehicleStatus } from '../entities/vehicle-management /vehicle-status.entity';
import { CreateDisposalTypeDto } from '../dtos/vehicle-management/disposal-types.dto';
import { DisposalTypes } from '../entities/vehicle-management /disposal types.entity';
import { CreateDisposalReasonLevelDto } from '../dtos/vehicle-management/disposal-reasons.dto';
import { DisposalReasons } from '../entities/vehicle-management /disposal-reasons.entity';
import { ActivityOnVehicleDto } from '../dtos/vehicle-management/activity-On-Vehicle.dto';
import { ActivityOnVehicle } from '../entities/vehicle-management /activities-on-vehicle.entity';
import { OilServiceCategory } from '../entities/vehicle-management /oil-service-category.entity';
import { OilServiceCategoryDto } from '../dtos/vehicle-management/oil-service-category.dto';
// import {
//   CreateCommentDto,
//   UpdateCommentDto,
// } from '../dtos/vehicle-management/comments.dto';
@Injectable()
export class VehicleManagementService {
  constructor(
    private readonly vehicleTypeRepository: VehicleTypeRepository,
    private ownershipTypeRepository: OwnershipTypeRepository,
    private activityOnVehicleRepository: ActivityOnVehicleRepository,
    private oilServiceCategoryRepository: OilServiceCategoryRepository,
    private statusTypeRepository: StatusTypeRepository,
    private requestTypeRepository: RequestTypeRepository,
    private requesttbRepository: RequesttbRepository,
    // private commentsRepository: CommentsRepository,
    private approvalLevelRepository: ApprovalLevelRepository,
    private vehicleStatusRepository: VehicleStatusRepository,
    private vehicleManufactureRepository: VehicleManufactureRepository,
    private vehicleModelRepository: VehicleModelRepository,
    private disposalTypesRepository: DisposalTypesRepository,
    private disposalReasonsRepository: DisposalReasonsRepository,
  ) {}
  // vehicle type CRUD Section
  async createVehicleType(createVehicleTypeDto: CreateVehicleTypeDto) {
    const vehicleType = new VehicleType({
      ...createVehicleTypeDto,
    });

    return this.vehicleTypeRepository.create(vehicleType);
  }

  async findAllVehicleType() {
    return this.vehicleTypeRepository.find({});
  }

  async findOneVehicleType(id: string) {
    return this.vehicleTypeRepository.findOne({ id });
  }

  async updateVehicleType(
    id: string,
    updateVehicleTypeDto: UpdateVehicleTypeDto,
  ) {
    return this.vehicleTypeRepository.findOneAndUpdate(
      { id },
      updateVehicleTypeDto,
    );
  }

  async removeVehicleType(id: string) {
    return this.vehicleTypeRepository.findOneAndDelete({ id });
  }

  // VehicleModel CRUD
  async createVehicleModel(createVehicleModelDto: CreateVehicleModelDto) {
    const vehicleModel = new VehicleModel({
      ...createVehicleModelDto,
    });

    return this.vehicleModelRepository.create(vehicleModel);
  }
  async findAllVehicleModel() {
    return this.vehicleModelRepository.find({});
  }
  async findOneVehicleModel(id: string) {
    return this.vehicleModelRepository.findOne({ id });
  }
  async updateVehicleModel(
    id: string,
    updateVehicleModelDto: CreateVehicleModelDto,
  ) {
    return this.vehicleModelRepository.findOneAndUpdate(
      { id },
      updateVehicleModelDto,
    );
  }
  async removeVehicleModel(id: string) {
    return this.vehicleModelRepository.findOneAndDelete({ id });
  }
  // VehicleManufacture CRUD
  async createVehicleManufacture(
    createVehicleManufactureDto: CreateVehicleManufactureDto,
  ) {
    const vehicleManufacture = new VehicleModel({
      ...createVehicleManufactureDto,
    });

    return this.vehicleManufactureRepository.create(vehicleManufacture);
  }
  async findAllVehicleManufacture() {
    return this.vehicleManufactureRepository.find({});
  }
  async findOneVehicleManufacture(id: string) {
    return this.vehicleManufactureRepository.findOne({ id });
  }
  async updateVehicleManufacture(
    id: string,
    updateVehicleManufactureDto: CreateVehicleManufactureDto,
  ) {
    return this.vehicleManufactureRepository.findOneAndUpdate(
      { id },
      updateVehicleManufactureDto,
    );
  }
  async removeVehicleManufacture(id: string) {
    return this.vehicleManufactureRepository.findOneAndDelete({ id });
  }
  // VehicleStatus CRUD
  async createVehicleStatus(createVehicleStatusDto: CreateVehicleStatusDto) {
    const vehicleStatus = new VehicleStatus({
      ...createVehicleStatusDto,
    });

    return this.vehicleStatusRepository.create(vehicleStatus);
  }
  async findAllVehicleStatus() {
    return this.vehicleStatusRepository.find({});
  }
  async findOneVehicleStatus(id: string) {
    return this.vehicleStatusRepository.findOne({ id });
  }
  async updateVehicleStatus(
    id: string,
    updateVehicleStatusDto: CreateVehicleStatusDto,
  ) {
    return this.vehicleStatusRepository.findOneAndUpdate(
      { id },
      updateVehicleStatusDto,
    );
  }
  async removeVehicleStatus(id: string) {
    return this.vehicleStatusRepository.findOneAndDelete({ id });
  }

  async createStatusType(statusTypeDto: StatusTypeDto) {
    try {
      const statusType = new StatusType({
        ...statusTypeDto,
      });
      // console.log(statusTypeDto);
      return await this.statusTypeRepository.create(statusType);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  async findAllStatusType() {
    return this.statusTypeRepository.find({});
  }
  async findOneStatusType(id: string) {
    return this.statusTypeRepository.findOne({ id });
  }

  async updateStatusType(id: string, updateStatusTypeDto: UpdateStatusTypeDto) {
    return this.vehicleTypeRepository.findOneAndUpdate(
      { id },
      updateStatusTypeDto,
    );
  }
  async removeStatusType(id: string) {
    return this.statusTypeRepository.findOneAndDelete({ id });
  }
  // ownership type CRUD Section
  async createOwnershipType(ownershipTypeDto: OwnershipType) {
    try {
      const ownershipType = new OwnershipType({
        ...ownershipTypeDto,
      });
      // console.log(ownershipTypeDto);
      return await this.ownershipTypeRepository.create(ownershipType);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  async findAllOwnershipType() {
    return this.ownershipTypeRepository.find({});
  }
  async findOneOwnershipType(id: string) {
    return this.ownershipTypeRepository.findOne({ id });
  }
  async updateOwnershipType(
    id: string,
    updateOwnershipTypeDto: UpdateOwnershipTypeDto,
  ) {
    return this.ownershipTypeRepository.findOneAndUpdate(
      { id },
      updateOwnershipTypeDto,
    );
  }
  async removeOwnershipType(id: string) {
    return this.ownershipTypeRepository.findOneAndDelete({ id });
  }
  // request type CRUD Section
  async createRequestType(createRequestTypeDto: CreateRequestTypeDto) {
    try {
      const requestType = new RequestType({
        ...createRequestTypeDto,
      });
      // console.log(requestTypeDto);
      return await this.requestTypeRepository.create(requestType);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  async findAllRequestType() {
    return this.requestTypeRepository.find({});
  }
  async findOneRequestType(id: string) {
    return this.requestTypeRepository.findOne({ id });
  }
  async updateRequestType(
    id: string,
    updateRequestTypeDto: UpdateRequestTypeDto,
  ) {
    return this.requestTypeRepository.findOneAndUpdate(
      { id },
      updateRequestTypeDto,
    );
  }
  async removeRequestType(id: string) {
    return this.requestTypeRepository.findOneAndDelete({ id });
  }
  // request type CRUD Section
  async createRequesttb(createRequestDto: CreateRequestDto) {
    try {
      const requesttb = new Requesttb({
        ...createRequestDto,
      });
      // console.log(requesttbDto);
      return await this.requesttbRepository.create(requesttb);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  async findAllRequesttb() {
    return this.requesttbRepository.find({});
  }
  async findOneRequesttb(id: string) {
    return this.requesttbRepository.findOne({ id });
  }
  async updateRequesttb(id: string, updateRequestDto: UpdateRequestDto) {
    return this.requesttbRepository.findOneAndUpdate({ id }, updateRequestDto);
  }

  async removeRequesttb(id: string) {
    return this.requesttbRepository.findOneAndDelete({ id });
  }
  // approval level CRUD Section
  async createApprovalLevel(createApprovalLevelDto: CreateApprovalLevelDto) {
    try {
      const approvalLevel = new ApprovalLevel({
        ...createApprovalLevelDto,
      });
      // console.log(approvalLevelDto);
      return await this.approvalLevelRepository.create(approvalLevel);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  async findAllApprovalLevel() {
    return this.approvalLevelRepository.find({});
  }
  async findOneApprovalLevel(id: string) {
    return this.approvalLevelRepository.findOne({ id });
  }
  async updateApprovalLevel(
    id: string,
    updateApprovalLevelDto: UpdateApprovalLevelDto,
  ) {
    return this.approvalLevelRepository.findOneAndUpdate(
      { id },
      updateApprovalLevelDto,
    );
  }
  async removeApprovalLevel(id: string) {
    return this.approvalLevelRepository.findOneAndDelete({ id });
  }
  // DisposalTypes CRUD operation
  async createDisposalType(createDisposalTypeDto: CreateDisposalTypeDto) {
    try {
      const disposalType = new DisposalTypes({
        ...createDisposalTypeDto,
      });
      // console.log(disposalTypeDto);
      return await this.disposalTypesRepository.create(disposalType);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }

  // update vehicle disposal  types
  async updateDisposalType(
    id: string,
    updateDisposalTypeDto: CreateDisposalTypeDto,
  ) {
    return this.disposalTypesRepository.findOneAndUpdate(
      { id },
      updateDisposalTypeDto,
    );
  }
  // getting all vehicle disposal types
  async findAllDisposalType() {
    return this.disposalTypesRepository.find({});
  }
  // getting one vehicle disposal types
  async findOneDisposalType(id: string) {
    return this.disposalTypesRepository.findOne({ id });
  }
  // delete vehicle disposal types
  async removeDisposalType(id: string) {
    return this.disposalTypesRepository.findOneAndDelete({ id });
  }
  // DisposalReasons CRUD operations
  // create disposal reasons

  async createDisposalReasons(
    CreateDisposalReasonDto: CreateDisposalReasonLevelDto,
  ) {
    try {
      const disposalReasons = new DisposalReasons({
        ...CreateDisposalReasonDto,
      });
      // console.log(disposalTypeDto);
      return await this.disposalReasonsRepository.create(disposalReasons);
    } catch (error) {
      console.error(
        'Error occurred while saving disposal reasons types:',
        error,
      );
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  // update disposal reasons
  async updateDisposalReason(
    id: string,
    updateDisposalReasonDto: CreateDisposalReasonLevelDto,
  ) {
    return this.disposalReasonsRepository.findOneAndUpdate(
      { id },
      updateDisposalReasonDto,
    );
  }
  // getting all disposal reasons
  async getAllDisposalReason() {
    return this.disposalReasonsRepository.find({});
  }
  // getting one disposal reasons
  async getOneDisposalReason(id: string) {
    return this.disposalReasonsRepository.findOne({ id });
  }
  // delete disposal reasons
  async deleteDisposalReason(id: string) {
    return this.disposalReasonsRepository.findOneAndDelete({ id });
  }
  // CRUD operations on activity performed
  async createPerformedOnVehicle(activityOnVehicleDto: ActivityOnVehicleDto) {
    try {
      const activityOnVehicle = new ActivityOnVehicle({
        ...activityOnVehicleDto,
      });
      // console.log(ownershipTypeDto);
      return await this.activityOnVehicleRepository.create(activityOnVehicle);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  // getting all activity performed
  async findAllActivityOnVehicle() {
    return this.activityOnVehicleRepository.find({});
  }

  // activity on vehicle by id
  async findOneActivityOnVehicle(id: string) {
    return this.activityOnVehicleRepository.findOne({ id });
  }

  // update activity on Vehicle
  async updateActivityOnVehicle(
    id: string,
    activityOnVehicleDto: ActivityOnVehicleDto,
  ) {
    return this.activityOnVehicleRepository.findOneAndUpdate(
      { id },
      activityOnVehicleDto,
    );
  }

  //  remove activity on Vehicle
  async removeActivityOnVehicle(id: string) {
    return this.activityOnVehicleRepository.findOneAndDelete({ id });
  }
  // oil service CRUD operations
  async createOilService(oilServiceCategoryDto: OilServiceCategoryDto) {
    try {
      const oilServiceCategory = new OilServiceCategory({
        ...oilServiceCategoryDto,
      });
      // console.log(ownershipTypeDto);
      return await this.oilServiceCategoryRepository.create(oilServiceCategory);
    } catch (error) {
      console.error('Error occurred while saving vehicle type:', error);
      // You can also throw the error if you want to propagate it further
      // throw error;
    }
  }
  // getting all oil service categories
  async findAllOilCategory() {
    return this.oilServiceCategoryRepository.find({});
  }
  // getting oil service category by id
  // activity on vehicle by id
  async findOneOilServiceCategory(id: string) {
    return this.activityOnVehicleRepository.findOne({ id });
  }
  // update oil service category

  async updateOilServiceCategory(
    id: string,
    oilServiceCategoryDto: OilServiceCategoryDto,
  ) {
    return this.activityOnVehicleRepository.findOneAndUpdate(
      { id },
      oilServiceCategoryDto,
    );
  }
  // removing Oil service category
  async removeOilServiceCategory(id: string) {
    return this.oilServiceCategoryRepository.findOneAndDelete({ id });
  }
}
