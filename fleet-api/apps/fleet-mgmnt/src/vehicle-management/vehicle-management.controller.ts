import {
  Controller,
  Post,
  Body,
  Get,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { VehicleManagementService } from './vehicle-management.service';
import {
  CreateVehicleTypeDto,
  UpdateVehicleTypeDto,
} from '../dtos/vehicle-management/vehicle-type.dto';
import {
  StatusTypeDto,
  UpdateStatusTypeDto,
} from '../dtos/vehicle-management/status-type.dto';
import {
  CreateOwnershipTypeDto,
  UpdateOwnershipTypeDto,
} from '../dtos/vehicle-management/ownership-type.dto';
import {
  CreateRequestTypeDto,
  UpdateRequestTypeDto,
} from '../dtos/vehicle-management/request-type.dto';
import {
  CreateRequestDto,
  UpdateRequestDto,
} from '../dtos/vehicle-management/request.dto';
import {
  CreateApprovalLevelDto,
  UpdateApprovalLevelDto,
} from '../dtos/vehicle-management/approval-levels.dto';
import { CreateVehicleModelDto } from '../dtos/vehicle-management/vehicle-model.dto';
import { CreateVehicleManufactureDto } from '../dtos/vehicle-management/vehicle-manufacture.dto';
import { CreateVehicleStatusDto } from '../dtos/vehicle-management/vehicle-status.dto';
import {
  CreateDisposalTypeDto,
  UpdateDisposalTypeDto,
} from '../dtos/vehicle-management/disposal-types.dto';
import {
  CreateDisposalReasonLevelDto,
  UpdateDisposalReasonLevelDto,
} from '../dtos/vehicle-management/disposal-reasons.dto';
import { ActivityOnVehicleDto } from '../dtos/vehicle-management/activity-On-Vehicle.dto';
import { OilServiceCategoryDto } from '../dtos/vehicle-management/oil-service-category.dto';
// import {
//   CreateCommentDto,
//   UpdateCommentDto,
// } from '../dtos/vehicle-management/comments.dto';

@ApiTags('Vehicle-management')
@Controller('fleet-mgmnt/vehicle-management')
export class VehicleManagementController {
  constructor(
    private readonly vehicleManagementService: VehicleManagementService,
  ) {}
  // @ApiTags('Vehicle Management')

  // start of vehicle type CRUD operations
  @ApiOperation({
    summary: 'Create a new vehicle type',
    description:
      'Vehicle types may include:Pick-Up,SUV,Truck,Motorcycle,Lorry,Voiture,Bus,MiniBus, Others',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of vehicle type',
          example: 'Truck',
        },
        // password: {
        //   type: 'string',
        //   title: 'User Password',
        //   example: 'Flu_Game@123!',
        // },
      },
    },
  })
  @Post('vehicleType')
  async createVehicalType(@Body() createVehicleTypeDto: CreateVehicleTypeDto) {
    // console.log(vehicleTypeDto);
    return this.vehicleManagementService.createVehicleType(
      createVehicleTypeDto,
    );
  }

  @ApiOperation({
    summary: 'Find all vehicle types',
  })
  @Get('vehicleTypes')
  async findAllVehicleTypes() {
    return this.vehicleManagementService.findAllVehicleType();
  }
  @ApiOperation({
    summary: 'Find one vehicle type',
  })
  @Get('vehicleType/:id')
  async findOneVehicleType(@Param('id') id: string) {
    return this.vehicleManagementService.findOneVehicleType(id);
  }
  @ApiOperation({
    summary: 'Update  vehicle type by ID',
  })
  @Patch('vehicleType/:id')
  async update(
    @Param('id') id: string,
    @Body() updateVehicleTypeDto: UpdateVehicleTypeDto,
  ) {
    return this.vehicleManagementService.updateVehicleType(
      id,
      updateVehicleTypeDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  vehicle type by ID',
  })
  @Delete('vehicleType/:id')
  async remove(@Param('id') id: string) {
    return this.vehicleManagementService.removeVehicleType(id);
  }
  // Start of VehicleModel type CRUD operations
  @ApiOperation({
    summary: 'Create a new vehicle model',
    description:
      'Vehicle models may include:Toyota,Honda,Suzuki,Nissan,Mitsubishi,Mazda,Daihatsu,Kia,Hyundai,Chevrolet,Volkswagen,Mercedes-Benz,BMW,Audi,Renault,Peugeot,Citroen,Fiat,Opel,Ford',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of vehicle model',
          example: 'Toyota',
        },
        // password: {
        //   type:'string',
        //   title: 'User Password',
        //   example: 'Flu_Game@123!',
        // },
      },
    },
  })
  @Post('vehicleModel')
  async createVehicalModel(
    @Body() createVehicleModelDto: CreateVehicleModelDto,
  ) {
    // console.log(vehicleTypeDto);
    return this.vehicleManagementService.createVehicleModel(
      createVehicleModelDto,
    );
  }
  @ApiOperation({
    summary: 'Find all vehicle models',
  })
  @Get('vehicleModels')
  async findAllVehicleModels() {
    return this.vehicleManagementService.findAllVehicleModel();
  }
  @ApiOperation({
    summary: 'Find one vehicle model',
  })
  @Get('vehicleModel/:id')
  async findOneVehicleModel(@Param('id') id: string) {
    return this.vehicleManagementService.findOneVehicleModel(id);
  }
  @ApiOperation({
    summary: 'Update  vehicle model by ID',
  })
  @Patch('vehicleModel/:id')
  async updateModel(
    @Param('id') id: string,
    @Body() updateVehicleModelDto: CreateVehicleModelDto,
  ) {
    return this.vehicleManagementService.updateVehicleModel(
      id,
      updateVehicleModelDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  vehicle model by ID',
  })
  @Delete('vehicleModel/:id')
  async removeModel(@Param('id') id: string) {
    return this.vehicleManagementService.removeVehicleModel(id);
  }
  // Start of VehicleManufacture type CRUD operations
  @ApiOperation({
    summary: 'Create a new vehicle manufacture',
    description:
      'Vehicle manufacture may include:Toyota,Honda,Suzuki,Nissan,Mitsubishi,Mazda,Daihatsu,Kia,Hyundai,Chevrolet,Volkswagen,Mercedes-Benz,BMW,Audi,Renault,Peugeot,Citroen,Fiat,Opel,Ford',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of vehicle manufacture',
          example: 'Toyota',
        },
        // password: {
        //   type:'string',
        //   title: 'User Password',
        //   example: 'Flu_Game@123!',
        // },
      },
    },
  })
  @Post('vehicleManufacture')
  async createVehicalManufacture(
    @Body() createVehicleManufactureDto: CreateVehicleManufactureDto,
  ) {
    // console.log(vehicleTypeDto);
    return this.vehicleManagementService.createVehicleManufacture(
      createVehicleManufactureDto,
    );
  }
  @ApiOperation({
    summary: 'Find all vehicle manufacture',
  })
  @Get('vehicleManufacture')
  async findAllVehicleManufacture() {
    return this.vehicleManagementService.findAllVehicleManufacture();
  }
  @ApiOperation({
    summary: 'Find one vehicle manufacture',
  })
  @Get('vehicleManufacture/:id')
  async findOneVehicleManufacture(@Param('id') id: string) {
    return this.vehicleManagementService.findOneVehicleManufacture(id);
  }
  @ApiOperation({
    summary: 'Update  vehicle manufacture by ID',
  })
  @Patch('vehicleManufacture/:id')
  async updateManufacture(
    @Param('id') id: string,
    @Body() updateVehicleManufactureDto: CreateVehicleManufactureDto,
  ) {
    return this.vehicleManagementService.updateVehicleManufacture(
      id,
      updateVehicleManufactureDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  vehicle manufacture by ID',
  })
  @Delete('vehicleManufacture/:id')
  async removeManufacture(@Param('id') id: string) {
    return this.vehicleManagementService.removeVehicleManufacture(id);
  }
  // Start of VehicleStatus  CRUD operations
  @ApiOperation({
    summary: 'Create a new vehicle status',
    description:
      'Vehicle status may include:AUCTIONED,RETURNED,GOOD CONDITION,BAD CONDITION,',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of vehicle status',
          example: 'GOOD CONDITION',
        },
        // password: {
        //   type:'string',
        //   title: 'User Password',
        //   example: 'Flu_Game@123!',
        // },
      },
    },
  })
  @Post('vehicleStatus')
  async createVehicalStatus(
    @Body() createVehicleStatusDto: CreateVehicleStatusDto,
  ) {
    // console.log(vehicleTypeDto);
    return this.vehicleManagementService.createVehicleStatus(
      createVehicleStatusDto,
    );
  }
  @ApiOperation({
    summary: 'Find all vehicle status',
  })
  @Get('vehicleStatus')
  async findAllVehicleStatus() {
    return this.vehicleManagementService.findAllVehicleStatus();
  }
  @ApiOperation({
    summary: 'Find one vehicle status',
  })
  @Get('vehicleStatus/:id')
  async findOneVehicleStatus(@Param('id') id: string) {
    return this.vehicleManagementService.findOneVehicleStatus(id);
  }
  @ApiOperation({
    summary: 'Update  vehicle status by ID',
  })
  @Patch('vehicleStatus/:id')
  async updateVehicleStatus(
    @Param('id') id: string,
    @Body() updateVehicleStatusDto: CreateVehicleStatusDto,
  ) {
    return this.vehicleManagementService.updateVehicleStatus(
      id,
      updateVehicleStatusDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  vehicle status by ID',
  })
  @Delete('vehicleStatus/:id')
  async removeVehicleStatus(@Param('id') id: string) {
    return this.vehicleManagementService.removeVehicleStatus(id);
  }

  // Start of status type CRUD operations
  @ApiOperation({
    summary: 'Create a new status',
    description:
      'Status types may include: SUBMITTED,PENDING,REJECTED,APPROVED',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of status type',
          example: 'SUBMITTED',
        },
        // password: {
        //   type: 'string',
        //   title: 'User Password',
        //   example: 'Flu_Game@123!',
        // },
      },
    },
  })
  @Post('statusType')
  async createStatusTpe(@Body() statusTypeDto: StatusTypeDto) {
    // console.log(statusTypeDto);
    return this.vehicleManagementService.createStatusType(statusTypeDto);
  }
  @ApiOperation({
    summary: 'Find all status types',
  })
  @Get('statusTypes')
  async findAllStatusTypes() {
    return this.vehicleManagementService.findAllStatusType();
  }
  @ApiOperation({
    summary: 'Find one status type',
  })
  @Get('statusType/:id')
  async findOneStatusType(@Param('id') id: string) {
    return this.vehicleManagementService.findOneStatusType(id);
  }
  @ApiOperation({
    summary: 'Update  status type by ID',
  })
  @Patch('statusType/:id')
  async updateStatus(
    @Param('id') id: string,
    @Body() updateStatusTypeDto: UpdateStatusTypeDto,
  ) {
    return this.vehicleManagementService.updateStatusType(
      id,
      updateStatusTypeDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  status type by ID',
  })
  @Delete('statusType/:id')
  async removeStatus(@Param('id') id: string) {
    return this.vehicleManagementService.removeStatusType(id);
  }
  // Start of ownership type CRUD operations
  @ApiOperation({
    summary: 'Create a new ownership type',
    description: 'Ownership types may include: Co-ownership,GR,GP',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of ownership type',
          example: 'Gorvement Vehicle',
        },
        code: {
          type: 'string',
          title: 'code',
          example: 'GR',
        },
      },
    },
  })
  @Post('ownershipType')
  async createOwnershipType(@Body() ownershipTypeDto: CreateOwnershipTypeDto) {
    // console.log(ownershipTypeDto);
    return this.vehicleManagementService.createOwnershipType(ownershipTypeDto);
  }
  @ApiOperation({
    summary: 'Find all ownership types',
  })
  @Get('ownershipTypes')
  async findAllOwnershipTypes() {
    return this.vehicleManagementService.findAllOwnershipType();
  }
  @ApiOperation({
    summary: 'Find one ownership type',
  })
  @Get('ownershipType/:id')
  async findOneOwnershipType(@Param('id') id: string) {
    return this.vehicleManagementService.findOneOwnershipType(id);
  }
  @ApiOperation({
    summary: 'Update  ownership type by ID',
  })
  @Patch('ownershipType/:id')
  async updateOwnership(
    @Param('id') id: string,
    @Body() updateOwnershipTypeDto: UpdateOwnershipTypeDto,
  ) {
    return this.vehicleManagementService.updateOwnershipType(
      id,
      updateOwnershipTypeDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  ownership type by ID',
  })
  @Delete('ownershipType/:id')
  async removeOwnership(@Param('id') id: string) {
    return this.vehicleManagementService.removeOwnershipType(id);
  }
  // start of request type CRUD operations
  @ApiOperation({
    summary: 'Create a new request type',
    description: 'Request types may include:Purchase, Available',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of request type',
          example: 'Purchase',
        },
        // password: {
        //   type: 'string',
        //   title: 'User Password',
        //   example: 'Flu_Game@123!',
        // },
      },
    },
  })
  @Post('requestType')
  async createRequestType(@Body() requestTypeDto: CreateRequestTypeDto) {
    // console.log(requestTypeDto);
    return this.vehicleManagementService.createRequestType(requestTypeDto);
  }
  @ApiOperation({
    summary: 'Find all request types',
  })
  @Get('requestTypes')
  async findAllRequestTypes() {
    return this.vehicleManagementService.findAllRequestType();
  }
  @ApiOperation({
    summary: 'Find one request type',
  })
  @Get('requestType/:id')
  async findOneRequestType(@Param('id') id: string) {
    return this.vehicleManagementService.findOneRequestType(id);
  }
  @ApiOperation({
    summary: 'Update  request type by ID',
  })
  @Patch('requestType/:id')
  async updateRequest(
    @Param('id') id: string,
    @Body() updateRequestTypeDto: UpdateRequestTypeDto,
  ) {
    return this.vehicleManagementService.updateRequestType(
      id,
      updateRequestTypeDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  request type by ID',
  })
  @Delete('requestType/:id')
  async removeRequest(@Param('id') id: string) {
    return this.vehicleManagementService.removeRequestType(id);
  }
  // start of request type CRUD operations
  @ApiOperation({
    summary: 'Create a new request type',
    description:
      'Request types may include:Vehicle Acquisition (VA), Vehicle Registration(VR),Vehicle Quantaly Report(VQR), Vehicle Disposal Request(VDR)',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of request type',
          example: 'Vehicle Acquisition',
        },
        code: {
          type: 'string',
          title: 'code',
          example: 'VA',
        },
      },
    },
  })
  @Post('requesttb')
  async createRequesttb(@Body() createRequestDto: CreateRequestDto) {
    console.log(createRequestDto);
    return this.vehicleManagementService.createRequesttb(createRequestDto);
  }
  @ApiOperation({
    summary: 'Find all request types',
  })
  @Get('requesttb')
  async findAllRequesttb() {
    return this.vehicleManagementService.findAllRequesttb();
  }
  @ApiOperation({
    summary: 'Find one request type',
  })
  @Get('requesttb/:id')
  async findOneRequesttb(@Param('id') id: string) {
    return this.vehicleManagementService.findOneRequesttb(id);
  }
  @ApiOperation({
    summary: 'Update  request type by ID',
  })
  @Patch('requesttb/:id')
  async updateRequesttb(
    @Param('id') id: string,
    @Body() updateRequestDto: UpdateRequestDto,
  ) {
    return this.vehicleManagementService.updateRequesttb(id, updateRequestDto);
  }
  @ApiOperation({
    summary: 'Delete  request type by ID',
  })
  @Delete('requesttb/:id')
  async removeRequesttb(@Param('id') id: string) {
    return this.vehicleManagementService.removeRequesttb(id);
  }
  // start of approval level CRUD operations
  @ApiOperation({
    summary: 'Create a new approval level',
    description:
      'Approval levels may include:\
      Institutional logistics(IL1)\
      Institutional CBM(CBM2)\
      Mininfra Fleet Mgt Senior Engineer(MFMSE3)\
      Mininfra DG Transport(MDGT4)\
      Mininfra Permanent Secretary(MPS5)\
      Minister of state(MOS6)\
      MINECOFIN Staff(MINECOFINS7)\
      RRA Staff(RRA8',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of approval level',
          example: 'Institutional logistics',
        },
        code: {
          type: 'string',
          title: 'code',
          example: 'IL1',
        },
      },
    },
  })
  @Post('approvalLevel')
  async createApprovalLevel(
    @Body() createApprovalLevelDto: CreateApprovalLevelDto,
  ) {
    console.log(createApprovalLevelDto);
    return this.vehicleManagementService.createApprovalLevel(
      createApprovalLevelDto,
    );
  }
  @ApiOperation({
    summary: 'Find all approval levels',
  })
  @Get('approvalLevel')
  async findAllApprovalLevel() {
    return this.vehicleManagementService.findAllApprovalLevel();
  }
  @ApiOperation({
    summary: 'Find one approval level',
  })
  @Get('approvalLevel/:id')
  async findOneApprovalLevel(@Param('id') id: string) {
    return this.vehicleManagementService.findOneApprovalLevel(id);
  }
  @ApiOperation({
    summary: 'Update  approval level by ID',
  })
  @Patch('approvalLevel/:id')
  async updateApprovalLevel(
    @Param('id') id: string,
    @Body() updateApprovalLevelDto: UpdateApprovalLevelDto,
  ) {
    return this.vehicleManagementService.updateApprovalLevel(
      id,
      updateApprovalLevelDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  approval level by ID',
  })
  @Delete('approvalLevel/:id')
  async removeApprovalLevel(@Param('id') id: string) {
    return this.vehicleManagementService.removeApprovalLevel(id);
  }
  // Vehicle disposal CRUD operations
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of disposal type',
          example: 'Public Auction',
        },
      },
    },
  })
  @Post('disposalType')
  async createDisposalType(
    @Body() createDisposalTypeDto: CreateDisposalTypeDto,
  ) {
    // console.log(createDisposalTypeDto);
    return this.vehicleManagementService.createDisposalType(
      createDisposalTypeDto,
    );
  }
  @ApiOperation({
    summary: 'Find all disposal types',
  })
  @Get('disposalType')
  async findAllDisposalType() {
    return this.vehicleManagementService.findAllDisposalType();
  }
  @ApiOperation({
    summary: 'Find one disposal type',
  })
  @Get('disposalType/:id')
  async findOneDisposalType(@Param('id') id: string) {
    return this.vehicleManagementService.findOneDisposalType(id);
  }

  @ApiOperation({
    summary: 'Update  disposal type by ID',
  })
  @Patch('disposalType/:id')
  async updateDisposalType(
    @Param('id') id: string,
    @Body() updateDisposalTypeDto: UpdateDisposalTypeDto,
  ) {
    return this.vehicleManagementService.updateDisposalType(
      id,
      updateDisposalTypeDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  disposal type by ID',
  })
  @Delete('disposalType/:id')
  async removeDisposalType(@Param('id') id: string) {
    return this.vehicleManagementService.removeDisposalType(id);
  }
  // CRUD operations for vehicle reasons
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of vehicle reason',
          example: 'Vehicle is damaged',
        },
      },
    },
  })
  @Post('disposalReason')
  async createDisposalReason(
    @Body() createDisposalReasonDto: CreateDisposalReasonLevelDto,
  ) {
    // console.log(createDisposalTypeDto);
    return this.vehicleManagementService.createDisposalReasons(
      createDisposalReasonDto,
    );
  }
  @ApiOperation({
    summary: 'Find all disposal reasons',
  })
  @Get('disposalReason')
  async findAllDisposalReason() {
    return this.vehicleManagementService.getAllDisposalReason();
  }
  @ApiOperation({
    summary: 'Find one disposal reason',
  })
  @Get('disposalReason/:id')
  async findOneDisposalReason(@Param('id') id: string) {
    return this.vehicleManagementService.getOneDisposalReason(id);
  }
  @ApiOperation({
    summary: 'Update  disposal reason by ID',
  })
  @Patch('disposalReason/:id')
  async updateDisposalReason(
    @Param('id') id: string,
    @Body() updateDisposalReasonDto: UpdateDisposalReasonLevelDto,
  ) {
    return this.vehicleManagementService.updateDisposalReason(
      id,
      updateDisposalReasonDto,
    );
  }
  @ApiOperation({
    summary: 'Delete  disposal reason by ID',
  })
  @Delete('disposalReason/:id')
  async removeDisposalReason(@Param('id') id: string) {
    return this.vehicleManagementService.deleteDisposalReason(id);
  }
  // perform crud operation on activity performed on vehicle
  @ApiTags('Activities on Vehicle Settings')
  @ApiOperation({
    summary: 'Create a new Activity',
    description: 'Activities to be performed on vehicle',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of ownership type',
          example: 'Fuel Consumption',
        },
        code: {
          type: 'string',
          title: 'code',
          example: 'FC',
        },
      },
    },
  })
  @Post('activityOnVehicle')
  async createActivityOnVehicle(
    @Body() activityOnVehicleDto: ActivityOnVehicleDto,
  ) {
    // console.log(ownershipTypeDto);
    return this.vehicleManagementService.createPerformedOnVehicle(
      activityOnVehicleDto,
    );
  }
  // getting all activity performed
  @ApiTags('Activities on Vehicle Settings')
  @ApiOperation({
    summary: 'Find all Activity ON Vehicle',
  })
  @Get('AllactivityOnVehicle')
  async findAllActivityPerformOnVehicle() {
    return this.vehicleManagementService.findAllActivityOnVehicle();
  }

  // getting activity performed by ID
  @ApiTags('Activities on Vehicle Settings')
  @ApiOperation({
    summary: 'Find activity on vehicle by ID',
  })
  @Get('activity-on-vehicle/:id')
  async findActivityOnVehicle(@Param('id') id: string) {
    return this.vehicleManagementService.findOneActivityOnVehicle(id);
  }

  // update activity on vehicle by ID
  @ApiTags('Activities on Vehicle Settings')
  @ApiOperation({
    summary: 'Update  Activity On Vehicle type by ID',
  })
  @Patch('activity-on-vehicle/:id')
  async updateActivityOnVehicle(
    @Param('id') id: string,
    @Body() activityOnVehicleDto: ActivityOnVehicleDto,
  ) {
    return this.vehicleManagementService.updateActivityOnVehicle(
      id,
      activityOnVehicleDto,
    );
  }
  // remove activity on vehicle
  @ApiTags('Activities on Vehicle Settings')
  @ApiOperation({
    summary: 'Delete  activity on Vehicle',
  })
  @Delete('activity-on-vehicle/:id')
  async removeActivityOnVehicle(@Param('id') id: string) {
    return this.vehicleManagementService.removeActivityOnVehicle(id);
  }

  // oil service operations
  @ApiTags('Oil Service Category')
  @ApiOperation({
    summary: 'Create a new Oil Service Category',
    description: 'Activities Oil Service Category',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        name: {
          type: 'string',
          title: 'This is example of ownership type',
          example: 'sample name on service category',
        },
        category: {
          type: 'string',
          title: 'cat',
          example: 'A',
        },
      },
    },
  })
  @Post('oilService')
  async createOilService(@Body() oilServiceCategoryDto: OilServiceCategoryDto) {
    // console.log(ownershipTypeDto);
    return this.vehicleManagementService.createOilService(
      oilServiceCategoryDto,
    );
  }
  // getting all oil service category
  @ApiTags('Oil Service Category')
  @ApiOperation({
    summary: 'Find all Oil Service Category',
  })
  @Get('AllOilServiceCategory')
  async findAllOilServiceCategory() {
    return this.vehicleManagementService.findAllOilCategory();
  }
  // find oil category by ID

  @ApiTags('Oil Service Category')
  @ApiOperation({
    summary: 'Find Oil Activity by ID',
  })
  @Get('oil-service-category/:id')
  async findOilServiceCategory(@Param('id') id: string) {
    return this.vehicleManagementService.findOneOilServiceCategory(id);
  }

  // updating oil service category

  @ApiTags('Oil Service Category')
  @ApiOperation({
    summary: 'Update  Oil Service Category',
  })
  @Patch('oil-service-category/:id')
  async updateOilServiceCategory(
    @Param('id') id: string,
    @Body() oilServiceCategoryDto: OilServiceCategoryDto,
  ) {
    return this.vehicleManagementService.updateOilServiceCategory(
      id,
      oilServiceCategoryDto,
    );
  }
  // deleting oil service category
  @ApiTags('Oil Service Category')
  @ApiOperation({
    summary: 'Delete  Oil Service Category',
  })
  @Delete('oil-deleting-service-category/:id')
  async removeOilServiceCategory(@Param('id') id: string) {
    return this.vehicleManagementService.removeOilServiceCategory(id);
  }
}
