import { AbstractRepository } from '@app/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
// import { Injectable } from '@nestjs/common';
import { VehicleType } from '../entities/vehicle-management /vehicle-types.entity';
import { OwnershipType } from '../entities/vehicle-management /vehicle-ownership.entity';
import { StatusType } from '../entities/vehicle-management /status.entity';
import { RequestType } from '../entities/vehicle-management /requestType.entity';
import { Requesttb } from '../entities/vehicle-management /request.entity';
// import { Comments } from '../entities/vehicle-management /comments.entity';
import { ApprovalLevel } from '../entities/vehicle-management /approvel-level.entity';
import { VehicleModel } from '../entities/vehicle-management /vehicle-model.entity';
import { VehicleManufacture } from '../entities/vehicle-management /vehicles-manifacturer.entity';
import { VehicleStatus } from '../entities/vehicle-management /vehicle-status.entity';
import { DisposalTypes } from '../entities/vehicle-management /disposal types.entity';
import { DisposalReasons } from '../entities/vehicle-management /disposal-reasons.entity';
import { OilServiceCategory } from '../entities/vehicle-management /oil-service-category.entity';
import { ActivityOnVehicle } from '../entities/vehicle-management /activities-on-vehicle.entity';

export class VehicleTypeRepository extends AbstractRepository<VehicleType> {
  constructor(
    @InjectRepository(VehicleType)
    vehicleTypeRepository: Repository<VehicleType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleTypeRepository);
  }
}

export class OwnershipTypeRepository extends AbstractRepository<OwnershipType> {
  constructor(
    @InjectRepository(OwnershipType)
    OwnershipTypeRepository: Repository<OwnershipType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, OwnershipTypeRepository);
  }
}
export class StatusTypeRepository extends AbstractRepository<StatusType> {
  constructor(
    @InjectRepository(StatusType)
    statusTypeRepository: Repository<StatusType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, statusTypeRepository);
  }
}

export class RequestTypeRepository extends AbstractRepository<RequestType> {
  constructor(
    @InjectRepository(RequestType)
    requestTypeRepository: Repository<RequestType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, requestTypeRepository);
  }
}
export class RequesttbRepository extends AbstractRepository<Requesttb> {
  constructor(
    @InjectRepository(Requesttb)
    requesttbRepository: Repository<Requesttb>,
    entityManager: EntityManager,
  ) {
    super(entityManager, requesttbRepository);
  }
}

// export class CommentsRepository extends AbstractRepository<Comments> {
//   constructor(
//     @InjectRepository(Comments)
//     commentsRepository: Repository<Comments>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, commentsRepository);
//   }
// }
export class ApprovalLevelRepository extends AbstractRepository<ApprovalLevel> {
  constructor(
    @InjectRepository(ApprovalLevel)
    approvalLevel: Repository<ApprovalLevel>,
    entityManager: EntityManager,
  ) {
    super(entityManager, approvalLevel);
  }
}

export class VehicleModelRepository extends AbstractRepository<VehicleModel> {
  constructor(
    @InjectRepository(VehicleModel)
    vehicleModel: Repository<VehicleModel>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleModel);
  }
}

export class VehicleManufactureRepository extends AbstractRepository<VehicleManufacture> {
  constructor(
    @InjectRepository(VehicleManufacture)
    vehicleManufacture: Repository<VehicleManufacture>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleManufacture);
  }
}

export class VehicleStatusRepository extends AbstractRepository<VehicleStatus> {
  constructor(
    @InjectRepository(VehicleStatus)
    vehicleStatus: Repository<VehicleStatus>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleStatus);
  }
}

export class DisposalTypesRepository extends AbstractRepository<DisposalTypes> {
  constructor(
    @InjectRepository(DisposalTypes)
    disposalTypes: Repository<DisposalTypes>,
    entityManager: EntityManager,
  ) {
    super(entityManager, disposalTypes);
  }
}

export class DisposalReasonsRepository extends AbstractRepository<DisposalReasons> {
  constructor(
    @InjectRepository(DisposalReasons)
    disposalReasons: Repository<DisposalReasons>,
    entityManager: EntityManager,
  ) {
    super(entityManager, disposalReasons);
  }
}

export class OilServiceCategoryRepository extends AbstractRepository<OilServiceCategory> {
  constructor(
    @InjectRepository(OilServiceCategory)
    oilServiceCategory: Repository<OilServiceCategory>,
    entityManager: EntityManager,
  ) {
    super(entityManager, oilServiceCategory);
  }
}

export class ActivityOnVehicleRepository extends AbstractRepository<ActivityOnVehicle> {
  constructor(
    @InjectRepository(ActivityOnVehicle)
    activityOnVehicle: Repository<ActivityOnVehicle>,
    entityManager: EntityManager,
  ) {
    super(entityManager, activityOnVehicle);
  }
}
