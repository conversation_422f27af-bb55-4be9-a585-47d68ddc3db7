import { Module } from '@nestjs/common';
import { VehicleManagementController } from './vehicle-management.controller';
import { VehicleManagementService } from './vehicle-management.service';
import { DatabaseModule } from '@app/common';
import { VehicleType } from '../entities/vehicle-management /vehicle-types.entity';
import { OwnershipType } from '../entities/vehicle-management /vehicle-ownership.entity';
import { StatusType } from '../entities/vehicle-management /status.entity';
import { Requesttb } from '../entities/vehicle-management /request.entity';
import { RequestType } from '../entities/vehicle-management /requestType.entity';
// import { Comments } from '../entities/vehicle-management /comments.entity';
import { ApprovalLevel } from '../entities/vehicle-management /approvel-level.entity';
import {
  ActivityOnVehicleRepository,
  ApprovalLevelRepository,
  DisposalReasonsRepository,
  DisposalTypesRepository,
  OilServiceCategoryRepository,
  // CommentsRepository,
  OwnershipTypeRepository,
  RequesttbRepository,
  RequestTypeRepository,
  StatusTypeRepository,
  VehicleManufactureRepository,
  VehicleModelRepository,
  VehicleStatusRepository,
  VehicleTypeRepository,
} from './vehicle-management.repository';
import { VehicleModel } from '../entities/vehicle-management /vehicle-model.entity';
import { VehicleManufacture } from '../entities/vehicle-management /vehicles-manifacturer.entity';
import { VehicleStatus } from '../entities/vehicle-management /vehicle-status.entity';
import { DisposalTypes } from '../entities/vehicle-management /disposal types.entity';
import { DisposalReasons } from '../entities/vehicle-management /disposal-reasons.entity';
import { OilServiceCategory } from '../entities/vehicle-management /oil-service-category.entity';
import { ActivityOnVehicle } from '../entities/vehicle-management /activities-on-vehicle.entity';
@Module({
  controllers: [VehicleManagementController],
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      VehicleType,
      OwnershipType,
      StatusType,
      Requesttb,
      RequestType,
      // Comments,
      ApprovalLevel,
      VehicleModel,
      VehicleManufacture,
      VehicleStatus,
      DisposalTypes,
      DisposalReasons,
      OilServiceCategory,
      ActivityOnVehicle,
    ]),
  ],
  providers: [
    VehicleManagementService,
    VehicleTypeRepository,
    OwnershipTypeRepository,
    StatusTypeRepository,
    RequestTypeRepository,
    RequesttbRepository,
    // CommentsRepository,
    ApprovalLevelRepository,
    VehicleStatusRepository,
    VehicleManufactureRepository,
    VehicleModelRepository,
    DisposalTypesRepository,
    DisposalReasonsRepository,
    OilServiceCategoryRepository,
    ActivityOnVehicleRepository,
  ],
  exports: [
    VehicleManagementService,
    RequestTypeRepository,
    OwnershipTypeRepository,
    StatusTypeRepository,
    ApprovalLevelRepository,
    VehicleTypeRepository,
    VehicleStatusRepository,
    VehicleManufactureRepository,
    VehicleModelRepository,
    ActivityOnVehicleRepository,
    OilServiceCategoryRepository,
  ],
})
export class VehicleManagementModule {}
