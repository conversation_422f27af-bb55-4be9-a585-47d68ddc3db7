import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { FleetMgmntController } from './fleet-mgmnt.controller';
import { FleetMgmntService } from './fleet-mgmnt.service';
import { VehicleManagementModule } from './vehicle-management/vehicle-management.module';
// import { VehicleAcquisitionModule } from './vehicle-acquisition/vehicle-acquisition.module';
import { AUTH_SERVICE } from '@app/common/constants';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import {
  ActivityRecodingRepository,
  AuctionReportRepository,
  CostBenefitRepository,
  DriverDetailsRepository,
  HiringCostRepository,
  MaintenanceActivitiesRepository,
  ProjectExtensionRepository,
  RegisteredVehicleRepository,
  ReportActivitiesRepository,
  RequestedVehicleRepository,
  StaffRelavanceRepository,
  VehicleAcquisitionRepository,
  VehicleDisposalRepository,
  VehicleQuarterlyReportRepository,
  VehicleRegRequestRepository,
  VehilceNumberPerAccquisitionRepository,
} from './fleet-mgmnt.repository';
import { VehicleAcquisition } from './entities/vehicle-acquisition/vehicle-acquisition.entity';
import { DatabaseModule } from '@app/common';
import { RequestedVehicle } from './entities/vehicle-acquisition/requested-vehicle.entity';
import { ApprovalController } from './approval.controller';
import { AquistionApprovalService } from './approval.service';
import { ActivityRecoding } from './entities/comments/activity-recording.entity';
import { VehicleRegRequest } from './entities/vehicles-registration /vehicle-reg-request';
import { RegisteredVehicle } from './entities/vehicles-registration /registred-vehicle';
import { VehicleManufacture } from './entities/vehicle-management /vehicles-manifacturer.entity';
import { VehicleModel } from './entities/vehicle-management /vehicle-model.entity';
import { VehicleStatus } from './entities/vehicle-management /vehicle-status.entity';
import { DisposalTypes } from './entities/vehicle-management /disposal types.entity';
import { DisposalReasons } from './entities/vehicle-management /disposal-reasons.entity';
import { StatusType } from './entities/vehicle-management /status.entity';
import { ApprovalLevel } from './entities/vehicle-management /approvel-level.entity';
import {
  ActivityOnVehicleRepository,
  DisposalReasonsRepository,
  DisposalTypesRepository,
  OilServiceCategoryRepository,
} from './vehicle-management/vehicle-management.repository';
import { VehicleDisposal } from './entities/vehicle-disposal/vehicle-disposal.entity';
import { DisposalManagementService } from './disposal-management.service';
import { DisposalManagementController } from './disposal-management.controller';
import { AuctionReport } from './entities/auction-report/auction-report.entity';
import { VehilceNumberPerAccquisition } from './entities/vehicle-acquisition/vehicleNumber-perAquisition.entity';
import { VehicleQuarterlyReport } from './entities/vehicale-status-report/vehicle-status-report.entity';
import { TasksService } from './schedule.service';
import { ScheduleModule } from '@nestjs/schedule';
import { ProjectExtension } from './entities/project-extension/project-extension.entity';
import { CostBenefit } from './entities/vehicle-acquisition-relevance/cost-benefity.entity';
import { StaffRelavance } from './entities/vehicle-acquisition-relevance/staff-relavance.entity';
import { DriverDetails } from './entities/vehicle-acquisition-relevance/drive-details.entity';
import { HiringCost } from './entities/vehicle-acquisition-relevance/hiring-cost.entity';
import { MaintenanceActivities } from './entities/maintenance-activities/maintenance-activities.entity';
import { OilServiceCategory } from './entities/vehicle-management /oil-service-category.entity';
import { ActivityOnVehicle } from './entities/vehicle-management /activities-on-vehicle.entity';
import { ReportActivities } from './entities/vehicale-status-report/report-activities.entity ';
@Module({
  imports: [
    VehicleManagementModule,
    DatabaseModule,
    HttpModule,
    ScheduleModule.forRoot(),
    DatabaseModule.forFeature([
      VehicleAcquisition,
      RequestedVehicle,
      ActivityRecoding,
      VehicleRegRequest,
      RegisteredVehicle,
      VehicleManufacture,
      VehicleModel,
      VehicleStatus,
      DisposalTypes,
      DisposalReasons,
      StatusType,
      ApprovalLevel,
      VehicleDisposal,
      // RequestType,
      AuctionReport,
      VehilceNumberPerAccquisition,
      VehicleQuarterlyReport,
      ProjectExtension,
      CostBenefit,
      StaffRelavance,
      DriverDetails,
      HiringCost,
      MaintenanceActivities,
      OilServiceCategory,
      ActivityOnVehicle,
      ReportActivities,
    ]),
    // VehicleAcquisitionModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ClientsModule.registerAsync([
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('AUTH_HOST'),
            port: configService.get('AUTH_PORT'),
          },
        }),
        inject: [ConfigService],
      },
      // {
      //   name: FLEETAPIGATWAY_SERVICE,
      //   useFactory: (configService: ConfigService) => ({
      //     transport: Transport.TCP,
      //     options: {
      //       host: configService.get('PAYMENTS_HOST'),
      //       port: configService.get('PAYMENTS_PORT'),
      //     },
      //   }),
      //   inject: [ConfigService],
      // },
      // {
      //   name: NOTIFICATIONS_SERVICE,
      //   useFactory: (configService: ConfigService) => ({
      //     transport: Transport.TCP,
      //     options: {
      //       host: configService.get('NOTIFICATIONS_HOST'),
      //       port: configService.get('NOTIFICATIONS_PORT'),
      //     },
      //   }),
      //   inject: [ConfigService],
      // },
      // {
      //   name: DOCUMENTS_SERVICE,
      //   useFactory: (configService: ConfigService) => ({
      //     transport: Transport.TCP,
      //     options: {
      //       host: configService.get('DOCUMENTS_HOST'),
      //       port: configService.get('DOCUMENTS_PORT'),
      //     },
      //   }),
      //   inject: [ConfigService],
      // },
    ]),
  ],

  controllers: [
    FleetMgmntController,
    ApprovalController,
    DisposalManagementController,
  ],
  providers: [
    FleetMgmntService,
    AquistionApprovalService,
    DisposalManagementService,
    VehicleAcquisitionRepository,
    RequestedVehicleRepository,
    ActivityRecodingRepository,
    VehicleRegRequestRepository,
    RegisteredVehicleRepository,
    VehicleDisposalRepository,
    DisposalTypesRepository,
    DisposalReasonsRepository,
    AuctionReportRepository,
    VehilceNumberPerAccquisitionRepository,
    VehicleQuarterlyReportRepository,
    TasksService,
    ProjectExtensionRepository,
    CostBenefitRepository,
    StaffRelavanceRepository,
    DriverDetailsRepository,
    HiringCostRepository,
    MaintenanceActivitiesRepository,
    OilServiceCategoryRepository,
    ActivityOnVehicleRepository,
    ReportActivitiesRepository,
  ],
  exports: [FleetMgmntService, DisposalManagementService],
})
export class FleetMgmntModule {}
