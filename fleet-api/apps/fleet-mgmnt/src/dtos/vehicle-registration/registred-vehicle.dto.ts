import { IsISO8601, <PERSON><PERSON>otEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class VehicleRegistrationDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field vehicleRegRequest ID is required' })
  @IsNotEmpty({ message: 'The field vehicleRegRequest ID cannot be empty' })
  vehicleRegRequest: string;

  @ApiProperty({ description: 'The field beneficiaryAgency Id is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency Id cannot be empty' })
  beneficiaryAgencyId: string;

  @ApiProperty({ description: 'The field beneficiaryAgency is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency cannot be empty' })
  beneficiaryAgency: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsNotEmpty({ message: 'The field ownershipType cannot be empty' })
  ownershipType: string;

  @ApiProperty({ description: 'The field vehicleType is required' })
  @IsNotEmpty({ message: 'The field vehicleType cannot be empty' })
  vehicleType: string;
  @ApiProperty({ description: 'The field fuelType is required' })
  @IsNotEmpty({ message: 'The field fuelType cannot be empty' })
  fuelType: string;

  @ApiProperty({ description: 'The field vehicleManufacture is required' })
  @IsNotEmpty({ message: 'The field vehicleManufacture cannot be empty' })
  vehicleManufacture: string;

  @ApiProperty({ description: 'The field vehicleModel is required' })
  @IsNotEmpty({ message: 'The field vehicleModel cannot be empty' })
  vehicleModel: string;

  @ApiProperty({ description: 'The field chassisNumber is required' })
  @IsNotEmpty({ message: 'The field chassisNumber cannot be empty' })
  chassisNumber: string;

  @ApiProperty({ description: 'The field engineNumber is required' })
  @IsNotEmpty({ message: 'The field engineNumber cannot be empty' })
  engineNumber: string;

  @ApiProperty({ description: 'The field transmissionType is required' })
  @IsNotEmpty({ message: 'The field transmissionType cannot be empty' })
  transmissionType: string;

  @ApiProperty({ description: 'The field manufactureYear is required' })
  @IsNotEmpty({ message: 'The field manufactureYear cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message: 'The field manufactureYear must be a valid ISO 8601 date string',
    },
  )
  manufactureYear: Date;

  @ApiProperty({ description: 'The field odometerReading is required' })
  @IsNotEmpty({ message: 'The field odometerReading cannot be empty' })
  odometerReading: string;

  @ApiProperty({ description: 'The field acquisitionDate is required' })
  @IsNotEmpty({ message: 'The field acquisitionDate cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message: 'The field acquisitionDate must be a valid ISO 8601 date string',
    },
  )
  acquisitionDate: Date;

  @ApiProperty({ description: 'The field invoiceNumber is required' })
  @IsNotEmpty({ message: 'The field invoiceNumber cannot be empty' })
  invoiceNumber: string;

  @ApiProperty({ description: 'The field invoiceDate is required' })
  @IsNotEmpty({ message: 'The field invoiceDate cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message: 'The field invoiceDate must be a valid ISO 8601 date string',
    },
  )
  invoiceDate: Date;

  @ApiProperty({
    description: 'The field customsDeclarationNumber is required',
  })
  @IsNotEmpty({ message: 'The field customsDeclarationNumber cannot be empty' })
  customsDeclarationNumber: string;

  @ApiProperty({ description: 'The field customsDeclarationDate is required' })
  @IsNotEmpty({ message: 'The field customsDeclarationDate cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message:
        'The field customsDeclarationDate must be a valid ISO 8601 date string',
    },
  )
  customsDeclarationDate: Date;

  @ApiProperty({ description: 'The field declaredAmount is required' })
  @IsNotEmpty({ message: 'The field declaredAmount cannot be empty' })
  declaredAmount: number;

  @ApiProperty({ description: 'The field projectName is required' })
  @IsOptional({ message: 'The field projectName cannot be empty' })
  projectName: string;

  @ApiProperty({ description: 'The field projectStartDate is required' })
  @IsOptional({ message: 'The field projectStartDate cannot be empty' })
  projectStartDate: Date;

  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsOptional({ message: 'The field projectDescription cannot be empty' })
  projectDescription: string;

  @ApiProperty({ description: 'The field project End Date is required' })
  @IsOptional({ message: 'The field project End Date cannot be empty' })
  projectEndDate: Date;

  // @ApiProperty({ description: 'The field pickCardNumber is required' })
  // @IsOptional({ message: 'The field pickCardNumber cannot be empty' })
  // pickCardNumber: string;

  //   @ApiProperty({ description: 'The field registrationStatus is required' })
  //   @IsOptional({ message: 'The field registrationStatus cannot be empty' })
  //   registrationStatus: string;
}

export class UpdateRegistredVehicleDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  // @ApiProperty({ description: 'The field vehicleRegRequest ID is required' })
  // @IsNotEmpty({ message: 'The field vehicleRegRequest ID cannot be empty' })
  // vehicleRegRequest: string;

  @ApiProperty({ description: 'The field beneficiaryAgency Id is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency Id cannot be empty' })
  beneficiaryAgencyId: string;

  @ApiProperty({ description: 'The field beneficiaryAgency is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency cannot be empty' })
  beneficiaryAgency: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsNotEmpty({ message: 'The field ownershipType cannot be empty' })
  ownershipType: string;

  @ApiProperty({ description: 'The field vehicleType is required' })
  @IsNotEmpty({ message: 'The field vehicleType cannot be empty' })
  vehicleType: string;
  @ApiProperty({ description: 'The field fuelType is required' })
  @IsNotEmpty({ message: 'The field fuelType cannot be empty' })
  fuelType: string;
  @ApiProperty({ description: 'The field fuelType is required' })
  @IsOptional({ message: 'The field fuelType cannot be empty' })
  plateNumber: string;

  @ApiProperty({ description: 'The field vehicleManufacture is required' })
  @IsOptional({ message: 'The field fuelType cannot be empty' })
  vehicleManufacture: string;

  @ApiProperty({ description: 'The field vehicleModel is required' })
  @IsOptional({ message: 'The field vehicleModel cannot be empty' })
  vehicleModel: string;

  @ApiProperty({ description: 'The field chassisNumber is required' })
  @IsOptional({ message: 'The field chassisNumber cannot be empty' })
  chassisNumber: string;

  @ApiProperty({ description: 'The field engineNumber is required' })
  @IsOptional({ message: 'The field engineNumber can be empty' })
  engineNumber: string;

  @ApiProperty({ description: 'The field transmissionType is required' })
  @IsOptional({ message: 'The field transmissionType cannot be empty' })
  transmissionType: string;

  @ApiProperty({ description: 'The field manufactureYear is required' })
  @IsOptional({ message: 'The field fuelType cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message: 'The field manufactureYear must be a valid ISO 8601 date string',
    },
  )
  manufactureYear: Date;

  @ApiProperty({ description: 'The field odometerReading is required' })
  @IsOptional({ message: 'The field odometerReading cannot be empty' })
  odometerReading: string;

  @ApiProperty({ description: 'The field acquisitionDate is required' })
  @IsOptional({ message: 'The field acquisitionDate cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message: 'The field acquisitionDate must be a valid ISO 8601 date string',
    },
  )
  acquisitionDate: Date;

  @ApiProperty({ description: 'The field invoiceNumber is required' })
  @IsOptional({ message: 'The field invoiceNumber cannot be empty' })
  invoiceNumber: string;

  @ApiProperty({ description: 'The field invoiceDate is required' })
  @IsOptional({ message: 'The field invoiceDate cannot be empty' })
  invoiceDate: Date;

  @ApiProperty({
    description: 'The field customsDeclarationNumber is required',
  })
  @IsOptional({ message: 'The field customsDeclarationNumber cannot be empty' })
  customsDeclarationNumber: string;

  @ApiProperty({ description: 'The field customsDeclarationDate is required' })
  @IsOptional({ message: 'The field customsDeclarationDate cannot be empty' })
  customsDeclarationDate: Date;

  @ApiProperty({ description: 'The field declaredAmount is required' })
  @IsOptional({ message: 'The field declaredAmount cannot be empty' })
  declaredAmount: number;

  @ApiProperty({ description: 'The field projectName is required' })
  @IsOptional({ message: 'The field projectName cannot be empty' })
  projectName: string;

  @ApiProperty({ description: 'The field projectStartDate is required' })
  @IsOptional({ message: 'The field projectStartDate cannot be empty' })
  projectStartDate: Date;

  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsOptional({ message: 'The field projectDescription cannot be empty' })
  projectDescription: string;

  @ApiProperty({ description: 'The field project End Date is required' })
  @IsOptional({ message: 'The field project End Date cannot be empty' })
  projectEndDate: Date;

  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field isVehicleActive cannot be empty' })
  isVehicleActive: boolean;
  @ApiProperty({ description: 'The field vehicleStatus is required' })
  @IsOptional({ message: 'The field vehicleStatus cannot be empty' })
  vehicleStatus: string;
  @ApiProperty({ description: 'The field pickCardNumber is required' })
  @IsOptional({ message: 'The field pickCardNumber cannot be empty' })
  pickCardNumber: string;

  // @ApiProperty({ description: 'The field pickCardNumber is required' })
  // @IsOptional({ message: 'The field pickCardNumber cannot be empty' })
  // pickCardNumber: string;

  //   @ApiProperty({ description: 'The field registrationStatus is required' })
  //   @IsOptional({ message: 'The field registrationStatus cannot be empty' })
  //   registrationStatus: string;
}

// existing   vehicle registration

export class ExistingVehicleRegistrationDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  // @ApiProperty({ description: 'The field vehicleRegRequest ID is required' })
  // @IsNotEmpty({ message: 'The field vehicleRegRequest ID cannot be empty' })
  // vehicleRegRequest: string;

  @ApiProperty({ description: 'The field beneficiaryAgency Id is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency Id cannot be empty' })
  beneficiaryAgencyId: string;

  @ApiProperty({ description: 'The field beneficiaryAgency is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency cannot be empty' })
  beneficiaryAgency: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsNotEmpty({ message: 'The field ownershipType cannot be empty' })
  ownershipType: string;

  @ApiProperty({ description: 'The field vehicleType is required' })
  @IsNotEmpty({ message: 'The field vehicleType cannot be empty' })
  vehicleType: string;
  @ApiProperty({ description: 'The field fuelType is required' })
  @IsNotEmpty({ message: 'The field fuelType cannot be empty' })
  fuelType: string;
  @ApiProperty({ description: 'The field fuelType is required' })
  @IsOptional({ message: 'The field fuelType can be empty' })
  plateNumber: string;

  @ApiProperty({ description: 'The field vehicleManufacture is required' })
  @IsNotEmpty({ message: 'The field vehicleManufacture cannot be empty' })
  vehicleManufacture: string;

  @ApiProperty({ description: 'The field vehicleModel is required' })
  @IsOptional({ message: 'The field vehicleModel cannot be empty' })
  vehicleModel: string;

  @ApiProperty({ description: 'The field chassisNumber is required' })
  @IsOptional({ message: 'The field chassisNumber cannot be empty' })
  chassisNumber: string;

  @ApiProperty({ description: 'The field engineNumber is required' })
  @IsOptional({ message: 'The field engineNumber can be empty' })
  engineNumber: string;

  @ApiProperty({ description: 'The field transmissionType is required' })
  @IsOptional({ message: 'The field transmissionType cannot be empty' })
  transmissionType: string;

  @ApiProperty({ description: 'The field manufactureYear is required' })
  @IsNotEmpty({ message: 'The field manufactureYear cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message: 'The field manufactureYear must be a valid ISO 8601 date string',
    },
  )
  manufactureYear: Date;

  @ApiProperty({ description: 'The field odometerReading is required' })
  @IsOptional({ message: 'The field odometerReading cannot be empty' })
  odometerReading: string;

  @ApiProperty({ description: 'The field acquisitionDate is required' })
  @IsOptional({ message: 'The field acquisitionDate cannot be empty' })
  @IsISO8601(
    { strict: true },
    {
      message: 'The field acquisitionDate must be a valid ISO 8601 date string',
    },
  )
  acquisitionDate: Date;

  @ApiProperty({ description: 'The field invoiceNumber is required' })
  @IsOptional({ message: 'The field invoiceNumber cannot be empty' })
  invoiceNumber: string;

  @ApiProperty({ description: 'The field invoiceDate is required' })
  @IsOptional({ message: 'The field invoiceDate cannot be empty' })
  invoiceDate: Date;

  @ApiProperty({
    description: 'The field customsDeclarationNumber is required',
  })
  @IsOptional({ message: 'The field customsDeclarationNumber cannot be empty' })
  customsDeclarationNumber: string;

  @ApiProperty({ description: 'The field customsDeclarationDate is required' })
  @IsOptional({ message: 'The field customsDeclarationDate cannot be empty' })
  customsDeclarationDate: Date;

  @ApiProperty({ description: 'The field declaredAmount is required' })
  @IsOptional({ message: 'The field declaredAmount cannot be empty' })
  declaredAmount: number;

  @ApiProperty({ description: 'The field projectName is required' })
  @IsOptional({ message: 'The field projectName cannot be empty' })
  projectName: string;

  @ApiProperty({ description: 'The field projectStartDate is required' })
  @IsOptional({ message: 'The field projectStartDate cannot be empty' })
  projectStartDate: Date;

  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsOptional({ message: 'The field projectDescription cannot be empty' })
  projectDescription: string;

  @ApiProperty({ description: 'The field project End Date is required' })
  @IsOptional({ message: 'The field project End Date cannot be empty' })
  projectEndDate: Date;

  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field isVehicleActive cannot be empty' })
  isVehicleActive: boolean;
  @ApiProperty({ description: 'The field vehicleStatus is required' })
  @IsOptional({ message: 'The field vehicleStatus cannot be empty' })
  vehicleStatus: string;
  @ApiProperty({ description: 'The field pickCardNumber is required' })
  @IsOptional({ message: 'The field pickCardNumber cannot be empty' })
  pickCardNumber: string;

  // @ApiProperty({ description: 'The field pickCardNumber is required' })
  // @IsOptional({ message: 'The field pickCardNumber cannot be empty' })
  // pickCardNumber: string;

  //   @ApiProperty({ description: 'The field registrationStatus is required' })
  //   @IsOptional({ message: 'The field registrationStatus cannot be empty' })
  //   registrationStatus: string;
}
