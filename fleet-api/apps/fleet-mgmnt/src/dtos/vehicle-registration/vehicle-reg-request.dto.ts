import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateVehicleRegRequestDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  userId: string;
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field institution cannot be empty' })
  institution: string;
  @ApiProperty({ description: 'The field institutionId is required' })
  @IsNotEmpty({ message: 'The field institutionId cannot be empty' })
  institutionId: string;
  @ApiProperty({ description: 'The field description is required' })
  @IsOptional({ message: 'The field VehicleAcquisition cannot be empty' })
  vehicleAcquisition: string;
}

// export class UpdateAcquisitionDto {
//   @IsOptional({ message: 'The field id cannot be empty' })
//   id: string;

//   @ApiProperty({ description: 'The field requestTypeId is required' })
//   @IsNotEmpty({ message: 'The field requestTypeId cannot be empty' })
//   requestType: string;
//   @ApiProperty({ description: 'The field ownershipTypeId is required' })
//   @IsNotEmpty({ message: 'The field ownershipTypeId cannot be empty' })
//   ownershipType: string;
//   @ApiProperty({ description: 'The field ownershipTypeId is required' })
//   @IsOptional({ message: 'The field description can be empty' })
//   description: string;
// }
