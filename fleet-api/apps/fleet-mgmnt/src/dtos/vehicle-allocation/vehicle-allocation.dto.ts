import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class VehicleAllocationDto {
  // @ApiProperty({ description: 'The field  Id is required' })
  // @IsNotEmpty({ message: 'The field Id cannot be empty' })
  // id: string;
  @ApiProperty({ description: 'The field acquisitionId Id is required' })
  @IsNotEmpty({ message: 'The field acquisitionId Id cannot be empty' })
  acquisitionId: string;

  @ApiProperty({ description: 'The field beneficiaryAgency Id is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency Id cannot be empty' })
  beneficiaryAgencyId: string;

  @ApiProperty({ description: 'The field beneficiaryAgency is required' })
  @IsNotEmpty({ message: 'The field beneficiaryAgency cannot be empty' })
  beneficiaryAgency: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsNotEmpty({ message: 'The field ownershipType cannot be empty' })
  ownershipType: string;

  @ApiProperty({ description: 'The field projectName is required' })
  @IsOptional({ message: 'The field projectName cannot be empty' })
  projectName: string;

  @ApiProperty({ description: 'The field pickCardNumber is required' })
  @IsOptional({ message: 'The field pickCardNumber cannot be empty' })
  pickCardNumber: string;

  @ApiProperty({ description: 'The field pickCardNumber is required' })
  @IsOptional({ message: 'The field pickCardNumber cannot be empty' })
  plateNumber: string;

  @ApiProperty({ description: 'The field projectStartDate is required' })
  @IsOptional({ message: 'The field projectStartDate cannot be empty' })
  projectStartDate: Date;

  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsOptional({ message: 'The field projectDescription cannot be empty' })
  projectDescription: string;

  @ApiProperty({ description: 'The field project End Date is required' })
  @IsOptional({ message: 'The field project End Date cannot be empty' })
  projectEndDate: Date;
}
