import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ReportActivitiesDto {
  // @ApiProperty({ description: 'The field  Id is required' })
  // @IsNotEmpty({ message: 'The field Id cannot be empty' })
  // id: string;
  @ApiProperty({
    description: 'The field  vehicleQuarterlyReportId is required',
  })
  @IsNotEmpty({ message: 'The field vehicleQuarterlyReportId be empty' })
  vehicleQuarterlyReportId: string;

  @ApiProperty({ description: 'The field maintenanceActivitiesId is required' })
  @IsOptional({ message: 'The field maintenanceActivitiesId can be empty' })
  maintenanceActivitiesId: string;
}
