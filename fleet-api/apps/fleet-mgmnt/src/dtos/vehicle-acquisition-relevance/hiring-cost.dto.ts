import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class HiringCostDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field isDriveAvailable is required' })
  @IsNotEmpty({ message: 'The field isDriveAvailable cannot be empty' })
  hiringCost: number;
  @ApiProperty({ description: 'The field VehicleAcquisition name is required' })
  @IsOptional({ message: 'The field VehicleAcquisition cannot be empty' })
  VehicleAcquisitionID?: string;
  @ApiProperty({ description: 'The field RequestedVehicle name is required' })
  @IsOptional({ message: 'The field RequestedVehicle cannot be empty' })
  RequestedVehicleID?: string;
}
