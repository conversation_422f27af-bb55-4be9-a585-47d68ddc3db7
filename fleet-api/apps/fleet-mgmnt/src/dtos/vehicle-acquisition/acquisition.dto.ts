import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAcquisitionDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  userId: string;
  @ApiProperty({ description: 'The field requestTypeId is required' })
  @IsNotEmpty({ message: 'The field requestTypeId cannot be empty' })
  requestType: string;
  @ApiProperty({ description: 'The field ownershipTypeId is required' })
  @IsNotEmpty({ message: 'The field ownershipTypeId cannot be empty' })
  ownershipType: string;
  // @ApiProperty({ description: 'The field name is required' })
  // @IsNotEmpty({ message: 'The field approvalLevelCode cannot be empty' })
  // approvalLevelCode: string;
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field approvalLevelCode cannot be empty' })
  institution: string;
  @IsNotEmpty({ message: 'The field approvalLevelCode cannot be empty' })
  institutionId: string;
  @ApiProperty({ description: 'The field description is required' })
  @IsNotEmpty({ message: 'The field description cannot be empty' })
  description?: string;
}

export class UpdateAcquisitionDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field requestTypeId is required' })
  @IsNotEmpty({ message: 'The field requestTypeId cannot be empty' })
  requestType: string;
  @ApiProperty({ description: 'The field ownershipTypeId is required' })
  @IsNotEmpty({ message: 'The field ownershipTypeId cannot be empty' })
  ownershipType: string;
  @ApiProperty({ description: 'The field ownershipTypeId is required' })
  @IsOptional({ message: 'The field description can be empty' })
  description: string;
}

// ===================================================================

export class searcAcquisionAndFilterDTO {
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  searchString?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  id?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  userId?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  requestType?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  ownershipType?: string;
  // @ApiProperty({ description: 'The field name is required' })
  // @IsNotEmpty({ message: 'The field approvalLevelCode cannot be empty' })
  // approvalLevelCode: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  institution?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  acquisitionStatus?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvalLeval?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  statusType?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  isAllVehicleregrequestSubmitted?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  createdDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  createdDateEnd?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  submitedDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  submitedDateEnd?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvalDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvedDateEnd?: string;
}
