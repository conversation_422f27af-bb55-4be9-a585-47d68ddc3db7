import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOwnershipTypeDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  code: string;

  //   @ApiProperty({ description: 'The field code is required' })
  //   @IsNotEmpty({ message: 'The field code cannot be empty' })
  //   code: string;
}
export class UpdateOwnershipTypeDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  code: string;

  //   @ApiProperty({ description: 'The field code is required' })
  //   @IsNotEmpty({ message: 'The field code cannot be empty' })
  //   code: string;
}
