import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class OilServiceCategoryDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional({ message: 'The field name cannot be empty' })
  name?: string;
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field category cannot be empty' })
  category: string;
}
