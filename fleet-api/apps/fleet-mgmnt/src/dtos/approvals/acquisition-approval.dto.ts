import { IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AcquisitionApprovalDto {
  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  userId: string;
  @ApiProperty({ description: 'The field acqyisitionId is required' })
  @IsNotEmpty({ message: 'The field acquisitionId cannot be empty' })
  acquisitionId: string;
  @ApiProperty({ description: 'The field comments is required' })
  @IsNotEmpty({ message: 'The field comments cannot be empty' })
  comments: string;
  @ApiProperty({ description: 'The field acqyisitionId is required' })
  @IsNotEmpty({ message: 'The field acquisitionId cannot be empty' })
  decision: string;
}
