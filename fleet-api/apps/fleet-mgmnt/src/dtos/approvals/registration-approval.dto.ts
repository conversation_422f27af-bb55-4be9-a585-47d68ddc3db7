import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class VehicleRegApprovalDto {
  @ApiProperty({ description: 'The field vehicle Id is required' })
  @IsNotEmpty({ message: 'The field vehicle Id cannot be empty' })
  vehicleId: string;
  @ApiProperty({ description: 'The field userId Id is required' })
  @IsNotEmpty({ message: 'The field userId Id cannot be empty' })
  userId: string;
  @ApiProperty({ description: 'The field comments is option' })
  @IsOptional({ message: 'The field comments can be empty' })
  comments: string;

  @ApiProperty({ description: 'The field plate Number is option' })
  @IsOptional({ message: 'The field plate Number can be empty' })
  plateNumber: string;

  @ApiProperty({ description: 'The field pick Card Number is option' })
  @IsOptional({ message: 'The field pick Card Number can be empty' })
  pickCardNumber: string;

  @ApiProperty({ description: 'The field decision is required' })
  @IsNotEmpty({ message: 'The field decision cannot be empty' })
  decision: string;
}

export class ExistingVehicleRegApprovalDto {
  @ApiProperty({ description: 'The field vehicle Id is required' })
  @IsNotEmpty({ message: 'The field vehicle Id cannot be empty' })
  vehicleId: string;
  @ApiProperty({ description: 'The field userId Id is required' })
  @IsNotEmpty({ message: 'The field userId Id cannot be empty' })
  userId: string;
  @ApiProperty({ description: 'The field comments is option' })
  @IsOptional({ message: 'The field comments can be empty' })
  comments: string;

  @ApiProperty({ description: 'The field vehicle Status is option' })
  @IsOptional({ message: 'The field vehicle Status can be empty' })
  vehicleStatusId: string;

  @ApiProperty({ description: 'The field pick Card Number is option' })
  @IsOptional({ message: 'The field pick Card Number can be empty' })
  isVehicleActive: boolean;

  @ApiProperty({ description: 'The field decision is required' })
  @IsNotEmpty({ message: 'The field decision cannot be empty' })
  decision: string;
}
