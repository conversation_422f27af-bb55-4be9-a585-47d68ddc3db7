import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AuctionReportApprovalDto {
  @ApiProperty({ description: 'The field disposal Id is required' })
  @IsNotEmpty({ message: 'The field disposal Id cannot be empty' })
  auctionReportId: string;
  @ApiProperty({ description: 'The field userId Id is required' })
  @IsNotEmpty({ message: 'The field userId Id cannot be empty' })
  userId: string;
  @ApiProperty({ description: 'The field comments is option' })
  @IsOptional({ message: 'The field comments can be empty' })
  comments: string;

  @ApiProperty({ description: 'The field decision is required' })
  @IsNotEmpty({ message: 'The field decision cannot be empty' })
  decision: string;
}
