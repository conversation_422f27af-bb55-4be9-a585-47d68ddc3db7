import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class MaintenanceActivitiesDTO {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field vehicleID is required' })
  @IsNotEmpty({ message: 'The field vehicleID cannot be empty' })
  vehicleID: string;
  @ApiProperty({ description: 'The field maintenanceCost is option' })
  @IsOptional({ message: 'The field activityOnVehicleID  is option' })
  activityOnVehicleID?: string;
  @ApiProperty({ description: 'The field fuelCost is required' })
  @IsOptional({ message: 'The field oilServiceCategoryID be empty' })
  oilServiceCategoryID?: string;
  @ApiProperty({ description: 'The field fuelQuantity name is required' })
  @IsOptional({ message: 'The field fuelQuantity cannot be empty' })
  fuelQuantity?: number;
  // new field description
  @ApiProperty({ description: 'The field fuelMileage name is required' })
  @IsOptional({ message: 'The field fuelMileage cannot be empty' })
  fuelMileage?: number;
  @ApiProperty({ description: 'The field oilServiceMileage name is required' })
  @IsOptional({ message: 'The field oilServiceMileage cannot be empty' })
  oilServiceMileage?: number;
  @ApiProperty({ description: 'The field driveName is required' })
  @IsOptional({ message: 'The field driveName cannot be empty' })
  driveName?: string;
  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field isVehicleActive cannot be empty' })
  isVehicleActive?: true;
  @ApiProperty({ description: 'The field vehicleStatus is required' })
  @IsOptional({ message: 'The field vehicleStatus cannot be empty' })
  vehicleStatusId?: string;

  @ApiProperty({
    description: 'The field maintenanceActivityObeservation is required',
  })
  @IsOptional({
    message: 'The field maintenanceActivityObeservation cannot be empty',
  })
  maintenanceActivityObeservation?: string;

  @ApiProperty({ description: 'The field insurancePeriod is required' })
  @IsOptional({ message: 'The field insurancePeriod cannot be empty' })
  insurancePeriod?: number;

  @ApiProperty({ description: 'The field fuelCost is required' })
  @IsOptional({ message: 'The field fuelCost cannot be empty' })
  fuelCost?: number;

  @ApiProperty({ description: 'The field driveCost is required' })
  @IsOptional({ message: 'The field driveCost cannot be empty' })
  driveCost?: number;

  @ApiProperty({ description: 'The field fuelCost is required' })
  @IsOptional({ message: 'The field fuelCost cannot be empty' })
  fuelConsumptionDate?: Date;

  @ApiProperty({ description: 'The field insuranceCost is required' })
  @IsOptional({ message: 'The field insuranceCost cannot be empty' })
  insuranceCost?: number;

  @ApiProperty({
    description: 'The field insuranceAcquisitionDate is required',
  })
  @IsOptional({ message: 'The field insuranceAcquisitionDate cannot be empty' })
  insuranceAcquisitionDate?: Date;

  @ApiProperty({ description: 'The field maintenanceCost is required' })
  @IsOptional({ message: 'The field maintenanceCost cannot be empty' })
  maintenanceCost?: number;

  @ApiProperty({ description: 'The field maintenanceActivityDate is required' })
  @IsOptional({ message: 'The field maintenanceActivityDate cannot be empty' })
  maintenanceActivityDate?: Date;

  @ApiProperty({ description: 'The field oilServiceCost is required' })
  @IsOptional({ message: 'The field oilServiceCost cannot be empty' })
  oilServiceCost?: number;

  @ApiProperty({ description: 'The field oilServiceDate is required' })
  @IsOptional({ message: 'The field oilServiceDate cannot be empty' })
  oilServiceDate?: Date;

  @ApiProperty({ description: 'The field insuranceCost name is required' })
  @IsOptional({ message: 'The field insuranceCost cannot be empty' })
  sparePartsRepaired?: string;
  @ApiProperty({ description: 'The field driveCost is required' })
  @IsOptional({ message: 'The field driveCost cannot be empty' })
  activityDescription: string;
  @ApiProperty({ description: 'The field depreciationCost is required' })
  @IsOptional({ message: 'The field depreciationCost cannot be empty' })
  insuranceType: string;

  // @ApiProperty({ description: 'The field totalCost is required' })
  // @IsNotEmpty({ message: 'The field totalCost cannot be empty' })
  // totalCost: number;
  // @ApiProperty({ description: 'The field dateofActivity is required' })
  // @IsNotEmpty({ message: 'The field dateofActivity cannot be empty' })
  // dateofActivity: Date;
}
