import { IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class VehicleFilterDto {
  //   @ApiProperty({ description: 'The field beneficiaryAgencyId is required' })
  //   @IsNotEmpty({ message: 'The field beneficiaryAgencyId cannot be empty' })
  //   beneficiaryAgencyId?: string;
  @ApiProperty({ description: 'The field beneficiaryAgency is required' })
  @IsOptional({ message: 'The field beneficiaryAgency cannot be empty' })
  searchString?: string;

  @ApiProperty({ description: 'The field beneficiaryAgency is required' })
  @IsOptional({ message: 'The field beneficiaryAgency cannot be empty' })
  beneficiaryAgency?: string;

  @ApiProperty({ description: 'The field projectStartDate is required' })
  @IsOptional({ message: 'The field projectStartDate cannot be empty' })
  reportingInstitution?: string;

  //   @ApiProperty({ description: 'The field projectDescription is required' })
  //   @IsOptional({ message: 'The field projectDescription cannot be empty' })
  //   reportingInstitutionId?: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field project ownershipType be empty' })
  ownershipType?: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field project ownershipType be empty' })
  vehicleType?: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field project ownershipType be empty' })
  vehicleModel?: string;

  @ApiProperty({ description: 'The field registrationStatus is required' })
  @IsOptional({ message: 'The field project registrationStatus be empty' })
  registrationStatus?: string;

  @ApiProperty({ description: 'The field approvalLevel is required' })
  @IsOptional({ message: 'The field project approvalLevel be empty' })
  approvalLevel?: string;
  @ApiProperty({ description: 'The field vehicleStatus is required' })
  @IsOptional({ message: 'The field project vehicleStatus be empty' })
  vehicleStatus?: string;

  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field project isVehicleActive be empty' })
  isVehicleActive?: string;
  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field project isVehicleActive be empty' })
  vehicleManufacture?: string;

  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field project isVehicleActive be empty' })
  chassisNumber?: string;

  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field project isVehicleActive be empty' })
  pickCardNumber?: string;

  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field project isVehicleActive be empty' })
  plateNumber?: string;

  @ApiProperty({ description: 'The field isVehicleActive is required' })
  @IsOptional({ message: 'The field project isVehicleActive be empty' })
  invoiceNumber?: string;

  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  manufacturingYearStart?: string;
  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  manufacturingYearEnd?: string;

  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  acquisitionDateStart?: string;
  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  acquisitionDateEnd?: string;

  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  projectStartYearStart?: string;

  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  projectStartYearEnd?: string;

  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  projectEndYearStart?: string;

  @ApiProperty({ description: 'The field manufacturingYear is required' })
  @IsOptional({ message: 'The field project manufacturingYear be empty' })
  projectEndYearEnd?: string;
}
