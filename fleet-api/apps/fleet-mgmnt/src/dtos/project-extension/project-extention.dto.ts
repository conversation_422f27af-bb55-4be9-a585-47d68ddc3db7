import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SubmitProjectExtensionDto {
  // @ApiProperty({ description: 'The field  Id is required' })
  // @IsNotEmpty({ message: 'The field Id cannot be empty' })
  // id: string;
  @ApiProperty({ description: 'The field vehicle Id is required' })
  @IsNotEmpty({ message: 'The field vehicle Id cannot be empty' })
  vehicleId: string;

  @ApiProperty({ description: 'The field  project end data  is required' })
  @IsNotEmpty({ message: 'The field project end data Id cannot be empty' })
  newProjectEndDate: Date;

  @ApiProperty({ description: 'The field description is required' })
  @IsOptional({ message: 'The field description can be empty' })
  description: string;

  //   @ApiProperty({ description: 'The field isVehicleActive is required' })
  //   @IsNotEmpty({ message: 'The field isVehicleActive cannot be empty' })
  //   isVehicleActive: boolean;
}
