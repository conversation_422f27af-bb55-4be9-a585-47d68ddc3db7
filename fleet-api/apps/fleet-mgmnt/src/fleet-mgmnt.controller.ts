import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { FleetMgmntService } from './fleet-mgmnt.service';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  searcAcquisionAndFilterDTO,
  UpdateAcquisitionDto,
} from './dtos/vehicle-acquisition/acquisition.dto';
import { UpdateRequestedVehicleDto } from './dtos/vehicle-acquisition/requested-vehicle.dto';
import { CreateVehicleRegRequestDto } from './dtos/vehicle-registration/vehicle-reg-request.dto';
import {
  ExistingVehicleRegistrationDto,
  UpdateRegistredVehicleDto,
  VehicleRegistrationDto,
} from './dtos/vehicle-registration/registred-vehicle.dto';
import { VehicleAllocationDto } from './dtos/vehicle-allocation/vehicle-allocation.dto';
import { VehicleFilterDto } from './dtos/vehicle-reporting/vehicle-reporting.dto';
import {
  SubmitQuateryReportDto,
  UpdateQuateryReportDto,
} from './dtos/vehicale-status-report/vehicale-status-report.dto';
import { SubmitProjectExtensionDto } from './dtos/project-extension/project-extention.dto';
import { CostBenefitDto } from './dtos/vehicle-acquisition-relevance/cost-benefity.dto';
import { StaffRelevanceDto } from './dtos/vehicle-acquisition-relevance/staff-relavance.dto';
import { DriverDetailsDto } from './dtos/vehicle-acquisition-relevance/drive-details.dto';
import { HiringCostDto } from './dtos/vehicle-acquisition-relevance/hiring-cost.dto';
import { MaintenanceActivitiesDTO } from './dtos/maintenance-activities/maintenance-activities.dto';
import { ReportActivitiesDto } from './dtos/vehicale-status-report/report-activities.dto';
// import { UpdateAcquisitionDto } from './dtos/vehicle-acquisition/acquisition.dto';
// import { CreateAcquisitionDto } from './dtos/vehicle-acquisition/acquisition.dto';
// import { CreateRequestedVehicleDto } from './dtos/vehicle-acquisition/requested-vehicle.dto';
@Controller('fleet-mgmnt')
export class FleetMgmntController {
  aquistionApprovalService: any;
  constructor(private readonly fleetMgmntService: FleetMgmntService) {}
  // Create vehicle regstration request
  // @ApiOperation({
  //   summary: 'This is API to create vehicle registration request',
  //   description:
  //     'Bellow is sample data for creating vehicle registration request',
  // })
  // @ApiBody({
  //   type: 'object',
  //   schema: {
  //     properties: {
  //       userId: {
  //         type: 'string',
  //         title: 'This is example of user ID (a user who has submitted data)',
  //         example: 'e1bf80e2-9821-4b4d-a6a5-03e81afcadc8',
  //       },
  //       acquisitionId: {
  //         type: 'string',
  //         title: 'Acquisition Id ',
  //         example: 'e1bf80e2-9821-4b4d-a6a5-03e81afcadc8',
  //       },
  //     },
  //   },
  // })

  @ApiTags('Vehicle Registration Request')
  @Post('vehicle-reg-request')
  async createVehicleRegRequest(
    @Body() createVehicleRegRequestDto: CreateVehicleRegRequestDto,
  ) {
    return this.fleetMgmntService.createVehicleRegRequest(
      createVehicleRegRequestDto,
    );
  }

  // Vehicle registration

  @ApiOperation({
    summary: 'Create Vehicle ',
    description: 'Bellow is sample data for registering vehicle',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleRegRequest: {
          type: 'string',
          title: 'registration request ID',
          example: 'f3ee9313-7b97-4638-8c80-90f895a65f0b',
        },
        beneficiaryAgencyId: {
          type: 'string',
          title: 'beneficiary Agency Id',
          example: '75e3eee3-110e-463e-be05-aaa499efb583',
        },
        beneficiaryAgency: {
          type: 'string',
          title: 'beneficiary Agency',
          example: 'mininfra',
        },
        ownershipType: {
          type: 'string',
          title: 'ownership Type id',
          example: 'a2b08a98-6c18-4436-bbc3-bd16d9022c28',
        },
        vehicleType: {
          type: 'string',
          title: 'vehicle Type ID',
          example: '14d0287d-3742-4928-a7f7-50c30cd507f7',
        },
        fuelType: {
          type: 'string',
          title: 'fuel Type',
          example: 'sample',
        },
        vehicleManufacture: {
          type: 'string',
          title: 'vehicle Manufacture Id',
          example: '3cd4eda8-90db-4493-9acf-4c40ab891c90',
        },
        vehicleModel: {
          type: 'string',
          title: 'vehicle Model Id',
          example: '9a4b5bc5-d5d0-49ba-8359-533adfac0cea',
        },
        chassisNumber: {
          type: 'string',
          title: 'chassis Number',
          example: 'HDHR12HDTR-P',
        },
        engineNumber: {
          type: 'string',
          title: 'engine Number',
          example: 'HDHR12HDTR-P12321',
        },
        transmissionType: {
          type: 'string',
          title: 'transmission Type',
          example: 'SAMPLE OF TRANSMISSION TYPE',
        },
        manufactureYear: {
          type: 'string',
          title: 'manufacture Year',
          example: '2024-04-16',
        },
        odometerReading: {
          type: 'string',
          title: 'odometer Reading',
          example: 'sAMPLE OF ODOMETER READING',
        },
        acquisitionDate: {
          type: 'string',
          title: 'acquisition Date',
          example: '2024-04-16',
        },
        invoiceNumber: {
          type: 'string',
          title: 'invoice Number',
          example: 'KJHS64324',
        },
        invoiceDate: {
          type: 'string',
          title: 'invoice Date',
          example: '2024-04-16',
        },
        customsDeclarationNumber: {
          type: 'string',
          title: 'customs Declaration Number',
          example: '2024-04-16',
        },
        customsDeclarationDate: {
          type: 'string',
          title: 'customs Declaration Date',
          example: '2024-04-16',
        },
        declaredAmount: {
          type: 'string',
          title: 'declared Amount',
          example: 4545,
        },
        projectName: {
          type: 'project Name',
          title: 'project Name',
          example: '',
        },
        projectStartDate: {
          type: 'project Start Date',
          title: 'project Name',
          example: '',
        },
        projectDescription: {
          type: 'project Description',
          title: 'project Description',
          example: '',
        },
        projectEndDate: {
          type: 'projec project End Date',
          title: 'project End Date',
          example: '',
        },
      },
    },
  })
  @ApiTags('Vehicle Registration')
  @Post('vehicle-registration')
  async VehicleRegistration(
    @Body() vehicleRegistrationDto: VehicleRegistrationDto,
  ) {
    for (const key in vehicleRegistrationDto) {
      if (vehicleRegistrationDto[key] === '') {
        vehicleRegistrationDto[key] = null;
      }
    }

    return this.fleetMgmntService.VehicleRegistration(vehicleRegistrationDto);
  }

  // Existing vehicle registration

  @ApiOperation({
    summary: 'Create Available Vehicle ',
    description: 'Bellow is sample data for registering vehicle',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        // vehicleRegRequest: {
        //   type: 'string',
        //   title: 'registration request ID',
        //   example: 'f3ee9313-7b97-4638-8c80-90f895a65f0b',
        // },
        beneficiaryAgencyId: {
          type: 'string',
          title: 'beneficiary Agency Id',
          example: '75e3eee3-110e-463e-be05-aaa499efb583',
        },
        beneficiaryAgency: {
          type: 'string',
          title: 'beneficiary Agency',
          example: 'mininfra',
        },
        ownershipType: {
          type: 'string',
          title: 'ownership Type id',
          example: 'a2b08a98-6c18-4436-bbc3-bd16d9022c28',
        },
        vehicleType: {
          type: 'string',
          title: 'vehicle Type ID',
          example: '14d0287d-3742-4928-a7f7-50c30cd507f7',
        },
        fuelType: {
          type: 'string',
          title: 'vehicle Type ID',
          example: '14d0287d-3742-4928-a7f7-50c30cd507f7',
        },
        plateNumber: {
          type: 'string',
          title: 'plate Number',
          example: 'sample',
        },
        vehicleManufacture: {
          type: 'string',
          title: 'vehicle Manufacture Id',
          example: '3cd4eda8-90db-4493-9acf-4c40ab891c90',
        },
        vehicleModel: {
          type: 'string',
          title: 'vehicle Model Id',
          example: '9a4b5bc5-d5d0-49ba-8359-533adfac0cea',
        },
        chassisNumber: {
          type: 'string',
          title: 'chassis Number',
          example: 'HDHR12HDTR-P',
        },
        engineNumber: {
          type: 'string',
          title: 'engine Number',
          example: 'HDHR12HDTR-P12321',
        },
        transmissionType: {
          type: 'string',
          title: 'transmission Type',
          example: 'SAMPLE OF TRANSMISSION TYPE',
        },
        manufactureYear: {
          type: 'string',
          title: 'manufacture Year',
          example: '2024-04-16',
        },
        odometerReading: {
          type: 'string',
          title: 'odometer Reading',
          example: 'sAMPLE OF ODOMETER READING',
        },
        isVehicleActive: {
          type: 'boolean',
          title: 'is Vehicle Active',
          example: true,
        },
        vehicleStatus: {
          type: 'string',
          title: 'vehicle Status',
          example: '46429974-7168-4c64-97da-dbb9410c3e7f',
        },
        pickCardNumber: {
          type: 'string',
          title: 'pick Card Number',
          example: '857645jkr',
        },
        acquisitionDate: {
          type: 'string',
          title: 'acquisition Date',
          example: '2024-04-16',
        },
        invoiceNumber: {
          type: 'string',
          title: 'invoice Number',
          example: 'KJHS64324',
        },
        invoiceDate: {
          type: 'string',
          title: 'invoice Date',
          example: '2024-04-16',
        },
        customsDeclarationNumber: {
          type: 'string',
          title: 'customs Declaration Number',
          example: '2024-04-16',
        },
        customsDeclarationDate: {
          type: 'string',
          title: 'customs Declaration Date',
          example: '2024-04-16',
        },
        declaredAmount: {
          type: 'string',
          title: 'declared Amount',
          example: 4545,
        },
        projectName: {
          type: 'project Name',
          title: 'project Name',
          example: '',
        },
        projectStartDate: {
          type: 'project Start Date',
          title: 'project Name',
          example: '',
        },
        projectDescription: {
          type: 'project Description',
          title: 'project Description',
          example: '',
        },
        projectEndDate: {
          type: 'projec project End Date',
          title: 'project End Date',
          example: '',
        },
      },
    },
  })
  @ApiTags('Existing Vehicle Registration')
  @Post('existing-vehicle-registration')
  async ExistingVehicleRegistration(
    @Body() vehicleRegistrationDto: ExistingVehicleRegistrationDto,
  ) {
    for (const key in vehicleRegistrationDto) {
      if (vehicleRegistrationDto[key] === '') {
        vehicleRegistrationDto[key] = null;
      }
    }

    return this.fleetMgmntService.ExistingVehicleRegistration(
      vehicleRegistrationDto,
    );
  }
  // getting all vehicles

  @ApiOperation({
    summary: 'Find all vehicles',
  })
  @ApiTags('Vehicle Registration')
  @Get('allVehicles')
  async findAllVehicleStatus() {
    return this.fleetMgmntService.getAllVehicles();
  }
  // getting all vehicles by institution here by institution we mean agency made request
  @ApiOperation({
    summary:
      'Find all vehicles by institution (agency made registration  request)',
  })
  @ApiTags('Vehicle Registration')
  @Get('allVehicles/:institutionId')
  async findAllVehicleStatusByInstitutionId(
    @Param('institutionId') institutionId: string,
  ) {
    return this.fleetMgmntService.getAllVehiclesByInstitutionId(institutionId);
  }
  // getting all vehicles by beneficiaryAgencyId
  @ApiOperation({
    summary: 'Find all vehicles by beneficiaryAgencyId',
  })
  @ApiTags('Vehicle Registration')
  @Get('allVehiclesbybeneficiaryAgencyId/:beneficiaryAgencyId')
  async findAllVehicleStatusByBeneficiaryAgencyId(
    @Param('beneficiaryAgencyId') beneficiaryAgencyId: string,
  ) {
    return this.fleetMgmntService.getAllVehiclesByBeneficiaryAgencyId(
      beneficiaryAgencyId,
    );
  }
  // getting all vehiclea by reporting institution

  @ApiOperation({
    summary: 'Find all vehicles by reporting institution',
  })
  @ApiTags('Vehicle Registration')
  @Get('allVehiclesByRepInstitution/:reportingInstitutionId')
  async findAllVehicleStatusByReportingInstitutionId(
    @Param('reportingInstitutionId') reportingInstitutionId: string,
  ) {
    return this.fleetMgmntService.getAllVehiclesByReportingInstitutionId(
      reportingInstitutionId,
    );
  }

  // getting vehicle by id
  @ApiOperation({
    summary: 'Find vehicle by id',
  })
  @ApiTags('Vehicle Registration')
  @Get('vehicle/:id')
  async findVehicleById(@Param('id') id: string) {
    return this.fleetMgmntService.getVehicleById(id);
  }
  @ApiTags('Vehicle Registration')
  @Get('allVehiclesByVehicleStatusId/:vehicleStatusId')
  async findAllVehicleByVehicleStatusId(
    @Param('vehicleStatusId') vehicleStatusId: string,
  ) {
    return this.fleetMgmntService.getAllVehiclesByVehiclestatus(
      vehicleStatusId,
    );
  }
  // getting vehicle registration by plate number or chassis number
  @ApiOperation({
    summary: 'Find vehicle by plate number or chassis number',
  })
  @ApiTags('Vehicle Registration')
  @Get('vehicleByPlateNumberOrChassisNumber')
  async findVehicleByPlateNumberOrChassisNumber(
    @Query('plateNumberOrChassisNumber') plateNumberOrChassisNumber: string,
  ) {
    return this.fleetMgmntService.getVehicleByPlateNumberOrChassisNumber(
      plateNumberOrChassisNumber,
    );
  }

  // all retuned vehicles
  @ApiOperation({
    summary: 'Find all returned vehicles',
  })
  @ApiTags('Vehicle Registration')
  @Get('allReturnedVehicles')
  async allReturnedVehicles() {
    return this.fleetMgmntService.getAllReturnedVehicles();
  }
  // updating regstred vehicle

  @ApiOperation({
    summary: 'Update Registred Vehicle ',
    description: 'Bellow is sample data for registering vehicle',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        // vehicleRegRequest: {
        //   type: 'string',
        //   title: 'registration request ID',
        //   example: 'f3ee9313-7b97-4638-8c80-90f895a65f0b',
        // },
        beneficiaryAgencyId: {
          type: 'string',
          title: 'beneficiary Agency Id',
          example: '75e3eee3-110e-463e-be05-aaa499efb583',
        },
        beneficiaryAgency: {
          type: 'string',
          title: 'beneficiary Agency',
          example: 'mininfra',
        },
        ownershipType: {
          type: 'string',
          title: 'ownership Type id',
          example: 'a2b08a98-6c18-4436-bbc3-bd16d9022c28',
        },
        vehicleType: {
          type: 'string',
          title: 'vehicle Type ID',
          example: '14d0287d-3742-4928-a7f7-50c30cd507f7',
        },
        fuelType: {
          type: 'string',
          title: 'vehicle Type ID',
          example: '14d0287d-3742-4928-a7f7-50c30cd507f7',
        },
        plateNumber: {
          type: 'string',
          title: 'plate Number',
          example: 'sample',
        },
        vehicleManufacture: {
          type: 'string',
          title: 'vehicle Manufacture Id',
          example: '3cd4eda8-90db-4493-9acf-4c40ab891c90',
        },
        vehicleModel: {
          type: 'string',
          title: 'vehicle Model Id',
          example: '9a4b5bc5-d5d0-49ba-8359-533adfac0cea',
        },
        chassisNumber: {
          type: 'string',
          title: 'chassis Number',
          example: 'HDHR12HDTR-P',
        },
        engineNumber: {
          type: 'string',
          title: 'engine Number',
          example: 'HDHR12HDTR-P12321',
        },
        transmissionType: {
          type: 'string',
          title: 'transmission Type',
          example: 'SAMPLE OF TRANSMISSION TYPE',
        },
        manufactureYear: {
          type: 'string',
          title: 'manufacture Year',
          example: '2024-04-16',
        },
        odometerReading: {
          type: 'string',
          title: 'odometer Reading',
          example: 'sAMPLE OF ODOMETER READING',
        },
        isVehicleActive: {
          type: 'boolean',
          title: 'is Vehicle Active',
          example: true,
        },
        vehicleStatus: {
          type: 'string',
          title: 'vehicle Status',
          example: '46429974-7168-4c64-97da-dbb9410c3e7f',
        },
        pickCardNumber: {
          type: 'string',
          title: 'pick Card Number',
          example: '857645jkr',
        },
        acquisitionDate: {
          type: 'string',
          title: 'acquisition Date',
          example: '2024-04-16',
        },
        invoiceNumber: {
          type: 'string',
          title: 'invoice Number',
          example: 'KJHS64324',
        },
        invoiceDate: {
          type: 'string',
          title: 'invoice Date',
          example: '2024-04-16',
        },
        customsDeclarationNumber: {
          type: 'string',
          title: 'customs Declaration Number',
          example: '2024-04-16',
        },
        customsDeclarationDate: {
          type: 'string',
          title: 'customs Declaration Date',
          example: '2024-04-16',
        },
        declaredAmount: {
          type: 'string',
          title: 'declared Amount',
          example: 4545,
        },
        projectName: {
          type: 'project Name',
          title: 'project Name',
          example: '',
        },
        projectStartDate: {
          type: 'project Start Date',
          title: 'project Name',
          example: '',
        },
        projectDescription: {
          type: 'project Description',
          title: 'project Description',
          example: '',
        },
        projectEndDate: {
          type: 'projec project End Date',
          title: 'project End Date',
          example: '',
        },
      },
    },
  })
  @ApiTags('Vehicle Registration')
  @Patch('updateRegistredVehicle/:id')
  async updateRegistredVehicle(
    @Param('id') id: string,
    @Body() updateRegistredVehicleDto: UpdateRegistredVehicleDto,
  ) {
    for (const key in updateRegistredVehicleDto) {
      if (updateRegistredVehicleDto[key] === '') {
        updateRegistredVehicleDto[key] = null; // Set empty key to null
      }
    }
    // return updateAcquisitionDto;
    return this.fleetMgmntService.updateRegisVehicle(
      id,
      updateRegistredVehicleDto,
    );
  }

  // vehicle allocation
  @ApiOperation({
    summary: 'Vehicle allocation ',
    description: 'Bellow is sample data for allocating vehicle to institution',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        acquisitionId: {
          type: 'string',
          title: 'acquisition Id',
          example: '7bc2d411-a0aa-4d54-9f69-b5086d329986',
        },
        beneficiaryAgencyId: {
          type: 'string',
          title: 'beneficiary Agency Id',
          example: '75e3eee3-110e-463e-be05-aaa499efb583',
        },
        beneficiaryAgency: {
          type: 'string',
          title: 'beneficiary Agency',
          example: 'mininfra',
        },
        ownershipType: {
          type: 'string',
          title: 'ownership Type id',
          example: 'a2b08a98-6c18-4436-bbc3-bd16d9022c28',
        },
        pickCardNumber: {
          type: 'string',
          title: 'pickCardNumber',
          example: 'jfjfyryr6',
        },
        plateNumber: {
          type: 'string',
          title: 'plateNumber',
          example: 'jfjfyryr6ccdde',
        },
        projectName: {
          type: 'project Name',
          title: 'project Name',
          example: '',
        },
        projectStartDate: {
          type: 'project Start Date',
          title: 'project Name',
          example: '',
        },
        projectDescription: {
          type: 'project Description',
          title: 'project Description',
          example: '',
        },
        projectEndDate: {
          type: 'projec project End Date',
          title: 'project End Date',
          example: '',
        },
      },
    },
  })
  @ApiTags('Vehicle Allocation')
  @Patch('vehicleAllocation/:id')
  async vehicleAllocation(
    @Param('id') id: string,
    @Body() vehicleAllocationDto: VehicleAllocationDto,
  ) {
    for (const key in vehicleAllocationDto) {
      if (vehicleAllocationDto[key] === '') {
        vehicleAllocationDto[key] = null; // Set empty key to null
      }
    }
    // return updateAcquisitionDto;
    return this.fleetMgmntService.VehicleAllocation(id, vehicleAllocationDto);
  }
  // getting vehicle by filter
  @ApiTags('Searching AND filter(Generate report)')
  @Post('gettingVehicleByFilter')
  async getVehicleByFilter(@Body() filters: VehicleFilterDto) {
    for (const key in filters) {
      if (filters[key] === '' || filters[key] === 'string') {
        filters[key] = null;
      }
    }

    return this.fleetMgmntService.getVehiclesWithFilters(filters);
  }

  // getting vehicle by filter
  @ApiTags('Searching AND filter(Generate report)')
  @Post('gettingAcquisitionByFilter')
  async findAcquisitionByfielter(@Body() filters: searcAcquisionAndFilterDTO) {
    for (const key in filters) {
      if (filters[key] === '' || filters[key] === 'string') {
        filters[key] = null;
      }
    }

    return this.fleetMgmntService.findAcquisitionByfielter(filters);
  }

  // vehicle acquisition logic
  @ApiOperation({
    summary: 'Create Acquisition ',
    description: 'Bellow is sample data is sample data ',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        userId: {
          type: 'string',
          title: 'This is example of user ID in user table ',
          example: '20ea3499-f919-4224-a371-ad77b4431c3d',
        },
        requestType: {
          type: 'string',
          title: 'requestType ID ',
          example: 'f227a6fe-554e-4152-b6df-50a569cec7cf',
        },
        ownershipType: {
          type: 'string',
          title: 'ownershipType ID ',
          example: 'a2b08a98-6c18-4436-bbc3-bd16d9022c28',
        },
        institution: {
          type: 'string',
          title: 'institution name ',
          example: 'mininfra',
        },
        institutionId: {
          type: 'string',
          title: 'institution ID ',
          example: '75e3eee3-110e-463e-be05-aaa499efb583',
        },
        description: {
          type: 'string',
          title: 'Description of acquisition  free text area ',
          example: 'this is sample of narrative',
        },
      },
    },
  })
  @ApiTags('Vehicle Acquisition Request')
  @Post('acquisition')
  async CreateProject(@Body() createAcquisitionDto: any) {
    const { userId, requestType, ownershipType, institution, institutionId } =
      createAcquisitionDto;
    if (
      !userId ||
      !requestType ||
      !ownershipType ||
      !institution ||
      !institutionId
    ) {
      return {
        message:
          'userId , requestType,ownershipType,institution and institutionId are required',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    for (const key in createAcquisitionDto) {
      if (createAcquisitionDto[key] === '') {
        delete createAcquisitionDto[key];
      }
    }

    return this.fleetMgmntService.createAcquisition(createAcquisitionDto);
  }

  @ApiTags('Vehicle Acquisition Request')
  @Get('AllAcquisitions')
  async findAllProjects() {
    return this.fleetMgmntService.findAllAcquisition();
  }

  @ApiTags('Vehicle Acquisition Request')
  @Get('AllAcquisitionsByInstitution')
  async AllAcquisitionsByInstitution(
    @Query('acquisitionId') institutionId: string,
  ) {
    return this.fleetMgmntService.findAllAcquisitionByInstitution(
      institutionId,
    );
  }
  @ApiTags('Vehicle Acquisition Request')
  @Get('AcquisitionsById')
  async AcquisitionsById(@Query('id') id: string) {
    return this.fleetMgmntService.findAcquisitionById(id);
  }
  @ApiTags('Vehicle Acquisition Request')
  @Patch('updateAcquisition/:id')
  async update(
    @Param('id') id: string,
    @Body() updateAcquisitionDto: UpdateAcquisitionDto,
  ) {
    const { requestType, ownershipType } = updateAcquisitionDto;
    if (!requestType || !ownershipType) {
      return {
        message: 'requestType , ownershipType are rewuired during update',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    for (const key in updateAcquisitionDto) {
      if (updateAcquisitionDto[key] === '') {
        updateAcquisitionDto[key] = null; // Set empty key to null
      }
    }
    // return updateAcquisitionDto;
    return this.fleetMgmntService.updateAcquisition(id, updateAcquisitionDto);
  }

  @ApiOperation({
    summary: 'Vehicles ',
    description: 'Bellow is sample data of requested vehicle',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        acquisitionId: {
          type: 'string',
          title: 'This is example of Acquisition ID',
          example: 'e1bf80e2-9821-4b4d-a6a5-03e81afcadc8',
        },
        vehicleTypeid: {
          type: 'string',
          title: 'vehicleType ID ',
          example: 'eada1846-b5ee-4830-8b79-20d2cc72d59f',
        },
        numberOfVehicles: {
          type: 'string',
          title: 'number of this kind of vehicle  ',
          example: 5,
        },
        intendedUsage: {
          type: 'string',
          title: 'Intended use of this vehicles',
          example: 'For staff transport',
        },
        beneficiaryAgencyId: {
          type: 'string',
          title: 'beneficiary Agency ID ',
          example: '6d61eacd-8138-4ce6-97b6-904c45a598aa',
        },
        beneficiaryAgency: {
          type: 'string',
          title: 'beneficiary Agency',
          example: 'RRA',
        },
        projectName: {
          type: 'string',
          title: 'project Name (this is option)',
          example: 'Project name is optional',
        },
        projectDescription: {
          type: 'string',
          title: 'project Description (this is option)',
          example: 'Project Description is optional',
        },
        projectStartDate: {
          type: 'date',
          title: 'project Start Date (this is option)',
          example: '2024-04-16',
        },
        projectEndDate: {
          type: 'date',
          title: 'project End Date (this is option)',
          example: '2024-04-17',
        },
      },
    },
  })
  @ApiTags('Request Vehicle')
  @Post('createVehicle')
  async CreateVehicle(@Body() createRequestedVehicleDto: any) {
    const {
      acquisitionId,
      vehicleTypeid,
      numberOfVehicles,
      intendedUsage,
      beneficiaryAgencyId,
      beneficiaryAgency,
    } = createRequestedVehicleDto;
    if (
      !acquisitionId ||
      !vehicleTypeid ||
      !numberOfVehicles ||
      !intendedUsage ||
      !beneficiaryAgencyId ||
      !beneficiaryAgency
    ) {
      return {
        message:
          'acquisitionId,vehicleTypeid,numberOfVehicles,\
          intendedUsage, beneficiaryAgencyId\
          and beneficiaryAgency are required',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    for (const key in createRequestedVehicleDto) {
      if (createRequestedVehicleDto[key] === '') {
        delete createRequestedVehicleDto[key];
      }
    }

    return this.fleetMgmntService.createRequestedVehicle(
      createRequestedVehicleDto,
    );
    // return createRequestedVehicleDto
  }
  @ApiTags('Request Vehicle')
  @Get('allRequestedVehicles')
  async findAllVehicles() {
    return this.fleetMgmntService.findRequestedVehicles();
  }

  @ApiTags('Request Vehicle')
  @Get('AllVehiclesPerAcquisition')
  async findAllVehiclesPerAcquisition(
    @Query('acquisitionId') acquisitionId: string,
  ) {
    return this.fleetMgmntService.AcquisitionDetailsByAcquisitionID(
      acquisitionId,
    );
  }
  // this update function is used when request for more information is requested

  // updating vehicle for with specific id

  @ApiTags('Request Vehicle')
  @Patch('updateRequestedVehicle/:id')
  async updateVehicle(
    @Param('id') id: string,
    @Body() updateRequestedVehicleDto: UpdateRequestedVehicleDto,
  ) {
    for (const key in updateRequestedVehicleDto) {
      if (updateRequestedVehicleDto[key] === '') {
        updateRequestedVehicleDto[key] = null; // Set empty key to null
      }
    }
    return this.fleetMgmntService.updateVehicle(id, updateRequestedVehicleDto);
  }

  @ApiOperation({
    summary: 'find user by id',
  })
  @ApiTags('Getting User')
  @Get('findUser/:id')
  async getingUser(@Param('id') id: string) {
    return this.fleetMgmntService.checkUserData(id);
  }
  //
  @ApiTags('Getting User')
  @ApiOperation({
    summary: 'find user by id',
  })
  @Get('findUsers/:institutionId')
  async getingAllUser(@Param('institutionId') institutionId: string) {
    return this.fleetMgmntService.getUsers(institutionId);
  }

  // vehicle reporting staff

  // Vehicle registration

  @ApiOperation({
    summary: 'Create vehicle quarterly ',
    description:
      'Bellow is sample data required for submitting quarterly vehicle report  ',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'vehicleId',
          example: '876629d0-a025-40ce-8434-a60141d661f6',
        },
        vehicleStatusId: {
          type: 'string',
          title: 'vehicle Status Id',
          example: '46429974-7168-4c64-97da-dbb9410c3e7f',
        },
        description: {
          type: 'string',
          title: 'beneficiary Agency',
          example: 'sample description of quarterly report ',
        },
        isVehicleActive: {
          type: 'boolean',
          title: 'isVehicleActive',
          example: true,
        },
      },
    },
  })
  @ApiTags('Quarterly  Report submission')
  @Post('submit-Quarterly-vehicle-report')
  async VehicleQuarterlyReport(
    @Body() submitQuateryReportDto: SubmitQuateryReportDto,
  ) {
    for (const key in submitQuateryReportDto) {
      if (submitQuateryReportDto[key] === '') {
        submitQuateryReportDto[key] = null;
      }
    }

    return this.fleetMgmntService.submitQuateryReport(submitQuateryReportDto);
  }
  // getting all quatery report
  @ApiTags('Quarterly  Report submission')
  @Get('allQuarterly')
  async findQuarterly() {
    return this.fleetMgmntService.getAllQuateryReports();
  }
  // getting quaterly report by vehicle id
  @ApiTags('Quarterly  Report submission')
  @Get('AllQuarterlyByVehicleId')
  async allQuarterlyByVehicleId(@Query('vehicleId') vehicleId: string) {
    return this.fleetMgmntService.getQuateryReportsByVehicleID(vehicleId);
  }

  // getting quaterly report by vehicle id
  @ApiTags('Quarterly  Report submission')
  @Get('quarterlyByReportId')
  async quarterlyByReportId(@Query('id') id: string) {
    return this.fleetMgmntService.getQuateryReportsByReportID(id);
  }
  // updating quaterly report

  @ApiOperation({
    summary: 'updating vehicle quarterly ',
    description:
      'Bellow is sample data required for submitting quarterly vehicle report  ',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'vehicleId',
          example: '876629d0-a025-40ce-8434-a60141d661f6',
        },
        vehicleStatusId: {
          type: 'string',
          title: 'vehicle Status Id',
          example: '46429974-7168-4c64-97da-dbb9410c3e7f',
        },
        description: {
          type: 'string',
          title: 'beneficiary Agency',
          example: 'sample description of quarterly report ',
        },
        isVehicleActive: {
          type: 'boolean',
          title: 'isVehicleActive',
          example: true,
        },
      },
    },
  })
  @ApiTags('Quarterly  Report submission')
  @Patch('updateQuaterlyReport/:id')
  async updateQuarterlyReport(
    @Param('id') id: string,
    @Body() updateQuateryReportDto: UpdateQuateryReportDto,
  ) {
    for (const key in updateQuateryReportDto) {
      if (updateQuateryReportDto[key] === '') {
        updateQuateryReportDto[key] = null; // Set empty key to null
      }
    }
    return this.fleetMgmntService.updateQuateryReport(
      id,
      updateQuateryReportDto,
    );
  }

  // geting quaters of year logic
  @ApiTags('Quarterly  Report submission')
  @Get('quarter-dates')
  async getQuarterDates(@Query('date') dateStr: string) {
    const date = new Date(dateStr);
    return this.fleetMgmntService.getQuarterDates(date);
  }

  @ApiOperation({
    summary: 'CheckQuarterly ReportDates',
  })
  @ApiTags('CheckQuarterly ReportDates')
  @Get('checkQuarterlyReportDates')
  async checkQuarterlyReportDates() {
    return this.fleetMgmntService.checkQuarterlyReportDates();
  }

  @ApiOperation({
    summary: 'CheckQuarterly ReportDates',
  })
  @ApiTags('CheckQuarterly ReportDates')
  @Get('checkQuarterlyReportDates')
  async notifyQuarterlyReportsubmittion() {
    return this.fleetMgmntService.nitifiyInstitionToReportDates();
  }
  // Quatery report activities
  // creating  quatery  activities
  @ApiOperation({
    summary: 'Create quarterly Report activities ',
    description: 'Bellow is sample quarterly report activities',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleQuarterlyReportId: {
          type: 'string',
          title: 'vehicle Quarterly Report Id',
          example: '7b5c4665-fd95-41d0-879e-12b7bd405feb',
        },
        maintenanceActivitiesId: {
          type: 'string',
          title: 'maintenance Activities Id',
          example: '7e076548-bcd5-4404-b6c8-1aed28cf44fb',
        },
      },
    },
  })
  @ApiTags('Quarterly  Report submission')
  @Post('submit-Quarterly-activities')
  async quarterlyReportActivities(
    @Body() reportActivitiesDto: ReportActivitiesDto,
  ) {
    for (const key in reportActivitiesDto) {
      if (reportActivitiesDto[key] === '') {
        reportActivitiesDto[key] = null;
      }
    }

    return this.fleetMgmntService.createQuateryReportActivity(
      reportActivitiesDto,
    );
  }
  //  update quatery report activities
  @ApiOperation({
    summary: 'Update quarterly Report activities ',
    description: 'Bellow is sample quarterly report activities',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleQuarterlyReportId: {
          type: 'string',
          title: 'vehicle Quarterly Report Id',
          example: '7b5c4665-fd95-41d0-879e-12b7bd405feb',
        },
        maintenanceActivitiesId: {
          type: 'string',
          title: 'maintenance Activities Id',
          example: '7e076548-bcd5-4404-b6c8-1aed28cf44fb',
        },
      },
    },
  })
  @ApiTags('Quarterly  Report submission')
  @Patch('updateReportActivities/:id')
  async updateReportActivities(
    @Param('id') id: string,
    @Body() reportActivitiesDto: ReportActivitiesDto,
  ) {
    for (const key in reportActivitiesDto) {
      if (reportActivitiesDto[key] === '') {
        reportActivitiesDto[key] = null; // Set empty key to null
      }
    }
    return this.fleetMgmntService.updateActivitiesReport(
      id,
      reportActivitiesDto,
    );
  }
  // getting all activities by report ID
  @ApiTags('Quarterly  Report submission')
  @Get('AllQuarterlyActivitiesReportId')
  async allQuarterlyActivityByReportId(@Query('reportId') reportId: string) {
    return this.fleetMgmntService.getQuateryReportActivityByRportID(reportId);
  }
  // ========= request for project extension

  @ApiOperation({
    summary: 'Create Project extension request ',
    description:
      'Bellow is sample data required for submitting project extensions  ',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'vehicleId',
          example: '876629d0-a025-40ce-8434-a60141d661f6',
        },
        newProjectEndDate: {
          type: 'Date',
          title: 'new project end date of the project',
          example: '2024-12-16',
        },
        description: {
          type: 'string',
          title: 'description of request',
          example: 'sample description of project extension request ',
        },
      },
    },
  })
  @ApiTags('Project Extension Request')
  @Post('submit-Project-Extension-Request')
  async ProjectExtension(
    @Body() submitProjectExtensionDto: SubmitProjectExtensionDto,
  ) {
    for (const key in submitProjectExtensionDto) {
      if (submitProjectExtensionDto[key] === '') {
        submitProjectExtensionDto[key] = null;
      }
    }

    return this.fleetMgmntService.submitProjectExtensionRequest(
      submitProjectExtensionDto,
    );
  }

  // getting all quatery report
  @ApiTags('Project Extension Request')
  @Get('allProjectExtension')
  async findProjectExtension() {
    return this.fleetMgmntService.getAllProjectExtension();
  }

  // getting quaterly report by vehicle id
  @ApiTags('Project Extension Request')
  @Get('AllProjectExtensionByVehicleId')
  async allProjectExtensionByVehicleId(@Query('vehicleId') vehicleId: string) {
    return this.fleetMgmntService.getProjectExtensionByVehicleID(vehicleId);
  }

  //  // getting quaterly report by vehicle id
  @ApiTags('Project Extension Request')
  @Get('ProjectExtensionById')
  async ProjectExtensionByReportId(@Query('id') id: string) {
    return this.fleetMgmntService.getProjectExtensionByReportID(id);
  }

  @ApiOperation({
    summary: 'Updating Project extension request ',
    description:
      'Bellow is sample data required for updating project extensions  ',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'vehicleId',
          example: '876629d0-a025-40ce-8434-a60141d661f6',
        },
        newProjectEndDate: {
          type: 'Date',
          title: 'new project end date of the project',
          example: '2024-12-16',
        },
        description: {
          type: 'string',
          title: 'description of request',
          example: 'sample description of project extension request ',
        },
      },
    },
  })
  @ApiTags('Project Extension Request')
  @Patch('updateProjectExtension/:id')
  async updateProjectExtension(
    @Param('id') id: string,
    @Body() submitProjectExtensionDto: SubmitProjectExtensionDto,
  ) {
    for (const key in submitProjectExtensionDto) {
      if (submitProjectExtensionDto[key] === '') {
        submitProjectExtensionDto[key] = null;
      }
    }

    return this.fleetMgmntService.updateProjectExtension(
      id,

      submitProjectExtensionDto,
    );
  }

  // checking project end date
  @ApiOperation({
    summary: 'Check Project end date',
  })
  @ApiTags('Check project End Date')
  @Get('checkProjectEndDate')
  async checkProjectEndDate() {
    return this.fleetMgmntService.checkProjectEndDate();
  }

  //vehicle acquisition relevance

  @ApiTags('Vehicle Acquisition relevance')
  @ApiOperation({
    summary: 'Create Cost benefit',
    description: 'Bellow is sample data for cost benefit',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        acquisitionCost: {
          type: 'number',
          title: 'acquisition Cost(in RWF)',
          example: 5000,
        },
        maintenanceCost: {
          type: 'number',
          title: 'maintenance Cost (in RWF)',
          example: 8000,
        },
        fuelCost: {
          type: 'number',
          title: 'fuel Cost(in RWF)',
          example: 5000,
        },
        oilRefillingCost: {
          type: 'number',
          title: 'oil Refilling Cost(total per year)',
          example: 5000,
        },
        insuranceCost: {
          type: 'number',
          title: 'insurance Cost',
          example: 10000,
        },
        driveCost: {
          type: 'number',
          title: 'drive Cost',
          example: 10000,
        },
        depreciationCost: {
          type: 'number',
          title: 'depreciation Cost',
          example: 20000,
        },
        VehicleAcquisitionID: {
          type: 'string',
          title: 'Vehicle Acquisition ID ',
          example: '5ed63431-295b-43a1-93c0-1645a131f4e0',
        },
        RequestedVehicleID: {
          type: 'string',
          title: 'Requested Vehicle ID',
          example: 'c157b547-9a66-42c5-a24d-357f59cf756e',
        },
      },
    },
  })
  @Post('cost-benefit')
  async costBenefit(@Body() costBenefitDto: CostBenefitDto) {
    return this.fleetMgmntService.createCostBenefit(costBenefitDto);
  }

  // getting all cost benefits by acquisition id
  @ApiTags('Vehicle Acquisition relevance')
  @Get('cost-benefit-by-acquisitionID')
  async CostBenefitByAcquisitionId(@Query('id') id: string) {
    return this.fleetMgmntService.findCostBenefitByAcquisitionById(id);
  }

  // getting cost benefits by requested vehicle id

  @ApiTags('Vehicle Acquisition relevance')
  @Get('cost-benefit-by-reqvehicleID')
  async CostBenefitByReqvehicleId(@Query('id') id: string) {
    return this.fleetMgmntService.findCostBenefitByReqVehicleById(id);
  }
  // updating cost benefits by ID
  @ApiTags('Vehicle Acquisition relevance')
  @Patch('cost-benefit/:id')
  async updateCostBenefit(
    @Param('id') id: string,
    @Body() costBenefitDto: CostBenefitDto,
  ) {
    return this.fleetMgmntService.updateCostBenefit(id, costBenefitDto);
  }
  // staff relevance
  @ApiTags('Vehicle Acquisition relevance')
  @ApiOperation({
    summary: 'Create Staff relevance',
    description: 'Bellow is sample data staff relevance',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        staffCategory: {
          type: 'string',
          title: 'staff Category',
          example: 'institutional staff',
        },
        staffsDepartments: {
          type: 'string',
          title: 'staffs Departments',
          example: 'transport',
        },
        numberOfStaff: {
          type: 'number',
          title: 'numberOfStaff',
          example: 3,
        },
        staffPosition: {
          type: 'string',
          title: 'staff Position',
          example: 'sample of staff position',
        },
        staffLevel: {
          type: 'string',
          title: 'staffLevel',
          example: 'IV',
        },
        workLocation: {
          type: 'string',
          title: 'workLocation',
          example: 'Kacyiru',
        },
        officeLocation: {
          type: 'string',
          title: 'office Location',
          example: 'Kamonyi',
        },
        dailyWorkFrequency: {
          type: 'string',
          title: 'daily Work Frequency',
          example: '4',
        },
        numberOfMonthPerYear: {
          type: 'number',
          title: 'number OfMonth PerYear',
          example: 5,
        },
        OthersDescription: {
          type: 'string',
          title: 'Others Description',
          example: 'sample of description',
        },
        VehicleAcquisitionID: {
          type: 'string',
          title: 'Vehicle Acquisition ID ',
          example: '5ed63431-295b-43a1-93c0-1645a131f4e0',
        },
        RequestedVehicleID: {
          type: 'string',
          title: 'Requested Vehicle ID',
          example: 'c157b547-9a66-42c5-a24d-357f59cf756e',
        },
      },
    },
  })
  @Post('staff-relevance')
  async staffRelevance(@Body() staffRelevanceDto: StaffRelevanceDto) {
    return this.fleetMgmntService.createStaffRelevance(staffRelevanceDto);
  }

  // getting all staff relevance by acquisition ID

  @ApiTags('Vehicle Acquisition relevance')
  @Get('staff-relevance-by-acquisitionID')
  async staffRelevanceByAcquisitionId(@Query('id') id: string) {
    return this.fleetMgmntService.findStaffRelavcanceByAcquisitionById(id);
  }

  // getting staff relevance by vehicle reg request id
  @ApiTags('Vehicle Acquisition relevance')
  @Get('staff-relevance-by-reqvehicleID')
  async staffRelevanceByReqvehicleId(@Query('id') id: string) {
    return this.fleetMgmntService.findStaffRelevanceByReqVehicleById(id);
  }
  // updating staff relevance by ID
  @ApiTags('Vehicle Acquisition relevance')
  @Patch('staff-relevance/:id')
  async updateStaffRelevance(
    @Param('id') id: string,
    @Body() staffRelevanceDto: StaffRelevanceDto,
  ) {
    return this.fleetMgmntService.updateStaffRelevance(id, staffRelevanceDto);
  }

  // create drive details
  @ApiTags('Vehicle Acquisition relevance')
  @ApiOperation({
    summary: 'Create Deriver Details',
    description: 'Bellow is sample data Driver details',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        isDriveAvailable: {
          type: 'boolean',
          title: 'is drive available',
          example: true,
        },
        palanToRecruitDrive: {
          type: 'string',
          title: 'plan to recruit drive ',
          example: 'sample of plan ',
        },
        driverQualification: {
          type: 'string',
          title: 'driver Qualification',
          example: 'B',
        },
        numberOfDriver: {
          type: 'number',
          title: 'driver Qualification',
          example: 3,
        },
        VehicleAcquisitionID: {
          type: 'string',
          title: 'Vehicle Acquisition ID ',
          example: '5ed63431-295b-43a1-93c0-1645a131f4e0',
        },
        RequestedVehicleID: {
          type: 'string',
          title: 'Requested Vehicle ID',
          example: 'c157b547-9a66-42c5-a24d-357f59cf756e',
        },
      },
    },
  })
  @Post('crete-drive-details')
  async createDriveDetails(@Body() driveDetailsDto: DriverDetailsDto) {
    return this.fleetMgmntService.createDriveDetails(driveDetailsDto);
  }

  // get drive details by vehicle id
  @ApiTags('Vehicle Acquisition relevance')
  @Get('drive-details-by-vehicleId')
  async getDriveDetailsByVehicleId(@Query('id') id: string) {
    return this.fleetMgmntService.findDriveDetailsByVehicleId(id);
  }

  // get drive details by vehicle acquisition id
  @ApiTags('Vehicle Acquisition relevance')
  @Get('drive-details-by-reqvehicleID')
  async driveDetailsByReqvehicleId(@Query('id') id: string) {
    return this.fleetMgmntService.findDriverDetailsByReqVehicleById(id);
  }
  // updating staff relevance by ID
  @ApiTags('Vehicle Acquisition relevance')
  @Patch('drive-details/:id')
  async updateDriverDetails(
    @Param('id') id: string,
    @Body() driverDetailsDto: DriverDetailsDto,
  ) {
    return this.fleetMgmntService.updateDriverDetails(id, driverDetailsDto);
  }
  // create hiring cost information
  @ApiTags('Vehicle Acquisition relevance')
  @ApiOperation({
    summary: 'Create hiring cost information',
    description: 'Bellow is sample data ',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        hiringCost: {
          type: 'number',
          title: 'hiring cost',
          example: 10000,
        },
        VehicleAcquisitionID: {
          type: 'string',
          title: 'Vehicle Acquisition ID ',
          example: '5ed63431-295b-43a1-93c0-1645a131f4e0',
        },
        RequestedVehicleID: {
          type: 'string',
          title: 'Requested Vehicle ID',
          example: 'c157b547-9a66-42c5-a24d-357f59cf756e',
        },
      },
    },
  })
  @Post('crete-hiring-cost')
  async createHiringCost(@Body() hiringCostDto: HiringCostDto) {
    return this.fleetMgmntService.createHiringCost(hiringCostDto);
  }
  // getting hiring cost by Acquisition id
  @ApiTags('Vehicle Acquisition relevance')
  @Get('hiring-cost-by-vehicleId')
  async hiringDriverByVehicleId(@Query('id') id: string) {
    return this.fleetMgmntService.findHiringDriverByVehicleId(id);
  }
  // getting hiring cost by reqvehicle
  @ApiTags('Vehicle Acquisition relevance')
  @Get('hiring-cost-by-reqvehicleID')
  async hiringCostByReqvehicleId(@Query('id') id: string) {
    return this.fleetMgmntService.findHiringCostByReqVehicleById(id);
  }
  // updating hiring cost
  @ApiTags('Vehicle Acquisition relevance')
  @Patch('hiring-cost/:id')
  async updateHiringCost(
    @Param('id') id: string,
    @Body() hiringCostDto: HiringCostDto,
  ) {
    return this.fleetMgmntService.updateHiringCost(id, hiringCostDto);
  }

  // implement performed activities CRUD Operation
  @ApiTags('Maintenance Activity Performed')
  @ApiOperation({
    summary: 'Create Maintenance Activity Performed',
    description: 'Bellow is Performed by Maintenance',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleID: {
          type: 'string',
          title: 'vehicle ID',
          example: '0e815468-8b96-45d5-83c2-5715c15ad841',
        },
        activityOnVehicle: {
          type: 'string',
          title: 'vehicle ID',
          example: '0e815468-8b96-45d5-83c2-5715c15ad841',
        },
        oilServiceCategoryID: {
          type: 'string',
          title: 'oilService Category ID',
          example: 'f87d109b-91eb-4994-8ea0-3c38fdcd88ec',
        },
        fuelQuantity: {
          type: 'number',
          title: 'fuel Quantity',
          example: 3,
        },
        fuelMileage: {
          type: 'number',
          title: 'fuel Mileage',
          example: 545354,
        },
        oilServiceMileage: {
          type: 'number',
          title: 'oil Service Mileage',
          example: 545354,
        },
        isVehicleActive: {
          type: 'boolean',
          title: 'is Vehicle Active',
          example: true,
        },
        vehicleStatusId: {
          type: 'string',
          title: 'vehicle Status',
          example: '46429974-7168-4c64-97da-dbb9410c3e7f',
        },
        maintenanceActivityObeservation: {
          type: 'string',
          title: 'maintenance Activity Observation',
          example: 'Service was done at 114,875',
        },
        insurancePeriod: {
          type: 'number',
          title: 'insurance Period',
          example: 12,
        },
        fuelCost: {
          type: 'number',
          title: 'fuel Cost',
          example: 12098766,
        },
        driveCost: {
          type: 'number',
          title: 'drive Cost',
          example: 12098766,
        },
        fuelConsumptionDate: {
          type: 'Date',
          title: 'fuel Consumption Date',
          example: '2024-04-16',
        },
        insuranceCost: {
          type: 'number',
          title: 'insurance Cost',
          example: 31632232,
        },
        insuranceAcquisitionDate: {
          type: 'Date',
          title: 'insurance Acquisition Date',
          example: '2024-04-16',
        },
        maintenanceCost: {
          type: 'number',
          title: 'maintenance Cost',
          example: 636463,
        },
        maintenanceActivityDate: {
          type: 'Date',
          title: 'maintenance Activity Date',
          example: '2024-04-16',
        },
        oilServiceCost: {
          type: 'number',
          title: 'oil Service Cost',
          example: 6363664,
        },
        oilServiceDate: {
          type: 'Date',
          title: 'oilServiceDate',
          example: '2024-04-16',
        },
        sparePartsRepaired: {
          type: 'string',
          title: 'spare Parts Repaired',
          example: 'motor parts',
        },
        activityDescription: {
          type: 'string',
          title: 'activity Description',
          example: 'This is the description',
        },
        insuranceType: {
          type: 'string',
          title: 'insurance Type',
          example: 'comprehensive insurance',
        },
      },
    },
  })
  @Post('create-activity-performed')
  async createActivityPerformance(@Body() maintenanceActivitiesDTO: any) {
    // Iterate over each key in the DTO
    // console.log(maintenanceActivitiesDTO);
    Object.keys(maintenanceActivitiesDTO).forEach((key) => {
      if (
        maintenanceActivitiesDTO[key] === '' ||
        maintenanceActivitiesDTO[key] == undefined
      ) {
        // If the value is an empty string, set it to null
        maintenanceActivitiesDTO[key] = null;
      }
    });

    return this.fleetMgmntService.createActivityPerformance(
      maintenanceActivitiesDTO,
    );
  }
  // getting performed activity by Id
  @ApiTags('Maintenance Activity Performed')
  @Get('activity-performed-by-ID')
  async maitenanceActivityById(@Query('id') id: string) {
    return this.fleetMgmntService.maitenamceActivityByID(id);
  }
  // getting performed activity by vehicle id
  @ApiTags('Maintenance Activity Performed')
  @Get('activity-performed-by-VehicleD')
  async maitenanceActivityByVehicleId(@Query('id') id: string) {
    return this.fleetMgmntService.maitenamceActivityByVehicleID(id);
  }
  // getting performed activity by vehicle id and quater perion
  // here we receive vehicle id and data and retrun all activity performent in data quater for specific vehicle

  @ApiTags('Maintenance Activity Performed')
  @Get('activity-performed-by-VehicleD-and-Quarter')
  async maitenanceActivityByVehicleIdAndQuater(
    @Query('vehicleid') vehicleId: string,
    @Query('date') date: Date,
  ) {
    return this.fleetMgmntService.getMaintenanceActivityByVehicleIDAndQuarter(
      vehicleId,
      date,
    );
  }

  // update performed activity

  // @ApiTags('Maintenance Activity Performed')
  @ApiTags('Maintenance Activity Performed')
  @ApiOperation({
    summary: 'Create Maintenance Activity Performed',
    description: 'Bellow is Performed by Maintenance',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleID: {
          type: 'string',
          title: 'vehicle ID',
          example: '0e815468-8b96-45d5-83c2-5715c15ad841',
        },
        activityOnVehicle: {
          type: 'string',
          title: 'vehicle ID',
          example: '0e815468-8b96-45d5-83c2-5715c15ad841',
        },
        oilServiceCategoryID: {
          type: 'string',
          title: 'oilService Category ID',
          example: 'f87d109b-91eb-4994-8ea0-3c38fdcd88ec',
        },
        fuelQuantity: {
          type: 'number',
          title: 'fuel Quantity',
          example: 3,
        },
        fuelMileage: {
          type: 'number',
          title: 'fuel Mileage',
          example: 545354,
        },
        oilServiceMileage: {
          type: 'number',
          title: 'oil Service Mileage',
          example: 545354,
        },
        isVehicleActive: {
          type: 'boolean',
          title: 'is Vehicle Active',
          example: true,
        },
        vehicleStatusId: {
          type: 'string',
          title: 'vehicle Status',
          example: '46429974-7168-4c64-97da-dbb9410c3e7f',
        },
        maintenanceActivityObeservation: {
          type: 'string',
          title: 'maintenance Activity Observation',
          example: 'Service was done at 114,875',
        },
        insurancePeriod: {
          type: 'number',
          title: 'insurance Period',
          example: 12,
        },
        fuelCost: {
          type: 'number',
          title: 'fuel Cost',
          example: 12098766,
        },
        fuelConsumptionDate: {
          type: 'Date',
          title: 'fuel Consumption Date',
          example: '2024-04-16',
        },
        insuranceCost: {
          type: 'number',
          title: 'insurance Cost',
          example: 31632232,
        },
        insuranceAcquisitionDate: {
          type: 'Date',
          title: 'insurance Acquisition Date',
          example: '2024-04-16',
        },
        maintenanceCost: {
          type: 'number',
          title: 'maintenance Cost',
          example: 636463,
        },
        maintenanceActivityDate: {
          type: 'Date',
          title: 'maintenance Activity Date',
          example: '2024-04-16',
        },
        oilServiceCost: {
          type: 'number',
          title: 'oil Service Cost',
          example: 6363664,
        },
        oilServiceDate: {
          type: 'Date',
          title: 'oilServiceDate',
          example: '2024-04-16',
        },
        sparePartsRepaired: {
          type: 'string',
          title: 'spare Parts Repaired',
          example: 'motor parts',
        },
        activityDescription: {
          type: 'string',
          title: 'activity Description',
          example: 'This is the description',
        },
        insuranceType: {
          type: 'string',
          title: 'insurance Type',
          example: 'comprehensive insurance',
        },
      },
    },
  })
  @Patch('update-activity-performed/:id')
  async updateActivityPerformed(
    @Param('id') id: string,
    @Body() maintenanceActivitiesDTO: MaintenanceActivitiesDTO,
  ) {
    return this.fleetMgmntService.updateActivityPerformed(
      id,
      maintenanceActivitiesDTO,
    );
  }

  @ApiTags('Maintenance Activity Performed')
  @Get('updating-isActivityDAteInCurrentQuater')
  async updateisActivityDAteInCurrentQuater() {
    return this.fleetMgmntService.updateisActivityDAteInCurrentQuater();
  }

  // Coste Operation Report
  @ApiTags('Cost  Operation Report')
  @ApiOperation({
    summary: 'Coste Operation Report',
    description: 'Bellow is Coste Operation Report',
  })
  @Get('allOperationalCost')
  async allOperationalCost() {
    return this.fleetMgmntService.allOperationalCost();
  }

  @ApiTags('Cost  Operation Report')
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        startDate: {
          type: 'string',
          title: 'start Date',
          example: '2024-08-29',
        },
        endDate: {
          type: 'string',
          title: 'end Date ',
          example: '2024-08-29',
        },
      },
    },
  })
  @Post('allOperationalCostByDateRange')
  async allOperationalCostByDateRange(
    @Body() dateRangeDto: { startDate: string; endDate: string },
  ) {
    const { startDate, endDate } = dateRangeDto;

    // Convert string to Date object
    const start = new Date(startDate);
    const end = new Date(endDate);

    return this.fleetMgmntService.allOperationalCostByDateRange(start, end);
  }

  // all operation cost per beneficial and detail
  @ApiTags('Cost  Operation Report')
  @Get('allOperationalCostByBeneficiaryAngencyId')
  async allOperationalCostByBeneficiaryAngencyId(
    @Query('beneficiaryAgencyId') beneficiaryAgencyId: string,
  ) {
    return this.fleetMgmntService.allOperationalCostByBeneficiaryAngencyId(
      beneficiaryAgencyId,
    );
  }

  @ApiTags('Cost  Operation Report')
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        beneficiaryAgencyId: {
          type: 'string',
          title: 'beneficiary Agency Id',
          example: '75e3eee3-110e-463e-be05-aaa499efb583',
        },
        startDate: {
          type: 'string',
          title: 'start Date',
          example: '2024-08-29',
        },
        endDate: {
          type: 'string',
          title: 'end Date ',
          example: '2024-08-29',
        },
      },
    },
  })
  @Post('allOperationalCostByBeneficiaryAngencyIdAndDateRange')
  async allOperationalCostByBeneficiaryAngencyIdAndDateRange(
    @Body()
    dateRangeDto: {
      startDate: string;
      endDate: string;
      beneficiaryAgencyId: string;
    },
  ) {
    const { startDate, endDate } = dateRangeDto;

    // Convert string to Date object
    const start = new Date(startDate);
    const end = new Date(endDate);

    return this.fleetMgmntService.allOperationalCostByBeneficiaryAngencyIdAndDateRange(
      dateRangeDto.beneficiaryAgencyId,
      start,
      end,
    );
  }
  // all cost per vehicle
  @ApiTags('Cost  Operation Report')
  @Get('allOperationalCostByVehicleId')
  async allOperationalCostByVehicleID(@Query('VehicleID') vehicleId: string) {
    return this.fleetMgmntService.allOperationalCostByVehicleId(vehicleId);
  }

  // by date range:

  @ApiTags('Cost  Operation Report')
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'vehicle Id',
          example: '0e815468-8b96-45d5-83c2-5715c15ad841',
        },
        startDate: {
          type: 'string',
          title: 'start Date',
          example: '2024-08-29',
        },
        endDate: {
          type: 'string',
          title: 'end Date ',
          example: '2024-08-29',
        },
      },
    },
  })
  @Post('allOperationalCostByVehicleIdAndDateRange')
  async allOperationalCostByVehicleIdAndDateRange(
    @Body()
    dateRangeDto: {
      startDate: string;
      endDate: string;
      vehicleId: string;
    },
  ) {
    const { startDate, endDate } = dateRangeDto;

    // Convert string to Date object
    const start = new Date(startDate);
    const end = new Date(endDate);

    return this.fleetMgmntService.allOperationalCostByVehicleIdAndDateRange(
      dateRangeDto.vehicleId,
      start,
      end,
    );
  }
}
