import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';

import { AquistionApprovalService } from './approval.service';
import { UpdateCommentDto } from './dtos/vehicle-management/comments.dto';
import {
  ExistingVehicleRegApprovalDto,
  VehicleRegApprovalDto,
} from './dtos/approvals/registration-approval.dto';
import { VehicleDisposalApprovalDto } from './dtos/approvals/disposal-approval.dto';
import { AuctionReportApprovalDto } from './dtos/approvals/auction-report.dto';
import { QuarterlyReportApprovalDto } from './dtos/approvals/vehicle-quatery-report-approval.dto';
import { ProjectExtensionApprovalDto } from './dtos/project-extension/project-extension-approval.dto';

// @ApiTags('Vehicle Acquisition Approval')
@Controller('approval')
export class ApprovalController {
  constructor(
    private readonly aquistionApprovalService: AquistionApprovalService,
  ) {}

  @ApiOperation({
    summary: 'This is API to perform acquisition approval ',
    description: 'Bellow is sample data for approving any vehicle acquisition ',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        userId: {
          type: 'string',
          title: 'This is example of user ID (a user who has submitted data)',
          example: 'e1bf80e2-9821-4b4d-a6a5-03e81afcadc8',
        },
        acquisitionId: {
          type: 'string',
          title: 'Acquisition Id ',
          example: '20ea3499-f919-4224-a371-ad77b4431c3d',
        },
        comments: {
          type: 'string',
          title: 'comments ID ',
          example: 'test',
        },
        decision: {
          type: 'string',
          title: 'decision taken',
          example: 'APPROVED',
        },
      },
    },
  })
  @ApiTags('Vehicle Acquisition Approval')
  @Post('approval')
  async vehicleAcquisitionApproval(
    @Body() acquisitionApprovalDto: any,
  ): Promise<any> {
    // Process the DTO here
    const { userId, acquisitionId, decision } = acquisitionApprovalDto;
    if (!userId || !acquisitionId || !decision) {
      return {
        message: 'userId , acquisitionId and decision are required',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }

    return await this.aquistionApprovalService.acquisitionapproval(
      acquisitionApprovalDto,
    );
  }

  // vehicle registration approval
  @ApiOperation({
    summary: 'This is API to perform vehicle registration approval ',
    description: 'Bellow is sample data for approving any vehicle registration',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'This is example of vehicle Id)',
          example: '92fd0aef-97d3-45d6-85e3-56b46a6ed0b2',
        },
        userId: {
          type: 'string',
          title: 'This is example of user Id)',
          example: '20ea3499-f919-4224-a371-ad77b4431c3d',
        },
        comments: {
          type: 'string',
          title: 'comments ',
          example: '',
        },
        plateNumber: {
          type: 'string',
          title: 'plate Number',
          example: '',
        },
        pickCardNumber: {
          type: 'string',
          title: 'pick Card Number',
          example: '',
        },
        decision: {
          type: 'string',
          title: 'decision taken',
          example: 'APPROVED',
        },
      },
    },
  })
  @ApiTags('Vehicle Registration Approval')
  @Post('VehicleRegApproval')
  async vehicleRegApproval(
    @Body() vehicleRegApprovalDto: VehicleRegApprovalDto,
  ): Promise<any> {
    for (const key in vehicleRegApprovalDto) {
      if (vehicleRegApprovalDto[key] === '') {
        vehicleRegApprovalDto[key] = null;
      }
    }
    return await this.aquistionApprovalService.vehicleRegapproval(
      vehicleRegApprovalDto,
    );
  }
  // registration of existing vehicle
  @ApiOperation({
    summary: 'This is API to perform Existing vehicle registration approval ',
    description:
      'Bellow is sample data for approving any Existing  vehicle registration',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'This is example of vehicle Id)',
          example: 'b393662f-a009-4260-bfa6-f9b6836647b0',
        },
        userId: {
          type: 'string',
          title: 'This is example of user Id)',
          example: '20ea3499-f919-4224-a371-ad77b4431c3d',
        },
        comments: {
          type: 'string',
          title: 'comments ',
          example: '',
        },
        isVehicleActive: {
          type: 'boolean',
          title: 'is Vehicle Active',
          example: true,
        },
        vehicleStatusId: {
          type: 'string',
          title: 'vehicle Status Id',
          example: '46429974-7168-4c64-97da-dbb9410c3e7f',
        },
        decision: {
          type: 'string',
          title: 'decision taken',
          example: 'APPROVED',
        },
      },
    },
  })
  @ApiTags('Existing Vehicle Registration Approval')
  @Post('ExistingVehicleRegApproval')
  async existingVehicleRegApproval(
    @Body() vehicleRegApprovalDto: ExistingVehicleRegApprovalDto,
  ): Promise<any> {
    for (const key in vehicleRegApprovalDto) {
      if (vehicleRegApprovalDto[key] === '') {
        vehicleRegApprovalDto[key] = null;
      }
    }
    return await this.aquistionApprovalService.existingVehicleRegapproval(
      vehicleRegApprovalDto,
    );
  }

  // vehicle disposal approval

  @ApiOperation({
    summary: 'This is API to perform vehicle disposal approval ',
    description:
      'Bellow is sample data for approving any vehicle vehicle disposal',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        userId: {
          type: 'string',
          title: 'This is example of user ID (a user who has submitted data)',
          example: '6fe8dd77-9f12-462e-87d2-e4d1c4a40fc3',
        },
        disposalId: {
          type: 'string',
          title: 'disposal Id ',
          example: '4d21e2f5-1833-4c6e-8131-effff17d1e1f',
        },
        comments: {
          type: 'string',
          title: 'comments ID ',
          example: 'comments is option',
        },
        decision: {
          type: 'string',
          title: 'decision taken',
          example: 'APPROVED',
        },
      },
    },
  })
  @ApiTags('Vehicle Disposal Approval')
  @Post('DisposalApproval')
  async vehicleDisposalApproval(
    @Body() vehicleDisposalApprovalDto: VehicleDisposalApprovalDto,
  ): Promise<any> {
    for (const key in vehicleDisposalApprovalDto) {
      if (vehicleDisposalApprovalDto[key] === '') {
        vehicleDisposalApprovalDto[key] = null;
      }
    }
    return await this.aquistionApprovalService.disposalAproval(
      vehicleDisposalApprovalDto,
    );
  }

  // auction report approval

  @ApiOperation({
    summary: 'This is API to perform auction disposal approval ',
    description: 'Bellow is sample data for approving any auction report',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        userId: {
          type: 'string',
          title: 'This is example of user ID (a user who has submitted data)',
          example: '6fe8dd77-9f12-462e-87d2-e4d1c4a40fc3',
        },
        auctionReportId: {
          type: 'string',
          title: 'auction Report Id',
          example: 'b312d4c2-764e-4f48-85db-f14de209683a',
        },
        comments: {
          type: 'string',
          title: 'comments ID ',
          example: 'comments is option',
        },
        decision: {
          type: 'string',
          title: 'decision taken',
          example: 'APPROVED',
        },
      },
    },
  })
  @ApiTags('Auction Report Approval')
  @Post('ActionReportApproval')
  async AuctionReportApproval(
    @Body() auctionReportApprovalDto: AuctionReportApprovalDto,
  ): Promise<any> {
    for (const key in auctionReportApprovalDto) {
      if (auctionReportApprovalDto[key] === '') {
        auctionReportApprovalDto[key] = null;
      }
    }
    return await this.aquistionApprovalService.actionReport(
      auctionReportApprovalDto,
    );
  }

  // quaterly report approval

  @ApiOperation({
    summary: 'This is API to perform quarterly Report Approval ',
    description: 'Bellow is sample data for approving any quarterly report',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        userId: {
          type: 'string',
          title: 'This is example of user ID (a user who has submitted data)',
          example: '6fe8dd77-9f12-462e-87d2-e4d1c4a40fc3',
        },
        quarterlyReportId: {
          type: 'string',
          title: 'auction Report Id',
          example: '876629d0-a025-40ce-8434-a60141d661f6',
        },
        comments: {
          type: 'string',
          title: 'comments ID ',
          example: 'comments is option',
        },
        decision: {
          type: 'string',
          title: 'decision taken',
          example: 'APPROVED',
        },
      },
    },
  })
  @ApiTags('Quarterly Report Approval')
  @Post('quarterlyReportApproval')
  async quarterlyReportApproval(
    @Body() quarterlyReportApprovalDto: QuarterlyReportApprovalDto,
  ): Promise<any> {
    for (const key in quarterlyReportApprovalDto) {
      if (quarterlyReportApprovalDto[key] === '') {
        quarterlyReportApprovalDto[key] = null;
      }
    }
    return await this.aquistionApprovalService.quateryReportApproval(
      quarterlyReportApprovalDto,
    );
  }
  // =================  poroject extension approval =================

  @ApiOperation({
    summary: 'This is API to perform project extension Approval ',
    description: 'Bellow is sample data for approving project extension',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        userId: {
          type: 'string',
          title: 'This is example of user ID (a user who has submitted data)',
          example: '6fe8dd77-9f12-462e-87d2-e4d1c4a40fc3',
        },
        projectExtensionId: {
          type: 'string',
          title: 'auction Report Id',
          example: '87037c7a-6f08-47dd-9fed-078d2a0516b3',
        },
        comments: {
          type: 'string',
          title: 'comments ID ',
          example: 'comments is option',
        },
        decision: {
          type: 'string',
          title: 'decision taken',
          example: 'APPROVED',
        },
      },
    },
  })
  @ApiTags('Project Extension Approval')
  @Post('projectExtensionApproval')
  async projectExtensionApproval(
    @Body() projectExtensionApprovalDto: ProjectExtensionApprovalDto,
  ): Promise<any> {
    for (const key in projectExtensionApprovalDto) {
      if (projectExtensionApprovalDto[key] === '') {
        projectExtensionApprovalDto[key] = null;
      }
    }
    return await this.aquistionApprovalService.ProjectExtensionApproval(
      projectExtensionApprovalDto,
    );
  }

  // historical handling
  @ApiTags('Comments management')
  @Patch('editComment/:id')
  async editComment(
    @Param('id') id: string,
    @Body() updateCommentDto: UpdateCommentDto,
  ) {
    return this.aquistionApprovalService.editComment(id, updateCommentDto);
  }

  // delete comment
  @ApiTags('Activity History management')
  @Get('getAllHistoryByActionId')
  async AllAcquisitionsByInstitution(@Query('actionId') actionId: string) {
    return this.aquistionApprovalService.gethistory(actionId);
  }
  @ApiTags('Activity History management')
  @Get('getAllHistoryByUserId')
  async AllAcquisitionsByUserId(@Query('userId') userId: string) {
    return this.aquistionApprovalService.gethistoryByUserId(userId);
  }
  @ApiTags('Activity History management')
  @ApiOperation({
    summary: 'Delete  by ID',
  })
  @Delete('deleteHistory/:id')
  async deleteHistory(@Param('id') id: string) {
    return this.aquistionApprovalService.deleteHistory(id);
  }
  @ApiTags('Ministry of Infrastructure ID')
  @Get('mininfraId')
  async getMininfraId() {
    return this.aquistionApprovalService.getMininfraId();
  }
}
