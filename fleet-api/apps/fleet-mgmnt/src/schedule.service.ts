import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { FleetMgmntService } from './fleet-mgmnt.service';

@Injectable()
export class TasksService {
  constructor(private readonly fleetMgmntService: FleetMgmntService) {}

  @Cron('0 0 1 * *') // Runs at midnight on the first day of every month
  // @Cron('*/1 * * * * *') // Runs every second for testing
  async checkQuarterlyReportSubmission() {
    console.log('Running check for quarterly report dates...'); // Log to confirm the cron job runs
    try {
      await this.fleetMgmntService.checkQuarterlyReportDates();
    } catch (error) {
      console.error('Error during quarterly report check:', error);
    }
  }

  @Cron('0 0 * * 0') // This cron expression means "At 00:00 on Sunday"
  // @Cron('*/1 * * * * *') // Runs every second for testing
  async notifyInstitutionForQuarterlyReportSubmission() {
    console.log('Running check for quarterly report dates...'); // Log to confirm the cron job runs
    try {
      await this.fleetMgmntService.nitifiyInstitionToReportDates();
    } catch (error) {
      console.error('Error during quarterly report check:', error);
    }
  }

  @Cron('0 0 * * 6') // This cron expression means "At 00:00 on Saturday"
  // @Cron('*/1 * * * * *') // Runs every second for testing
  async notifyInstitutionForReturnVehicleSubmission() {
    console.log('Running check for quarterly report dates...'); // Log to confirm the cron job runs
    try {
      await this.fleetMgmntService.checkProjectEndDate();
    } catch (error) {
      console.error('Error during quarterly report check:', error);
    }
  }

  // automated check if activity performed on vehicle is still quater range
  @Cron('0 6 * * 6') // This cron expression means "At 00:00 on Saturday"
  // @Cron('*/1 * * * * *') // Runs every second for testing
  async updateisActivityDAteInCurrentQuater() {
    console.log('Running check for quarterly report dates...'); // Log to confirm the cron job runs
    try {
      await this.fleetMgmntService.updateisActivityDAteInCurrentQuater();
    } catch (error) {
      console.error('Error during quarterly report check:', error);
    }
  }
}
