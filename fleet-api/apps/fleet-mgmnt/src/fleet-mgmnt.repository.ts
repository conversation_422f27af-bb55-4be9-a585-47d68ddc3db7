import { AbstractRepository } from '@app/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { VehicleAcquisition } from './entities/vehicle-acquisition/vehicle-acquisition.entity';
import { RequestedVehicle } from './entities/vehicle-acquisition/requested-vehicle.entity';
import { ActivityRecoding } from './entities/comments/activity-recording.entity';
import { VehicleRegRequest } from './entities/vehicles-registration /vehicle-reg-request';
import { RegisteredVehicle } from './entities/vehicles-registration /registred-vehicle';
import { VehicleDisposal } from './entities/vehicle-disposal/vehicle-disposal.entity';
import { AuctionReport } from './entities/auction-report/auction-report.entity';
import { VehilceNumberPerAccquisition } from './entities/vehicle-acquisition/vehicleNumber-perAquisition.entity';
import { VehicleQuarterlyReport } from './entities/vehicale-status-report/vehicle-status-report.entity';
import { ProjectExtension } from './entities/project-extension/project-extension.entity';
import { CostBenefit } from './entities/vehicle-acquisition-relevance/cost-benefity.entity';
import { StaffRelavance } from './entities/vehicle-acquisition-relevance/staff-relavance.entity';
import { DriverDetails } from './entities/vehicle-acquisition-relevance/drive-details.entity';
import { HiringCost } from './entities/vehicle-acquisition-relevance/hiring-cost.entity';
import { MaintenanceActivities } from './entities/maintenance-activities/maintenance-activities.entity';
import { ReportActivities } from './entities/vehicale-status-report/report-activities.entity ';

// import { ActivityRecoding, Comments } from './entities/comments/activity-recording.entity';
// import { Injectable } from '@nestjs/common';

export class VehicleAcquisitionRepository extends AbstractRepository<VehicleAcquisition> {
  constructor(
    @InjectRepository(VehicleAcquisition)
    vehicleAcquisition: Repository<VehicleAcquisition>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleAcquisition);
  }
}

export class RequestedVehicleRepository extends AbstractRepository<RequestedVehicle> {
  constructor(
    @InjectRepository(RequestedVehicle)
    requestedVehicle: Repository<RequestedVehicle>,
    entityManager: EntityManager,
  ) {
    super(entityManager, requestedVehicle);
  }
}

export class ActivityRecodingRepository extends AbstractRepository<ActivityRecoding> {
  constructor(
    @InjectRepository(ActivityRecoding)
    activityRecoding: Repository<ActivityRecoding>,
    entityManager: EntityManager,
  ) {
    super(entityManager, activityRecoding);
  }
}

export class VehicleRegRequestRepository extends AbstractRepository<VehicleRegRequest> {
  constructor(
    @InjectRepository(VehicleRegRequest)
    vehicleRegRequest: Repository<VehicleRegRequest>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleRegRequest);
  }
}

export class RegisteredVehicleRepository extends AbstractRepository<RegisteredVehicle> {
  constructor(
    @InjectRepository(RegisteredVehicle)
    registeredVehicle: Repository<RegisteredVehicle>,
    entityManager: EntityManager,
  ) {
    super(entityManager, registeredVehicle);
  }
}

export class VehicleDisposalRepository extends AbstractRepository<VehicleDisposal> {
  constructor(
    @InjectRepository(VehicleDisposal)
    vehicleDisposal: Repository<VehicleDisposal>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleDisposal);
  }
}

export class AuctionReportRepository extends AbstractRepository<AuctionReport> {
  constructor(
    @InjectRepository(AuctionReport)
    auctionReport: Repository<AuctionReport>,
    entityManager: EntityManager,
  ) {
    super(entityManager, auctionReport);
  }
}

export class VehilceNumberPerAccquisitionRepository extends AbstractRepository<VehilceNumberPerAccquisition> {
  constructor(
    @InjectRepository(VehilceNumberPerAccquisition)
    vehilceNumberPerAccquisition: Repository<VehilceNumberPerAccquisition>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehilceNumberPerAccquisition);
  }
}

export class VehicleQuarterlyReportRepository extends AbstractRepository<VehicleQuarterlyReport> {
  constructor(
    @InjectRepository(VehicleQuarterlyReport)
    vehicleQuarterlyReport: Repository<VehicleQuarterlyReport>,
    entityManager: EntityManager,
  ) {
    super(entityManager, vehicleQuarterlyReport);
  }
}

export class ProjectExtensionRepository extends AbstractRepository<ProjectExtension> {
  constructor(
    @InjectRepository(ProjectExtension)
    projectExtension: Repository<ProjectExtension>,
    entityManager: EntityManager,
  ) {
    super(entityManager, projectExtension);
  }
}

export class CostBenefitRepository extends AbstractRepository<CostBenefit> {
  constructor(
    @InjectRepository(CostBenefit)
    costBenefit: Repository<CostBenefit>,
    entityManager: EntityManager,
  ) {
    super(entityManager, costBenefit);
  }
}

export class StaffRelavanceRepository extends AbstractRepository<StaffRelavance> {
  constructor(
    @InjectRepository(StaffRelavance)
    staffRelavance: Repository<StaffRelavance>,
    entityManager: EntityManager,
  ) {
    super(entityManager, staffRelavance);
  }
}

export class DriverDetailsRepository extends AbstractRepository<DriverDetails> {
  constructor(
    @InjectRepository(DriverDetails)
    driverDetails: Repository<DriverDetails>,
    entityManager: EntityManager,
  ) {
    super(entityManager, driverDetails);
  }
}

export class HiringCostRepository extends AbstractRepository<HiringCost> {
  constructor(
    @InjectRepository(HiringCost)
    hiringCost: Repository<HiringCost>,
    entityManager: EntityManager,
  ) {
    super(entityManager, hiringCost);
  }
}

export class MaintenanceActivitiesRepository extends AbstractRepository<MaintenanceActivities> {
  constructor(
    @InjectRepository(MaintenanceActivities)
    maintenanceActivities: Repository<MaintenanceActivities>,
    entityManager: EntityManager,
  ) {
    super(entityManager, maintenanceActivities);
  }
}

export class ReportActivitiesRepository extends AbstractRepository<ReportActivities> {
  constructor(
    @InjectRepository(ReportActivities)
    reportActivities: Repository<ReportActivities>,
    entityManager: EntityManager,
  ) {
    super(entityManager, reportActivities);
  }
}
