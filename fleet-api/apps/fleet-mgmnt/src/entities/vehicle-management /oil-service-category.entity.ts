import { AbstractEntity } from '@app/common';
import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

@Entity()
@Unique(['name']) // Ensure 'name' is unique
export class OilServiceCategory extends AbstractEntity<OilServiceCategory> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column({ nullable: true }) // Ensure 'name' is not nullable
  name: string;
  @Column({ nullable: true }) // Ensure 'name' is not nullable
  category: string;
}
