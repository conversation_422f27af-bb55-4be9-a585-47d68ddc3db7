import { AbstractEntity } from '@app/common';
import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

@Entity()
@Unique(['name']) // Ensure 'name' is unique
export class DisposalReasons extends AbstractEntity<DisposalReasons> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column({ nullable: false, unique: true }) // Ensure 'name' is not nullable
  name: string;
}
