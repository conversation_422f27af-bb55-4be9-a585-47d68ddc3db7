import { AbstractEntity } from '@app/common';
import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

@Entity()
@Unique(['name']) // Ensure 'name' is unique
export class Requesttb extends AbstractEntity<Requesttb> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column({ nullable: false }) // Ensure 'name' is not nullable
  name: string;
  @Column({ nullable: false }) // Ensure 'name' is not nullable
  code: string;
}
