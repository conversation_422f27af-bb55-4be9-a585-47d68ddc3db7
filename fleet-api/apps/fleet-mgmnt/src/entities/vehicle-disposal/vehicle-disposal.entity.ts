import { AbstractEntity } from '@app/common';
import {
  BeforeInsert,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  // Unique,
} from 'typeorm';
import { RegisteredVehicle } from '../vehicles-registration /registred-vehicle';
import { DisposalTypes } from '../vehicle-management /disposal types.entity';
import { DisposalReasons } from '../vehicle-management /disposal-reasons.entity';
import { StatusType } from '../vehicle-management /status.entity';
import { ApprovalLevel } from '../vehicle-management /approvel-level.entity';

@Entity()
// @Unique(['name']) // Ensure 'name' is unique
export class VehicleDisposal extends AbstractEntity<VehicleDisposal> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @OneToOne(
    () => RegisteredVehicle,
    (registeredVehicle) => registeredVehicle,
    { nullable: false }, // Add this line
  )
  @JoinColumn()
  vehicle: RegisteredVehicle;
  @ManyToOne(() => DisposalTypes)
  disposalTypes: DisposalTypes;
  @ManyToOne(() => DisposalReasons)
  disposalReasons: DisposalReasons;
  @Column({ nullable: true })
  description: string | null;
  @Column({ type: 'date', nullable: true })
  createddate: Date | null;

  @Column({ type: 'date', nullable: true })
  submittedDate: Date | null;
  @Column({ type: 'date', nullable: true })
  approveddate: Date | null;
  @BeforeInsert()
  updateSubmittedDate() {
    this.createddate = new Date();
  }
  @ManyToOne(() => StatusType)
  requestStatus: StatusType;
  @ManyToOne(() => ApprovalLevel)
  approvalLevel: ApprovalLevel;
  @Column({ default: false })
  isActionReportsubmitted: boolean;
}
