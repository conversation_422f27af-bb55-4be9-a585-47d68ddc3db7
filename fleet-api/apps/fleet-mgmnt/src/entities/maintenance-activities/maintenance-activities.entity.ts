import { AbstractEntity } from '@app/common';

import {
  Column,
  Entity,
  ManyToOne,
  BeforeInsert,
  PrimaryGeneratedColumn,
  JoinTable,
} from 'typeorm';

import { ActivityOnVehicle } from '../vehicle-management /activities-on-vehicle.entity';
import { OilServiceCategory } from '../vehicle-management /oil-service-category.entity';
import { RegisteredVehicle } from '../vehicles-registration /registred-vehicle';
import { VehicleStatus } from '../vehicle-management /vehicle-status.entity';

@Entity()
export class MaintenanceActivities extends AbstractEntity<MaintenanceActivities> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @ManyToOne(() => RegisteredVehicle)
  vehicle: RegisteredVehicle;
  @ManyToOne(() => ActivityOnVehicle)
  @JoinTable() // Add this line
  activityOnVehicle: ActivityOnVehicle;
  @ManyToOne(() => OilServiceCategory, { nullable: true })
  oilServiceCategory: OilServiceCategory | null;
  @Column({ nullable: true })
  fuelQuantity: number;
  // new added field
  @Column({ nullable: true })
  mileage: number;
  @Column({ nullable: true })
  driveName: string;
  @Column({ nullable: true })
  isVehicleActive: boolean;
  @ManyToOne(() => VehicleStatus, { nullable: true })
  vehicleStatus: VehicleStatus | null;
  @Column({ nullable: true })
  maintenanceActivityObeservation: string;
  @Column({ nullable: true })
  insurancePeriod: number;
  @Column({ nullable: true })
  cost: number;
  @Column({ nullable: true })
  activityDate: Date;
  @Column({ nullable: true })
  activityQuaterSatrtDate: Date;
  @Column({ nullable: true })
  activityQuaterEndDateDate: Date;
  @Column({ default: true, nullable: true })
  isActivityDAteInCurrentQuater: boolean;
  // end of new field

  @Column({ nullable: true })
  sparePartsRepaired: string;
  @Column({ nullable: true })
  activityDescription: string;
  @Column({ nullable: true })
  insuranceType: string;
  // @Column()
  // totalCost: number;
  // @Column({ type: 'date' })
  // dateofActivity: Date;

  @Column({ type: 'date', nullable: true })
  createddate: Date | null;
  @BeforeInsert()
  updateSubmittedDate() {
    this.createddate = new Date();
  }
}
