import { AbstractEntity } from '@app/common';
import {
  BeforeInsert,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  // Unique,
} from 'typeorm';
// import { RegisteredVehicle } from '../vehicles-registration /registred-vehicle';
// import { DisposalTypes } from '../vehicle-management /disposal types.entity';
// import { DisposalReasons } from '../vehicle-management /disposal-reasons.entity';
import { StatusType } from '../vehicle-management /status.entity';
import { ApprovalLevel } from '../vehicle-management /approvel-level.entity';
import { VehicleDisposal } from '../vehicle-disposal/vehicle-disposal.entity';

@Entity()
// @Unique(['name']) // Ensure 'name' is unique
export class AuctionReport extends AbstractEntity<AuctionReport> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @OneToOne(
    () => VehicleDisposal,
    (VehicleDisposal) => VehicleDisposal,
    { nullable: false }, // Add this line
  )
  @JoinColumn()
  disposal: VehicleDisposal;
  @Column({ type: 'bigint' })
  buyer_idNumber: number;
  @Column()
  buyer_FirstName: string;
  @Column()
  buyer_LastName: string;
  @Column()
  @Column({ type: 'bigint' })
  buyer_tinNumber: number;
  @Column()
  @Column({ type: 'bigint' })
  sale_amount: number;
  @Column()
  @Column({ type: 'bigint' })
  valuation_amount: number;
  @Column({ nullable: false })
  description: string;
  @Column({ type: 'date', nullable: true })
  createddate: Date | null;

  @Column({ type: 'date', nullable: true })
  submittedDate: Date | null;
  @Column({ type: 'date', nullable: true })
  approveddate: Date | null;
  @BeforeInsert()
  updateSubmittedDate() {
    this.createddate = new Date();
  }
  @ManyToOne(() => StatusType)
  requestStatus: StatusType;
  @ManyToOne(() => ApprovalLevel)
  approvalLevel: ApprovalLevel;
}
