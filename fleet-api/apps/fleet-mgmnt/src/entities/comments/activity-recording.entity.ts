import { AbstractEntity } from '@app/common';

import {
  Column,
  Entity,
  ManyToOne,
  BeforeInsert,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ApprovalLevel } from '../vehicle-management /approvel-level.entity';

// import { User } from 'apps/auth/src/entities/user-management/user.entity';
@Entity()
export class ActivityRecoding extends AbstractEntity<ActivityRecoding> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column()
  userId: string;
  @Column()
  actionId: string;
  @ManyToOne(() => ApprovalLevel)
  approvalLevel: ApprovalLevel;
  @Column({ nullable: true })
  comments?: string;
  @Column({ nullable: true })
  activityPerformed?: string;

  @Column({ type: 'date' })
  DateOfActivity: Date;
  @BeforeInsert()
  updateSubmittedDate() {
    this.DateOfActivity = new Date();
  }
}
