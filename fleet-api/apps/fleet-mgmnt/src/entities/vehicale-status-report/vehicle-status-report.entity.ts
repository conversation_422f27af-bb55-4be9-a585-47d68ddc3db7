import { AbstractEntity } from '@app/common';
import {
  BeforeInsert,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  // Unique,
} from 'typeorm';
import { RegisteredVehicle } from '../vehicles-registration /registred-vehicle';
import { StatusType } from '../vehicle-management /status.entity';
import { ApprovalLevel } from '../vehicle-management /approvel-level.entity';
import { VehicleStatus } from '../vehicle-management /vehicle-status.entity';
import { ReportActivities } from './report-activities.entity ';

@Entity()
// @Unique(['name']) // Ensure 'name' is unique
export class VehicleQuarterlyReport extends AbstractEntity<VehicleQuarterlyReport> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @ManyToOne(
    () => RegisteredVehicle,
    (registeredVehicle) => registeredVehicle,
    { nullable: true }, // Add this line
  )
  @JoinColumn()
  vehicle: RegisteredVehicle;
  @ManyToOne(() => VehicleStatus)
  vehicleStatus: VehicleStatus;
  @Column({ nullable: true })
  description: string | null;
  @Column({ nullable: true })
  comments: string | null;
  @Column({ type: 'date', nullable: true })
  reported_date: Date | null;
  @Column({ type: 'date', nullable: true })
  approveddate: Date | null;
  @BeforeInsert()
  updateReported_dateDate() {
    this.reported_date = new Date();
  }
  @ManyToOne(() => StatusType)
  reportStatus: StatusType;
  @ManyToOne(() => ApprovalLevel)
  approvalLevel: ApprovalLevel;
  // @Column({ default: false })
  // isActionReportsubmitted: boolean;
  @Column({ default: true })
  isVehicleActive: boolean;
  @Column({ type: 'date', nullable: true })
  submittedDate: Date | null;
  @BeforeInsert()
  updateSubmittedDate() {
    this.submittedDate = new Date();
  }
  @OneToMany(
    () => ReportActivities,
    (reportActivities) => reportActivities.vehicleQuarterlyReport,
    { nullable: true }, // Add this line
  )
  reportActivities: ReportActivities[];
}
