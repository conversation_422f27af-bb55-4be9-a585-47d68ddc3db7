import { AbstractEntity } from '@app/common';
import {
  BeforeInsert,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  // Unique,
} from 'typeorm';
import { VehicleQuarterlyReport } from './vehicle-status-report.entity';
import { MaintenanceActivities } from '../maintenance-activities/maintenance-activities.entity';

@Entity()
// @Unique(['name']) // Ensure 'name' is unique
export class ReportActivities extends AbstractEntity<ReportActivities> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @ManyToOne(
    () => VehicleQuarterlyReport,
    (vehicleQuarterlyReport) => vehicleQuarterlyReport,
    { nullable: true }, // Add this line
  )
  vehicleQuarterlyReport: VehicleQuarterlyReport | null;
  @ManyToOne(
    () => MaintenanceActivities,
    (maintenanceActivities) => maintenanceActivities,
    { nullable: true }, // Add this line
  )
  maintenanceActivities: MaintenanceActivities | null;
  @Column({ type: 'date', nullable: true })
  created_date: Date | null;
  @BeforeInsert()
  updateReported_dateDate() {
    this.created_date = new Date();
  }
}
