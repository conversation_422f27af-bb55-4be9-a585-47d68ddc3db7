import { AbstractEntity } from '@app/common';
import {
  BeforeInsert,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { VehicleAcquisition } from './vehicle-acquisition.entity';
import { VehicleType } from '../vehicle-management /vehicle-types.entity';

// import { User } from './User';

@Entity()
export class RequestedVehicle extends AbstractEntity<RequestedVehicle> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @ManyToOne(
    () => VehicleAcquisition,
    (acquisition) => acquisition.requestedVehicles,
  )
  acquisition: VehicleAcquisition;

  @ManyToOne(() => VehicleType)
  vehicleType: VehicleType;

  @Column({ default: 1 })
  numberOfVehicles: number;

  @Column()
  intendedUsage: string;

  @Column()
  beneficiaryAgencyId: string;
  @Column()
  beneficiaryAgency: string;

  @Column({ nullable: true })
  projectName: string;
  @Column({ type: 'date', nullable: true })
  projectStartDate: Date;

  @Column({ type: 'date', nullable: true })
  projectEndDate: Date;

  @Column({ nullable: true })
  projectDescription: string;

  @Column({ type: 'date', nullable: true })
  projectExtensionDate: Date;
  @BeforeInsert()
  setDefaultExtensionDate() {
    // Set projectExtensionDate to projectEndDate by default
    this.projectExtensionDate = this.projectEndDate;
  }
}
