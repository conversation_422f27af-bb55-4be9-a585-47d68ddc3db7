import { AbstractEntity } from '@app/common';

import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { VehicleAcquisition } from '../vehicle-acquisition/vehicle-acquisition.entity';
import { RequestedVehicle } from '../vehicle-acquisition/requested-vehicle.entity';
@Entity()
export class DriverDetails extends AbstractEntity<DriverDetails> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column({ default: true })
  isDriveAvailable: boolean;
  @Column({ nullable: true })
  palanToRecruitDrive: string;
  @Column({ nullable: true })
  driverQualification: string;
  @Column()
  numberOfDriver: number;

  @ManyToOne(() => VehicleAcquisition, { nullable: true })
  VehicleAcquisition: VehicleAcquisition | null;
  @ManyToOne(() => RequestedVehicle, { nullable: true })
  RequestedVehicle: RequestedVehicle | null;
}
