import { AbstractEntity } from '@app/common';

import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { VehicleAcquisition } from '../vehicle-acquisition/vehicle-acquisition.entity';
import { RequestedVehicle } from '../vehicle-acquisition/requested-vehicle.entity';
@Entity()
export class StaffRelavance extends AbstractEntity<StaffRelavance> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column({ nullable: true })
  staffCategory: string;
  @Column({ nullable: true })
  staffsDepartments: string;
  @Column({ nullable: true })
  numberOfStaff: number;
  @Column({ nullable: true })
  numberOfMonthPerYear: number;
  @Column()
  staffPosition: string;
  @Column()
  staffLevel: string;
  @Column()
  workLocation: string;
  @Column()
  officeLocation: string;
  @Column()
  dailyWorkFrequency: string;
  @Column()
  OthersDescription: string;
  @ManyToOne(() => VehicleAcquisition, { nullable: true })
  VehicleAcquisition: VehicleAcquisition | null;

  @ManyToOne(() => RequestedVehicle, { nullable: true })
  RequestedVehicle: RequestedVehicle | null;
}
