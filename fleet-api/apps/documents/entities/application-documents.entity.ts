import { Column, CreateDateColumn, Entity } from 'typeorm';

import { AbstractEntity } from '@app/common';

@Entity()
export class ApplicationDocument extends AbstractEntity<ApplicationDocument> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column({ type: 'varchar' })
  fileUrl: string;

  @Column({ type: 'varchar', nullable: true }) // Make documentsDescription optional
  documentsDescription: string;

  @Column({ type: 'varchar' })
  applicationId: string;

  // @Column({ type: 'varchar' })
  // RequiredDocumentid: string;

  @Column({ type: 'varchar' })
  fileName: string;

  @CreateDateColumn()
  createdAt: Date;
}
