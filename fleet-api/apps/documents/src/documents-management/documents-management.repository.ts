import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { DocumentType } from 'apps/documents/entities/document-type.entity';
import { RequiredDocument } from 'apps/documents/entities/required-document.entity';
import { ApplicationDocument } from 'apps/documents/entities/application-documents.entity';
@Injectable()
export class DocumentTypeRepository extends AbstractRepository<DocumentType> {
  constructor(
    @InjectRepository(DocumentType)
    documentTypeManagementRepository: Repository<DocumentType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, documentTypeManagementRepository);
  }
}

@Injectable()
export class RequiredDocumentRepository extends AbstractRepository<RequiredDocument> {
  constructor(
    @InjectRepository(RequiredDocument)
    requiredDocumentManagementRepository: Repository<RequiredDocument>,
    entityManager: EntityManager,
  ) {
    super(entityManager, requiredDocumentManagementRepository);
  }
}

@Injectable()
export class ApplicationDocumentRepository extends AbstractRepository<ApplicationDocument> {
  constructor(
    @InjectRepository(ApplicationDocument)
    applicationDocumentManagementRepository: Repository<ApplicationDocument>,
    entityManager: EntityManager,
  ) {
    super(entityManager, applicationDocumentManagementRepository);
  }
}
