import { Module } from '@nestjs/common';
import { DocumentsManagementController } from './documents-management.controller';
import { DocumentsManagementService } from './documents-management.service';

import { DatabaseModule } from '@app/common';
import { DocumentType } from 'apps/documents/entities/document-type.entity';
import {
  ApplicationDocumentRepository,
  DocumentTypeRepository,
  RequiredDocumentRepository,
} from './documents-management.repository';
import { RequiredDocument } from 'apps/documents/entities/required-document.entity';
import { MinioClientModule } from '../minio-client/minio-client.module';
import { ApplicationDocument } from 'apps/documents/entities/application-documents.entity';

@Module({
  imports: [
    DatabaseModule,
    MinioClientModule,
    DatabaseModule.forFeature([
      DocumentType,
      RequiredDocument,
      ApplicationDocument,
    ]),
  ],
  controllers: [DocumentsManagementController],
  providers: [
    DocumentsManagementService,
    DocumentTypeRepository,
    RequiredDocumentRepository,
    ApplicationDocumentRepository,
  ],
})
export class DocumentsManagementModule {}
