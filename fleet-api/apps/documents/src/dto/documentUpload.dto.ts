import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class DocumentUploadDto {
  // @ApiProperty({ description: 'The field Url is required' })
  // @IsNotEmpty({ message: 'The field Url cannot be empty' })
  // fileUrl: string;

  @ApiProperty({ description: 'The field application id is required' })
  @IsNotEmpty({ message: 'application ID is required' })
  applicationId: string;

  @ApiProperty({ description: 'The field RequiredDocument id is required' })
  @IsNotEmpty({ message: 'required document ID is required' })
  @IsOptional()
  documentDescription: string;
}
