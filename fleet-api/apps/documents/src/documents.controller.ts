import {
  <PERSON>,
  Post,
  Get,
  Param,
  Body,
  UploadedFile,
  UseInterceptors,
  Res,
  Delete,
  Put,
} from '@nestjs/common';
import { DocumentsService } from './documents.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { DocumentUpload } from '../entities/documentUpload.entity';
import { Express } from 'express'; // Import Express
import * as fs from 'fs';
import * as path from 'path';
import { DocumentUploadDto } from './dto/documentUpload.dto';
// import { DocumentUploadDto } from './dto/documentUpload.dto';

@ApiTags('fileMgt')
@Controller('fileMgt')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        documentDescription: { type: 'string' },
        applicationId: { type: 'string' },
        // Id: { type: 'integer' },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File, // Use Express.Multer.File
    @Body() createFileDto: DocumentUploadDto,
  ): Promise<DocumentUpload> {
    console.log(file);
    console.log(createFileDto.applicationId);
    console.log(createFileDto.documentDescription);
    console.log('=============================================');
    const savedFile = await this.documentsService.create(
      createFileDto.documentDescription,
      createFileDto.applicationId,
      file,
    );
    return savedFile;
  }

  @ApiTags('settings')
  @Get('documents')
  async findAllDocuments() {
    return this.documentsService.findAllDocuments();
  }

  @Get(':applicationId')
  async findAllByApplicationId(
    @Param('applicationId') applicationId: string,
  ): Promise<DocumentUpload[]> {
    return await this.documentsService.findAllByApplicationId(applicationId);
  }

  @Get('url/:fileUrl')
  async findOneByUrl(
    @Param('fileUrl') fileUrl: string,
  ): Promise<DocumentUpload> {
    return await this.documentsService.findOneByUrl(fileUrl);
  }

  @Get(':fileName/base64')
  async getFileAsBase64(
    @Param('fileName') fileName: string,
    @Res() res,
  ): Promise<void> {
    const filePath = path.join(__dirname, '..', 'docMgt', fileName);
    try {
      const fileData = fs.readFileSync(filePath);
      const base64Data = Buffer.from(fileData).toString('base64');
      res.status(200).send({ base64Data });
    } catch (error) {
      // Handle file not found or other errors
      res.status(404).send({ message: 'File not found' });
    }
  }

  // @Delete(':id')
  // async delete(@Param('id') id: string): Promise<void> {
  //   await this.documentsService.delete(id);
  // }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<void> {
    await this.documentsService.delete(id);
  }

  @Put(':id/status')
  async updateDocumentStatus(@Param('id') id: string): Promise<DocumentUpload> {
    return await this.documentsService.updateDocumentStatus(id);
  }
}
