import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as fs from 'fs';
import * as path from 'path';
import { Repository } from 'typeorm';

// import axios from 'axios';
import { FLEETMGMNT_SERVICE } from '@app/common/constants';
import { ClientProxy, EventPattern } from '@nestjs/microservices';
import { DocumentUpload } from '../entities/documentUpload.entity';

interface MulterFile {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
  fieldname: string;
}

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(DocumentUpload)
    private readonly fileRepository: Repository<DocumentUpload>,

    @Inject(FLEETMGMNT_SERVICE)
    private readonly fleetmgmgmntService: ClientProxy,
  ) {}

  // updating documentStatus
  @EventPattern('updateDocumentStatusEvent')
  async updateDocumentStatusEvent(documentId: string) {
    const document = await this.fileRepository.findOne({
      where: { id: documentId },
    });
    if (!document) {
      throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
    }
    document.documentStatusId = '1';
    return await this.fileRepository.save(document);
  }
  // requesting documents names from application service
  async requestRequiredDocumentData(requiredDocumentId: string) {
    return this.fleetmgmgmntService
      .send<any>({ cmd: 'requiredDocumentData' }, requiredDocumentId)
      .toPromise();
  }

  // uploading documents to the server
  // async create(
  //   documentDescription: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<DocumentUpload> {
  //   const fileUrl = path.join(__dirname, '..', 'docMgt', file.originalname);
  //   const fileName = file.originalname;
  //   fs.writeFileSync(fileUrl, file.buffer);

  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: fileName },
  //   });
  //   if (dataFromDb)
  //     throw new HttpException(
  //       'File name already exist, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   const newFile = this.fileRepository.create({
  //     documentDescription,
  //     applicationId,
  //     fileUrl,
  //     fileName,
  //   });
  //   return await this.fileRepository.save(newFile);
  // }
  // async create(
  //   documentDescription: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<DocumentUpload> {
  //   try {
  //     const addtional_string = Date.now().toString();
  //     const ext = file.originalname.substring(
  //       file.originalname.lastIndexOf('.'),
  //       file.originalname.length,
  //     );
  //     const root_filename = file.originalname.replace(ext, '');
  //     const filename = root_filename + '_' + addtional_string + ext;
  //     const newfileName: string = `${filename}`;
  //     const fileUrl = path.join(__dirname, '..', 'docMgt', newfileName);
  //     const fileName = newfileName;

  //     // Debugging: Log out the file URL to check if it matches expectations
  //     console.log('File URL:', fileUrl);

  //     // Check if directory exists, if not, create it
  //     const directory = path.dirname(fileUrl);
  //     if (!fs.existsSync(directory)) {
  //       fs.mkdirSync(directory, { recursive: true });
  //     }

  //     fs.writeFileSync(fileUrl, file.buffer);

  //     const dataFromDb = await this.fileRepository.findOne({
  //       where: { fileName: fileName },
  //     });
  //     if (dataFromDb)
  //       throw new HttpException(
  //         'File name already exists. Please change the name.',
  //         HttpStatus.BAD_REQUEST,
  //       );

  //     const newFile = this.fileRepository.create({
  //       documentDescription,
  //       applicationId,
  //       fileUrl,
  //       fileName,
  //     });
  //     return await this.fileRepository.save(newFile);
  //   } catch (error) {
  //     console.error('Error uploading document:', error);
  //     throw error; // Re-throw the error to be caught by the caller
  //   }
  // }
  // async create(
  //   documentDescription: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<DocumentUpload> {
  //   try {
  //     const addtional_string = Date.now().toString();
  //     const fileExtension = path.extname(file.originalname);
  //     const filename = `${file.originalname.replace(fileExtension, '')}_${addtional_string}${fileExtension}`;
  //     const fileUrl = path.join(__dirname, '..', 'docMgt', filename);
  //     const fileName = filename;

  //     // Check if directory exists, if not, create it
  //     const directory = path.dirname(fileUrl);
  //     if (!fs.existsSync(directory)) {
  //       fs.mkdirSync(directory, { recursive: true });
  //     }

  //     // Check if file with same name already exists in database
  //     const existingFile = await this.fileRepository.findOne({
  //       where: { fileName },
  //     });
  //     if (existingFile) {
  //       throw new HttpException(
  //         'File name already exists. Please change the name.',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     // Write file to disk
  //     fs.writeFileSync(fileUrl, file.buffer);

  //     // Create new file entity
  //     const newFile = this.fileRepository.create({
  //       documentDescription,
  //       applicationId,
  //       fileUrl,
  //       fileName,
  //     });
  //     return await this.fileRepository.save(newFile);
  //   } catch (error) {
  //     console.error('Error uploading document:', error);
  //     // Return a specific error response or send an error notification
  //     throw new HttpException(
  //       'Error uploading document',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
  async create(
    documentDescription: string,
    applicationId: string,
    file: MulterFile,
  ): Promise<DocumentUpload> {
    try {
      const addtional_string = Date.now().toString();
      const fileExtension = path.extname(file.originalname);
      const filename = `${file.originalname.replace(fileExtension, '')}_${addtional_string}${fileExtension}`;
      const fileUrl = path.join(__dirname, '..', 'docMgt', filename);
      const fileName = filename;
      // Check if directory exists, if not, create it
      const directory = path.dirname(fileUrl);
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
      }
      // Check if file with same name already exists in database
      const existingFile = await this.fileRepository.findOne({
        where: { fileName },
      });
      if (existingFile) {
        throw new HttpException(
          'File name already exists. Please change the name.',
          HttpStatus.BAD_REQUEST,
        );
      }
      // Write file to disk
      fs.writeFileSync(fileUrl, file.buffer);
      // Create new file entity
      const newFile = this.fileRepository.create({
        documentDescription,
        applicationId,
        fileUrl,
        fileName,
      });
      return await this.fileRepository.save(newFile);
    } catch (error) {
      console.error('Error uploading document:', error);
      // Return a specific error response or send an error notification
      throw new HttpException(
        'Error uploading document',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // async findAllByApplicationId(
  //   applicationId: string,
  // ): Promise<DocumentUpload[]> {
  //   return await this.fileRepository.find({ where: { applicationId } });
  // }

  // find all files by applicationId
  async findAllByApplicationId(applicationId: string): Promise<any[]> {
    const documents = await this.fileRepository.find({
      where: { applicationId },
    });

    // // Fetch requiredDocumentName for each document
    // const documentsWithNames = await Promise.all(
    //   documents.map(async (document) => {
    //     const requiredDocumentId = document.requiredDocumentId;
    //     const requiredDocumentName =
    //       await this.requestRequiredDocumentData(requiredDocumentId);
    //     return { ...document, requiredDocumentName };
    //   }),
    // );
    return documents;
  }
  // async findAllByApplicationId(applicationId: string): Promise<any[]> {
  //   const documents = await this.fileRepository.find({
  //     where: { applicationId },
  //   });

  //   // Fetch requiredDocumentName for each document
  //   const documentsWithNames = await Promise.all(
  //     documents.map(async (document) => {
  //       const requiredDocumentId = document.requiredDocumentId;
  //       const requiredDocumentName = await this.applicationService.send(
  //         'getRequiredDoc',
  //         requiredDocumentId,
  //       );
  //       return { ...document, requiredDocumentName };
  //     }),
  //   );

  //   return documentsWithNames;
  // }

  // async findAllByApplicationId(applicationId: string): Promise<any[]> {
  //   const documents = await this.fileRepository.find({
  //     where: { applicationId },
  //   });

  //   // Fetch requiredDocumentName for each document
  //   const documentsWithNames = await Promise.all(
  //     documents.map(async (document) => {
  //       const requiredDocumentId = document.requiredDocumentId;
  //       const requiredDocumentName =
  //         await this.fetchRequiredDocumentName(requiredDocumentId);
  //       return { ...document, requiredDocumentName };
  //     }),
  //   );

  //   return documentsWithNames;
  // }

  // async fetchRequiredDocumentName(requiredDocumentId: string) {
  //   const response = await axios.get(
  //     `http://localhost:3000/applications/requiredDocument/${requiredDocumentId}`,
  //   );
  //   return response.data.name;
  // }

  // find all documents
  async findAllDocuments() {
    return this.fileRepository.find({});
  }

  // find one document by its url
  async findOneByUrl(fileUrl: string): Promise<DocumentUpload> {
    return await this.fileRepository.findOne({ where: { fileUrl } });
  }

  // async delete(id: string): Promise<void> {
  //   const fileToDelete = await this.fileRepository.findOne(id);
  //   if (fileToDelete) {
  //     fs.unlinkSync(fileToDelete.fileUrl);
  //     await this.fileRepository.delete(id);
  //   }
  // }

  async delete(id: string): Promise<void> {
    // Find the document by Id
    const documentToDelete = await this.fileRepository.findOne({
      where: { id },
    });

    if (!documentToDelete) {
      throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
    }
    try {
      await this.fileRepository.remove(documentToDelete);
      fs.unlinkSync(documentToDelete.fileUrl);
    } catch (error) {
      throw new HttpException(
        'Failed to delete document',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // updating documentStatus
  async updateDocumentStatus(documentId: string): Promise<DocumentUpload> {
    const document = await this.fileRepository.findOne({
      where: { id: documentId },
    });
    if (!document) {
      throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
    }
    document.documentStatusId = '1';
    return await this.fileRepository.save(document);
  }
}
