import { NestFactory } from '@nestjs/core';
import { DocumentsModule } from './documents.module';
import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(DocumentsModule);
  const configService = app.get(ConfigService);
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: configService.get('TCP_PORT'),
    },
  });
  // -- Cors setup
  // -- Cors setup
  app.enableCors({
    origin: '*',
  });

  // -- Swagger Documentation
  const config = new DocumentBuilder()
    .setTitle('Fleet Management API')
    .setDescription('API Documents to Fleet management.')
    .addBasicAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('fileMgt/docs', app, document);

  // -- Port listening
  await app.listen(configService.get('HTTP_PORT'));
  // await app.listen(4005);
  await app.startAllMicroservices();
}
bootstrap();
