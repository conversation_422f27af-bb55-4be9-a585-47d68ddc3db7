services:
  fleet-api-gatway:
    build:
      context: . 
      dockerfile: ./apps/fleet-api-gatway/Dockerfile
      target: development
    command: pnpm run start:debug fleet-api-gatway
    env_file:
      - ./apps/fleet-api-gatway/.env
    ports:
      - '4000:4000'
    volumes:
      - .:/usr/src/app

  auth:
    build:
      context: .
      dockerfile: ./apps/auth/Dockerfile
      target: development
    command: pnpm r