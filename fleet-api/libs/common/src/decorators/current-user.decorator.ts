import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { User } from 'apps/auth/src/entities/user-management/user.entity';

const getCurrentUserByContext = (context: ExecutionContext): User => {
  return context.switchToHttp().getRequest().user;
};

export const CurrentUser = createParamDecorator(
  (_data: unknown, context: ExecutionContext) =>
    getCurrentUserByContext(context),
);
