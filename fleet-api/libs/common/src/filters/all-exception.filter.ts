import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpStatus,
} from '@nestjs/common';
import { HttpException } from '@nestjs/common';
// import { error } from 'console';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: <PERSON><PERSON><PERSON>, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    if (status === HttpStatus.UNAUTHORIZED)
      return response.status(status).render('views/401');
    if (status === HttpStatus.NOT_FOUND)
      return response.status(status).render('views/404');
    // if (status === HttpStatus.INTERNAL_SERVER_ERROR) {
    //   if (process.env.NODE_ENV === 'production') {
    //     console.error(error);
    //     return response.status(status).render('views/500');
    //   } else {
    //     const message = error;
    //     return response.status(status).send(message);
    //   }
    // }

    console.error(exception.stack);

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
