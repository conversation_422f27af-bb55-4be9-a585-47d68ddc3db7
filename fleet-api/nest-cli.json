{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/fleet-api-gatway/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/fleet-api-gatway/tsconfig.app.json"}, "projects": {"common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "fleet-apis": {"type": "fleet-api", "root": "apps/fleet-apis", "entryFile": "main", "sourceRoot": "apps/fleet-apis/src", "compilerOptions": {"tsConfigPath": "apps/fleet-apis/tsconfig.app.json"}}, "documents": {"type": "fleet-api", "root": "apps/documents", "entryFile": "main", "sourceRoot": "apps/documents/src", "compilerOptions": {"tsConfigPath": "apps/documents/tsconfig.app.json"}}, "notifications": {"type": "fleet-api", "root": "apps/notifications", "entryFile": "main", "sourceRoot": "apps/notifications/src", "compilerOptions": {"tsConfigPath": "apps/notifications/tsconfig.app.json"}}, "payments": {"type": "fleet-api", "root": "apps/payments", "entryFile": "main", "sourceRoot": "apps/payments/src", "compilerOptions": {"tsConfigPath": "apps/payments/tsconfig.app.json"}}, "auth": {"type": "fleet-api", "root": "apps/auth", "entryFile": "main", "sourceRoot": "apps/auth/src", "compilerOptions": {"tsConfigPath": "apps/auth/tsconfig.app.json"}}, "fleet-api-gatway": {"type": "fleet-api", "root": "apps/fleet-api-gatway", "entryFile": "main", "sourceRoot": "apps/fleet-api-gatway/src", "compilerOptions": {"tsConfigPath": "apps/fleet-api-gatway/tsconfig.app.json"}}, "fleet-mgmnt": {"type": "fleet-api", "root": "apps/fleet-mgmnt", "entryFile": "main", "sourceRoot": "apps/fleet-mgmnt/src", "compilerOptions": {"tsConfigPath": "apps/fleet-mgmnt/tsconfig.app.json"}}}, "monorepo": true, "root": "apps/fleet-api-gatway"}