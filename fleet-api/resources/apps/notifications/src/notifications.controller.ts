import { Controller } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
// import { EventPattern, Payload } from '@nestjs/microservices';
// import { EmailDto, NotifyEmailDto } from './dto/notify-email.dto';

@Controller()
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  // @UsePipes(new ValidationPipe())
  // @EventPattern('notify_email')
  // async notifyEmail(@Payload() data: NotifyEmailDto) {
  //   this.notificationsService.notifyEmail(data);
  // }

  // @UsePipes(new ValidationPipe())
  // @EventPattern('notify_email2')
  // async notifyEmail2(@Payload() data: EmailDto) {
  //   this.notificationsService.notifyEmail2(data);
  // }
}

// @Post('sendEmail')
// async notifyEmail2(@Body() data: EmailDto) {
//   this.notificationsService.notifyEmail2(data);
// }
