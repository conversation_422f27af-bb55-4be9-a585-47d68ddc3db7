import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import * as nodemailer from 'nodemailer';
// import { EmailDto, NotifyEmailDto } from './dto/notify-email.dto';

@Injectable()
export class NotificationsService {
  constructor(private readonly configService: ConfigService) {}

  // private readonly transporter = nodemailer.createTransport({
  //   service: 'gmail',
  //   auth: {
  //     type: 'OAuth2',
  //     user: this.configService.get('SMTP_USER'),
  //     clientId: this.configService.get('GOOGLE_OAUTH_CLIENT_ID'),
  //     clientSecret: this.configService.get('GOOGLE_OAUTH_CLIENT_SECRET'),
  //     refreshToken: this.configService.get('GOOGLE_OAUTH_REFRESH_TOKEN'),
  //   },
  // });

  // async notifyEmail({ email, text }: NotifyEmailDto) {
  //   await this.transporter.sendMail({
  //     from: this.configService.get('SMTP_USER'),
  //     to: email,
  //     subject: 'KUBAKA Notification',
  //     text,
  //   });
  // }

  // private readonly transporter2 = nodemailer.createTransport({
  //   host: 'smtp.gmail.com',
  //   auth: {
  //     user: '<EMAIL>',
  //     pass: 'cgnmqxzsbuzqlktt',
  //   },
  // });

  // async notifyEmail2({ email, subject, text, html }: EmailDto) {
  //   await this.transporter.sendMail({
  //     from: '<EMAIL>',
  //     to: email,
  //     subject: subject,
  //     text,
  //     html,
  //   });
  // }
}
