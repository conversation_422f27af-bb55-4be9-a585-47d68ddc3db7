import { NestFactory } from '@nestjs/core';
import { NotificationsModule } from './notifications.module';
import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';

async function bootstrap() {
  const app = await NestFactory.create(NotificationsModule);
  // app.setGlobalPrefix('api/v1');
  const configService = app.get(ConfigService);
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: configService.get('PORT'),
    },
  });
  // -- Cors setup
  app.enableCors({
    origin: '*',
  });
  await app.startAllMicroservices();
  // await app.listen(3004);
}
bootstrap();
