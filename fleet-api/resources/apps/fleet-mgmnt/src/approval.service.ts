import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import config from '../config';
import { Connection, EntityManager, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AcquisitionApprovalDto } from './dtos/approvals/acquisition-approval.dto';
import { VehicleAcquisition } from './entities/vehicle-acquisition/vehicle-acquisition.entity';
import {
  ActivityRecodingRepository,
  AuctionReportRepository,
  ProjectExtensionRepository,
  RegisteredVehicleRepository,
  VehicleAcquisitionRepository,
  VehicleDisposalRepository,
  VehicleQuarterlyReportRepository,
  VehicleRegRequestRepository,
} from './fleet-mgmnt.repository';
import {
  ApprovalLevelRepository,
  StatusTypeRepository,
  VehicleStatusRepository,
} from './vehicle-management/vehicle-management.repository';
// import { Comments } from './entities/comments/activity-recording.entity';
import { CreateCommentsDto } from './dtos/comments/comments.dto';
import { UpdateCommentDto } from './dtos/vehicle-management/comments.dto';
import { ActivityRecoding } from './entities/comments/activity-recording.entity';
import { RegisteredVehicle } from './entities/vehicles-registration /registred-vehicle';
import { VehicleRegRequest } from './entities/vehicles-registration /vehicle-reg-request';
import {
  ExistingVehicleRegApprovalDto,
  VehicleRegApprovalDto,
} from './dtos/approvals/registration-approval.dto';
import { VehicleDisposalApprovalDto } from './dtos/approvals/disposal-approval.dto';
import { VehicleDisposal } from './entities/vehicle-disposal/vehicle-disposal.entity';
import { AuctionReportApprovalDto } from './dtos/approvals/auction-report.dto';
import { AuctionReport } from './entities/auction-report/auction-report.entity';
import { QuarterlyReportApprovalDto } from './dtos/approvals/vehicle-quatery-report-approval.dto';
import { VehicleQuarterlyReport } from './entities/vehicale-status-report/vehicle-status-report.entity';
// import { FleetMgmntService } from './fleet-mgmnt.service';
import { startOfQuarter, endOfQuarter, addQuarters } from 'date-fns';
import { ProjectExtensionApprovalDto } from './dtos/project-extension/project-extension-approval.dto';
import { ProjectExtension } from './entities/project-extension/project-extension.entity';
// import { DisposalManagementService } from './disposal-management.service';
@Injectable()
export class AquistionApprovalService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly generalEntityManagerRepository: EntityManager,

    @InjectRepository(VehicleAcquisition)
    private vehicleAcquisitionEntityManagerRepository: Repository<VehicleAcquisition>,
    private readonly vehicleAcquisitionRepository: VehicleAcquisitionRepository,
    private readonly statusRepository: StatusTypeRepository,
    private readonly approvalLevelRepository: ApprovalLevelRepository,
    private readonly vehicleStatusRepository: VehicleStatusRepository,
    @InjectRepository(ActivityRecoding)
    private ActivityRecodingEntityManagerRepository: Repository<ActivityRecoding>,
    private readonly activityRecodingRepository: ActivityRecodingRepository,

    @InjectRepository(RegisteredVehicle)
    private RegisteredVehicleentityManagerRepository: Repository<RegisteredVehicle>,
    private readonly registeredVehicleRepository: RegisteredVehicleRepository,

    @InjectRepository(VehicleRegRequest)
    private vehicleRegRequestentityManagerRepository: Repository<VehicleRegRequest>,
    private readonly vehicleRegRequestRepository: VehicleRegRequestRepository,
    private readonly connection: Connection,
    @InjectRepository(VehicleDisposal)
    private vehicleDisposalManagerRepository: Repository<VehicleDisposal>,
    private readonly vehicleDisposalRepository: VehicleDisposalRepository,
    @InjectRepository(AuctionReport)
    private auctionReportManagerRepository: Repository<AuctionReport>,
    private readonly auctionReportRepository: AuctionReportRepository,
    @InjectRepository(VehicleQuarterlyReport)
    private vehicleQuarterlyReportManagerRepository: Repository<VehicleQuarterlyReport>,
    private readonly vehicleQuarterlyReportRepository: VehicleQuarterlyReportRepository,
    @InjectRepository(ProjectExtension)
    private projectExtensionManagerReportRepository: Repository<ProjectExtension>,
    private readonly projectExtensionRepository: ProjectExtensionRepository,
    // private readonly fleetMgmntService: FleetMgmntService,
    // private readonly disposalManagementService: DisposalManagementService,
  ) {}
  // Getting all user detail in case is needed
  async getUserDetails(userId: string): Promise<any> {
    const authbaseUrl = this.configService.get<string>('AUTH_URL');
    try {
      const response = await axios.get(authbaseUrl, {
        params: { userId: userId },
        headers: { accept: '*/*' }, // Set headers as needed
      });
      return response.data;
    } catch (error) {
      // Handle error
      console.error('Error fetching user details:', error);
      throw error; // You can handle the error according to your application's needs
    }
  }
  // getting all user detail per institution

  // geting all users by institution id

  async getUsersByInstitution(institutionId: string) {
    console.log(institutionId);
    try {
      const query = `
      SELECT
        u.id,
        u.identification,
        u."firstName",
        u."lastName",
        u."email",
        u."personalEmail",
        u."isEmailValid",
        u."phoneNumber",
        u."createdAt",
        u."isActive",
        u."created_at",
        u."updated_at",
        u."deleted_at",
        r.id AS roleId,
        r.name AS roleName,
        i.id AS institutionId,
        i.name AS institutionName,
        i."isLocationAvailable" AS isLocationAvailable
      FROM
        public.User u
      LEFT JOIN
        public.Role r ON u."roleId" = r.id
      LEFT JOIN
        public.Institution i ON u."institutionId" = i.id
        WHERE
      i.id = $1
    `;
      const result = await this.connection.query(query, [institutionId]);
      return result;
    } catch (error) {
      // Handle any errors that occur during query execution
      console.error('Error retrieving users:', error.message);
      throw error; // Rethrow the error to propagate it up the call stack
    }
  }

  async getDetailByRole(institutionId: string, roleName: string) {
    const usersByInstitution = await this.getUsersByInstitution(institutionId);
    const userByInstitution = usersByInstitution.filter(
      (user) => user.rolename === roleName,
    );
    return userByInstitution;
  }

  // getting institytions by name
  async getInstitution(name: string) {
    // console.log(institutionId);
    try {
      const query = `
      SELECT
        i.id,
        i."name",
        i."isLocationAvailable",
        i."isbudgeted"
      FROM
        public.Institution i
        WHERE
      i.name = $1
    `;
      const result = await this.connection.query(query, [name]);
      return result;
    } catch (error) {
      // Handle any errors that occur during query execution
      console.error('Error retrieving institution:', error.message);
      throw error; // Rethrow the error to propagate it up the call stack
    }
  }

  // sendining email for notiginig user
  async sendNotificationEmail(
    email: string,
    // phoneNumber: string,
    userNames: string,
  ): Promise<boolean> {
    const requestData = {
      sender_name: 'FLEET MIS',
      sender_email: '<EMAIL>',
      receiver_name: userNames,
      receiver_email: email,
      subject: 'Notification Email',
      message: `
        Hi ${userNames}<br>
         your request has change status <br><br>
        you can  simply click the link below to see more detail about notification: <br>
      
         ${config.host.url} <br><br>
        Best regards, <br>
        FLEET MIS Team       
        `,
    };

    try {
      const emailResponse = await Promise.all([
        // sending email and sms responses
        axios.post(config.notification.email_url, requestData),
        // axios.post(config.notification.sms_url, {
        //   msisdn: phoneNumber,
        //   message: `Hi ${userNames}there,\nPlease check your email ${model.email} and activate your account,\nThis is your token ${model.emailToken}\n\nBest regards\nFLEET MIS Team`,
        // }),
      ]);

      console.log('Email sent successfully:', emailResponse);
      // console.log('SMS sent successfully:', smsResponse);
      return true;
    } catch (error) {
      console.error('Error sending email:', error.response.data);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async sendRFCEmail(
    email: string,
    // phoneNumber: string,
    userNames: string,
  ): Promise<boolean> {
    const requestData = {
      sender_name: 'FLEET MIS',
      sender_email: '<EMAIL>',
      receiver_name: userNames,
      receiver_email: email,
      subject: 'Notification Email',
      message: `
        Hi ${userNames}<br>
        There is a fleet MIS dossier that need additional clarity <br><br>
        For further information: <br>
      
         ${config.host.url} <br><br>
        Best regards, <br>
        FLEET MIS Team       
        `,
    };

    try {
      const emailResponse = await Promise.all([
        // sending email and sms responses
        axios.post(config.notification.email_url, requestData),
        // axios.post(config.notification.sms_url, {
        //   msisdn: phoneNumber,
        //   message: `Hi ${userNames}there,\nPlease check your email ${model.email} and activate your account,\nThis is your token ${model.emailToken}\n\nBest regards\nFLEET MIS Team`,
        // }),
      ]);

      console.log('Email sent successfully:', emailResponse);
      // console.log('SMS sent successfully:', smsResponse);
      return true;
    } catch (error) {
      console.error('Error sending email:', error.response.data);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async sendReminderQuarterlyEmail(
    email: string,
    // phoneNumber: string,
    userNames: string,
  ): Promise<boolean> {
    const requestData = {
      sender_name: 'FLEET MIS',
      sender_email: '<EMAIL>',
      receiver_name: userNames,
      receiver_email: email,
      subject: 'Notification Email',
      message: `
        Hi ${userNames}<br>
         your have vehicle(s) its quarterly not yet submitted   <br><br>
        you can  simply click the link below to see more detail about notification: <br>
      
         ${config.host.url} <br><br>
        Best regards, <br>
        FLEET MIS Team       
        `,
    };

    try {
      const emailResponse = await Promise.all([
        // sending email and sms responses
        axios.post(config.notification.email_url, requestData),
        // axios.post(config.notification.sms_url, {
        //   msisdn: phoneNumber,
        //   message: `Hi ${userNames}there,\nPlease check your email ${model.email} and activate your account,\nThis is your token ${model.emailToken}\n\nBest regards\nFLEET MIS Team`,
        // }),
      ]);

      console.log('Email sent successfully:', emailResponse);
      // console.log('SMS sent successfully:', smsResponse);
      return true;
    } catch (error) {
      console.error('Error sending email:', error.response.data);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendReminderOverdueQuarterlyEmail(
    email: string,
    // phoneNumber: string,
    userNames: string,
  ): Promise<boolean> {
    const requestData = {
      sender_name: 'FLEET MIS',
      sender_email: '<EMAIL>',
      receiver_name: userNames,
      receiver_email: email,
      subject: 'Notification Email',
      message: `
        Hi ${userNames}<br>
         your have vehicle(s) its quarterly not yet submitted,and overdue date  <br><br>
        you can  simply click the link below to see more detail about notification: <br>
      
         ${config.host.url} <br><br>
        Best regards, <br>
        FLEET MIS Team       
        `,
    };

    try {
      const emailResponse = await Promise.all([
        // sending email and sms responses
        axios.post(config.notification.email_url, requestData),
        // axios.post(config.notification.sms_url, {
        //   msisdn: phoneNumber,
        //   message: `Hi ${userNames}there,\nPlease check your email ${model.email} and activate your account,\nThis is your token ${model.emailToken}\n\nBest regards\nFLEET MIS Team`,
        // }),
      ]);

      console.log('Email sent successfully:', emailResponse);
      // console.log('SMS sent successfully:', smsResponse);
      return true;
    } catch (error) {
      console.error('Error sending email:', error.response.data);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendReminderReturnProjectVehicleEmail(
    email: string,
    // phoneNumber: string,
    userNames: string,
  ): Promise<boolean> {
    const requestData = {
      sender_name: 'FLEET MIS',
      sender_email: '<EMAIL>',
      receiver_name: userNames,
      receiver_email: email,
      subject: 'Notification Email',
      message: `
        Hi ${userNames}<br>
         your have vehicle(s) need to be returned since its project is almost at the end  or you can request project extension  <br><br>
        you can  simply click the link below to see more detail about notification: <br>
      
         ${config.host.url} <br><br>
        Best regards, <br>
        FLEET MIS Team       
        `,
    };

    try {
      const emailResponse = await Promise.all([
        // sending email and sms responses
        axios.post(config.notification.email_url, requestData),
        // axios.post(config.notification.sms_url, {
        //   msisdn: phoneNumber,
        //   message: `Hi ${userNames}there,\nPlease check your email ${model.email} and activate your account,\nThis is your token ${model.emailToken}\n\nBest regards\nFLEET MIS Team`,
        // }),
      ]);

      console.log('Email sent successfully:', emailResponse);
      // console.log('SMS sent successfully:', smsResponse);
      return true;
    } catch (error) {
      console.error('Error sending email:', error.response.data);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendReminderReturnProjectOverdueVehicleEmail(
    email: string,
    // phoneNumber: string,
    userNames: string,
  ): Promise<boolean> {
    const requestData = {
      sender_name: 'FLEET MIS',
      sender_email: '<EMAIL>',
      receiver_name: userNames,
      receiver_email: email,
      subject: 'Notification Email',
      message: `
        Hi ${userNames}<br>
         your have vehicle(s) need to be returned since its project is ended    <br><br>
        you can  simply click the link below to see more detail about notification: <br>
      
         ${config.host.url} <br><br>
        Best regards, <br>
        FLEET MIS Team       
        `,
    };

    try {
      const emailResponse = await Promise.all([
        // sending email and sms responses
        axios.post(config.notification.email_url, requestData),
        // axios.post(config.notification.sms_url, {
        //   msisdn: phoneNumber,
        //   message: `Hi ${userNames}there,\nPlease check your email ${model.email} and activate your account,\nThis is your token ${model.emailToken}\n\nBest regards\nFLEET MIS Team`,
        // }),
      ]);

      console.log('Email sent successfully:', emailResponse);
      // console.log('SMS sent successfully:', smsResponse);
      return true;
    } catch (error) {
      console.error('Error sending email:', error.response.data);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async getQuarterDates(date: Date) {
    // Calculate the start and end dates of the current quarter
    const currentQuarterStartDate = startOfQuarter(date);
    const currentQuarterEndDate = endOfQuarter(date);

    // Calculate the start and end dates of the next quarter
    const nextQuarterDate = addQuarters(date, 1);
    const nextQuarterStartDate = startOfQuarter(nextQuarterDate);
    const nextQuarterEndDate = endOfQuarter(nextQuarterDate);

    // Calculate the start and end dates of all quarters of the year
    const year = date.getFullYear();
    const quarters = [];
    for (let i = 0; i < 4; i++) {
      const startDate = startOfQuarter(new Date(year, i * 3));
      const endDate = endOfQuarter(new Date(year, i * 3));
      quarters.push({ startDate, endDate });
    }

    return {
      currentQuarter: {
        startDate: currentQuarterStartDate.toISOString().split('T')[0],
        endDate: currentQuarterEndDate.toISOString().split('T')[0],
      },
      nextQuarter: {
        startDate: nextQuarterStartDate.toISOString().split('T')[0],
        endDate: nextQuarterEndDate.toISOString().split('T')[0],
      },
      allQuarters: quarters.map((quarter) => ({
        startDate: quarter.startDate.toISOString().split('T')[0],
        endDate: quarter.endDate.toISOString().split('T')[0],
      })),
    };
  }

  async findIndexByApprovalLevel(approvalLevel: string): Promise<number> {
    const orderedAprovalLevels = [
      'IL1',
      'ICBM2',
      'FMSE',
      'DGT',
      'PS',
      'MOS',
      'RRA',
      'SMINECOFIN',
    ];
    for (let i = 0; i < orderedAprovalLevels.length; i++) {
      if (orderedAprovalLevels[i] === approvalLevel) {
        return i; // Return the index if the approval level code is found
      }
    }
    return -1; // Return -1 if the approval level code is not found
  }
  async findApprovalLevelByIndex(index: number): Promise<string | null> {
    const orderedAprovalLevels = [
      'IL1',
      'ICBM2',
      'FMSE',
      'DGT',
      'PS',
      'MOS',
      'RRA',
      'SMINECOFIN',
    ];

    if (index >= 0 && index < orderedAprovalLevels.length) {
      return orderedAprovalLevels[index];
    } else {
      return null; // Return null if the index is out of range
    }
  }

  async acquisitionapproval(acquisitionApprovalDto: AcquisitionApprovalDto) {
    const acquisitionId = acquisitionApprovalDto.acquisitionId;
    const userId = acquisitionApprovalDto.userId;
    let dataapprovalLevel;
    let dataapprovedstatus;
    let dataPendingstatus;
    let dataaRFstutus;
    let dataaRejectedStatus;
    let cuurentLeveldata;
    let takendecision;
    let finalApprovedstatus;
    // let institutionReceiveEmail
    console.log(acquisitionId);
    // getting Acquisition
    const acquisition = await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      .where('vehicleAcquisition.id = :acquisitionId', {
        acquisitionId,
      })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
      .getMany();
    // check if acquisition exisit or not
    // const UserBurole = await this.getDetailByRole(
    //   acquisition[0].institutionId,acquisition[0].
    // );
    // console.log(
    //   '======================= this my test =============================',
    // );
    // const userToSendEmail = await this.getDetailByRole(
    //   acquisition[0].institutionId,
    //   'admin',
    // );
    // const userbyInstitution = await this.getUsersByInstitution(
    //   acquisition[0].institutionId,
    // );
    // // console.log(acquisition);

    if (!acquisition || acquisition.length === 0) {
      return { message: 'ACQUISITION NOT FOUND', error: HttpStatus.NOT_FOUND };
    }
    // current application level
    const currentLevel = acquisition[0].approvalLevel.code;
    const currentlevelIndex = this.findIndexByApprovalLevel(currentLevel);
    const nextLevelIndex = (await currentlevelIndex) + 1;
    const previousLevelIndex = (await currentlevelIndex) - 1;
    const nextapprovalLevel =
      await this.findApprovalLevelByIndex(nextLevelIndex);
    const previousapprovalLevel =
      await this.findApprovalLevelByIndex(previousLevelIndex);

    const decision = acquisitionApprovalDto.decision;
    const currentStatus = acquisition[0].statusType.name;
    if (currentStatus === 'APPROVED') {
      return {
        message: 'ACQUISITION ALREADY APPROVED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if (currentStatus === 'DENIED') {
      return {
        message: 'ACQUISITION ALREADY REJECTED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    console.log(currentlevelIndex);
    if ((await currentlevelIndex) <= 5) {
      console.log(currentLevel);
      console.log(currentlevelIndex);
      // handoling logic when decision is approved to any leve
      if (decision === 'APPROVED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: nextapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataapprovedstatus = await this.statusRepository.findOne({
            name: 'PROGRESS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataPendingstatus = await this.statusRepository.findOne({
            name: 'PENDING',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          finalApprovedstatus = await this.statusRepository.findOne({
            name: 'APPROVED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          if (currentLevel === 'MOS') {
            // console.log('hfrloo hhhhfhfhhffh');
            takendecision = decision;
            try {
              cuurentLeveldata = await this.approvalLevelRepository.findOne({
                code: currentLevel,
                // cuurentLeveldata = await this.approvalLevelRepository.findOne({
                //   code: currentLevel,
              });
            } catch (error) {
              if (error.message === 'Entity not found.') {
                return {
                  message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
                  statusCode: HttpStatus.NOT_FOUND,
                };
              } else {
                return {
                  message:
                    error.message ||
                    'An error occurred while fetching data approvalLevel.',
                  statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                };
              }
            }
            acquisition.forEach(async (acquisitionEntity) => {
              // acquisitionEntity.approvalLevel = dataapprovalLevel;
              acquisitionEntity.statusType = finalApprovedstatus;
              acquisitionEntity.approveddate = new Date();
              // acquisitionEntity.approvalLevel = cuurentLeveldata;
              const savedEntity =
                await this.vehicleAcquisitionEntityManagerRepository.save(
                  acquisitionEntity,
                );

              if (savedEntity) {
                const userToSendEmail = await this.getDetailByRole(
                  acquisition[0].institutionId,
                  'Institutional logistics', // role name sending email to instution logistics
                );
                const email = userToSendEmail[0].email;
                console.log(email);
                const name = userToSendEmail[0].firstName;
                console.log(name);
                const sentEmail = await this.sendNotificationEmail(email, name);
                if (sentEmail) {
                  console.log('========= email sent successfully======');
                }
              }
            });
          }
          if (currentLevel === 'IL1') {
            takendecision = decision;
            acquisition.forEach(async (acquisitionEntity) => {
              // acquisitionEntity.approvalLevel = dataapprovalLevel;
              acquisitionEntity.statusType = dataPendingstatus;
              acquisitionEntity.approvalLevel = dataapprovalLevel;
              await this.vehicleAcquisitionEntityManagerRepository.save(
                acquisitionEntity,
              );
            });
          }
          if (currentLevel === 'ICBM2') {
            takendecision = decision;
            acquisition.forEach(async (acquisitionEntity) => {
              acquisitionEntity.approvalLevel = dataapprovalLevel;
              acquisitionEntity.statusType = dataapprovedstatus;
              acquisitionEntity.submittedDate = new Date();
              // acquisitionEntity.statusType = dataPendingstatus;
              await this.vehicleAcquisitionEntityManagerRepository.save(
                acquisitionEntity,
              );
            });
          }
          if (
            currentLevel === 'FMSE' ||
            currentLevel === 'DGT' ||
            currentLevel === 'PS'
          ) {
            acquisition.forEach(async (acquisitionEntity) => {
              acquisitionEntity.approvalLevel = dataapprovalLevel;
              acquisitionEntity.statusType = dataapprovedstatus;
              // acquisitionEntity.statusType = dataPendingstatus;
              await this.vehicleAcquisitionEntityManagerRepository.save(
                acquisitionEntity,
              );
            });

            // here i neeed to update the approval level to dataapprovalLevel and status to datastatus
            await this.generalEntityManagerRepository.transaction(
              async (transactionManager) => {
                // Your commit logic here, if using TypeORM
                // For example:
                await transactionManager.query('COMMIT');
              },
            );
          }

          //sending emails:

          console.log('login test');
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating acquisition.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      // handoling logic when decision is request for action at any reval
      if (decision === 'RFAC') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: previousapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataaRFstutus = await this.statusRepository.findOne({
            name: 'RFAC',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' RFAC STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          acquisition.forEach(async (acquisitionEntity) => {
            acquisitionEntity.approvalLevel = dataapprovalLevel;
            acquisitionEntity.statusType = dataaRFstutus;
            const savedEntity =
              await this.vehicleAcquisitionEntityManagerRepository.save(
                acquisitionEntity,
              );
            // sending email
            if (previousapprovalLevel === 'IL1') {
              if (savedEntity) {
                const userToSendEmail = await this.getDetailByRole(
                  acquisition[0].institutionId,
                  'Institutional logistics', // role name sending email to instution logistics
                );
                const email = userToSendEmail[0].email;
                console.log(email);
                const name = userToSendEmail[0].firstName;
                console.log(name);
                const sentEmail = await this.sendRFCEmail(email, name);
                if (sentEmail) {
                  console.log('========= email sent successfully======');
                }
              }
            }

            if (previousapprovalLevel === 'ICBM2') {
              if (savedEntity) {
                const userToSendEmail = await this.getDetailByRole(
                  acquisition[0].institutionId,
                  'Institutional CBM', // role name sending email to Institutional CBM
                );
                const email = userToSendEmail[0].email;
                console.log(email);
                const name = userToSendEmail[0].firstName;
                console.log(name);
                const sentEmail = await this.sendRFCEmail(email, name);
                if (sentEmail) {
                  console.log('========= email sent successfully======');
                }
              }
            }

            if (previousapprovalLevel === 'FMSE') {
              if (savedEntity) {
                const userToSendEmail = await this.getDetailByRole(
                  acquisition[0].institutionId,
                  'Fleet Mgt Senior Engineer', // role name sending email to Fleet Mgt Senior Engineer
                );
                const email = userToSendEmail[0].email;
                console.log(email);
                const name = userToSendEmail[0].firstName;
                console.log(name);
                const sentEmail = await this.sendRFCEmail(email, name);
                if (sentEmail) {
                  console.log('========= email sent successfully======');
                }
              }
            }

            if (previousapprovalLevel === 'DGT') {
              if (savedEntity) {
                const userToSendEmail = await this.getDetailByRole(
                  acquisition[0].institutionId,
                  'DG Transport', // role name sending email to Fleet DG Transport
                );
                const email = userToSendEmail[0].email;
                console.log(email);
                const name = userToSendEmail[0].firstName;
                console.log(name);
                const sentEmail = await this.sendRFCEmail(email, name);
                if (sentEmail) {
                  console.log('========= email sent successfully======');
                }
              }
            }

            if (previousapprovalLevel === 'PS') {
              if (savedEntity) {
                const userToSendEmail = await this.getDetailByRole(
                  acquisition[0].institutionId,
                  'Permanent Secretary', // role name sending email to Permanent Secretary
                );
                const email = userToSendEmail[0].email;
                console.log(email);
                const name = userToSendEmail[0].firstName;
                console.log(name);
                const sentEmail = await this.sendRFCEmail(email, name);
                if (sentEmail) {
                  console.log('========= email sent successfully======');
                }
              }
            }
          });

          // here i neeed to update the approval level to dataapprovalLevel and status to datastatus
          await this.generalEntityManagerRepository.transaction(
            async (transactionManager) => {
              // Your commit logic here, if using TypeORM
              // For example:
              await transactionManager.query('COMMIT');
            },
          );
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating acquisition.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      // handoling logic when decision is rejected to any level
      if (decision === 'DENIED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'IL1 LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataaRejectedStatus = await this.statusRepository.findOne({
            name: 'DENIED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' REJECTED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          acquisition.forEach(async (acquisitionEntity) => {
            acquisitionEntity.approvalLevel = dataapprovalLevel;
            acquisitionEntity.statusType = dataaRejectedStatus;

            const savedEntity =
              await this.vehicleAcquisitionEntityManagerRepository.save(
                acquisitionEntity,
              );
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                acquisition[0].institutionId,
                'Institutional logistics', // role name "sendint email to instutiion logisttic"
              );
              const email = userToSendEmail[0].email;
              const name = userToSendEmail[0].firstName;
              const sentEmail = await this.sendNotificationEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          });
          // here i neeed to update the approval level to dataapprovalLevel and status to datastatus
          await this.generalEntityManagerRepository.transaction(
            async (transactionManager) => {
              // Your commit logic here, if using TypeORM
              // For example:
              await transactionManager.query('COMMIT');
            },
          );
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating acquisition.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
    }
    if ((await currentlevelIndex) >= 6) {
      return {
        message: 'APPROVAL LEVAL TERMINATED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    const updatedacquisition =
      await this.vehicleAcquisitionEntityManagerRepository
        .createQueryBuilder('vehicleAcquisition')
        .where('vehicleAcquisition.id = :acquisitionId', {
          acquisitionId,
        })
        .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
        .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
        .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
        .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
        .getMany();
    try {
      cuurentLeveldata = await this.approvalLevelRepository.findOne({
        code: currentLevel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: `APPROVAL LEVEL WITH ${currentLevel} AS CODE NOT FOUND`,
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data approvalLevel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // after checking is acquisitionApprovalDto.comments has value  (saving that comment)
    // (create comment)saving applicationID, userID,comments and cuurentLevel into Comments entity
    const comments = acquisitionApprovalDto.comments;
    let activityHistory;
    // if (comments !== undefined && comments !== null && comments.trim() !== '') {
    // if (comments) {
    // async saveComment(acquisitionId, userId, comments, cuurentLeveldata) {
    try {
      const activityRecodingDto = new ActivityRecoding({
        ...CreateCommentsDto,
        userId: userId, // Assign the StatusType object
        approvalLevel: cuurentLeveldata, // Assign the ApprovalLevel object
        actionId: acquisitionId, // Assign the
        comments: comments,
        activityPerformed: takendecision, // Assign the RequestType object
      });

      activityHistory =
        await this.activityRecodingRepository.create(activityRecodingDto);
    } catch (error) {
      throw new Error('Failed to save comments: ' + error.message);
    }
    // }
    // }

    return {
      acquisition: updatedacquisition,
      activityHistory: activityHistory,
    };
    // return acquisitionApprovalDto;
  }
  // approving vehicle registration
  async vehicleRegapproval(vehicleRegApprovalDto: VehicleRegApprovalDto) {
    // console.log(vehicleRegApprovalDto);
    const vehicleId = vehicleRegApprovalDto.vehicleId;
    let dataapprovalLevel;
    let dataapprovedstatus;
    let dataPendingstatus;
    let dataaRFstutus;
    let dataaRejectedStatus;
    let cuurentLeveldata;
    let takendecision;
    let finalApprovedstatus;
    let institutionReceiveEmail;
    let finaldataapprovalLevel;
    const vehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    if (!vehicleData) {
      return { message: 'Vehicle NOT FOUND', error: HttpStatus.NOT_FOUND };
    }
    // institition to send email
    institutionReceiveEmail = vehicleData.reportingInstitutionId;
    if (!institutionReceiveEmail) {
      institutionReceiveEmail = vehicleData.beneficiaryAgencyId;
    }
    // console.log(vehicleData);
    const currentLevel = vehicleData.approvalLevel.code; // This will be "ICBM2"
    // console.log(currentLevel);
    const currentlevelIndex = this.findIndexByApprovalLevel(currentLevel);
    const nextLevelIndex = (await currentlevelIndex) + 1;
    const previousLevelIndex = (await currentlevelIndex) - 1;
    const nextapprovalLevel =
      await this.findApprovalLevelByIndex(nextLevelIndex);
    const previousapprovalLevel =
      await this.findApprovalLevelByIndex(previousLevelIndex);

    const decision = vehicleRegApprovalDto.decision;
    const currentStatus = vehicleData.registrationStatus.name;
    if (currentStatus === 'APPROVED') {
      return {
        message: 'VEHICLE  ALREADY APPROVED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if (currentStatus === 'DENIED') {
      return {
        message: 'VEHICLE ALREADY REJECTED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if ((await currentlevelIndex) <= 5) {
      if (decision === 'APPROVED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: nextapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          finaldataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'MOS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataapprovedstatus = await this.statusRepository.findOne({
            name: 'PROGRESS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataPendingstatus = await this.statusRepository.findOne({
            name: 'PENDING',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PENDING STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          finalApprovedstatus = await this.statusRepository.findOne({
            name: 'APPROVED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        // console.log(dataapprovalLevel);
        // check if cuurent lebal is equal to fleet management engeener
        if (currentLevel === 'FMSE') {
          if (
            vehicleRegApprovalDto.pickCardNumber === null ||
            vehicleRegApprovalDto.plateNumber === null
          )
            return {
              message: 'pick Card Number  and plate Number NUMBER IS REQUIRED',
              statusCode: HttpStatus.BAD_REQUEST,
            };
          try {
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                registrationStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :vehicleId', { vehicleId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'IL1') {
          takendecision = decision;
          try {
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                registrationStatus: dataPendingstatus,
                approvalLevel: dataapprovalLevel,
                pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :vehicleId', { vehicleId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'ICBM2') {
          takendecision = decision;
          try {
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                submittedDate: new Date(),
                registrationStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :vehicleId', { vehicleId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'MOS' || currentLevel === 'PS') {
          // console.log('testetettte');
          const quarterObject = await this.getQuarterDates(new Date());
          takendecision = decision;
          try {
            const savedEntity =
              await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
                .update(RegisteredVehicle)
                .set({
                  approveddate: new Date(),
                  registrationStatus: finalApprovedstatus,
                  isQuarterlyReportSubmited: true,
                  isoverdueQuarterlyReportSubmition: false,
                  lastQuarterlyReportsubmittedDate: new Date(),
                  startNextQuarterlyReportsubmittedDate:
                    quarterObject.nextQuarter.startDate,
                  endNextQuarterlyReportsubmittedDate:
                    quarterObject.nextQuarter.endDate,
                  approvalLevel: finaldataapprovalLevel,
                  // approvalLevel: finalApprovedstatus,
                  // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                  // plateNumber: vehicleRegApprovalDto.plateNumber,
                })
                .where('id = :vehicleId', { vehicleId })
                .execute();
            // sending email

            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );

              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendNotificationEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'DGT') {
          try {
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                registrationStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :vehicleId', { vehicleId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
      }
      if (decision === 'RFAC') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: previousapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataaRFstutus = await this.statusRepository.findOne({
            name: 'RFAC',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' RFAC STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          const savedEntity =
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                registrationStatus: dataaRFstutus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :vehicleId', { vehicleId })
              .execute();

          // sending email logic
          if (previousapprovalLevel === 'IL1') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'ICBM2') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional CBM', // role name sending email to Institutional CBM
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'FMSE') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Fleet Mgt Senior Engineer', // role name sending email to Fleet Mgt Senior Engineer
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'DGT') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'DG Transport', // role name sending email to Fleet DG Transport
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'PS') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Permanent Secretary', // role name sending email to Permanent Secretary
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      if (decision === 'DENIED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'IL1 LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          dataaRejectedStatus = await this.statusRepository.findOne({
            name: 'DENIED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' REJECTED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          const savedEntity =
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                registrationStatus: dataaRejectedStatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :vehicleId', { vehicleId })
              .execute();
          if (savedEntity) {
            const userToSendEmail = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional CBM', // role name sending email to Institutional CBM
            );
            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail = await this.sendRFCEmail(email, name);
            if (sentEmail) {
              console.log('========= email sent successfully======');
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
    }
    if ((await currentlevelIndex) >= 6) {
      return {
        message: 'APPROVAL LEVAL TERMINATED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }

    // Performing actions
    const updateVhicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    try {
      cuurentLeveldata = await this.approvalLevelRepository.findOne({
        code: currentLevel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: `APPROVAL LEVEL WITH ${currentLevel} AS CODE NOT FOUND`,
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data approvalLevel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // after checking is acquisitionApprovalDto.comments has value  (saving that comment)
    // (create comment)saving applicationID, userID,comments and cuurentLevel into Comments entity
    const comments = vehicleRegApprovalDto.comments;
    let activityHistory;
    // if (comments !== undefined && comments !== null && comments.trim() !== '') {
    // if (comments) {
    // async saveComment(acquisitionId, userId, comments, cuurentLeveldata) {
    try {
      const activityRecodingDto = new ActivityRecoding({
        ...CreateCommentsDto,
        userId: vehicleRegApprovalDto.userId, // Assign the StatusType object
        approvalLevel: cuurentLeveldata, // Assign the ApprovalLevel object
        actionId: vehicleId, // Assign the
        comments: comments,
        activityPerformed: takendecision, // Assign the RequestType object
      });

      activityHistory =
        await this.activityRecodingRepository.create(activityRecodingDto);
    } catch (error) {
      throw new Error('Failed to save comments: ' + error.message);
    }
    // console.log(updateVhicleData.registrationStatus.name);

    return {
      vehicle: updateVhicleData,
      activityHistory: activityHistory,
    };
  }

  // approval existing vehicle registration
  async existingVehicleRegapproval(
    vehicleRegApprovalDto: ExistingVehicleRegApprovalDto,
  ) {
    const vehicleId = vehicleRegApprovalDto.vehicleId;
    let dataapprovalLevel;
    let registrationlevel;
    // let dataapprovedstatus;
    // let dataPendingstatus;
    let dataaRFstutus;
    let dataaRejectedStatus;
    let cuurentLeveldata;
    let takendecision;
    let finalApprovedstatus;
    let institutionReceiveEmail;
    let vehicleStatusData;
    const vehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();
    if (!vehicleData) {
      return { message: 'Vehicle NOT FOUND', error: HttpStatus.NOT_FOUND };
    }
    const currentLevel = vehicleData.approvalLevel.code;
    // institition to send email
    institutionReceiveEmail = vehicleData.reportingInstitutionId;
    if (!institutionReceiveEmail) {
      institutionReceiveEmail = vehicleData.beneficiaryAgencyId;
    }
    // console.log(vehicleData)
    // console.log(vehicleData);
    // const currentLevel = vehicleData.approvalLevel.code; // This will be "ICBM2"
    // console.log(currentLevel);
    // const currentlevelIndex = this.findIndexByApprovalLevel(currentLevel);
    // const nextLevelIndex = (await currentlevelIndex) + 1;
    // const previousLevelIndex = (await currentlevelIndex) - 1;
    // const nextapprovalLevel =
    //   await this.findApprovalLevelByIndex(nextLevelIndex);
    // const previousapprovalLevel =
    //   await this.findApprovalLevelByIndex(previousLevelIndex);

    const decision = vehicleRegApprovalDto.decision;
    const currentStatus = vehicleData.registrationStatus.name;
    if (currentStatus === 'APPROVED') {
      return {
        message: 'VEHICLE  ALREADY APPROVED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if (currentStatus === 'DENIED') {
      return {
        message: 'VEHICLE ALREADY REJECTED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    try {
      registrationlevel = await this.approvalLevelRepository.findOne({
        code: 'MOS',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with MOS as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // vehicle status
    try {
      vehicleStatusData = await this.vehicleStatusRepository.findOne({
        id: vehicleRegApprovalDto.vehicleStatusId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with MOS as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    if (currentLevel === 'ICBM2') {
      // console.log(dataapprovalLevel);
      // check if cuurent lebal is equal to fleet management engeene

      if (decision === 'APPROVED') {
        takendecision = decision;
        try {
          finalApprovedstatus = await this.statusRepository.findOne({
            name: takendecision,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        const quarterObject = await this.getQuarterDates(new Date());
        // takendecision = decision;
        try {
          await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
            .update(RegisteredVehicle)
            .set({
              submittedDate: new Date(),
              approveddate: new Date(),
              registrationStatus: finalApprovedstatus,
              isQuarterlyReportSubmited: true,
              isoverdueQuarterlyReportSubmition: false,
              lastQuarterlyReportsubmittedDate: new Date(),
              startNextQuarterlyReportsubmittedDate:
                quarterObject.nextQuarter.startDate,
              endNextQuarterlyReportsubmittedDate:
                quarterObject.nextQuarter.endDate,
              approvalLevel: registrationlevel,
              vehicleStatus: vehicleStatusData,
              isVehicleActive: vehicleRegApprovalDto.isVehicleActive,
            })
            .where('id = :vehicleId', { vehicleId })
            .execute();
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }

      // if decision is for
      if (decision === 'RFAC') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          dataaRFstutus = await this.statusRepository.findOne({
            name: takendecision,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' RFAC STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          const savedEntity =
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                registrationStatus: dataaRFstutus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :vehicleId', { vehicleId })
              .execute();
          if (savedEntity) {
            const userToSendEmail = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional logistics', // role name sending email to instution logistics
            );

            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail = await this.sendRFCEmail(email, name);
            if (sentEmail) {
              console.log('========= email sent successfully======');
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message ||
              'An error occurred while updating Existing vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      if (decision === 'DENIED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'IL1 LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          dataaRejectedStatus = await this.statusRepository.findOne({
            name: 'DENIED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' REJECTED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          const savedEntity =
            await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
              .update(RegisteredVehicle)
              .set({
                registrationStatus: dataaRejectedStatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })

              .where('id = :vehicleId', { vehicleId })
              .execute();
          if (savedEntity) {
            const userToSendEmail = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional logistics', // role name sending email to Institutional CBM
            );

            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail = await this.sendRFCEmail(email, name);
            if (sentEmail) {
              console.log('========= email sent successfully======');
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
    }
    const updateVhicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )

        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();
    try {
      cuurentLeveldata = await this.approvalLevelRepository.findOne({
        code: currentLevel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: `APPROVAL LEVEL WITH ${currentLevel} AS CODE NOT FOUND`,
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data approvalLevel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // after checking is acquisitionApprovalDto.comments has value  (saving that comment)
    // (create comment)saving applicationID, userID,comments and cuurentLevel into Comments entity
    const comments = vehicleRegApprovalDto.comments;
    let activityHistory;
    // if (comments !== undefined && comments !== null && comments.trim() !== '') {
    // if (comments) {
    // async saveComment(acquisitionId, userId, comments, cuurentLeveldata) {
    try {
      const activityRecodingDto = new ActivityRecoding({
        ...CreateCommentsDto,
        userId: vehicleRegApprovalDto.userId, // Assign the StatusType object
        approvalLevel: cuurentLeveldata, // Assign the ApprovalLevel object
        actionId: vehicleId, // Assign the
        comments: comments,
        activityPerformed: takendecision, // Assign the RequestType object
      });

      activityHistory =
        await this.activityRecodingRepository.create(activityRecodingDto);
    } catch (error) {
      throw new Error('Failed to save comments: ' + error.message);
    }

    // console.log(updateVhicleData.registrationStatus.name);

    return {
      vehicle: updateVhicleData,
      activityHistory: activityHistory,
    };
  }

  // vehiclecle disposal approve
  async disposalAproval(
    vehicleDisposalApprovalDto: VehicleDisposalApprovalDto,
  ) {
    let disposalData;
    const disposaId = vehicleDisposalApprovalDto.disposalId;
    // console.log(vehicleDisposalApprovalDto);
    // const userId = vehicleDisposalApprovalDto.userId;
    let dataapprovalLevel;
    let dataapprovedstatus;
    let dataPendingstatus;
    let dataaRFstutus;
    let dataaRejectedStatus;
    let cuurentLeveldata;
    let takendecision;
    let finalApprovedstatus;
    let institutionReceiveEmail;
    let updateRegistredVehicale;
    // console.log(disposaId);
    // check if disposal id exist

    try {
      disposalData = await this.vehicleDisposalManagerRepository
        .createQueryBuilder('vehicleDisposal')
        .where('vehicleDisposal.id = :disposaId', { disposaId })
        .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
        .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
        .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
        .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect('vehicleDisposal.vehicle', 'vehicle')
        .getOne();
      // console.log(disposalData);

      if (!disposalData) {
        throw new NotFoundException(
          `Disposal with ID '${disposaId}' not found`,
        );
      }
      // return vehicleData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Handle NotFoundException
        return {
          message: 'Disposal not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        // Handle other errors
        throw new Error(`Failed to fetch Disapol data: ${error.message}`);
      }
    }
    // institution to send email
    institutionReceiveEmail = disposalData.vehicle.reportingInstitutionId;
    if (!institutionReceiveEmail) {
      institutionReceiveEmail = disposalData.vehicle.beneficiaryAgencyId;
    }
    const currentLevel = disposalData.approvalLevel.code;
    const currentlevelIndex = this.findIndexByApprovalLevel(currentLevel);
    const nextLevelIndex = (await currentlevelIndex) + 1;
    const previousLevelIndex = (await currentlevelIndex) - 1;
    const nextapprovalLevel =
      await this.findApprovalLevelByIndex(nextLevelIndex);
    const previousapprovalLevel =
      await this.findApprovalLevelByIndex(previousLevelIndex);
    const decision = vehicleDisposalApprovalDto.decision;
    const currentStatus = disposalData.requestStatus.name;
    if (currentStatus === 'APPROVED') {
      return {
        message: 'ACQUISITION ALREADY APPROVED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if (currentStatus === 'DENIED') {
      return {
        message: 'ACQUISITION ALREADY REJECTED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if ((await currentlevelIndex) <= 5) {
      // console.log(currentLevel);
      // console.log(currentlevelIndex);
      if (decision === 'APPROVED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: nextapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataapprovedstatus = await this.statusRepository.findOne({
            name: 'PROGRESS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataPendingstatus = await this.statusRepository.findOne({
            name: 'PENDING',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PENDING STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          finalApprovedstatus = await this.statusRepository.findOne({
            name: 'APPROVED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'FMSE') {
          try {
            await this.vehicleDisposalManagerRepository
              .createQueryBuilder()
              .update(VehicleDisposal)
              .set({
                requestStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :disposaId', { disposaId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'IL1') {
          takendecision = decision;
          try {
            await this.vehicleDisposalManagerRepository
              .createQueryBuilder()
              .update(VehicleDisposal)
              .set({
                requestStatus: dataPendingstatus,
                approvalLevel: dataapprovalLevel,
              })
              .where('id = :disposaId', { disposaId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'ICBM2') {
          takendecision = decision;
          try {
            await this.vehicleDisposalManagerRepository
              .createQueryBuilder()
              .update(VehicleDisposal)
              .set({
                submittedDate: new Date(),
                requestStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :disposaId', { disposaId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'MOS') {
          // console.log('testetettte');
          takendecision = decision;
          try {
            const savedEntity = await this.vehicleDisposalManagerRepository
              .createQueryBuilder()
              .update(VehicleDisposal)
              .set({
                approveddate: new Date(),
                requestStatus: finalApprovedstatus,
                // approvalLevel: finalApprovedstatus,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :disposaId', { disposaId })
              .execute();

            // handoling vehicle disposal logic by updating vehicle table
            const mininfraIstitionData = await this.getInstitution('mininfra');
            const vehicleId = disposalData.vehicle.id;
            const disposalTypeName = disposalData.disposalTypes.name;
            if (disposalTypeName === 'Handover') {
              let vehilceStatusData2;
              try {
                vehilceStatusData2 = await this.vehicleStatusRepository.findOne(
                  {
                    name: 'RETURNED',
                  },
                );
              } catch (error) {
                if (error.message === 'Entity not found.') {
                  return {
                    message: 'Vehicle status  WITH RETURNED AS name NOT FOUND',
                    statusCode: HttpStatus.NOT_FOUND,
                  };
                } else {
                  return {
                    message:
                      error.message ||
                      'An error occurred while fetching data of vehicle status.',
                    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                  };
                }
              }

              updateRegistredVehicale =
                await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
                  .update('RegisteredVehicle') // Provide the table name
                  .set({
                    beneficiaryAgencyId: mininfraIstitionData[0].id,
                    beneficiaryAgency: mininfraIstitionData[0].name,
                    reportingInstitution: null,
                    reportingInstitutionId: null,
                    vehicleStatus: vehilceStatusData2,
                  })
                  .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
                  .execute();
            }
            if (disposalTypeName === 'vehicle distractions') {
              let vehilceStatusData3;
              try {
                vehilceStatusData3 = await this.vehicleStatusRepository.findOne(
                  {
                    name: 'DESTRUCTED',
                  },
                );
              } catch (error) {
                if (error.message === 'Entity not found.') {
                  return {
                    message:
                      'Vehicle status  WITH DESTRUCTED AS name NOT FOUND',
                    statusCode: HttpStatus.NOT_FOUND,
                  };
                } else {
                  return {
                    message:
                      error.message ||
                      'An error occurred while fetching data of vehicle status DESTRUCTED.',
                    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                  };
                }
              }

              updateRegistredVehicale =
                await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
                  .update('RegisteredVehicle') // Provide the table name
                  .set({
                    beneficiaryAgencyId: null,
                    beneficiaryAgency: null,
                    reportingInstitution: null,
                    reportingInstitutionId: null,
                    isVehicleActive: false,
                    vehicleStatus: vehilceStatusData3,
                  })
                  .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
                  .execute();
            }

            if (savedEntity && updateRegistredVehicale) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
                // 'Institutional logistics',
              );

              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendNotificationEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }

              // handoling vehicle disposal logic
            }
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }

          // console.log(disposalTypeName);
        }

        if (currentLevel === 'DGT' || currentLevel === 'PS') {
          try {
            console.log(dataapprovalLevel);
            await this.vehicleDisposalManagerRepository
              .createQueryBuilder()
              .update(VehicleDisposal)
              .set({
                requestStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :disposaId', { disposaId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
      }

      if (decision === 'RFAC') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: previousapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataaRFstutus = await this.statusRepository.findOne({
            name: 'RFAC',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' RFAC STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          const savedEntity = await this.vehicleDisposalManagerRepository
            .createQueryBuilder()
            .update(VehicleDisposal)
            .set({
              requestStatus: dataaRFstutus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = :disposaId', { disposaId })
            .execute();

          // sending email logic
          if (previousapprovalLevel === 'IL1') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'ICBM2') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional CBM', // role name sending email to Institutional CBM
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'FMSE') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Fleet Mgt Senior Engineer', // role name sending email to Fleet Mgt Senior Engineer
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'DGT') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'DG Transport', // role name sending email to Fleet DG Transport
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'PS') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Permanent Secretary', // role name sending email to Permanent Secretary
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      if (decision === 'DENIED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'IL1 LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          dataaRejectedStatus = await this.statusRepository.findOne({
            name: 'DENIED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' REJECTED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          const savedEntity = await this.vehicleDisposalManagerRepository
            .createQueryBuilder()
            .update(VehicleDisposal)
            .set({
              requestStatus: dataaRejectedStatus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = :disposaId', { disposaId })
            .execute();
          if (savedEntity) {
            const userToSendEmail = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional CBM', // role name sending email to Institutional CBM
            );
            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail = await this.sendRFCEmail(email, name);
            if (sentEmail) {
              console.log('========= email sent successfully======');
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
    }

    if ((await currentlevelIndex) >= 6) {
      return {
        message: 'APPROVAL LEVAL TERMINATED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    const updatedDisposalData = await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .where('vehicleDisposal.id = :disposaId', {
        disposaId,
      })

      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicleDisposal.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.vehicleRegRequest', 'vehicleRegRequest')
      // .leftJoinAndSelect('vehicle.VehicleAcquisition', 'VehicleAcquisition')
      .leftJoinAndSelect('vehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect('vehicle.vehicleManufacture', 'vehicleManufacture')
      .leftJoinAndSelect('vehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('vehicle.vehicleStatus', 'vehicleStatus')
      // .leftJoinAndSelect('vehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicle.registrationStatus', 'registrationStatus')
      .getOne();
    // this code need to be refised to put into one function for bettter code reuse

    try {
      cuurentLeveldata = await this.approvalLevelRepository.findOne({
        code: currentLevel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: `APPROVAL LEVEL WITH ${currentLevel} AS CODE NOT FOUND`,
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data approvalLevel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // after checking is acquisitionApprovalDto.comments has value  (saving that comment)
    // (create comment)saving applicationID, userID,comments and cuurentLevel into Comments entity
    const comments = vehicleDisposalApprovalDto.comments;
    let activityHistory;
    // if (comments !== undefined && comments !== null && comments.trim() !== '') {
    // if (comments) {
    // async saveComment(acquisitionId, userId, comments, cuurentLeveldata) {
    try {
      const activityRecodingDto = new ActivityRecoding({
        ...CreateCommentsDto,
        userId: vehicleDisposalApprovalDto.userId, // Assign the StatusType object
        approvalLevel: cuurentLeveldata, // Assign the ApprovalLevel object
        actionId: disposaId, // Assign the
        comments: comments,
        activityPerformed: takendecision, // Assign the RequestType object
      });

      activityHistory =
        await this.activityRecodingRepository.create(activityRecodingDto);
    } catch (error) {
      throw new Error('Failed to save comments: ' + error.message);
    }
    // console.log(updateVhicleData.registrationStatus.name);

    return {
      vehicleDisposal: updatedDisposalData,
      activityHistory: activityHistory,
    };
  }

  // auction report approval
  //AuctionReportApprovalDto
  async actionReport(auctionReportApprovalDto: AuctionReportApprovalDto) {
    let dataapprovalLevel;
    let dataapprovedstatus;
    let dataPendingstatus;
    let dataaRFstutus;
    let dataaRejectedStatus;
    let cuurentLeveldata;
    let takendecision;
    let finalApprovedstatus;
    let vehilceStatusData;
    let institutionReceiveEmail;
    let finaldataapprovalLevel;
    const id = auctionReportApprovalDto.auctionReportId;
    const reportData = await this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      .where('auctionReport.id = :id', {
        id,
      })
      .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('disposal.vehicle', 'vehicle')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel')
      .getOne();
    if (!reportData) {
      return {
        message: 'disposal report  NOT FOUND',
        error: HttpStatus.NOT_FOUND,
      };
    }

    // instition to send email
    institutionReceiveEmail =
      reportData.disposal.vehicle.reportingInstitutionId;
    if (!institutionReceiveEmail) {
      institutionReceiveEmail = reportData.disposal.vehicle.beneficiaryAgencyId;
    }
    const currentLevel = reportData.approvalLevel.code;
    const currentlevelIndex = this.findIndexByApprovalLevel(currentLevel);
    const nextLevelIndex = (await currentlevelIndex) + 1;
    const previousLevelIndex = (await currentlevelIndex) - 1;
    const nextapprovalLevel =
      await this.findApprovalLevelByIndex(nextLevelIndex);
    const previousapprovalLevel =
      await this.findApprovalLevelByIndex(previousLevelIndex);
    const decision = auctionReportApprovalDto.decision;
    const currentStatus = reportData.requestStatus.name;
    if (currentStatus === 'APPROVED') {
      return {
        message: 'VEHICLE  ALREADY APPROVED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if (currentStatus === 'DENIED') {
      return {
        message: 'VEHICLE ALREADY REJECTED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    try {
      vehilceStatusData = await this.vehicleStatusRepository.findOne({
        name: 'AUCTIONED',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Vehicle status  WITH AUCTIONED AS name NOT FOUND',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data of vehicle status.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    if ((await currentlevelIndex) <= 5) {
      if (decision === 'APPROVED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: nextapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          finaldataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'MOS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataapprovedstatus = await this.statusRepository.findOne({
            name: 'PROGRESS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataPendingstatus = await this.statusRepository.findOne({
            name: 'PENDING',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PENDING STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          finalApprovedstatus = await this.statusRepository.findOne({
            name: 'APPROVED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'IL1') {
          takendecision = decision;
          try {
            await this.auctionReportManagerRepository
              .createQueryBuilder()
              .update(AuctionReport)
              .set({
                requestStatus: dataPendingstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :id', { id })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'ICBM2') {
          takendecision = decision;
          try {
            // console.log(reportData.disposal.vehicle.beneficiaryAgencyId);
            await this.auctionReportManagerRepository
              .createQueryBuilder()
              .update(AuctionReport)
              .set({
                submittedDate: new Date(),
                requestStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :id', { id })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while auction report.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'FMSE' || currentLevel === 'DGT') {
          takendecision = decision;
          try {
            // console.log(reportData.disposal.vehicle.beneficiaryAgencyId);
            await this.auctionReportManagerRepository
              .createQueryBuilder()
              .update(AuctionReport)
              .set({
                requestStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :id', { id })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while auction report.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'MOS' || currentLevel === 'PS') {
          // console.log('testetettte');
          takendecision = decision;
          try {
            const savedEntity = await this.auctionReportManagerRepository
              .createQueryBuilder()
              .update(AuctionReport)
              .set({
                approveddate: new Date(),
                requestStatus: finalApprovedstatus,
                approvalLevel: finaldataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :id', { id })
              .execute();
            // sending email

            if (savedEntity) {
              // i need to change status of vehicle
              const disposalId = reportData.disposal.id;
              const vehicleId = reportData.disposal.vehicle.id;
              try {
                await this.vehicleDisposalManagerRepository
                  .createQueryBuilder()
                  .update(VehicleDisposal)
                  .set({
                    isActionReportsubmitted: true,
                  })
                  .where('id = :disposalId', { disposalId })
                  .execute();
              } catch (error) {
                // Handle errors
                return {
                  message:
                    error.message ||
                    'An error occurred while updating vegicle disposal status.',
                  statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                };
              }

              // updating registred vehicle
              try {
                await this.RegisteredVehicleentityManagerRepository.createQueryBuilder()
                  .update(RegisteredVehicle)
                  .set({
                    vehicleStatus: vehilceStatusData,
                    isVehicleActive: false,
                    beneficiaryAgencyId: null,
                    beneficiaryAgency: null,
                    reportingInstitution: null,
                    reportingInstitutionId: null,
                  })
                  .where('id = :vehicleId', { vehicleId })
                  .execute();
              } catch (error) {
                // Handle errors
                return {
                  message:
                    error.message ||
                    'An error occurred while updating vehicle.',
                  statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                };
              }

              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendNotificationEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
      }
      if (decision === 'RFAC') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: previousapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataaRFstutus = await this.statusRepository.findOne({
            name: 'RFAC',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' RFAC STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          const savedEntity = await this.auctionReportManagerRepository
            .createQueryBuilder()
            .update(AuctionReport)
            .set({
              requestStatus: dataaRFstutus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = : id', { id })
            .execute();

          // sending email logic
          if (previousapprovalLevel === 'IL1') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'ICBM2') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional CBM', // role name sending email to Institutional CBM
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'FMSE') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Fleet Mgt Senior Engineer', // role name sending email to Fleet Mgt Senior Engineer
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'DGT') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'DG Transport', // role name sending email to Fleet DG Transport
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'PS') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Permanent Secretary', // role name sending email to Permanent Secretary
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      if (decision === 'DENIED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'IL1 LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          dataaRejectedStatus = await this.statusRepository.findOne({
            name: 'DENIED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' REJECTED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          const savedEntity = await this.auctionReportManagerRepository
            .createQueryBuilder()
            .update(AuctionReport)

            .set({
              requestStatus: dataaRejectedStatus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = :id', { id })
            .execute();
          if (savedEntity) {
            const userToSendEmail = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional CBM', // role name sending email to Institutional CBM
            );
            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail = await this.sendRFCEmail(email, name);
            if (sentEmail) {
              console.log('========= email sent successfully to CBM======');
            }

            const userToSendEmail2 = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional logistics', // role name sending email to instution logistics
            );
            const email2 = userToSendEmail2[0].email;
            console.log(email);
            const name2 = userToSendEmail2[0].firstName;
            console.log(name);
            const sentEmail2 = await this.sendRFCEmail(email2, name2);
            if (sentEmail2) {
              console.log(
                '========= email sent successfully to logistics======',
              );
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
    }
    if ((await currentlevelIndex) >= 6) {
      return {
        message: 'APPROVAL LEVAL TERMINATED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    const updatedReportData = await this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      .where('auctionReport.id = :id', {
        id,
      })

      // .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      // .leftJoinAndSelect('disposal.vehicle', 'vehicle')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel')
      .getOne();

    try {
      cuurentLeveldata = await this.approvalLevelRepository.findOne({
        code: currentLevel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: `APPROVAL LEVEL WITH ${currentLevel} AS CODE NOT FOUND`,
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data approvalLevel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const comments = auctionReportApprovalDto.comments;
    let activityHistory;
    // if (comments !== undefined && comments !== null && comments.trim() !== '') {
    // if (comments) {
    // async saveComment(acquisitionId, userId, comments, cuurentLeveldata) {
    try {
      const activityRecodingDto = new ActivityRecoding({
        ...CreateCommentsDto,
        userId: auctionReportApprovalDto.userId, // Assign the StatusType object
        approvalLevel: cuurentLeveldata, // Assign the ApprovalLevel object
        actionId: id, // Assign the
        comments: comments,
        activityPerformed: takendecision, // Assign the RequestType object
      });

      activityHistory =
        await this.activityRecodingRepository.create(activityRecodingDto);
    } catch (error) {
      throw new Error('Failed to save comments: ' + error.message);
    }

    return {
      auctionReport: updatedReportData,
      activityHistory: activityHistory,
    };
  }
  // end of quatery report approval

  async quateryReportApproval(
    quarterlyReportApprovalDto: QuarterlyReportApprovalDto,
  ) {
    let dataapprovalLevel;
    let dataapprovedstatus;
    let dataPendingstatus;
    let dataaRFstutus;
    let dataaRejectedStatus;
    let cuurentLeveldata;
    let takendecision;
    let finalApprovedstatus;
    let institutionReceiveEmail;
    let finaldataapprovalLevel;
    const quarterlyReportId = quarterlyReportApprovalDto.quarterlyReportId;
    const quarterlyReportData =
      await this.vehicleQuarterlyReportManagerRepository
        .createQueryBuilder('VehicleQuarterlyReport')
        .where('VehicleQuarterlyReport.id = :quarterlyReportId', {
          quarterlyReportId,
        })

        .leftJoinAndSelect(
          'VehicleQuarterlyReport.vehicleStatus',
          'vehicleStatus',
        )
        .leftJoinAndSelect(
          'VehicleQuarterlyReport.reportStatus',
          'reportStatus',
        )
        .leftJoinAndSelect(
          'VehicleQuarterlyReport.approvalLevel',
          'approvalLevel',
        )
        .leftJoinAndSelect('VehicleQuarterlyReport.vehicle', 'vehicle')
        .getOne();
    if (!quarterlyReportData) {
      return {
        message: 'quarterly ReportData  NOT FOUND',
        error: HttpStatus.NOT_FOUND,
      };
    }
    institutionReceiveEmail =
      quarterlyReportData.vehicle.reportingInstitutionId;
    if (!institutionReceiveEmail) {
      institutionReceiveEmail = quarterlyReportData.vehicle.beneficiaryAgencyId;
    }

    const currentLevel = quarterlyReportData.approvalLevel.code;
    const currentlevelIndex = this.findIndexByApprovalLevel(currentLevel);
    const nextLevelIndex = (await currentlevelIndex) + 1;
    const previousLevelIndex = (await currentlevelIndex) - 1;
    const nextapprovalLevel =
      await this.findApprovalLevelByIndex(nextLevelIndex);
    const previousapprovalLevel =
      await this.findApprovalLevelByIndex(previousLevelIndex);
    const decision = quarterlyReportApprovalDto.decision;
    const currentStatus = quarterlyReportData.reportStatus.name;

    if (currentStatus === 'APPROVED') {
      return {
        message: 'Quartely report  ALREADY received',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if (currentStatus === 'DENIED') {
      return {
        message: 'Quartely report  ALREADY REJECTED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if ((await currentlevelIndex) <= 5) {
      if (decision === 'APPROVED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: nextapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          finaldataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'MOS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataapprovedstatus = await this.statusRepository.findOne({
            name: 'PROGRESS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataPendingstatus = await this.statusRepository.findOne({
            name: 'PENDING',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PENDING STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          finalApprovedstatus = await this.statusRepository.findOne({
            name: 'APPROVED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'IL1') {
          takendecision = decision;
          try {
            await this.vehicleQuarterlyReportManagerRepository
              .createQueryBuilder()
              .update(VehicleQuarterlyReport)
              .set({
                reportStatus: dataPendingstatus,
                approvalLevel: dataapprovalLevel,
                // approveddate: new Date(),
                // comments: quarterlyReportApprovalDto.comments,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :quarterlyReportId', { quarterlyReportId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'ICBM2') {
          takendecision = decision;
          try {
            // console.log(reportData.disposal.vehicle.beneficiaryAgencyId);
            await this.vehicleQuarterlyReportManagerRepository
              .createQueryBuilder()
              .update(VehicleQuarterlyReport)
              .set({
                submittedDate: new Date(),
                reportStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :quarterlyReportId', { quarterlyReportId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while auction report.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'FMSE' || currentLevel === 'DGT') {
          takendecision = decision;
          try {
            // console.log(reportData.disposal.vehicle.beneficiaryAgencyId);
            await this.vehicleQuarterlyReportManagerRepository
              .createQueryBuilder()
              .update(VehicleQuarterlyReport)
              .set({
                reportStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :quarterlyReportId', { quarterlyReportId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while auction report.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'MOS' || currentLevel === 'PS') {
          // console.log('testetettte');
          takendecision = decision;
          try {
            const savedEntity = await this.auctionReportManagerRepository
              .createQueryBuilder()
              .update(VehicleQuarterlyReport)
              .set({
                approveddate: new Date(),
                reportStatus: finalApprovedstatus,
                approvalLevel: finaldataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :quarterlyReportId', { quarterlyReportId })
              .execute();
            // sending email

            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendNotificationEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
      }
      if (decision === 'RFAC') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: previousapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataaRFstutus = await this.statusRepository.findOne({
            name: 'RFAC',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' RFAC STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          const savedEntity = await this.vehicleQuarterlyReportManagerRepository
            .createQueryBuilder()
            .update(VehicleQuarterlyReport)
            .set({
              reportStatus: dataaRFstutus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = : quarterlyReportId', { quarterlyReportId })
            .execute();

          // sending email logic
          if (previousapprovalLevel === 'IL1') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'ICBM2') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional CBM', // role name sending email to Institutional CBM
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'FMSE') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Fleet Mgt Senior Engineer', // role name sending email to Fleet Mgt Senior Engineer
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'DGT') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'DG Transport', // role name sending email to Fleet DG Transport
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'PS') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Permanent Secretary', // role name sending email to Permanent Secretary
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      if (decision === 'DENIED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'IL1 LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          dataaRejectedStatus = await this.statusRepository.findOne({
            name: 'DENIED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' REJECTED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          const savedEntity = await this.vehicleQuarterlyReportManagerRepository
            .createQueryBuilder()
            .update(VehicleQuarterlyReport)

            .set({
              reportStatus: dataaRejectedStatus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = :quarterlyReportId', { quarterlyReportId })
            .execute();
          if (savedEntity) {
            const userToSendEmail = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional CBM', // role name sending email to Institutional CBM
            );
            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail = await this.sendRFCEmail(email, name);
            if (sentEmail) {
              console.log('========= email sent successfully to CBM======');
            }

            const userToSendEmail2 = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional logistics', // role name sending email to instution logistics
            );
            const email2 = userToSendEmail2[0].email;
            console.log(email);
            const name2 = userToSendEmail2[0].firstName;
            console.log(name);
            const sentEmail2 = await this.sendRFCEmail(email2, name2);
            if (sentEmail2) {
              console.log(
                '========= email sent successfully to logistics======',
              );
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
    }
    if ((await currentlevelIndex) >= 6) {
      return {
        message: 'APPROVAL LEVAL TERMINATED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }

    const updateQuarterlyReportData =
      await this.vehicleQuarterlyReportManagerRepository
        .createQueryBuilder('VehicleQuarterlyReport')
        .where('VehicleQuarterlyReport.id = :quarterlyReportId', {
          quarterlyReportId,
        })

        .leftJoinAndSelect(
          'VehicleQuarterlyReport.vehicleStatus',
          'vehicleStatus',
        )
        .leftJoinAndSelect(
          'VehicleQuarterlyReport.reportStatus',
          'reportStatus',
        )
        .leftJoinAndSelect(
          'VehicleQuarterlyReport.approvalLevel',
          'approvalLevel',
        )
        .leftJoinAndSelect('VehicleQuarterlyReport.vehicle', 'vehicle')
        .getOne();

    try {
      cuurentLeveldata = await this.approvalLevelRepository.findOne({
        code: currentLevel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: `APPROVAL LEVEL WITH ${currentLevel} AS CODE NOT FOUND`,
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data approvalLevel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const comments = quarterlyReportApprovalDto.comments;
    let activityHistory;
    try {
      const activityRecodingDto = new ActivityRecoding({
        ...CreateCommentsDto,
        userId: quarterlyReportApprovalDto.userId, // Assign the StatusType object
        approvalLevel: cuurentLeveldata, // Assign the ApprovalLevel object
        actionId: quarterlyReportId, // Assign the
        comments: comments,
        activityPerformed: takendecision, // Assign the RequestType object
      });

      activityHistory =
        await this.activityRecodingRepository.create(activityRecodingDto);
    } catch (error) {
      throw new Error('Failed to save comments: ' + error.message);
    }
    return {
      updateQuarterlyReportData: updateQuarterlyReportData,
      activityHistory: activityHistory,
    };

    // return quarterlyReportData;
  }

  async ProjectExtensionApproval(
    projectExtensionApprovalDto: ProjectExtensionApprovalDto,
  ) {
    let dataapprovalLevel;
    let dataapprovedstatus;
    let dataPendingstatus;
    let dataaRFstutus;
    let dataaRejectedStatus;
    let cuurentLeveldata;
    let takendecision;
    let finalApprovedstatus;
    let institutionReceiveEmail;
    let finaldataapprovalLevel;

    const projectExtensionId = projectExtensionApprovalDto.projectExtensionId;
    const projectExtansionData =
      await this.projectExtensionManagerReportRepository
        .createQueryBuilder('ProjectExtension')
        .where('ProjectExtension.id = :projectExtensionId', {
          projectExtensionId,
        })
        .leftJoinAndSelect('ProjectExtension.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect('ProjectExtension.requestStatus', 'requestStatus')
        .leftJoinAndSelect('ProjectExtension.vehicle', 'vehicle')
        .getOne();
    if (!projectExtansionData) {
      return {
        message: ' Project Extension NOT FOUND',
        error: HttpStatus.NOT_FOUND,
      };
    }
    institutionReceiveEmail =
      projectExtansionData.vehicle.reportingInstitutionId;
    if (!institutionReceiveEmail) {
      institutionReceiveEmail =
        projectExtansionData.vehicle.beneficiaryAgencyId;
    }
    const currentLevel = projectExtansionData.approvalLevel.code;

    const currentlevelIndex = this.findIndexByApprovalLevel(currentLevel);
    const nextLevelIndex = (await currentlevelIndex) + 1;
    const previousLevelIndex = (await currentlevelIndex) - 1;
    const nextapprovalLevel =
      await this.findApprovalLevelByIndex(nextLevelIndex);
    const previousapprovalLevel =
      await this.findApprovalLevelByIndex(previousLevelIndex);
    const decision = projectExtensionApprovalDto.decision;
    // console.log(decision);
    const currentStatus = projectExtansionData.requestStatus.name;
    // console.log(decision);
    console.log(currentStatus);
    if (currentStatus === 'APPROVED') {
      return {
        message: 'Extention request approved',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if (currentStatus === 'DENIED') {
      return {
        message: 'Project Extension  ALREADY REJECTED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    if ((await currentlevelIndex) <= 5) {
      if (decision === 'APPROVED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: nextapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          finaldataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'MOS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataapprovedstatus = await this.statusRepository.findOne({
            name: 'PROGRESS',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PROGRESS STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataPendingstatus = await this.statusRepository.findOne({
            name: 'PENDING',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'PENDING STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          finalApprovedstatus = await this.statusRepository.findOne({
            name: 'APPROVED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching status data.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'IL1') {
          takendecision = decision;
          try {
            await this.projectExtensionManagerReportRepository
              .createQueryBuilder()
              .update(ProjectExtension)
              .set({
                requestStatus: dataPendingstatus,
                approvalLevel: dataapprovalLevel,
                // comments: projectExtensionApprovalDto.comments,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :projectExtensionId', { projectExtensionId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'ICBM2') {
          takendecision = decision;
          try {
            // console.log(reportData.disposal.vehicle.beneficiaryAgencyId);
            await this.projectExtensionManagerReportRepository
              .createQueryBuilder()
              .update(ProjectExtension)
              .set({
                submittedDate: new Date(),
                requestStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :projectExtensionId', { projectExtensionId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while auction report.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        if (currentLevel === 'FMSE' || currentLevel === 'DGT') {
          takendecision = decision;
          try {
            // console.log(reportData.disposal.vehicle.beneficiaryAgencyId);
            await this.projectExtensionManagerReportRepository
              .createQueryBuilder()
              .update(ProjectExtension)
              .set({
                requestStatus: dataapprovedstatus,
                approvalLevel: dataapprovalLevel,
                // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                // plateNumber: vehicleRegApprovalDto.plateNumber,
              })
              .where('id = :projectExtensionId', { projectExtensionId })
              .execute();
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while auction report.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        if (currentLevel === 'MOS' || currentLevel === 'PS') {
          // console.log('testetettte');
          takendecision = decision;
          try {
            const savedEntity =
              await this.projectExtensionManagerReportRepository
                .createQueryBuilder()
                .update(ProjectExtension)
                .set({
                  approveddate: new Date(),
                  requestStatus: finalApprovedstatus,
                  approvalLevel: finaldataapprovalLevel,
                  // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
                  // plateNumber: vehicleRegApprovalDto.plateNumber,
                })
                .where('id = :projectExtensionId', { projectExtensionId })
                .execute();
            // sending email

            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendNotificationEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          } catch (error) {
            // Handle errors
            return {
              message:
                error.message || 'An error occurred while updating vehicle.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
      }
      if (decision === 'RFAC') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: previousapprovalLevel,
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'APPROVAL LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          dataaRFstutus = await this.statusRepository.findOne({
            name: 'RFAC',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' RFAC STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }

        try {
          const savedEntity = await this.projectExtensionManagerReportRepository
            .createQueryBuilder()
            .update(ProjectExtension)
            .set({
              requestStatus: dataaRFstutus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = : projectExtensionId', { projectExtensionId })
            .execute();

          // sending email logic
          if (previousapprovalLevel === 'IL1') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional logistics', // role name sending email to instution logistics
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'ICBM2') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Institutional CBM', // role name sending email to Institutional CBM
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'FMSE') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Fleet Mgt Senior Engineer', // role name sending email to Fleet Mgt Senior Engineer
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'DGT') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'DG Transport', // role name sending email to Fleet DG Transport
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }

          if (previousapprovalLevel === 'PS') {
            if (savedEntity) {
              const userToSendEmail = await this.getDetailByRole(
                institutionReceiveEmail,
                'Permanent Secretary', // role name sending email to Permanent Secretary
              );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail = await this.sendRFCEmail(email, name);
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      if (decision === 'DENIED') {
        takendecision = decision;
        try {
          dataapprovalLevel = await this.approvalLevelRepository.findOne({
            code: 'IL1',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: 'IL1 LEVEL WITH FMSE AS CODE NOT FOUND',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          dataaRejectedStatus = await this.statusRepository.findOne({
            name: 'DENIED',
          });
        } catch (error) {
          if (error.message === 'Entity not found.') {
            return {
              message: ' REJECTED STATUS NOT FIND IN TABLE',
              statusCode: HttpStatus.NOT_FOUND,
            };
          } else {
            return {
              message:
                error.message ||
                'An error occurred while fetching data approvalLevel.',
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            };
          }
        }
        try {
          const savedEntity = await this.projectExtensionManagerReportRepository
            .createQueryBuilder()
            .update(ProjectExtension)

            .set({
              requestStatus: dataaRejectedStatus,
              approvalLevel: dataapprovalLevel,
              // pickCardNumber: vehicleRegApprovalDto.pickCardNumber,
              // plateNumber: vehicleRegApprovalDto.plateNumber,
            })
            .where('id = :projectExtensionId', { projectExtensionId })
            .execute();
          if (savedEntity) {
            const userToSendEmail = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional CBM', // role name sending email to Institutional CBM
            );
            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail = await this.sendRFCEmail(email, name);
            if (sentEmail) {
              console.log('========= email sent successfully to CBM======');
            }

            const userToSendEmail2 = await this.getDetailByRole(
              institutionReceiveEmail,
              'Institutional logistics', // role name sending email to instution logistics
            );
            const email2 = userToSendEmail2[0].email;
            console.log(email);
            const name2 = userToSendEmail2[0].firstName;
            console.log(name);
            const sentEmail2 = await this.sendRFCEmail(email2, name2);
            if (sentEmail2) {
              console.log(
                '========= email sent successfully to logistics======',
              );
            }
          }
        } catch (error) {
          // Handle errors
          return {
            message:
              error.message || 'An error occurred while updating vehicle.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
    }

    if ((await currentlevelIndex) >= 6) {
      return {
        message: 'APPROVAL LEVAL TERMINATED',
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }
    const updatedprojectExtansionData =
      await this.projectExtensionManagerReportRepository
        .createQueryBuilder('ProjectExtension')
        .where('ProjectExtension.id = :projectExtensionId', {
          projectExtensionId,
        })
        .leftJoinAndSelect('ProjectExtension.requestStatus', 'requestStatus')
        .leftJoinAndSelect('ProjectExtension.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect('ProjectExtension.vehicle', 'vehicle')
        .getOne();

    try {
      cuurentLeveldata = await this.approvalLevelRepository.findOne({
        code: currentLevel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: `APPROVAL LEVEL WITH ${currentLevel} AS CODE NOT FOUND`,
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data approvalLevel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const comments = projectExtensionApprovalDto.comments;
    let activityHistory;
    try {
      const activityRecodingDto = new ActivityRecoding({
        ...CreateCommentsDto,
        userId: projectExtensionApprovalDto.userId, // Assign the StatusType object
        approvalLevel: cuurentLeveldata, // Assign the ApprovalLevel object
        actionId: projectExtensionId, // Assign the
        comments: comments,
        activityPerformed: takendecision, // Assign the RequestType object
      });

      activityHistory =
        await this.activityRecodingRepository.create(activityRecodingDto);
    } catch (error) {
      throw new Error('Failed to save comments: ' + error.message);
    }
    console.log(updatedprojectExtansionData);
    return {
      updateQuarterlyReportData: updatedprojectExtansionData,
      activityHistory: activityHistory,
    };
  }

  async gethistory(actionId: string) {
    try {
      const activityHistory =
        await this.ActivityRecodingEntityManagerRepository.createQueryBuilder(
          'activityRecoding',
        )
          .where('activityRecoding.actionId = :actionId', {
            actionId,
          })
          .leftJoinAndSelect('activityRecoding.approvalLevel', 'approvalLevel')
          .getMany();
      return activityHistory;
    } catch (error) {
      throw new Error('Failed to get activity history: ' + error.message);
    }
  }

  async gethistoryByUserId(userId: string) {
    try {
      const activityHistory =
        await this.ActivityRecodingEntityManagerRepository.createQueryBuilder(
          'activityRecoding',
        )
          .where('activityRecoding.userId = :userId', { userId })
          .leftJoinAndSelect('activityRecoding.approvalLevel', 'approvalLevel')
          .getMany();

      // Grouping the activities by userId
      const groupedHistory = activityHistory.reduce((acc, activity) => {
        const userId = activity.userId;
        if (!acc[userId]) {
          acc[userId] = {
            userId,
            activities: [],
          };
        }
        acc[userId].activities.push({
          id: activity.id,
          actionId: activity.actionId,
          comments: activity.comments,
          activityPerformed: activity.activityPerformed,
          DateOfActivity: activity.DateOfActivity,
          approvalLevel: activity.approvalLevel,
        });
        return acc;
      }, {});

      // Converting the grouped history object back to an array
      return Object.values(groupedHistory);
    } catch (error) {
      throw new Error('Failed to get activity history: ' + error.message);
    }
  }

  // editing comment where comment userid and commentid
  async editComment(id: string, updateCommentDto: UpdateCommentDto) {
    try {
      await this.ActivityRecodingEntityManagerRepository.createQueryBuilder(
        'activityRecoding',
      )
        .where('activityRecoding.id = :id', { id })
        .update(ActivityRecoding)
        .set({ comments: updateCommentDto.comment })
        .execute();
      const newactivityRecoding =
        await this.ActivityRecodingEntityManagerRepository.createQueryBuilder(
          'activityRecoding',
        )
          .where('newactivityRecoding.id = :id', {
            id,
          })
          .leftJoinAndSelect('comments.approvalLevel', 'approvalLevel')
          .getMany();
      return newactivityRecoding;
    } catch (error) {
      throw new Error('Failed to edit comment: ' + error.message);
    }
  }
  async deleteHistory(id: string) {
    const deletedActivityRecoding =
      await this.ActivityRecodingEntityManagerRepository.createQueryBuilder()
        .delete()
        .from(ActivityRecoding)
        .where('id = :id', { id })
        .execute();
    return deletedActivityRecoding;
  }
}
