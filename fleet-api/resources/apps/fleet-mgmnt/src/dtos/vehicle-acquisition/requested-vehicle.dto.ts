import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateRequestedVehicleDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field acquisitionId is required' })
  @IsNotEmpty({ message: 'The field acquisitionId cannot be empty' })
  acquisitionId: string;
  @ApiProperty({ description: 'The field vehicleTypeid is required' })
  @IsNotEmpty({ message: 'The field vehicleTypeid cannot be empty' })
  vehicleTypeid: string;
  @ApiProperty({ description: 'The field numberOfVehicles is required' })
  @IsNotEmpty({ message: 'The field numberOfVehicles cannot be empty' })
  numberOfVehicles: number;

  @ApiProperty({ description: 'The field intended Usage ID is required' })
  @IsNotEmpty({ message: 'The field intended Usage cannot be empty' })
  intendedUsage: string;
  @IsNotEmpty({ message: 'The field beneficiary Agency IDcannot be empty' })
  beneficiaryAgencyId: string;
  @IsNotEmpty({ message: 'The field beneficiary Agency cannot be empty' })
  beneficiaryAgency: string;
  @ApiProperty({ description: 'The field projectName is required' })
  projectName?: string;
  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsNotEmpty({ message: 'The field projectDescription cannot be empty' })
  projectDescription?: string;
  @ApiProperty({ description: 'The projectStartDate is required' })
  projectStartDate?: Date;
  @ApiProperty({ description: 'The projectEndDate is required' })
  projectEndDate?: Date;
}

export class UpdateRequestedVehicleDto {
  @ApiProperty({ description: 'The field vehicleTypeid is required' })
  @IsNotEmpty({ message: 'The field vehicleTypeid cannot be empty' })
  vehicleTypeid: string;
  @ApiProperty({ description: 'The field numberOfVehicles is required' })
  @IsNotEmpty({ message: 'The field numberOfVehicles cannot be empty' })
  numberOfVehicles: number;
  @ApiProperty({ description: 'The field intended Usage ID is required' })
  @IsNotEmpty({ message: 'The field intended Usage cannot be empty' })
  intendedUsage: string;
  @ApiProperty({ description: 'The field intended Usage ID is required' })
  @IsNotEmpty({ message: 'The field beneficiary Agency IDcannot be empty' })
  beneficiaryAgencyId: string;
  @ApiProperty({ description: 'The field intended Usage ID is required' })
  @IsNotEmpty({ message: 'The field beneficiary Agency cannot be empty' })
  beneficiaryAgency: string;
  @ApiProperty({ description: 'The field projectName is required' })
  @IsOptional({ message: 'The field projectName can be empty' })
  projectName?: string;
  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsOptional({ message: 'The field projectDescription can be empty' })
  projectDescription?: string;
  @ApiProperty({ description: 'The projectStartDate is required' })
  @IsOptional({ message: 'The field projectStartDate can be empty' })
  projectStartDate?: Date;
  @ApiProperty({ description: 'The projectEndDate is required' })
  @IsOptional({ message: 'The field projectStartDate can be empty' })
  projectEndDate?: Date;
}
