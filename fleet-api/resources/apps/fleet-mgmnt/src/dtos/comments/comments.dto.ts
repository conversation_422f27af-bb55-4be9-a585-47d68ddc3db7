import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCommentsDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  userId: string;
  @ApiProperty({ description: 'The field application Id is required' })
  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  actionId: string;
  @ApiProperty({ description: 'The field approval level is required' })
  @IsNotEmpty({ message: 'The field approval level cannot be empty' })
  approvalLevel: string;
  @IsOptional({ message: 'The field id cannot be empty' })
  comments: string;
}
