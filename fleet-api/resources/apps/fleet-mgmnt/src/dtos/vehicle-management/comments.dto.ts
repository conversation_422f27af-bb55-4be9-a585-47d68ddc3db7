import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCommentDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field actionId is required' })
  @IsNotEmpty({ message: 'The field actionId cannot be empty' })
  activityId: string;
  @ApiProperty({ description: 'The field comment is required' })
  @IsNotEmpty({ message: 'The field comment cannot be empty' })
  comment: string;
  @ApiProperty({ description: 'The field comment is required' })
  @IsNotEmpty({ message: 'The field comment cannot be empty' })
  requestCode: string;
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  approvalLevelCode: string;

  //   @ApiProperty({ description: 'The field code is required' })
  //   @IsNotEmpty({ message: 'The field code cannot be empty' })
  //   code: string;
}

export class UpdateCommentDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  comment: string;
}
