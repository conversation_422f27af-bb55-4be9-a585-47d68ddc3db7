import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class DriverDetailsDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field isDriveAvailable is required' })
  @IsNotEmpty({ message: 'The field isDriveAvailable cannot be empty' })
  isDriveAvailable: boolean;
  @ApiProperty({ description: 'The field palanToRecruitDrive is required' })
  @IsOptional({ message: 'The field palanToRecruitDrive cannot be empty' })
  palanToRecruitDrive?: string;
  @ApiProperty({ description: 'The field driverQualification is required' })
  @IsOptional({ message: 'The field driverQualification can be empty' })
  driverQualification?: string;
  @ApiProperty({ description: 'The field driverQualification is required' })
  @IsNotEmpty({ message: 'The field driverQualification cannot be empty' })
  numberOfDriver: number;
  @ApiProperty({ description: 'The field VehicleAcquisition name is required' })
  @IsOptional({ message: 'The field VehicleAcquisition cannot be empty' })
  VehicleAcquisitionID?: string;
  @ApiProperty({ description: 'The field RequestedVehicle name is required' })
  @IsOptional({ message: 'The field RequestedVehicle cannot be empty' })
  RequestedVehicleID?: string;
}
