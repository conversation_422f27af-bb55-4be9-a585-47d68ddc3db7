import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CostBenefitDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field acquisitionCost is required' })
  @IsNotEmpty({ message: 'The field acquisitionCost cannot be empty' })
  acquisitionCost: number;
  @ApiProperty({ description: 'The field maintenanceCost is required' })
  @IsNotEmpty({ message: 'The field maintenanceCost cannot be empty' })
  maintenanceCost: number;
  @ApiProperty({ description: 'The field fuelCost is required' })
  @IsNotEmpty({ message: 'The field fuelCost cannot be empty' })
  fuelCost: number;
  @ApiProperty({ description: 'The field oilRefillingCost name is required' })
  @IsNotEmpty({ message: 'The field oilRefillingCost cannot be empty' })
  oilRefillingCost: number;
  @ApiProperty({ description: 'The field insuranceCost name is required' })
  @IsNotEmpty({ message: 'The field insuranceCost cannot be empty' })
  insuranceCost: number;
  @ApiProperty({ description: 'The field driveCost is required' })
  @IsNotEmpty({ message: 'The field driveCost cannot be empty' })
  driveCost: number;
  @ApiProperty({ description: 'The field depreciationCost is required' })
  @IsOptional({ message: 'The field depreciationCost can be empty' })
  depreciationCost: number;
  @ApiProperty({ description: 'The field VehicleAcquisitionID is required' })
  @IsOptional({ message: 'The field VehicleAcquisitionID cannot be empty' })
  VehicleAcquisitionID?: string;
  @ApiProperty({ description: 'The field RequestedVehicleID is required' })
  @IsOptional({ message: 'The field RequestedVehicleID cannot be empty' })
  RequestedVehicleID?: string;
}
