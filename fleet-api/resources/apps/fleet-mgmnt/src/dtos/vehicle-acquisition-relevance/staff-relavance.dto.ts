import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class StaffRelevanceDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;
  @ApiProperty({ description: 'The field staffCategory is required' })
  @IsNotEmpty({ message: 'The field staffCategory cannot be empty' })
  staffCategory: string;
  @ApiProperty({ description: 'The field staffsDepartments is required' })
  @IsOptional({ message: 'The field staffsDepartments cannot be empty' })
  staffsDepartments: string;
  @ApiProperty({ description: 'The field numberOfStaff is required' })
  @IsOptional({ message: 'The field numberOfStaff cannot be empty' })
  numberOfStaff: number;
  @ApiProperty({ description: 'The field acquisitionCost is required' })
  @IsNotEmpty({ message: 'The field acquisitionCost cannot be empty' })
  staffPosition: string;
  // @ApiProperty({ description: 'The field acquisitionCost is required' })
  // @IsNotEmpty({ message: 'The field staff Type cannot be empty' })
  // staffType: string;
  @ApiProperty({ description: 'The field maintenanceCost is required' })
  @IsNotEmpty({ message: 'The field maintenanceCost cannot be empty' })
  staffLevel: string;
  @ApiProperty({ description: 'The field fuelCost is required' })
  @IsNotEmpty({ message: 'The field fuelCost cannot be empty' })
  workLocation: string;
  @ApiProperty({ description: 'The field oilRefillingCost name is required' })
  @IsNotEmpty({ message: 'The field oilRefillingCost cannot be empty' })
  officeLocation: string;
  @ApiProperty({ description: 'The field insuranceCost name is required' })
  @IsNotEmpty({ message: 'The field insuranceCost cannot be empty' })
  dailyWorkFrequency: string;
  @IsNotEmpty({ message: 'The field number Of Month Per Year cannot be empty' })
  numberOfMonthPerYear: number;
  @ApiProperty({ description: 'The field driveCost is required' })
  @IsNotEmpty({ message: 'The field driveCost cannot be empty' })
  OthersDescription: string;
  @IsOptional({ message: 'The field VehicleAcquisitionID cannot be empty' })
  VehicleAcquisitionID?: string;
  @ApiProperty({ description: 'The field RequestedVehicleID is required' })
  @IsOptional({ message: 'The field RequestedVehicleID cannot be empty' })
  RequestedVehicleID?: string;
}
