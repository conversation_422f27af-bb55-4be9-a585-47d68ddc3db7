import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAuctionReportDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field disposal Id is required' })
  @IsNotEmpty({ message: 'The field disposal Id cannot be empty' })
  disposalId: string;

  @ApiProperty({ description: 'The field buyer_idNumber is required' })
  @IsNotEmpty({ message: 'The field buyer_idNumber cannot be empty' })
  buyer_idNumber: number;

  @ApiProperty({ description: 'The field buyer_First Name is required' })
  @IsNotEmpty({ message: 'The field buyer_First Name cannot be empty' })
  buyer_FirstName: string;

  @ApiProperty({ description: 'The field buyer_Last Name is required' })
  @IsNotEmpty({ message: 'The field buyer_Last Name cannot be empty' })
  buyer_LastName: string;

  @ApiProperty({ description: 'The field buyer_tin Number is required' })
  @IsNotEmpty({ message: 'The field buyer_tin Number cannot be empty' })
  buyer_tinNumber: number;

  @ApiProperty({ description: 'The field sale_amount is required' })
  @IsNotEmpty({ message: 'The field sale_amount cannot be empty' })
  sale_amount: number;

  @ApiProperty({ description: 'The field valuation_amount is required' })
  @IsNotEmpty({ message: 'The field valuation_amount cannot be empty' })
  valuation_amount: number;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  description: string;
}

export class UpdateAuctionReportDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field disposal Id is required' })
  @IsNotEmpty({ message: 'The field disposal Id cannot be empty' })
  UserId: string;

  @ApiProperty({ description: 'The field buyer_idNumber is required' })
  @IsNotEmpty({ message: 'The field buyer_idNumber cannot be empty' })
  buyer_idNumber: number;

  @ApiProperty({ description: 'The field buyer_First Name is required' })
  @IsNotEmpty({ message: 'The field buyer_First Name cannot be empty' })
  buyer_FirstName: string;

  @ApiProperty({ description: 'The field buyer_Last Name is required' })
  @IsNotEmpty({ message: 'The field buyer_Last Name cannot be empty' })
  buyer_LastName: string;

  @ApiProperty({ description: 'The field buyer_tin Number is required' })
  @IsNotEmpty({ message: 'The field buyer_tin Number cannot be empty' })
  buyer_tinNumber: number;

  @ApiProperty({ description: 'The field sale_amount is required' })
  @IsNotEmpty({ message: 'The field sale_amount cannot be empty' })
  sale_amount: number;

  @ApiProperty({ description: 'The field valuation_amount is required' })
  @IsNotEmpty({ message: 'The field valuation_amount cannot be empty' })
  valuation_amount: number;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  description: string;
}

export class GetDisposalReportByFilterDto {
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  searchString?: string;
  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  buyer_idNumber?: number;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  buyer_FirstName?: string;
  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  buyer_LastName?: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  buyer_tinNumber?: number;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  requestStatus?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvalLeval?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  isActionReportsubmitted?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  createdDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  createdDateEnd?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  submitedDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  submitedDateEnd?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvalDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvedDateEnd?: string;
}
