import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateVehicleDisposalDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field vehicle ID is required' })
  @IsNotEmpty({ message: 'The field vehicle ID cannot be empty' })
  vehicleId: string;

  @ApiProperty({ description: 'The field vehicle ID is required' })
  @IsNotEmpty({ message: 'The field Use Id cannot be empty' })
  UserId: string;
  @ApiProperty({ description: 'The field disposalType Id is required' })
  @IsNotEmpty({ message: 'The field disposalType Id cannot be empty' })
  disposalTypeId: string;

  @ApiProperty({ description: 'The field disposalReasonId is required' })
  @IsNotEmpty({ message: 'The field disposalReasonId cannot be empty' })
  disposalReasonId: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  description: string;
}

export class UpdateVehicleDisposalDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  // @ApiProperty({ description: 'The field vehicle ID is required' })
  // @IsNotEmpty({ message: 'The field vehicle ID cannot be empty' })
  // vehicleId: string;

  @ApiProperty({ description: 'The field disposalType Id is required' })
  @IsNotEmpty({ message: 'The field disposalType Id cannot be empty' })
  disposalTypeId: string;

  @ApiProperty({ description: 'The field disposalReasonId is required' })
  @IsNotEmpty({ message: 'The field disposalReasonId cannot be empty' })
  disposalReasonId: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  description: string;
}

export class disposalSearchAndFilterDto {
  // @IsOptional({ message: 'The field id cannot be empty' })
  // id: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  vehicleId?: string;
  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  searchString: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  disposalType?: string;

  @ApiProperty({ description: 'The field ownershipType is required' })
  @IsOptional({ message: 'The field description can be empty' })
  disposalReasonId?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  requestStatus?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvalLeval?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  isActionReportsubmitted?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  createdDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  createdDateEnd?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  submitedDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  submitedDateEnd?: string;

  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvalDateStart?: string;
  @ApiProperty({ description: 'The field id is required' })
  @IsOptional({ message: 'The field id cannot be empty' })
  approvedDateEnd?: string;
}
