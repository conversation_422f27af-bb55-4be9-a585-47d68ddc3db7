import { Body, Controller, Get, Param, Patch, Post } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DisposalManagementService } from './disposal-management.service';
import {
  CreateVehicleDisposalDto,
  disposalSearchAndFilterDto,
  UpdateVehicleDisposalDto,
} from './dtos/vehicle-disposal/vehicle-disposal.dto';
import {
  CreateAuctionReportDto,
  GetDisposalReportByFilterDto,
  UpdateAuctionReportDto,
} from './dtos/auction-report/auction-report.dto';
// import { AcquisitionApprovalDto } from './dtos/approvals/acquisition-approval.dto';

// @ApiTags('Vehicle Acquisition Approval')
@Controller('disposal')
export class DisposalManagementController {
  constructor(
    private readonly disposalManagementService: DisposalManagementService,
  ) {}
  // delete comment

  @ApiOperation({
    summary: 'vehicle disposal request ',
    description: 'Bellow is sample data for Vehicle disposal',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        vehicleId: {
          type: 'string',
          title: 'vehicleId',
          example: '876629d0-a025-40ce-8434-a60141d661f6',
        },
        UserId: {
          type: 'string',
          title: 'UseId',
          example: '6fe8dd77-9f12-462e-87d2-e4d1c4a40fc3',
        },
        disposalTypeId: {
          type: 'string',
          title: 'disposalType Id',
          example: 'f41c304b-0ba7-47fd-82d2-c83ab0f48d09',
        },
        disposalReasonId: {
          type: 'string',
          title: 'disposal Reason Id',
          example: '1a667114-72fb-45d8-b1cf-1cfabd191c91',
        },
        description: {
          type: 'string',
          title: 'description',
          example: 'description is option',
        },
      },
    },
  })
  @ApiTags('Vehicle Disposal ')
  @Post('vehicle-disposal')
  async vehicleDisposal(
    @Body() createVehicleDisposalDto: CreateVehicleDisposalDto,
  ) {
    for (const key in createVehicleDisposalDto) {
      if (createVehicleDisposalDto[key] === '') {
        createVehicleDisposalDto[key] = null;
      }
    }

    return this.disposalManagementService.VehicleDisposal(
      createVehicleDisposalDto,
    );
  }

  @ApiOperation({
    summary: 'Find all vehicle disposal',
  })
  @ApiTags('Vehicle Disposal ')
  @Get('allVehicleDisposal')
  async findAllVehicleDisposal() {
    return this.disposalManagementService.getAllDisposal();
  }
  // getting disposal by search and filter
  @ApiTags('Searching AND filter(Generate report)')
  @Post('gettingDisposalByFilter')
  async findDisposalByfielter(@Body() filters: disposalSearchAndFilterDto) {
    for (const key in filters) {
      if (filters[key] === '' || filters[key] === 'string') {
        filters[key] = null;
      }
    }

    return this.disposalManagementService.getAllDisposalByfilterAndSort(
      filters,
    );
  }
  // getting disposal by Id

  @ApiOperation({
    summary: 'Find disposal  by id',
  })
  @ApiTags('Vehicle Disposal ')
  @Get('disposal/:id')
  async findVehicleDisposalById(@Param('id') id: string) {
    return this.disposalManagementService.getDisposalById(id);
  }
  // getting disposal type by id
  @ApiOperation({
    summary: 'Find disposal  by disposal type',
  })
  @ApiTags('Vehicle Disposal ')
  @Get('disposalByTypeId/:id')
  async findDisposalByTypeId(@Param('id') id: string) {
    return this.disposalManagementService.getDisposalByTypeId(id);
  }

  // updating regstred vehicl

  @ApiOperation({
    summary: 'Update vehicle disposal ',
    description: 'Bellow is sample data for Vehicle disposal',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        // vehicleId: {
        //   type: 'string',
        //   title: 'vehicleId',
        //   example: '876629d0-a025-40ce-8434-a60141d661f6',
        // },
        UserId: {
          type: 'string',
          title: 'UseId',
          example: '6fe8dd77-9f12-462e-87d2-e4d1c4a40fc3',
        },
        disposalTypeId: {
          type: 'string',
          title: 'disposalType Id',
          example: 'f41c304b-0ba7-47fd-82d2-c83ab0f48d09',
        },
        disposalReasonId: {
          type: 'string',
          title: 'disposal Reason Id',
          example: '1a667114-72fb-45d8-b1cf-1cfabd191c91',
        },
        description: {
          type: 'string',
          title: 'description',
          example: 'description is option',
        },
      },
    },
  })
  @ApiTags('Vehicle Disposal ')
  @Patch('updateDisposalVehicle/:id')
  async updateRegistredVehicle(
    @Param('id') id: string,
    @Body() updateVehicleDisposalDto: UpdateVehicleDisposalDto,
  ) {
    for (const key in updateVehicleDisposalDto) {
      if (updateVehicleDisposalDto[key] === '') {
        updateVehicleDisposalDto[key] = null; // Set empty key to null
      }
    }
    // return updateAcquisitionDto;
    return this.disposalManagementService.updateDisposal(
      id,
      updateVehicleDisposalDto,
    );
  }

  @ApiOperation({
    summary: 'submitting Auctioning report',
    description: 'Bellow is sample data for auction report',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        disposalId: {
          type: 'string',
          title: 'disposalId',
          example: '4d21e2f5-1833-4c6e-8131-effff17d1e1f',
        },
        buyer_idNumber: {
          type: 'number',
          title: 'buyer_idNumber',
          example: 111111111111111,
        },
        buyer_FirstName: {
          type: 'string',
          title: 'buyer_FirstName',
          example: 'First Name',
        },
        buyer_LastName: {
          type: 'string',
          title: 'buyer_LastName',
          example: 'Last Name',
        },
        buyer_tinNumber: {
          type: 'number',
          title: 'buyer_tinNumber',
          example: 23443343432,
        },
        sale_amount: {
          type: 'number',
          title: 'sale_amount',
          example: 2332,
        },
        valuation_amount: {
          type: 'number',
          title: 'sale_amount',
          example: 2332,
        },
        description: {
          type: 'string',
          title: 'description',
          example: 'description is option',
        },
      },
    },
  })
  @ApiTags('Auctioning Report ')
  @Post('auction-report')
  async AuctionReport(@Body() createAuctionReportDto: CreateAuctionReportDto) {
    for (const key in createAuctionReportDto) {
      if (createAuctionReportDto[key] === '') {
        createAuctionReportDto[key] = null;
      }
    }

    return this.disposalManagementService.AuctionReport(createAuctionReportDto);
  }
  // getting All auctionReports
  @ApiOperation({
    summary: 'Find all Auction Reports',
  })
  @ApiTags('Auctioning Report ')
  @Get('allAuctionReports')
  async findAllAuctionReports() {
    return this.disposalManagementService.getAuctionReports();
  }
  // getting auction report by filter and search
  // getting disposal by search and filter
  @ApiTags('Searching AND filter(Generate report)')
  @Post('gettingAuctionReportByFilter')
  async getAuctionBySearchAndFilter(
    @Body() filters: GetDisposalReportByFilterDto,
  ) {
    for (const key in filters) {
      if (filters[key] === '' || filters[key] === 'string') {
        filters[key] = null;
      }
    }

    return this.disposalManagementService.getAuctionBySearchAndFilter(filters);
  }
  // getting AuctionReport by Id
  @ApiOperation({
    summary: 'Find Auction Report  by id',
  })
  @ApiTags('Auctioning Report ')
  @Get('auctionReport/:id')
  async findAuctionReportById(@Param('id') id: string) {
    return this.disposalManagementService.getAuctionReportById(id);
  }
  // update auctionReport

  @ApiOperation({
    summary: 'Update Auction report ',
    description: 'Bellow is sample data for Auction Report',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        // vehicleId: {
        //   type: 'string',
        //   title: 'vehicleId',
        //   example: '876629d0-a025-40ce-8434-a60141d661f6',
        // },
        UserId: {
          type: 'string',
          title: 'UseId',
          example: '6fe8dd77-9f12-462e-87d2-e4d1c4a40fc3',
        },
        buyer_idNumber: {
          type: 'number',
          title: 'buyer_idNumber',
          example: 111111111111111,
        },
        buyer_FirstName: {
          type: 'string',
          title: 'buyer_FirstName',
          example: 'First Name',
        },
        buyer_LastName: {
          type: 'string',
          title: 'buyer_LastName',
          example: 'Last Name',
        },
        buyer_tinNumber: {
          type: 'number',
          title: 'buyer_tinNumber',
          example: 23443343432,
        },
        sale_amount: {
          type: 'number',
          title: 'sale_amount',
          example: 2332,
        },
        valuation_amount: {
          type: 'number',
          title: 'sale_amount',
          example: 2332,
        },
        description: {
          type: 'string',
          title: 'description',
          example: 'description is option',
        },
      },
    },
  })
  @ApiTags('Auctioning Report ')
  @Patch('updateAuctionReport/:id')
  async updateAuctionReport(
    @Param('id') id: string,
    @Body() updateAuctionReportDto: UpdateAuctionReportDto,
  ) {
    for (const key in updateAuctionReportDto) {
      if (updateAuctionReportDto[key] === '') {
        updateAuctionReportDto[key] = null; // Set empty key to null
      }
    }
    // return updateAcquisitionDto;
    return this.disposalManagementService.updateAuctionReport(
      id,
      updateAuctionReportDto,
    );
  }
}
