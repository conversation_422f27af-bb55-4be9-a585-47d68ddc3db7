import { AUTH_SERVICE } from '@app/common/constants';
import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  startOfQuarter,
  endOfQuarter,
  addQuarters,
  eachDayOfInterval,
  format,
} from 'date-fns';

import { ClientProxy, EventPattern } from '@nestjs/microservices';
// import { VehicleAcquisitionRepository } from './fleet-mgmnt.repository';
import {
  CreateAcquisitionDto,
  searcAcquisionAndFilterDTO,
  UpdateAcquisitionDto,
} from './dtos/vehicle-acquisition/acquisition.dto';
import {
  ActivityOnVehicleRepository,
  ApprovalLevelRepository,
  OilServiceCategoryRepository,
  OwnershipTypeRepository,
  RequestTypeRepository,
  StatusTypeRepository,
  VehicleManufactureRepository,
  VehicleModelRepository,
  VehicleStatusRepository,
  VehicleTypeRepository,
} from './vehicle-management/vehicle-management.repository';
// import { VehicleAcquisition } from './entities/vehicle-acquisition/vehicle-acquisition.entity';
// import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueryFailedError, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { VehicleAcquisition } from './entities/vehicle-acquisition/vehicle-acquisition.entity';
import {
  ProjectExtensionRepository,
  RegisteredVehicleRepository,
  RequestedVehicleRepository,
  VehicleAcquisitionRepository,
  VehicleQuarterlyReportRepository,
  VehicleRegRequestRepository,
  VehilceNumberPerAccquisitionRepository,
  CostBenefitRepository,
  StaffRelavanceRepository,
  DriverDetailsRepository,
  HiringCostRepository,
  MaintenanceActivitiesRepository,
  ReportActivitiesRepository,
} from './fleet-mgmnt.repository';

import { RequestedVehicle } from './entities/vehicle-acquisition/requested-vehicle.entity';
import {
  CreateRequestedVehicleDto,
  UpdateRequestedVehicleDto,
} from './dtos/vehicle-acquisition/requested-vehicle.dto';
import { AquistionApprovalService } from './approval.service';
import { CreateVehicleRegRequestDto } from './dtos/vehicle-registration/vehicle-reg-request.dto';
import { VehicleRegRequest } from './entities/vehicles-registration /vehicle-reg-request';
import {
  ExistingVehicleRegistrationDto,
  UpdateRegistredVehicleDto,
  VehicleRegistrationDto,
} from './dtos/vehicle-registration/registred-vehicle.dto';
import { RegisteredVehicle } from './entities/vehicles-registration /registred-vehicle';
import { VehicleManufacture } from './entities/vehicle-management /vehicles-manifacturer.entity';
import { VehicleModel } from './entities/vehicle-management /vehicle-model.entity';
import { VehicleStatus } from './entities/vehicle-management /vehicle-status.entity';
import { Connection } from 'typeorm';
import { VehilceNumberPerAccquisition } from './entities/vehicle-acquisition/vehicleNumber-perAquisition.entity';
import { VehicleAllocationDto } from './dtos/vehicle-allocation/vehicle-allocation.dto';
import { VehicleFilterDto } from './dtos/vehicle-reporting/vehicle-reporting.dto';
import {
  SubmitQuateryReportDto,
  UpdateQuateryReportDto,
} from './dtos/vehicale-status-report/vehicale-status-report.dto';
import { VehicleQuarterlyReport } from './entities/vehicale-status-report/vehicle-status-report.entity';
import { ProjectExtension } from './entities/project-extension/project-extension.entity';
import { SubmitProjectExtensionDto } from './dtos/project-extension/project-extention.dto';
import { CostBenefitDto } from './dtos/vehicle-acquisition-relevance/cost-benefity.dto';
import { CostBenefit } from './entities/vehicle-acquisition-relevance/cost-benefity.entity';
import { DriverDetails } from './entities/vehicle-acquisition-relevance/drive-details.entity';
import { StaffRelavance } from './entities/vehicle-acquisition-relevance/staff-relavance.entity';
import { StaffRelevanceDto } from './dtos/vehicle-acquisition-relevance/staff-relavance.dto';
import { DriverDetailsDto } from './dtos/vehicle-acquisition-relevance/drive-details.dto';
import { HiringCostDto } from './dtos/vehicle-acquisition-relevance/hiring-cost.dto';
import { HiringCost } from './entities/vehicle-acquisition-relevance/hiring-cost.entity';
import { MaintenanceActivities } from './entities/maintenance-activities/maintenance-activities.entity';
// import { MaintenanceActivitiesDTO } from './dtos/maintenance-activities/maintenance-activities.dto';
import { ReportActivitiesDto } from './dtos/vehicale-status-report/report-activities.dto';
import { ReportActivities } from './entities/vehicale-status-report/report-activities.entity ';
// import { ActivityOnVehicle } from './entities/vehicle-management /activities-on-vehicle.entity';
// import { MaintenanceActivities } from './entities/maintenance-activities/maintenance-activities.entity';
@Injectable()
export class FleetMgmntService {
  private readonly logger = new Logger(FleetMgmntService.name);
  constructor(
    private readonly aquistionApprovalService: AquistionApprovalService,
    @Inject(AUTH_SERVICE)
    private readonly authService: ClientProxy,
    @InjectRepository(VehicleAcquisition)
    private vehicleAcquisitionEntityManagerRepository: Repository<VehicleAcquisition>,
    private readonly vehicleAcquisitionRepository: VehicleAcquisitionRepository,
    @InjectRepository(RequestedVehicle)
    private RequestedVehiclentityManagerRepository: Repository<RequestedVehicle>,
    private readonly requestedVehicleRepository: RequestedVehicleRepository,
    private readonly vehicleTypeRepository: VehicleTypeRepository,
    private readonly requestTypeRepository: RequestTypeRepository,
    private readonly ownershipTypeRepository: OwnershipTypeRepository,
    private readonly statusTypeRepository: StatusTypeRepository,
    private readonly approvalLevelRepository: ApprovalLevelRepository,
    @InjectRepository(VehilceNumberPerAccquisition)
    private VehilceNumberPerAccquisitionEntityManagerRepository: Repository<VehilceNumberPerAccquisition>,
    private readonly VehilceNumberPerAccquisitionRepositoryRepository: VehilceNumberPerAccquisitionRepository,

    @InjectRepository(VehicleManufacture)
    private VehicleManufactureentityManagerRepository: Repository<VehicleManufacture>,
    private readonly vehicleManufactureRepository: VehicleManufactureRepository,

    @InjectRepository(VehicleModel)
    private vehicleModelentityManagerRepository: Repository<VehicleModel>,
    private readonly vehicleModelRepository: VehicleModelRepository,

    @InjectRepository(VehicleStatus)
    private vehicleStatusManagerRepository: Repository<VehicleStatus>,
    private readonly vehicleStatusRepository: VehicleStatusRepository,
    // for vehicle registration
    @InjectRepository(VehicleRegRequest)
    private vehicleRegRequestentityManagerRepository: Repository<VehicleRegRequest>,
    private readonly vehicleRegRequestRepository: VehicleRegRequestRepository,
    @InjectRepository(RegisteredVehicle)
    private RegisteredVehicleentityManagerRepository: Repository<RegisteredVehicle>,
    private readonly registeredVehicleRepository: RegisteredVehicleRepository,
    private readonly connection: Connection,
    @InjectRepository(VehicleQuarterlyReport)
    private vehicleQuarterlyManagerReportRepository: Repository<VehicleQuarterlyReport>,
    private readonly vehicleQuarterlyReportRepository: VehicleQuarterlyReportRepository,
    @InjectRepository(ProjectExtension)
    private projectExtensionManagerReportRepository: Repository<ProjectExtension>,
    private readonly projectExtensionRepository: ProjectExtensionRepository,
    // private readonly aquistionApprovalService: AquistionApprovalService,
    // vehicle acquisition
    @InjectRepository(CostBenefit)
    private costBenefitManagerRepository: Repository<CostBenefit>,
    private readonly costBenefitRepository: CostBenefitRepository,

    @InjectRepository(DriverDetails)
    private driverDetailsManagerRepository: Repository<DriverDetails>,
    private readonly driverDetailsRepository: DriverDetailsRepository,

    @InjectRepository(StaffRelavance)
    private StaffRelavanceManagerRepository: Repository<StaffRelavance>,
    private readonly staffRelavanceRepository: StaffRelavanceRepository,
    @InjectRepository(HiringCost)
    private HiringCostManagerRepository: Repository<HiringCost>,
    private readonly hiringCostRepository: HiringCostRepository,

    @InjectRepository(MaintenanceActivities)
    private maintenanceActivitiesManagerRepository: Repository<MaintenanceActivities>,
    private readonly maintenanceActivitiesRepository: MaintenanceActivitiesRepository,
    private activityOnVehicleRepository: ActivityOnVehicleRepository,
    private oilServiceCategoryRepository: OilServiceCategoryRepository,

    @InjectRepository(ReportActivities)
    private ReportActivitiesManagerRepository: Repository<ReportActivities>,
    private readonly reportActivitiesRepository: ReportActivitiesRepository,
  ) {}

  // async getUsers(): Promise<any[]> {
  //   const query = 'SELECT * FROM public.User'; // Example SQL query
  //   const result = await this.connection.query(query);
  //   return result;
  // }

  // geting all users by institution id
  async getUsers(institutionId: string) {
    console.log(institutionId);
    try {
      const query = `
      SELECT
        u.id,
        u.identification,
        u."firstName",
        u."lastName",
        u."email",
        u."personalEmail",
        u."isEmailValid",
        u."phoneNumber",
        u."createdAt",
        u."isActive",
        u."created_at",
        u."updated_at",
        u."deleted_at",
        r.id AS roleId,
        r.name AS roleName,
        i.id AS institutionId,
        i.name AS institutionName,
        i."isLocationAvailable" AS isLocationAvailable
      FROM
        public.User u
      LEFT JOIN
        public.Role r ON u."roleId" = r.id
      LEFT JOIN
        public.Institution i ON u."institutionId" = i.id
        WHERE
      i.id = $1
    `;
      const result = await this.connection.query(query, [institutionId]);
      return result;
    } catch (error) {
      // Handle any errors that occur during query execution
      console.error('Error retrieving users:', error.message);
      throw error; // Rethrow the error to propagate it up the call stack
    }
  }
  // getting main by institution by institution id.
  async getInstitution(institutionId: string) {
    // console.log(institutionId);
    try {
      const query = `
      SELECT
        i.id,
        i."name",
        i."isLocationAvailable",
        i."isbudgeted"
      FROM
        public.Institution i
        WHERE
      i.id = $1
    `;
      const result = await this.connection.query(query, [institutionId]);
      return result;
    } catch (error) {
      // Handle any errors that occur during query execution
      console.error('Error retrieving institution:', error.message);
      throw error; // Rethrow the error to propagate it up the call stack
    }
  }
  async getNonBudgetedAgency(agency: string) {
    // console.log(institutionId);
    try {
      const query = `
      SELECT
        i.id,
        i."institution",
        i."agency"
      FROM
        public.non_budgeted_agencies i
        WHERE
      i."agency" = $1
    `;
      const result = await this.connection.query(query, [agency]);
      return result;
    } catch (error) {
      // Handle any errors that occur during query execution
      console.error('Error retrieving Non-Budgeted agency:', error.message);
      throw error; // Rethrow the error to propagate it up the call stack
    }
  }
  async checkUserExists(userId: string): Promise<boolean> {
    return this.authService
      .send<boolean>({ cmd: 'userExists' }, userId)
      .toPromise();
  }
  async checkUser(userId: string) {
    return this.authService.send<any>({ cmd: 'userData' }, userId).toPromise();
  }
  @EventPattern('checkUserData')
  async checkUserData(userId: string) {
    try {
      return await this.authService
        .send<any>({ cmd: 'userData' }, userId)
        .toPromise();
    } catch (error) {
      this.logger.error(`Error fetching user data: ${error.message}`);
      throw new HttpException(
        'Failed to fetch user data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  // getting date of quarter

  async getQuarterDates(dateInput: Date) {
    // Calculate the start and end dates of the current quarter
    const date =
      typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    const currentQuarterStartDate = startOfQuarter(date);
    const currentQuarterEndDate = endOfQuarter(date);

    // Calculate the start and end dates of the next quarter
    const nextQuarterDate = addQuarters(date, 1);
    const nextQuarterStartDate = startOfQuarter(nextQuarterDate);
    const nextQuarterEndDate = endOfQuarter(nextQuarterDate);

    // Calculate the start and end dates of all quarters of the year
    const year = date.getFullYear();
    const quarters = [];
    for (let i = 0; i < 4; i++) {
      const startDate = startOfQuarter(new Date(year, i * 3));
      const endDate = endOfQuarter(new Date(year, i * 3));
      quarters.push({ startDate, endDate });
    }

    return {
      currentQuarter: {
        startDate: currentQuarterStartDate.toISOString().split('T')[0],
        endDate: currentQuarterEndDate.toISOString().split('T')[0],
      },
      nextQuarter: {
        startDate: nextQuarterStartDate.toISOString().split('T')[0],
        endDate: nextQuarterEndDate.toISOString().split('T')[0],
      },
      allQuarters: quarters.map((quarter) => ({
        startDate: quarter.startDate.toISOString().split('T')[0],
        endDate: quarter.endDate.toISOString().split('T')[0],
      })),
    };
  }

  // vehicle registration
  // crate vehicle registration request
  async createVehicleRegRequest(
    createVehicleRegRequestDto: CreateVehicleRegRequestDto,
  ) {
    let dataAcquisition;
    // check if createVehicleRegRequestDto has acquisition or not
    if (
      createVehicleRegRequestDto.vehicleAcquisition != null &&
      createVehicleRegRequestDto.vehicleAcquisition !== '' &&
      createVehicleRegRequestDto.vehicleAcquisition !== undefined &&
      createVehicleRegRequestDto.vehicleAcquisition.trim() !== ''
    ) {
      try {
        dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
          id: createVehicleRegRequestDto.vehicleAcquisition,
        });
      } catch (error) {
        if (error.message === 'Entity not found.') {
          return {
            message: 'acquisition data not find in database',
            statusCode: HttpStatus.NOT_FOUND,
          };
        } else {
          return {
            message:
              error.message ||
              'An error occurred while fetching data for acquisition .',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }
      const vehicleRegRequest = new VehicleRegRequest({
        ...createVehicleRegRequestDto,
        userId: createVehicleRegRequestDto.userId, // Assign the StatusType object
        institutionId: createVehicleRegRequestDto.institutionId, // Assign the ApprovalLevel object
        institution: createVehicleRegRequestDto.institution,
        VehicleAcquisition: dataAcquisition, // Assign the RequestType object
      });

      const savedVehicleRegRequest =
        await this.vehicleRegRequestRepository.create(vehicleRegRequest);

      return { VehicleRegRequestId: savedVehicleRegRequest.id };
    }
    const vehicleRegRequest = new VehicleRegRequest({
      ...createVehicleRegRequestDto,
      userId: createVehicleRegRequestDto.userId, // Assign the StatusType object
      institutionId: createVehicleRegRequestDto.institutionId, // Assign the ApprovalLevel object
      institution: createVehicleRegRequestDto.institution,
      // VehicleAcquisition: dataAcquisition, // Assign the RequestType object
    });

    const savedVehicleRegRequest =
      await this.vehicleRegRequestRepository.create(vehicleRegRequest);
    return { VehicleRegRequestId: savedVehicleRegRequest.id };
  }
  // all vehicles
  async getAllVehicles() {
    return await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
      'registeredVehicle',
    )
      .leftJoinAndSelect(
        'registeredVehicle.vehicleRegRequest',
        'vehicleRegRequest',
      )
      .leftJoinAndSelect(
        'vehicleRegRequest.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect(
        'registeredVehicle.vehicleManufacture',
        'vehicleManufacture',
      )
      .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
      .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'registeredVehicle.registrationStatus',
        'registrationStatus',
      )
      .getMany();
  }
  // getting vehicle by filter
  async getVehiclesWithFilters(filters: VehicleFilterDto) {
    const query =
      this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        );

    if (filters.beneficiaryAgency) {
      query.andWhere(
        'registeredVehicle.beneficiaryAgency = :beneficiaryAgency',
        { beneficiaryAgency: filters.beneficiaryAgency },
      );
    }
    if (filters.reportingInstitution) {
      query.andWhere(
        'registeredVehicle.reportingInstitution = :reportingInstitution',
        { reportingInstitution: filters.reportingInstitution },
      );
    }
    if (filters.ownershipType) {
      query.andWhere('ownershipType.name = :ownershipType', {
        ownershipType: filters.ownershipType,
      });
    }
    if (filters.vehicleType) {
      query.andWhere('vehicleType.name = :vehicleType', {
        vehicleType: filters.vehicleType,
      });
    }
    if (filters.vehicleModel) {
      query.andWhere('vehicleModel.name = :vehicleModel', {
        vehicleModel: filters.vehicleModel,
      });
    }
    if (filters.registrationStatus) {
      query.andWhere('registrationStatus.name = :registrationStatus', {
        registrationStatus: filters.registrationStatus,
      });
    }
    if (filters.approvalLevel) {
      query.andWhere('approvalLevel.name = :approvalLevel', {
        approvalLevel: filters.approvalLevel,
      });
    }
    if (filters.vehicleStatus) {
      query.andWhere('vehicleStatus.name = :vehicleStatus', {
        vehicleStatus: filters.vehicleStatus,
      });
    }

    if (filters.vehicleManufacture) {
      query.andWhere('vehicleManufacture.name = :vehicleManufacture', {
        vehicleManufacture: filters.vehicleManufacture,
      });
    }
    if (filters.chassisNumber) {
      query.andWhere('registeredVehicle.chassisNumber = :chassisNumber', {
        chassisNumber: filters.chassisNumber,
      });
    }
    if (filters.pickCardNumber) {
      query.andWhere('registeredVehicle.pickCardNumber = :pickCardNumber', {
        pickCardNumber: filters.pickCardNumber,
      });
    }

    if (filters.plateNumber) {
      query.andWhere('registeredVehicle.plateNumber = :plateNumber', {
        plateNumber: filters.plateNumber,
      });
    }
    if (filters.invoiceNumber) {
      query.andWhere('registeredVehicle.invoiceNumber = :invoiceNumber', {
        invoiceNumber: filters.invoiceNumber,
      });
    }

    if (filters.manufacturingYearStart && filters.manufacturingYearEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.manufacturingYear) BETWEEN :manufacturingYearStart AND :manufacturingYearEnd',
        {
          manufacturingYearStart: filters.manufacturingYearStart,
          manufacturingYearEnd: filters.manufacturingYearEnd,
        },
      );
    } else if (filters.manufacturingYearStart) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.manufacturingYear) >= :manufacturingYearStart',
        {
          manufacturingYearStart: filters.manufacturingYearStart,
        },
      );
    } else if (filters.manufacturingYearEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.manufacturingYear) <= :manufacturingYearEnd',
        {
          manufacturingYearEnd: filters.manufacturingYearEnd,
        },
      );
    }
    // for acquisitionDate (range)
    if (filters.acquisitionDateStart && filters.acquisitionDateEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.acquisitionDate) BETWEEN :acquisitionDateStart AND :acquisitionDateEnd',
        {
          acquisitionDateStart: filters.acquisitionDateStart,
          acquisitionDateEnd: filters.acquisitionDateEnd,
        },
      );
    } else if (filters.acquisitionDateStart) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.acquisitionDate) >= :acquisitionDateStart',
        {
          acquisitionDateStart: filters.acquisitionDateStart,
        },
      );
    } else if (filters.acquisitionDateEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.acquisitionDate) <= :acquisitionDateEnd',
        {
          acquisitionDateEnd: filters.acquisitionDateEnd,
        },
      );
    }

    if (filters.projectStartYearStart && filters.projectStartYearEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.projectStartDate) BETWEEN :projectStartYearStart AND :projectStartYearEnd',
        {
          projectStartYearStart: filters.projectStartYearStart,
          projectStartYearEnd: filters.projectStartYearEnd,
        },
      );
    } else if (filters.projectStartYearStart) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.projectStartDate) >= :projectStartYearStart',
        {
          projectStartYearStart: filters.projectStartYearStart,
        },
      );
    } else if (filters.projectStartYearEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.projectStartDate) <= :projectStartYearEnd',
        {
          projectStartYearEnd: filters.projectStartYearEnd,
        },
      );
    }

    if (filters.projectEndYearStart && filters.projectEndYearEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.projectEndDate) BETWEEN :projectEndYearStart AND :projectEndYearEnd',
        {
          projectEndYearStart: filters.projectEndYearStart,
          projectEndYearEnd: filters.projectEndYearEnd,
        },
      );
    } else if (filters.projectEndYearStart) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.projectEndDate) >= :projectEndYearStart',
        {
          projectEndYearStart: filters.projectEndYearStart,
        },
      );
    } else if (filters.projectEndYearEnd) {
      query.andWhere(
        'EXTRACT(YEAR FROM registeredVehicle.projectEndDate) <= :projectEndYearEnd',
        {
          projectEndYearEnd: filters.projectEndYearEnd,
        },
      );
    }
    if (filters.searchString) {
      const trimmedSearchString = filters.searchString.trim();
      const lowerCaseSearchString = `%${trimmedSearchString.toLowerCase()}%`;

      query
        .where(
          'LOWER(registeredVehicle.beneficiaryAgency) LIKE :searchString',
          { searchString: lowerCaseSearchString },
        )
        .orWhere(
          'LOWER(registeredVehicle.reportingInstitution) LIKE :searchString',
          { searchString: lowerCaseSearchString },
        )
        .orWhere('LOWER(registeredVehicle.chassisNumber) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(registeredVehicle.engineNumber) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(registeredVehicle.invoiceNumber) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere(
          'LOWER(registeredVehicle.customsDeclarationNumber) LIKE :searchString',
          { searchString: lowerCaseSearchString },
        )
        .orWhere('LOWER(registeredVehicle.projectName) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere(
          'LOWER(registeredVehicle.projectDescription) LIKE :searchString',
          { searchString: lowerCaseSearchString },
        )
        .orWhere('LOWER(registeredVehicle.pickCardNumber) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(registeredVehicle.plateNumber) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(ownershipType.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(vehicleType.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(vehicleManufacture.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(registeredVehicle.projectName) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        });
    }

    const [vehicles, count] = await query.getManyAndCount();
    return { vehicles, count };
  }
  // Generic search with string

  // geting all vehicles by institution id here institution we mean agency made request
  async getAllVehiclesByInstitutionId(institutionId: string) {
    console.log(institutionId);
    return await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
      'registeredVehicle',
    )
      .leftJoinAndSelect(
        'registeredVehicle.vehicleRegRequest',
        'vehicleRegRequest',
      )
      .where('vehicleRegRequest.institutionId = :institutionId', {
        institutionId,
      })
      .leftJoinAndSelect(
        'vehicleRegRequest.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect(
        'registeredVehicle.vehicleManufacture',
        'vehicleManufacture',
      )
      .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
      .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'registeredVehicle.registrationStatus',
        'registrationStatus',
      )
      .getMany();
  }

  async getAllVehiclesByBeneficiaryAgencyId(beneficiaryAgencyId: string) {
    try {
      // Log the original and trimmed beneficiaryAgencyId
      console.log(`Original beneficiaryAgencyId: ${beneficiaryAgencyId}`);
      const trimmedBeneficiaryAgencyId = beneficiaryAgencyId.trim();
      console.log(`Trimmed beneficiaryAgencyId: ${trimmedBeneficiaryAgencyId}`);

      // Build the query with trimmed beneficiaryAgencyId
      const query =
        this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
          'registeredVehicle',
        )
          .where(
            'registeredVehicle.beneficiaryAgencyId = :beneficiaryAgencyId',
            {
              beneficiaryAgencyId: trimmedBeneficiaryAgencyId,
            },
          )
          .leftJoinAndSelect(
            'registeredVehicle.vehicleRegRequest',
            'vehicleRegRequest',
          )
          .leftJoinAndSelect(
            'vehicleRegRequest.VehicleAcquisition',
            'VehicleAcquisition',
          )
          .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
          .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
          .leftJoinAndSelect(
            'registeredVehicle.vehicleManufacture',
            'vehicleManufacture',
          )
          .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
          .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
          .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
          .leftJoinAndSelect(
            'registeredVehicle.registrationStatus',
            'registrationStatus',
          );

      // Log the generated query
      console.log(`Generated Query: ${query.getQuery()}`);

      // Execute the query and fetch the results
      const vehicles = await query.getMany();

      // Log the result of the query
      console.log(`Query Result: ${JSON.stringify(vehicles, null, 2)}`);

      return vehicles;
    } catch (error) {
      // Log any error that occurs during the process
      console.error('Error retrieving vehicles:', error);
      throw error; // Rethrow the error to be handled by the caller
    }
  }

  // getting all vehicle by reporting institytion
  async getAllVehiclesByReportingInstitutionId(reportingInstitutionId: string) {
    console.log(reportingInstitutionId);
    return await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
      'registeredVehicle',
    )

      .where(
        'registeredVehicle.reportingInstitutionId = :reportingInstitutionId',
        {
          reportingInstitutionId,
        },
      )
      .leftJoinAndSelect(
        'registeredVehicle.vehicleRegRequest',
        'vehicleRegRequest',
      )
      .leftJoinAndSelect(
        'vehicleRegRequest.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect(
        'registeredVehicle.vehicleManufacture',
        'vehicleManufacture',
      )
      .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
      .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'registeredVehicle.registrationStatus',
        'registrationStatus',
      )
      .getMany();
  }
  // getting vehicle by ID

  async getVehicleById(id: string) {
    // console.log(beneficiaryAgencyId);
    return await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
      'registeredVehicle',
    )

      .where('registeredVehicle.id = :id', {
        id,
      })
      .leftJoinAndSelect(
        'registeredVehicle.vehicleRegRequest',
        'vehicleRegRequest',
      )
      .leftJoinAndSelect(
        'vehicleRegRequest.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect(
        'registeredVehicle.vehicleManufacture',
        'vehicleManufacture',
      )
      .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
      .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'registeredVehicle.registrationStatus',
        'registrationStatus',
      )
      .getOne();
  }

  // getting all vehicle by vehicle status
  async getAllVehiclesByVehiclestatus(vehicleStatutusId: string) {
    console.log(vehicleStatutusId);
    return await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
      'registeredVehicle',
    )
      .leftJoinAndSelect(
        'registeredVehicle.vehicleRegRequest',
        'vehicleRegRequest',
      )
      .leftJoinAndSelect(
        'vehicleRegRequest.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect(
        'registeredVehicle.vehicleManufacture',
        'vehicleManufacture',
      )
      .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
      .where('vehicleStatus.id = :vehicleStatutusId', {
        vehicleStatutusId,
      })
      .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'registeredVehicle.registrationStatus',
        'registrationStatus',
      )
      .getMany();
  }
  // all returned vehicles
  async getAllReturnedVehicles() {
    const vehicleStatus = 'RETURNED';
    return await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
      'registeredVehicle',
    )
      .leftJoinAndSelect(
        'registeredVehicle.vehicleRegRequest',
        'vehicleRegRequest',
      )
      .leftJoinAndSelect(
        'vehicleRegRequest.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect(
        'registeredVehicle.vehicleManufacture',
        'vehicleManufacture',
      )
      .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
      .where('vehicleStatus.name = :vehicleStatus', {
        vehicleStatus,
      })
      .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'registeredVehicle.registrationStatus',
        'registrationStatus',
      )
      .getMany();
  }

  // Implementing vehicle registration
  async VehicleRegistration(vehicleRegistrationDto: VehicleRegistrationDto) {
    // i am gonna to receive implement institution reporting
    console.log('============ Beneficiary Angency=============');
    console.log(vehicleRegistrationDto.beneficiaryAgency);
    console.log(vehicleRegistrationDto.beneficiaryAgencyId);
    let reportingInstitution = null;
    let reportingInstitutionId = null;
    const institutionId = vehicleRegistrationDto.beneficiaryAgencyId;
    const beneficiaryAgencyData = await this.getInstitution(institutionId);

    if (!beneficiaryAgencyData) {
      return {
        message: 'beneficiaryAgencyData not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }

    const budgetedStatus = beneficiaryAgencyData[0].isbudgeted;

    // console.log(budgetedStatus), console.log(beneficiaryAgencyName);
    if (!budgetedStatus) {
      const beneficiaryAgencyName = beneficiaryAgencyData[0].name;
      const non_budgeted_agenciesData = await this.getNonBudgetedAgency(
        beneficiaryAgencyName,
      );
      if (!non_budgeted_agenciesData) {
        return {
          message: 'non_budgeted_agenciesData not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      }

      const reportinganecy = non_budgeted_agenciesData[0].institution;
      const reportingInstitutionData =
        await this.getInstitution(reportinganecy);
      if (!reportingInstitutionData) {
        return {
          message: 'non_budgeted_agenciesData not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      }
      reportingInstitution = reportingInstitutionData[0].name;
      reportingInstitutionId = reportingInstitutionData[0].id;
    }

    console.log('============ reporting institution=============');
    console.log(reportingInstitution);
    console.log(reportingInstitutionId);
    let allowedVehicleNUmber;
    let registredVehicleNUmber;
    let vehicleAcquistionId;
    // checking if number of vehicle to be registred per aquisition if is recheded
    const vehicleRegId = vehicleRegistrationDto.vehicleRegRequest;
    console.log(vehicleRegId);
    const vehicleRegRequestData =
      await this.vehicleRegRequestentityManagerRepository
        .createQueryBuilder('vehicleRegRequest')

        .where('vehicleRegRequest.id = :vehicleRegId', {
          vehicleRegId,
        })
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .getOne();
    if (vehicleRegRequestData.VehicleAcquisition) {
      vehicleAcquistionId = vehicleRegRequestData.VehicleAcquisition.id;
      console.log(vehicleAcquistionId);
      const previousVehicleNumberData =
        await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder(
          'vehilceNumberPerAccquisition',
        )

          .where(
            'vehilceNumberPerAccquisition.acquisition = :vehicleAcquistionId',
            {
              vehicleAcquistionId,
            },
          )
          .getOne();
      // console.log(previousVehicleNumberData);
      allowedVehicleNUmber = Number(
        previousVehicleNumberData.numberOfRequestedVehicle,
      );
      registredVehicleNUmber = Number(
        previousVehicleNumberData.numberOfRegistredVehicle,
      );

      console.log(previousVehicleNumberData);
      if (registredVehicleNUmber >= allowedVehicleNUmber) {
        return {
          message: 'Number of vehicle allowed to this Acquisition is reached',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    let vehicleRegRequestData2;
    let ownershipTypedata;
    let vehicleTypedata;
    let vehicleManufactureata;
    let vehicleModeldata;
    let dataapprovalLevel;
    let datastatusType;

    try {
      vehicleRegRequestData2 = await this.vehicleRegRequestRepository.findOne({
        id: vehicleRegistrationDto.vehicleRegRequest,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicle Reg Request data  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicle Reg Request .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      ownershipTypedata = await this.ownershipTypeRepository.findOne({
        id: vehicleRegistrationDto.ownershipType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'ownershipTypedata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for ownershipType.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleTypedata = await this.vehicleTypeRepository.findOne({
        id: vehicleRegistrationDto.vehicleType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleTypedata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleType.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleManufactureata = await this.vehicleManufactureRepository.findOne({
        id: vehicleRegistrationDto.vehicleManufacture,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleManufactureata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleManufacture.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleModeldata = await this.vehicleModelRepository.findOne({
        id: vehicleRegistrationDto.vehicleModel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleModeldata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleModel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      dataapprovalLevel = await this.approvalLevelRepository.findOne({
        code: 'ICBM2',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      datastatusType = await this.statusTypeRepository.findOne({
        name: 'PENDING',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    const registeredVehicleDto = new RegisteredVehicle({
      ...vehicleRegistrationDto,
      vehicleRegRequest: vehicleRegRequestData2, // Assign the StatusType object
      vehicleType: vehicleTypedata,
      vehicleManufacture: vehicleManufactureata,
      vehicleModel: vehicleModeldata,
      ownershipType: ownershipTypedata,
      approvalLevel: dataapprovalLevel,

      beneficiaryAgencyId: vehicleRegistrationDto.beneficiaryAgencyId,
      beneficiaryAgency: vehicleRegistrationDto.beneficiaryAgency,
      chassisNumber: vehicleRegistrationDto.chassisNumber,
      engineNumber: vehicleRegistrationDto.engineNumber,
      transmissionType: vehicleRegistrationDto.transmissionType,
      fuelType: vehicleRegistrationDto.fuelType,
      manufacturingYear: vehicleRegistrationDto.manufactureYear,
      odometerReading: vehicleRegistrationDto.odometerReading,
      acquisitionDate: vehicleRegistrationDto.acquisitionDate,
      invoiceNumber: vehicleRegistrationDto.invoiceNumber,
      invoiceDate: vehicleRegistrationDto.invoiceDate,
      customsDeclarationNumber: vehicleRegistrationDto.customsDeclarationNumber,
      customsDeclarationDate: vehicleRegistrationDto.customsDeclarationDate,
      declaredAmount: vehicleRegistrationDto.declaredAmount,
      projectName: vehicleRegistrationDto.projectName,
      projectStartDate: vehicleRegistrationDto.projectStartDate,
      projectEndDate: vehicleRegistrationDto.projectEndDate,
      projectDescription: vehicleRegistrationDto.projectDescription,
      registrationStatus: datastatusType,
      reportingInstitution: reportingInstitution,
      reportingInstitutionId: reportingInstitutionId,

      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });
    try {
      // Your insert/update operation here
      const registredVehicle =
        await this.registeredVehicleRepository.create(registeredVehicleDto);
      if (registredVehicle) {
        registredVehicleNUmber = Number(registredVehicleNUmber) + 1;
        // updating Vehicle number
        const previousVehicleNumberData =
          await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder(
            'vehilceNumberPerAccquisition',
          )

            .where(
              'vehilceNumberPerAccquisition.acquisition = :vehicleAcquistionId',
              {
                vehicleAcquistionId,
              },
            )
            .getOne();
        const preId = previousVehicleNumberData.id;
        const updateVehicleNumber =
          await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder() // Use query builder without alias
            .update('VehilceNumberPerAccquisition') // Provide the table name
            .set({
              numberOfRegistredVehicle: registredVehicleNUmber,
            })
            .where('id = :preId', { preId }) // Assuming id is the primary key column
            .execute();
        if (updateVehicleNumber) {
          // check now a statutus of vehicle number to update quition statutus
          const previousVehicleNumberData2 =
            await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder(
              'vehilceNumberPerAccquisition',
            )

              .where(
                'vehilceNumberPerAccquisition.acquisition = :vehicleAcquistionId',
                {
                  vehicleAcquistionId,
                },
              )
              .getOne();
          const allowedvecle = Number(
            previousVehicleNumberData2.numberOfRequestedVehicle,
          );
          const RegisteredVehicleNumber = Number(
            previousVehicleNumberData2.numberOfRegistredVehicle,
          );
          // updating acquisition
          if (RegisteredVehicleNumber >= allowedvecle) {
            // do update Aquisition
            await this.vehicleAcquisitionEntityManagerRepository
              .createQueryBuilder() // Use query builder without alias
              .update('VehicleAcquisition') // Provide the table name
              .set({
                isAllVehicleregrequestSubmitted: true, // Assuming nextapprovalLevelData has an id property
              })
              .where('id = :vehicleAcquistionId', { vehicleAcquistionId }) // Assuming id is the primary key column
              .execute();
          }
        }
      }
      return registredVehicle;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        console.error('QueryFailedError:', error.message);
        if (error.driverError && error.driverError.detail) {
          console.error('Detail:', error.driverError.detail);
        }
      } else {
        throw error;
      }
    }

    // return vehicleRegRequestData;
  }

  // available vehicle
  async ExistingVehicleRegistration(
    vehicleRegistrationDto: ExistingVehicleRegistrationDto,
  ) {
    // i am gonna to receive implement institution reporting
    console.log('============ Beneficiary Angency=============');
    console.log(vehicleRegistrationDto.beneficiaryAgency);
    console.log(vehicleRegistrationDto.beneficiaryAgencyId);
    let reportingInstitution = null;
    let reportingInstitutionId = null;
    const institutionId = vehicleRegistrationDto.beneficiaryAgencyId;
    const beneficiaryAgencyData = await this.getInstitution(institutionId);
    if (!beneficiaryAgencyData) {
      return {
        message: 'beneficiaryAgencyData not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const budgetedStatus = beneficiaryAgencyData[0].isbudgeted;
    if (!budgetedStatus) {
      const beneficiaryAgencyName = beneficiaryAgencyData[0].name;
      const non_budgeted_agenciesData = await this.getNonBudgetedAgency(
        beneficiaryAgencyName,
      );
      if (!non_budgeted_agenciesData) {
        return {
          message: 'non_budgeted_agenciesData not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      }

      const reportinganecy = non_budgeted_agenciesData[0].institution;
      const reportingInstitutionData =
        await this.getInstitution(reportinganecy);
      if (!reportingInstitutionData) {
        return {
          message: 'non_budgeted_agenciesData not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      }
      reportingInstitution = reportingInstitutionData[0].name;
      reportingInstitutionId = reportingInstitutionData[0].id;
    }
    console.log('============ reporting institution=============');
    console.log(reportingInstitution);
    console.log(reportingInstitutionId);
    // let allowedVehicleNUmber;
    // let registredVehicleNUmber;
    // let vehicleAcquistionId;
    // checking if number of vehicle to be registred per aquisition if is recheded
    // const vehicleRegId = vehicleRegistrationDto.vehicleRegRequest;
    // console.log(vehicleRegId);
    // const vehicleRegRequestData =
    //   await this.vehicleRegRequestentityManagerRepository
    //     .createQueryBuilder('vehicleRegRequest')

    //     .where('vehicleRegRequest.id = :vehicleRegId', {
    //       vehicleRegId,
    //     })
    //     .leftJoinAndSelect(
    //       'vehicleRegRequest.VehicleAcquisition',
    //       'VehicleAcquisition',
    //     )
    //     .getOne();
    // if (vehicleRegRequestData.VehicleAcquisition) {
    //   vehicleAcquistionId = vehicleRegRequestData.VehicleAcquisition.id;
    //   console.log(vehicleAcquistionId);
    //   const previousVehicleNumberData =
    //     await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder(
    //       'vehilceNumberPerAccquisition',
    //     )

    //       .where(
    //         'vehilceNumberPerAccquisition.acquisition = :vehicleAcquistionId',
    //         {
    //           vehicleAcquistionId,
    //         },
    //       )
    //       .getOne();
    //   // console.log(previousVehicleNumberData);
    //   allowedVehicleNUmber = Number(
    //     previousVehicleNumberData.numberOfRequestedVehicle,
    //   );
    //   registredVehicleNUmber = Number(
    //     previousVehicleNumberData.numberOfRegistredVehicle,
    //   );

    //   console.log(previousVehicleNumberData);
    //   if (registredVehicleNUmber >= allowedVehicleNUmber) {
    //     return {
    //       message: 'Number of vehicle allowed to this Acquisition is reached',
    //       statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    //     };
    //   }
    // }
    // let vehicleRegRequestData2;
    let ownershipTypedata;
    let vehicleTypedata;
    let vehicleManufactureata;
    let vehicleModeldata;
    let dataapprovalLevel;
    let datastatusType;
    let vehicleStatusData;
    // try {
    //   vehicleRegRequestData2 = await this.vehicleRegRequestRepository.findOne({
    //     id: vehicleRegistrationDto.vehicleRegRequest,
    //   });
    // } catch (error) {
    //   if (error.message === 'Entity not found.') {
    //     return {
    //       message: 'vehicle Reg Request data  not find in database',
    //       statusCode: HttpStatus.NOT_FOUND,
    //     };
    //   } else {
    //     return {
    //       message:
    //         error.message ||
    //         'An error occurred while fetching data for vehicle Reg Request .',
    //       statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    //     };
    //   }
    // }
    try {
      ownershipTypedata = await this.ownershipTypeRepository.findOne({
        id: vehicleRegistrationDto.ownershipType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'ownershipTypedata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for ownershipType.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleTypedata = await this.vehicleTypeRepository.findOne({
        id: vehicleRegistrationDto.vehicleType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleTypedata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleType.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      vehicleManufactureata = await this.vehicleManufactureRepository.findOne({
        id: vehicleRegistrationDto.vehicleManufacture,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleManufactureata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleManufacture.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      vehicleModeldata = await this.vehicleModelRepository.findOne({
        id: vehicleRegistrationDto.vehicleModel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleModeldata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleModel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      dataapprovalLevel = await this.approvalLevelRepository.findOne({
        code: 'ICBM2',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      datastatusType = await this.statusTypeRepository.findOne({
        name: 'PENDING',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleStatusData = await this.vehicleStatusRepository.findOne({
        id: vehicleRegistrationDto.vehicleStatus,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Vehicle status  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleStatusData.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const registeredVehicleDto = new RegisteredVehicle({
      ...vehicleRegistrationDto,
      // vehicleRegRequest: vehicleRegRequestData2, // Assign the StatusType object
      vehicleType: vehicleTypedata,
      vehicleManufacture: vehicleManufactureata,
      vehicleModel: vehicleModeldata,
      ownershipType: ownershipTypedata,
      approvalLevel: dataapprovalLevel,

      beneficiaryAgencyId: vehicleRegistrationDto.beneficiaryAgencyId,
      beneficiaryAgency: vehicleRegistrationDto.beneficiaryAgency,
      chassisNumber: vehicleRegistrationDto.chassisNumber,
      engineNumber: vehicleRegistrationDto.engineNumber,
      fuelType: vehicleRegistrationDto.fuelType,
      plateNumber: vehicleRegistrationDto.plateNumber,
      transmissionType: vehicleRegistrationDto.transmissionType,
      manufacturingYear: vehicleRegistrationDto.manufactureYear,
      odometerReading: vehicleRegistrationDto.odometerReading,
      acquisitionDate: vehicleRegistrationDto.acquisitionDate,
      invoiceNumber: vehicleRegistrationDto.invoiceNumber,
      invoiceDate: vehicleRegistrationDto.invoiceDate,
      customsDeclarationNumber: vehicleRegistrationDto.customsDeclarationNumber,
      customsDeclarationDate: vehicleRegistrationDto.customsDeclarationDate,
      declaredAmount: vehicleRegistrationDto.declaredAmount,
      projectName: vehicleRegistrationDto.projectName,
      projectStartDate: vehicleRegistrationDto.projectStartDate,
      projectEndDate: vehicleRegistrationDto.projectEndDate,
      projectDescription: vehicleRegistrationDto.projectDescription,
      registrationStatus: datastatusType,
      reportingInstitution: reportingInstitution,
      reportingInstitutionId: reportingInstitutionId,
      submittedDate: new Date(),
      approveddate: new Date(),
      isVehicleActive: vehicleRegistrationDto.isVehicleActive,
      vehicleStatus: vehicleStatusData,
      pickCardNumber: vehicleRegistrationDto.pickCardNumber,
      isrecordedFromOldVehicle: true,
      // isDisposalRequestSubmitted: vehicleRegistrationDto.isDisposalRequestSubmitted,
      // isProjectOnGoing: vehicleRegistrationDto.isProjectOnGoing,
      // isTimeToReturnProjectVehicle: vehicleRegistrationDto.isTimeToReturnProjectVehicle,

      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });
    try {
      // Your insert/update operation here
      const registredVehicle =
        await this.registeredVehicleRepository.create(registeredVehicleDto);
      return registredVehicle;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        console.error('QueryFailedError:', error.message);
        if (error.driverError && error.driverError.detail) {
          console.error('Detail:', error.driverError.detail);
        }
      } else {
        throw error;
      }
    }
  }

  async updateisActivityDAteInCurrentQuater() {
    // Fetch all records where isActivityDAteInCurrentQuater is true
    const activities = await this.maintenanceActivitiesRepository.find({
      isActivityDAteInCurrentQuater: true,
    });

    // // Loop through the records and update the status
    for (const activity of activities) {
      const {
        activityDate,
        activityQuaterSatrtDate,
        activityQuaterEndDateDate,
      } = activity;
      // const quatersObject = await this.getQuarterDates(activityDate);
      // console.log(quatersObject);

      // Check if activityDate is within the range
      const isWithinRange =
        activityDate >= activityQuaterSatrtDate &&
        activityDate <= activityQuaterEndDateDate;
      console.log(isWithinRange);
      if (!isWithinRange) {
        activity.isActivityDAteInCurrentQuater = false;
      }

      // Update the isActivityDAteInCurrentQuater field

      //Save the updated record
      await this.maintenanceActivitiesManagerRepository.save(activity);
    }

    return {
      message: 'Activity quarter status updated successfully',
      statusCode: 200,
    };
  }

  // update vehicle

  //  regisraring existing vehicle

  async updateRegisVehicle(
    id: string,
    updateRegistredVehicleDto: UpdateRegistredVehicleDto,
  ) {
    // let previousVehicleData;
    let ownershipTypedata;
    let vehicleTypedata;
    let vehicleManufactureata;
    let vehicleModeldata;
    // let dataapprovalLevel;
    let datastatusType;

    const previousVehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :id', {
          id,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    if (!previousVehicleData) {
      return {
        message: 'Vehicle data not found in database',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }

    try {
      ownershipTypedata = await this.ownershipTypeRepository.findOne({
        id: updateRegistredVehicleDto.ownershipType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'ownershipTypedata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for ownershipType.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleTypedata = await this.vehicleTypeRepository.findOne({
        id: updateRegistredVehicleDto.vehicleType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleTypedata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleType.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleManufactureata = await this.vehicleManufactureRepository.findOne({
        id: updateRegistredVehicleDto.vehicleManufacture,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleManufactureata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleManufacture.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleModeldata = await this.vehicleModelRepository.findOne({
        id: updateRegistredVehicleDto.vehicleModel,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicleModeldata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for vehicleModel.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // application level
    const approvalLevelcode = previousVehicleData.approvalLevel.code;
    const approvalLevelIndex =
      this.aquistionApprovalService.findIndexByApprovalLevel(approvalLevelcode);
    const nextapprovalLevelIndex = (await approvalLevelIndex) + 1;

    const nextapprovalLevelcode =
      await this.aquistionApprovalService.findApprovalLevelByIndex(
        nextapprovalLevelIndex,
      );

    // const nextapprovalLevelData = await this.approvalLevelRepository.findOne({
    //   code: nextapprovalLevelcode,
    // });
    // const currentapprovalLevelData = await this.approvalLevelRepository.findOne(
    //   {
    //     code: approvalLevelcode,
    //   },
    // );

    if (nextapprovalLevelcode === 'IL1') {
      // geting status data

      try {
        datastatusType = await this.statusTypeRepository.findOne({
          name: 'PENDING',
        });
      } catch (error) {
        if (error.message === 'Entity not found.') {
          return {
            message: 'PENDING status type not found in database',
            statusCode: HttpStatus.NOT_FOUND,
          };
        } else {
          return {
            message:
              error.message ||
              'An error occurred while fetching status data type.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }

      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          vehicleType: vehicleTypedata,
          vehicleManufacture: vehicleManufactureata,
          vehicleModel: vehicleModeldata,
          ownershipType: ownershipTypedata,
          // approvalLevel: currentapprovalLevelData,
          isVehicleActive: updateRegistredVehicleDto.isVehicleActive,
          plateNumber: updateRegistredVehicleDto.plateNumber,

          beneficiaryAgencyId: updateRegistredVehicleDto.beneficiaryAgencyId,
          beneficiaryAgency: updateRegistredVehicleDto.beneficiaryAgency,
          chassisNumber: updateRegistredVehicleDto.chassisNumber,
          engineNumber: updateRegistredVehicleDto.engineNumber,
          transmissionType: updateRegistredVehicleDto.transmissionType,
          fuelType: updateRegistredVehicleDto.fuelType,
          manufacturingYear: updateRegistredVehicleDto.manufactureYear,
          odometerReading: updateRegistredVehicleDto.odometerReading,
          acquisitionDate: updateRegistredVehicleDto.acquisitionDate,
          invoiceNumber: updateRegistredVehicleDto.invoiceNumber,
          invoiceDate: updateRegistredVehicleDto.invoiceDate,
          customsDeclarationNumber:
            updateRegistredVehicleDto.customsDeclarationNumber,
          customsDeclarationDate:
            updateRegistredVehicleDto.customsDeclarationDate,
          declaredAmount: updateRegistredVehicleDto.declaredAmount,
          projectName: updateRegistredVehicleDto.projectName,
          projectStartDate: updateRegistredVehicleDto.projectStartDate,
          projectEndDate: updateRegistredVehicleDto.projectEndDate,
          projectDescription: updateRegistredVehicleDto.projectDescription,
          registrationStatus: datastatusType,
        })
        .where('id = :id', { id }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }
    try {
      datastatusType = await this.statusTypeRepository.findOne({
        name: 'PROGRESS',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
      .update('RegisteredVehicle') // Provide the table name
      .set({
        vehicleType: vehicleTypedata,
        vehicleManufacture: vehicleManufactureata,
        vehicleModel: vehicleModeldata,
        ownershipType: ownershipTypedata,
        // approvalLevel: nextapprovalLevelData,
        beneficiaryAgencyId: updateRegistredVehicleDto.beneficiaryAgencyId,
        beneficiaryAgency: updateRegistredVehicleDto.beneficiaryAgency,
        chassisNumber: updateRegistredVehicleDto.chassisNumber,
        engineNumber: updateRegistredVehicleDto.engineNumber,
        transmissionType: updateRegistredVehicleDto.transmissionType,
        fuelType: updateRegistredVehicleDto.fuelType,
        manufacturingYear: updateRegistredVehicleDto.manufactureYear,
        odometerReading: updateRegistredVehicleDto.odometerReading,
        acquisitionDate: updateRegistredVehicleDto.acquisitionDate,
        invoiceNumber: updateRegistredVehicleDto.invoiceNumber,
        invoiceDate: updateRegistredVehicleDto.invoiceDate,
        customsDeclarationNumber:
          updateRegistredVehicleDto.customsDeclarationNumber,
        customsDeclarationDate:
          updateRegistredVehicleDto.customsDeclarationDate,
        declaredAmount: updateRegistredVehicleDto.declaredAmount,
        projectName: updateRegistredVehicleDto.projectName,
        projectStartDate: updateRegistredVehicleDto.projectStartDate,
        projectEndDate: updateRegistredVehicleDto.projectEndDate,
        projectDescription: updateRegistredVehicleDto.projectDescription,
        registrationStatus: datastatusType,
      })
      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();
    // return 'this is logistiocds';
    // getting updated vehicle
    const updatedVehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :id', {
          id,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    return updatedVehicleData;
  }

  // vehicle allocation
  async VehicleAllocation(
    id: string,
    vehicleAllocationDto: VehicleAllocationDto,
  ) {
    console.log('============ Beneficiary Angency=============');
    console.log(vehicleAllocationDto.beneficiaryAgency);
    console.log(vehicleAllocationDto.beneficiaryAgencyId);
    let reportingInstitution = null;
    let reportingInstitutionId = null;
    const institutionId = vehicleAllocationDto.beneficiaryAgencyId;
    const beneficiaryAgencyData = await this.getInstitution(institutionId);
    if (!beneficiaryAgencyData) {
      return {
        message: 'beneficiaryAgencyData not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const budgetedStatus = beneficiaryAgencyData[0].isbudgeted;
    // console.log(budgetedStatus), console.log(beneficiaryAgencyName);
    if (!budgetedStatus) {
      const beneficiaryAgencyName = beneficiaryAgencyData[0].name;
      const non_budgeted_agenciesData = await this.getNonBudgetedAgency(
        beneficiaryAgencyName,
      );
      if (!non_budgeted_agenciesData) {
        return {
          message: 'non_budgeted_agenciesData not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      }
      const reportinganecy = non_budgeted_agenciesData[0].institution;
      const reportingInstitutionData =
        await this.getInstitution(reportinganecy);
      if (!reportingInstitutionData) {
        return {
          message: 'non_budgeted_agenciesData not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      }
      reportingInstitution = reportingInstitutionData[0].name;
      reportingInstitutionId = reportingInstitutionData[0].id;
    }
    // getting acquisition data
    const acquisitionId = vehicleAllocationDto.acquisitionId;
    const acquistionData = await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      .where('vehicleAcquisition.id = :acquisitionId', {
        acquisitionId,
      })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
      .getMany();
    // checking if Aquisition is approved all not
    if (
      acquistionData[0].statusType.name != 'APPROVED' &&
      acquistionData[0].approvalLevel.code != 'MOS'
    ) {
      return {
        message: 'Aqcuistion not yet approved',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    // checking if number of vehicle per Acquisition if is reacged or not
    const previousVehicleNumberData =
      await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder(
        'vehilceNumberPerAccquisition',
      )

        .where('vehilceNumberPerAccquisition.acquisition = :acquisitionId', {
          acquisitionId,
        })
        .getOne();
    const allowedVehicleNUmber = Number(
      previousVehicleNumberData.numberOfRequestedVehicle,
    );
    const registredVehicleNUmber = Number(
      previousVehicleNumberData.numberOfRegistredVehicle,
    );

    console.log(previousVehicleNumberData);
    if (registredVehicleNUmber >= allowedVehicleNUmber) {
      return {
        message: 'Number of vehicle allowed to this Acquisition is reached',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }

    // checking if number of vehicle to be registred per aquisition if is recheded
    let ownershipTypedata;
    let datastatusType;
    // vehicle allocation longin
    const previousVehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :id', {
          id,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    if (!previousVehicleData) {
      return {
        message: 'Vehicle data not found in database',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    if (previousVehicleData.vehicleStatus === null) {
      return {
        message: 'Vehicle is not returened ',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    if (previousVehicleData.vehicleStatus.name != 'RETURNED') {
      return {
        message: 'Vehicle is not returened ',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    try {
      ownershipTypedata = await this.ownershipTypeRepository.findOne({
        id: vehicleAllocationDto.ownershipType,
      });
      console.log(ownershipTypedata);
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'ownershipTypedata  not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for ownershipType.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const approvalLeval = await this.approvalLevelRepository.findOne({
      code: 'DGT',
    });

    try {
      datastatusType = await this.statusTypeRepository.findOne({
        name: 'PROGRESS',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    const allocatingVehicle =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          ownershipType: ownershipTypedata,
          approvalLevel: approvalLeval,
          beneficiaryAgencyId: vehicleAllocationDto.beneficiaryAgencyId,
          beneficiaryAgency: vehicleAllocationDto.beneficiaryAgency,
          projectName: vehicleAllocationDto.projectName,
          projectStartDate: vehicleAllocationDto.projectStartDate,
          projectEndDate: vehicleAllocationDto.projectEndDate,
          projectDescription: vehicleAllocationDto.projectDescription,
          registrationStatus: datastatusType,
          vehicleStatus: null,
          pickCardNumber: vehicleAllocationDto.pickCardNumber,
          plateNumber: vehicleAllocationDto.plateNumber,
          reportingInstitution: reportingInstitution,
          reportingInstitutionId: reportingInstitutionId,
        })
        .where('id = :id', { id }) // Assuming id is the primary key column
        .execute();

    if (allocatingVehicle) {
      // updating registred vehicle number
      const currentregistredVehicleNUmber = Number(registredVehicleNUmber) + 1;
      const preId = previousVehicleNumberData.id;
      const updateVehicleNumber =
        await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder() // Use query builder without alias
          .update('VehilceNumberPerAccquisition') // Provide the table name
          .set({
            numberOfRegistredVehicle: currentregistredVehicleNUmber,
          })
          .where('id = :preId', { preId }) // Assuming id is the primary key column
          .execute();
      if (updateVehicleNumber) {
        // updating statust of aquisition
        const previousVehicleNumberData2 =
          await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder(
            'vehilceNumberPerAccquisition',
          )

            .where(
              'vehilceNumberPerAccquisition.acquisition = :acquisitionId',
              {
                acquisitionId,
              },
            )
            .getOne();
        const allowedvecle = Number(
          previousVehicleNumberData2.numberOfRequestedVehicle,
        );
        const RegisteredVehicleNumber = Number(
          previousVehicleNumberData2.numberOfRegistredVehicle,
        );
        if (RegisteredVehicleNumber >= allowedvecle) {
          // do update Aquisition
          await this.vehicleAcquisitionEntityManagerRepository
            .createQueryBuilder() // Use query builder without alias
            .update('VehicleAcquisition') // Provide the table name
            .set({
              isAllVehicleregrequestSubmitted: true, // Assuming nextapprovalLevelData has an id property
            })
            .where('id = :acquisitionId', { acquisitionId }) // Assuming id is the primary key column
            .execute();
        }
      }
    }

    const updatedVehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :id', {
          id,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();
    return updatedVehicleData;

    // return vehicleAllocationDto;
  }

  async createAcquisition(createAcquisitionDto: CreateAcquisitionDto) {
    // checking if preset data is exists
    // is requestType is exist
    let datarequestType;
    let dataownershipType;
    let datastatusType;
    let dataapprovalLevel;

    try {
      datarequestType = await this.requestTypeRepository.findOne({
        id: createAcquisitionDto.requestType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'request data type not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for request data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // verify ownership type
    try {
      dataownershipType = await this.ownershipTypeRepository.findOne({
        id: createAcquisitionDto.ownershipType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Owner type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for ownership.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // verfy for statust

    try {
      datastatusType = await this.statusTypeRepository.findOne({
        name: 'PENDING',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // verfy for aproval leval
    try {
      dataapprovalLevel = await this.approvalLevelRepository.findOne({
        code: 'ICBM2',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const newAcquisitionDto = new VehicleAcquisition({
      ...createAcquisitionDto,
      statusType: datastatusType, // Assign the StatusType object
      approvalLevel: dataapprovalLevel, // Assign the ApprovalLevel object
      requestType: datarequestType,
      ownershipType: dataownershipType,
      isAllVehicleregrequestSubmitted: false, // Assign the RequestType object
    });

    const savedAcquisition =
      await this.vehicleAcquisitionRepository.create(newAcquisitionDto);

    // Return the ID of the created acquisition
    return { acquisitionId: savedAcquisition.id };
  }

  async findAllAcquisition() {
    return this.vehicleAcquisitionRepository.findAll({
      relations: {
        requestType: true,
        ownershipType: true,
        approvalLevel: true,
        statusType: true,
      },
    });
  }
  // requesting Vehicle method
  async createRequestedVehicle(
    createRequestedVehicleDto: CreateRequestedVehicleDto,
  ) {
    const acquisitionId = createRequestedVehicleDto.acquisitionId;
    let datavehicleType;
    let dataVehicleAcquition;
    try {
      datavehicleType = await this.vehicleTypeRepository.findOne({
        id: createRequestedVehicleDto.vehicleTypeid,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicle data type  not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching vehicle data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // check if vehicle aquisition exists
    try {
      dataVehicleAcquition = await this.vehicleAcquisitionRepository.findOne({
        id: createRequestedVehicleDto.acquisitionId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicle aquisition not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching acquistion data.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const newVechileDto = new RequestedVehicle({
      ...createRequestedVehicleDto,
      acquisition: dataVehicleAcquition, // Assign the StatusType object
      vehicleType: datavehicleType, // Assign the ApprovalLevel object
      // Assign the RequestType object
    });
    const requestedVehicle =
      await this.requestedVehicleRepository.create(newVechileDto);
    if (requestedVehicle) {
      // updating or create  number of vehicle
      const previousVehicleNumberData =
        await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder(
          'vehilceNumberPerAccquisition',
        )

          .where('vehilceNumberPerAccquisition.acquisition = :acquisitionId', {
            acquisitionId,
          })
          .getOne();
      if (previousVehicleNumberData) {
        const preId = previousVehicleNumberData.id;
        const previousNumber = Number(
          previousVehicleNumberData.numberOfRequestedVehicle,
        );
        const newNumber = Number(createRequestedVehicleDto.numberOfVehicles);

        if (isNaN(previousNumber) || isNaN(newNumber)) {
          // Handle the error, e.g., throw an error or return a default value
          throw new Error('Invalid number input');
        }

        const newNumberOfRequestedVehicle = previousNumber + newNumber;
        // Update the existing entity
        await this.VehilceNumberPerAccquisitionEntityManagerRepository.createQueryBuilder() // Use query builder without alias
          .update('VehilceNumberPerAccquisition') // Provide the table name
          .set({
            numberOfRequestedVehicle: newNumberOfRequestedVehicle,
          })
          .where('id = :preId', { preId }) // Assuming id is the primary key column
          .execute();
      } else {
        // Create a new VehilceNumberPerAccquisition entity
        const newVehicleNumberDto = new VehilceNumberPerAccquisition({
          acquisition: dataVehicleAcquition, // Assign the StatusType object
          numberOfRequestedVehicle: createRequestedVehicleDto.numberOfVehicles, // Assign the ApprovalLevel object
          numberOfRegistredVehicle: 0,
          // saving data objec
        });
        await this.VehilceNumberPerAccquisitionRepositoryRepository.create(
          newVehicleNumberDto,
        );
      }
    }
    return requestedVehicle;
  }

  async findAllAcquisitionByInstitution(institutionId: string) {
    return await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      .where('vehicleAcquisition.institutionId = :institutionId', {
        institutionId,
      })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
      .getMany();
  }
  async findAcquisitionById(id: string) {
    return await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      .where('vehicleAcquisition.id = :id', {
        id,
      })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
      .getMany();
  }
  async findAcquisitionByfielter(filters: searcAcquisionAndFilterDTO) {
    const query = this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      // .where('vehicleAcquisition.institutionId = :institutionId', {
      //   institutionId,
      // })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel');

    if (filters.institution) {
      query.andWhere('vehicleAcquisition.institution = :institution', {
        institution: filters.institution,
      });
    }
    if (filters.id) {
      query.andWhere('vehicleAcquisition.id = :id', {
        id: filters.id,
      });
    }

    if (filters.userId) {
      query.andWhere('vehicleAcquisition.userId = :userId', {
        userId: filters.userId,
      });
    }
    if (filters.requestType) {
      query.andWhere('requestType.name = :requestType', {
        requestType: filters.requestType,
      });
    }
    if (filters.ownershipType) {
      query.andWhere('ownershipType.name = :ownershipType', {
        ownershipType: filters.ownershipType,
      });
    }
    if (filters.statusType) {
      query.andWhere('statusType.name = :statusType', {
        statusType: filters.statusType,
      });
    }
    if (filters.approvalLeval) {
      query.andWhere('approvalLevel.name = :approvalLeval', {
        approvalLeval: filters.approvalLeval,
      });
    }
    if (filters.isAllVehicleregrequestSubmitted) {
      query.andWhere(
        'vehicleAcquisition.isAllVehicleregrequestSubmitted = :isAllVehicleregrequestSubmitted',
        {
          isAllVehicleregrequestSubmitted:
            filters.isAllVehicleregrequestSubmitted,
        },
      );
    }
    // date related staff
    if (filters.createdDateStart && filters.createdDateEnd) {
      query.andWhere(
        'vehicleAcquisition.createddate BETWEEN :createdDateStart AND :createdDateEnd',
        {
          createdDateStart: filters.createdDateStart,
          createdDateEnd: filters.createdDateEnd,
        },
      );
    } else if (filters.createdDateStart) {
      query.andWhere('vehicleAcquisition.createddate >= :createdDateStart', {
        createdDateStart: filters.createdDateStart,
      });
    } else if (filters.createdDateEnd) {
      query.andWhere('vehicleAcquisition.createddate <= :createdDateEnd', {
        createdDateEnd: filters.createdDateEnd,
      });
    }

    if (filters.submitedDateStart && filters.submitedDateEnd) {
      query.andWhere(
        'vehicleAcquisition.submittedDate BETWEEN :submitedDateStart AND :submitedDateStart',
        {
          submitedDateStart: filters.submitedDateStart,
          submitedDateEnd: filters.submitedDateEnd,
        },
      );
    } else if (filters.submitedDateStart) {
      query.andWhere('vehicleAcquisition.submittedDate >= :submitedDateStart', {
        submitedDateStart: filters.submitedDateStart,
      });
    } else if (filters.submitedDateEnd) {
      query.andWhere('vehicleAcquisition.submittedDate <= :submitedDateEnd', {
        submitedDateEnd: filters.submitedDateEnd,
      });
    }

    if (filters.approvalDateStart && filters.approvedDateEnd) {
      query.andWhere(
        'vehicleAcquisition.approveddate BETWEEN :approvalDateStart AND :approvedDateEnd',
        {
          approvalDateStart: filters.approvalDateStart,
          approvedDateEnd: filters.approvedDateEnd,
        },
      );
    } else if (filters.approvalDateStart) {
      query.andWhere('vehicleAcquisition.approveddate >= :approvalDateStart', {
        approvalDateStart: filters.approvalDateStart,
      });
    } else if (filters.approvedDateEnd) {
      query.andWhere('vehicleAcquisition.approveddate <= :approvedDateEnd', {
        approvedDateEnd: filters.approvedDateEnd,
      });
    }

    // searching by string handoling logic
    if (filters.searchString) {
      const trimmedSearchString = filters.searchString.trim();
      const lowerCaseSearchString = `%${trimmedSearchString.toLowerCase()}%`;

      query
        // .where('vehicleAcquisition.id LIKE :searchString', {
        //   searchString: lowerCaseSearchString,
        // })
        .orWhere('LOWER(vehicleAcquisition.userId) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(requestType.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(ownershipType.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(vehicleAcquisition.institution) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(statusType.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(approvalLevel.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(approvalLevel.code) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        });
    }

    const [acquisitions, count] = await query.getManyAndCount();
    return { acquisitions, count };
  }

  async findRequestedVehicles() {
    return await this.RequestedVehiclentityManagerRepository.createQueryBuilder(
      'requestedVehicle',
    )
      .leftJoinAndSelect('requestedVehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect('requestedVehicle.acquisition', 'acquisition')
      .leftJoinAndSelect('acquisition.requestType', 'requestType')
      .leftJoinAndSelect('acquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('acquisition.statusType', 'statusType')
      .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .getMany();
  }

  // funding acquition detail by acqution ID
  async AcquisitionDetailsByAcquisitionID(acquisitionId: string) {
    // aqusition details
    const acquisition = await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      .where('vehicleAcquisition.id = :acquisitionId', {
        acquisitionId,
      })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
      .getOne();

    const vehicles =
      await this.RequestedVehiclentityManagerRepository.createQueryBuilder(
        'requestedVehicle', // Use lowercase for the table alias
      )
        // .select([
        //   'requestedVehicle.id', // Include primary key column
        //   'requestedVehicle.numberOfVehicles',
        //   'requestedVehicle.intendedUsage',
        //   'requestedVehicle.beneficiaryAgencyId',
        //   'requestedVehicle.beneficiaryAgency',
        //   'requestedVehicle.projectName',
        //   'requestedVehicle.projectStartDate',
        //   'requestedVehicle.projectEndDate',
        //   'requestedVehicle.projectDescription',
        // ])
        .where('requestedVehicle.acquisitionId = :acquisitionId', {
          acquisitionId,
        })
        .leftJoinAndSelect('requestedVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect('requestedVehicle.acquisition', 'acquisition')
        .leftJoinAndSelect('acquisition.requestType', 'requestType')
        .leftJoinAndSelect('acquisition.ownershipType', 'ownershipType')
        .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
        .getMany();

    // Transform data to desired format
    const formattedData = {
      id: acquisition.id, // Replace with a unique ID
      acquisition: {
        userId: acquisition.userId,
        institutionId: acquisition.institutionId,
        institution: acquisition.institution,
        submittedDate: acquisition.submittedDate,
        description: acquisition.description,
        requestType: {
          id: acquisition.requestType.id,
          name: acquisition.requestType.name,
        },
        ownershipType: {
          id: acquisition.ownershipType.id,
          name: acquisition.ownershipType.name,
        },
        statusType: {
          id: acquisition.statusType.id,
          name: acquisition.statusType.name,
        },
        approvalLevel: {
          id: acquisition.approvalLevel.id,
          name: acquisition.approvalLevel.name,
          code: acquisition.approvalLevel.code,
        },
      },
      vehicles: vehicles.map((vehicle) => ({
        id: vehicle.id.toString(), // Ensure id is a string
        numberOfVehicles: vehicle.numberOfVehicles,
        vehicleType: {
          id: vehicle.vehicleType.id,
          name: vehicle.vehicleType.name,
        },
        beneficiaryAgencyId: vehicle.beneficiaryAgencyId,
        beneficiaryAgency: vehicle.beneficiaryAgency,
        intendedUsage: vehicle.intendedUsage,
        projectName: vehicle.projectName,
        projectStartDate: vehicle.projectStartDate,
        projectEndDate: vehicle.projectEndDate,
        projectDescription: vehicle.projectDescription,
        projectExtensionDate: vehicle.projectExtensionDate,
      })),
    };

    return formattedData;
  }
  // getting acquisition by acquisition id
  async AcquisitionByAcquisitionID(acquisitionId: string) {
    // aqusition details
    const acquisition = await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      .where('vehicleAcquisition.id = :acquisitionId', {
        acquisitionId,
      })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
      .getOne();
    return acquisition;
  }

  // update acquisition by acquisition ID
  // this function is used to update when request more info status is requested
  async updateAcquisition(
    acquisitionId: string,
    updateAcquisitionDto: UpdateAcquisitionDto,
  ) {
    let datarequestType;
    let dataownershipType;
    // let dataAcquistion;
    try {
      datarequestType = await this.requestTypeRepository.findOne({
        id: updateAcquisitionDto.requestType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'request data type not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for request data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // verify ownership type
    try {
      dataownershipType = await this.ownershipTypeRepository.findOne({
        id: updateAcquisitionDto.ownershipType,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Owner type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for ownership.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const acquisition = await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder('vehicleAcquisition')
      .where('vehicleAcquisition.id = :acquisitionId', {
        acquisitionId,
      })
      .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
      .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
      .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
      .getOne();
    if (!acquisition) {
      return {
        message: 'Acquisition not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    // update acquisition details
    const approvalLevelcode = acquisition.approvalLevel.code;
    // const statusname = acquisition.statusType.name;

    const approvalLevelIndex =
      this.aquistionApprovalService.findIndexByApprovalLevel(approvalLevelcode);
    const nextapprovalLevelIndex = (await approvalLevelIndex) + 1;

    const nextapprovalLevelcode =
      await this.aquistionApprovalService.findApprovalLevelByIndex(
        nextapprovalLevelIndex,
      );

    const nextapprovalLevelData = await this.approvalLevelRepository.findOne({
      code: nextapprovalLevelcode,
    });
    // let updatedAcquisition;
    let datastatusType;

    if (nextapprovalLevelcode === 'IL1') {
      // geting status data

      try {
        datastatusType = await this.statusTypeRepository.findOne({
          name: 'PENDING',
        });
      } catch (error) {
        if (error.message === 'Entity not found.') {
          return {
            message: 'PENDING status type not found in database',
            statusCode: HttpStatus.NOT_FOUND,
          };
        } else {
          return {
            message:
              error.message ||
              'An error occurred while fetching status data type.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }

      await this.vehicleAcquisitionEntityManagerRepository
        .createQueryBuilder() // Use query builder without alias
        .update('VehicleAcquisition') // Provide the table name
        .set({
          requestType: datarequestType, // Assuming datarequestType has an id property
          ownershipType: dataownershipType, // Assuming dataownershipType has an id property
          description: updateAcquisitionDto.description,
          approvalLevel: nextapprovalLevelData,
          statusType: datastatusType, // Assuming nextapprovalLevelData has an id property
        })
        .where('id = :acquisitionId', { acquisitionId }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }
    // updating acqisition
    if (nextapprovalLevelcode !== 'IL1') {
      await this.vehicleAcquisitionEntityManagerRepository
        .createQueryBuilder() // Use query builder without alias
        .update('VehicleAcquisition') // Provide the table name
        .set({
          requestType: datarequestType, // Assuming datarequestType has an id property
          ownershipType: dataownershipType, // Assuming dataownershipType has an id property
          description: updateAcquisitionDto.description,
          approvalLevel: nextapprovalLevelData, // Assuming nextapprovalLevelData has an id property
        })
        .where('id = :acquisitionId', { acquisitionId }) // Assuming id is the primary key column
        .execute();
    }
    // retuning updated acquisition
    const updatedacquisition =
      await this.vehicleAcquisitionEntityManagerRepository
        .createQueryBuilder('vehicleAcquisition')
        .where('vehicleAcquisition.id = :acquisitionId', {
          acquisitionId,
        })
        .leftJoinAndSelect('vehicleAcquisition.requestType', 'requestType')
        .leftJoinAndSelect('vehicleAcquisition.ownershipType', 'ownershipType')
        .leftJoinAndSelect('vehicleAcquisition.statusType', 'statusType')
        .leftJoinAndSelect('vehicleAcquisition.approvalLevel', 'approvalLevel')
        .getOne();
    // console.log(nextapprovalLevelcode);
    return updatedacquisition;
  }
  // updating vehicle for with specific id
  async updateVehicle(
    id: string,
    // vehicleId: string,
    updateRequestedVehicleDto: UpdateRequestedVehicleDto,
  ) {
    let datavehicleType;
    try {
      datavehicleType = await this.vehicleTypeRepository.findOne({
        id: updateRequestedVehicleDto.vehicleTypeid,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicle data type  not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching vehicle data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // update vehicle

    await this.vehicleAcquisitionEntityManagerRepository
      .createQueryBuilder() // Use query builder without alias
      .update('RequestedVehicle') // Provide the table name
      .set({
        vehicleType: datavehicleType, // Assuming datavehicleType has an id property
        numberOfVehicles: updateRequestedVehicleDto.numberOfVehicles,
        intendedUsage: updateRequestedVehicleDto.intendedUsage,
        beneficiaryAgencyId: updateRequestedVehicleDto.beneficiaryAgencyId,
        beneficiaryAgency: updateRequestedVehicleDto.beneficiaryAgency,
        projectName: updateRequestedVehicleDto.projectName,
        projectDescription: updateRequestedVehicleDto.projectDescription,
        projectStartDate: updateRequestedVehicleDto.projectStartDate,
        projectEndDate: updateRequestedVehicleDto.projectEndDate,
      })
      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();

    // retuning updated vehicle
    const updatedvehicle =
      await this.RequestedVehiclentityManagerRepository.createQueryBuilder(
        'RequestedVehicle',
      )
        .where('RequestedVehicle.id = :id', { id })
        .leftJoinAndSelect('RequestedVehicle.vehicleType', 'vehicleType')
        .getOne();
    return updatedvehicle;
    // return datavehicleType;
  }

  // =============== IMPLEMENTING Quatery Report submittion =============================

  async submitQuateryReport(submitQuateryReportDto: SubmitQuateryReportDto) {
    let statusdata;
    let approvalData;
    let vehicleStatusData;
    let vehicleData2;
    const vehicleId = submitQuateryReportDto.vehicleId;

    const vehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    if (!vehicleData) {
      return {
        message: 'Vehicle not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }

    try {
      statusdata = await this.statusTypeRepository.findOne({
        name: 'PENDING',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      approvalData = await this.approvalLevelRepository.findOne({
        code: 'ICBM2',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleStatusData = await this.vehicleStatusRepository.findOne({
        id: submitQuateryReportDto.vehicleStatusId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleData2 = await this.getVehicleById(vehicleId);
      if (!vehicleData) {
        throw new NotFoundException(`Vehicle with ID '${vehicleId}' not found`);
      }
      // return vehicleData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Handle NotFoundException
        return {
          message: 'Vehicle not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        // Handle other errors
        throw new Error(`Failed to fetch vehicle data: ${error.message}`);
      }
    }

    const vehicleQuarterlyReportDTO = new VehicleQuarterlyReport({
      ...submitQuateryReportDto,
      vehicle: vehicleData2,
      description: submitQuateryReportDto.description,
      reported_date: new Date(),
      isVehicleActive: submitQuateryReportDto.isVehicleActive,
      reportStatus: statusdata,
      approvalLevel: approvalData,
      vehicleStatus: vehicleStatusData,
      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });

    const QuateryReport = await this.vehicleQuarterlyReportRepository.create(
      vehicleQuarterlyReportDTO,
    );

    if (QuateryReport) {
      // now updating vehicle status is required
      const quarterObject = await this.getQuarterDates(new Date());
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          isVehicleActive: submitQuateryReportDto.isVehicleActive,
          isQuarterlyReportSubmited: true,
          isoverdueQuarterlyReportSubmition: false,
          lastQuarterlyReportsubmittedDate: new Date(),
          startNextQuarterlyReportsubmittedDate:
            quarterObject.nextQuarter.startDate,
          endNextQuarterlyReportsubmittedDate:
            quarterObject.nextQuarter.endDate,
          vehicleStatus: vehicleStatusData,
          // isVehicleActive: vehicleStatusData.isVehicleActive,
        })

        .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }

    return QuateryReport;
  }

  // create quatery report activities
  async createQuateryReportActivity(reportActivitiesDto: ReportActivitiesDto) {
    const vehicleQuarterlyReportId =
      reportActivitiesDto.vehicleQuarterlyReportId;
    const maintenanceActivitiesId = reportActivitiesDto.maintenanceActivitiesId;
    const quarterlyReport = await this.vehicleQuarterlyManagerReportRepository
      .createQueryBuilder('vehicleQuarterlyReport')
      .where('vehicleQuarterlyReport.id = :vehicleQuarterlyReportId', {
        vehicleQuarterlyReportId,
      })
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.vehicleStatus',
        'vehicleStatus',
      )
      // .leftJoinAndSelect('vehicleQuarterlyReport.acquisition', 'acquisition')
      .leftJoinAndSelect('vehicleQuarterlyReport.reportStatus', 'reportStatus')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.approvalLevel',
        'approvalLevel',
      )
      .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')
      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .getOne();

    if (!quarterlyReport) {
      return {
        message: 'quarterlyReport not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const maintenanceActivities =
      await this.maintenanceActivitiesManagerRepository
        .createQueryBuilder('maintenanceActivities')

        .where('maintenanceActivities.id = :maintenanceActivitiesId', {
          maintenanceActivitiesId,
        })
        .leftJoinAndSelect('maintenanceActivities.vehicle', 'vehicle')

        .leftJoinAndSelect(
          'maintenanceActivities.oilServiceCategory',
          'oilServiceCategory',
        )
        .leftJoinAndSelect(
          'maintenanceActivities.activityOnVehicle',
          'activityOnVehicle',
        )
        .getOne();
    if (!maintenanceActivities) {
      return {
        message: 'maintenanceActivities not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const newReportActivitiesDTO = new ReportActivities({
      ...reportActivitiesDto,
      vehicleQuarterlyReport: quarterlyReport,
      maintenanceActivities: maintenanceActivities,
      created_date: new Date(),

      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });
    return await this.reportActivitiesRepository.create(newReportActivitiesDTO);

    // return maintenanceActivities;
  }
  // update quatery report activity

  async updateActivitiesReport(
    id: string,
    reportActivitiesDto: ReportActivitiesDto,
  ) {
    const vehicleQuarterlyReportId =
      reportActivitiesDto.vehicleQuarterlyReportId;
    const maintenanceActivitiesId = reportActivitiesDto.maintenanceActivitiesId;
    const quarterlyReport = await this.vehicleQuarterlyManagerReportRepository
      .createQueryBuilder('vehicleQuarterlyReport')
      .where('vehicleQuarterlyReport.id = :vehicleQuarterlyReportId', {
        vehicleQuarterlyReportId,
      })
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.vehicleStatus',
        'vehicleStatus',
      )
      // .leftJoinAndSelect('vehicleQuarterlyReport.acquisition', 'acquisition')
      .leftJoinAndSelect('vehicleQuarterlyReport.reportStatus', 'reportStatus')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.approvalLevel',
        'approvalLevel',
      )
      .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')
      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .getOne();

    if (!quarterlyReport) {
      return {
        message: 'quarterlyReport not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const maintenanceActivities =
      await this.maintenanceActivitiesManagerRepository
        .createQueryBuilder('maintenanceActivities')

        .where('maintenanceActivities.id = :maintenanceActivitiesId', {
          maintenanceActivitiesId,
        })
        .leftJoinAndSelect('maintenanceActivities.vehicle', 'vehicle')

        .leftJoinAndSelect(
          'maintenanceActivities.oilServiceCategory',
          'oilServiceCategory',
        )
        .leftJoinAndSelect(
          'maintenanceActivities.activityOnVehicle',
          'activityOnVehicle',
        )
        .getOne();
    if (!maintenanceActivities) {
      return {
        message: 'maintenanceActivities not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const reportActivitiesData =
      await this.ReportActivitiesManagerRepository.createQueryBuilder(
        'reportActivities',
      )

        .where('reportActivities.id = :id', {
          id,
        })
        .leftJoinAndSelect(
          'reportActivities.vehicleQuarterlyReport',
          'vehicleQuarterlyReport',
        )
        .leftJoinAndSelect(
          'reportActivities.maintenanceActivities',
          'maintenanceActivities',
        )
        // .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        // .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        // .leftJoinAndSelect(
        //   'registeredVehicle.vehicleManufacture',
        //   'vehicleManufacture',
        // )
        // .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        // .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        // .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        // .leftJoinAndSelect(
        //   'registeredVehicle.registrationStatus',
        //   'registrationStatus',
        // )
        .getOne();
    if (!reportActivitiesData) {
      return {
        message: 'No Activity Report found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    await this.ReportActivitiesManagerRepository.createQueryBuilder() // Use query builder without alias
      .update('ReportActivities') // Provide the table name
      .set({
        vehicleQuarterlyReport: quarterlyReport,
        maintenanceActivities: maintenanceActivities,
        created_date: new Date(),

        // isVehicleActive: vehicleStatusData.isVehicleActive,
      })

      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();
    const newreportActivitiesData =
      await this.ReportActivitiesManagerRepository.createQueryBuilder(
        'reportActivities',
      )

        .where('reportActivities.id = :id', {
          id,
        })
        .leftJoinAndSelect(
          'reportActivities.vehicleQuarterlyReport',
          'vehicleQuarterlyReport',
        )
        .leftJoinAndSelect(
          'reportActivities.maintenanceActivities',
          'maintenanceActivities',
        )
        // .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        // .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        // .leftJoinAndSelect(
        //   'registeredVehicle.vehicleManufacture',
        //   'vehicleManufacture',
        // )
        // .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        // .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        // .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        // .leftJoinAndSelect(
        //   'registeredVehicle.registrationStatus',
        //   'registrationStatus',
        // )
        .getOne();

    return newreportActivitiesData;
  }

  // getting all quatery report activities by report Id
  async groupByVehicleQuarterlyReport(reportActivities) {
    const groupedReports = {};

    reportActivities.forEach((activity) => {
      const reportId = activity.vehicleQuarterlyReport.id;

      if (!groupedReports[reportId]) {
        groupedReports[reportId] = {
          ...activity.vehicleQuarterlyReport,
          reportActivities: [],
        };
      }

      groupedReports[reportId].reportActivities.push({
        id: activity.id,
        created_date: activity.created_date,
        maintenanceActivities: activity.maintenanceActivities,
      });
    });

    return Object.values(groupedReports);
  }

  async getQuateryReportActivityByRportID(reportId: string) {
    const reportActivities =
      await this.ReportActivitiesManagerRepository.createQueryBuilder(
        'reportActivities',
      )
        .leftJoinAndSelect(
          'reportActivities.vehicleQuarterlyReport',
          'vehicleQuarterlyReport',
        )
        .leftJoinAndSelect(
          'reportActivities.maintenanceActivities',
          'maintenanceActivities',
        )
        .where('reportActivities.vehicleQuarterlyReport = :reportId', {
          reportId,
        })
        .getMany();

    return this.groupByVehicleQuarterlyReport(reportActivities);
  }

  // updating Quatery report submition
  async updateQuateryReport(
    id: string,
    updateQuateryReportDto: UpdateQuateryReportDto,
  ) {
    let statusdata;
    let approvalData;
    let vehicleStatusData;
    let vehicleData2;
    const vehicleId = updateQuateryReportDto.vehicleId;
    const vehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    if (!vehicleData) {
      return {
        message: 'Vehicle not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const quarterlyReport = await this.vehicleQuarterlyManagerReportRepository
      .createQueryBuilder('vehicleQuarterlyReport')
      .where('vehicleQuarterlyReport.id = :id', {
        id,
      })
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.vehicleStatus',
        'vehicleStatus',
      )
      // .leftJoinAndSelect('vehicleQuarterlyReport.acquisition', 'acquisition')
      .leftJoinAndSelect('vehicleQuarterlyReport.reportStatus', 'reportStatus')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.approvalLevel',
        'approvalLevel',
      )
      .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')
      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .getOne();

    if (!quarterlyReport) {
      return {
        message: 'quarterlyReport not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }

    try {
      statusdata = await this.statusTypeRepository.findOne({
        name: 'PROGRESS',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      approvalData = await this.approvalLevelRepository.findOne({
        code: 'FMSE',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleStatusData = await this.vehicleStatusRepository.findOne({
        id: updateQuateryReportDto.vehicleStatusId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      vehicleData2 = await this.getVehicleById(vehicleId);
      if (!vehicleData) {
        throw new NotFoundException(`Vehicle with ID '${vehicleId}' not found`);
      }
      // return vehicleData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Handle NotFoundException
        return {
          message: 'Vehicle not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        // Handle other errors
        throw new Error(`Failed to fetch vehicle data: ${error.message}`);
      }
    }

    const newQuateryReport = await this.vehicleQuarterlyManagerReportRepository
      .createQueryBuilder() // Use query builder without alias
      .update('VehicleQuarterlyReport') // Provide the table name
      .set({
        vehicle: vehicleData2,
        description: updateQuateryReportDto.description,
        reported_date: new Date(),
        isVehicleActive: updateQuateryReportDto.isVehicleActive,
        reportStatus: statusdata,
        approvalLevel: approvalData,
        vehicleStatus: vehicleStatusData,

        // isVehicleActive: vehicleStatusData.isVehicleActive,
      })

      .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
      .execute();
    if (newQuateryReport) {
      // now updating vehicle status is required
      const quarterObject = await this.getQuarterDates(new Date());
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          isVehicleActive: updateQuateryReportDto.isVehicleActive,
          isQuarterlyReportSubmited: true,
          isoverdueQuarterlyReportSubmition: false,
          lastQuarterlyReportsubmittedDate: new Date(),
          startNextQuarterlyReportsubmittedDate:
            quarterObject.nextQuarter.startDate,
          endNextQuarterlyReportsubmittedDate:
            quarterObject.nextQuarter.endDate,
          vehicleStatus: vehicleStatusData,
          // isVehicleActive: vehicleStatusData.isVehicleActive,
        })

        .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }
    const updatedquarterlyReport =
      await this.vehicleQuarterlyManagerReportRepository
        .createQueryBuilder('vehicleQuarterlyReport')
        .where('vehicleQuarterlyReport.id = :id', {
          id,
        })

        .leftJoinAndSelect(
          'vehicleQuarterlyReport.vehicleStatus',
          'vehicleStatus',
        )
        // .leftJoinAndSelect('vehicleQuarterlyReport.acquisition', 'acquisition')
        .leftJoinAndSelect(
          'vehicleQuarterlyReport.reportStatus',
          'reportStatus',
        )
        .leftJoinAndSelect(
          'vehicleQuarterlyReport.approvalLevel',
          'approvalLevel',
        )
        .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')
        // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
        .getOne();

    return updatedquarterlyReport;
  }
  // getting  all QuateryReport

  async getAllQuateryReports() {
    return await this.vehicleQuarterlyManagerReportRepository
      .createQueryBuilder('vehicleQuarterlyReport')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.vehicleStatus',
        'vehicleStatus',
      )
      // .leftJoinAndSelect('vehicleQuarterlyReport.acquisition', 'acquisition')
      .leftJoinAndSelect('vehicleQuarterlyReport.reportStatus', 'reportStatus')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.approvalLevel',
        'approvalLevel',
      )
      .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')
      // .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')

      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.reportActivities',
        'reportActivities',
      )
      .leftJoinAndSelect(
        'reportActivities.maintenanceActivities',
        'maintenanceActivities',
      )
      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .getMany();
  }

  // getting  QuateryReport by vehicle id
  async getQuateryReportsByVehicleID(vehicleId: string) {
    return await this.vehicleQuarterlyManagerReportRepository
      .createQueryBuilder('vehicleQuarterlyReport')
      .where('vehicleQuarterlyReport.vehicle = :vehicleId', {
        vehicleId,
      })
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.vehicleStatus',
        'vehicleStatus',
      )
      // .leftJoinAndSelect('vehicleQuarterlyReport.acquisition', 'acquisition')
      .leftJoinAndSelect('vehicleQuarterlyReport.reportStatus', 'reportStatus')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.approvalLevel',
        'approvalLevel',
      )
      .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')

      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.reportActivities',
        'reportActivities',
      )
      .leftJoinAndSelect(
        'reportActivities.maintenanceActivities',
        'maintenanceActivities',
      )
      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .getMany();
  }
  // geting vehicle report by report id
  async getQuateryReportsByReportID(id: string) {
    return await this.vehicleQuarterlyManagerReportRepository
      .createQueryBuilder('vehicleQuarterlyReport')
      .where('vehicleQuarterlyReport.id = :id', {
        id,
      })
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.vehicleStatus',
        'vehicleStatus',
      )
      // .leftJoinAndSelect('vehicleQuarterlyReport.acquisition', 'acquisition')
      .leftJoinAndSelect('vehicleQuarterlyReport.reportStatus', 'reportStatus')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.approvalLevel',
        'approvalLevel',
      )
      .leftJoinAndSelect('vehicleQuarterlyReport.vehicle', 'vehicle')

      // .leftJoinAndSelect('acquisition.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect(
        'vehicleQuarterlyReport.reportActivities',
        'reportActivities',
      )
      .leftJoinAndSelect(
        'reportActivities.maintenanceActivities',
        'maintenanceActivities',
      )

      .getOne();
  }
  // =============== IMPLEMENTING Quatery Report submittion =============================
  async checkQuarterlyReportDates() {
    const vehicles = await this.registeredVehicleRepository.findAll();
    // console.log(vehicles);
    let notifiedinstitutionQuatery;

    for (const vehicle of vehicles) {
      const vehicleId = vehicle.id;
      notifiedinstitutionQuatery = vehicle.reportingInstitutionId;
      if (!notifiedinstitutionQuatery) {
        notifiedinstitutionQuatery = vehicle.beneficiaryAgencyId;
      }
      if (
        !vehicle.lastQuarterlyReportsubmittedDate ||
        !vehicle.startNextQuarterlyReportsubmittedDate ||
        !vehicle.endNextQuarterlyReportsubmittedDate
      ) {
        // Skip this vehicle if any of the required dates are null
        console.log(
          `Skipping vehicle ID: ${vehicle.id} due to missing of repoting date dates.`,
        );
        continue;
      } else {
        if (
          !vehicle.beneficiaryAgency &&
          !vehicle.beneficiaryAgency &&
          !vehicle.reportingInstitution &&
          !vehicle.reportingInstitution
        ) {
          console.log(
            `Skipping vehicle ID: ${vehicle.id} due to missing of missing repoting institution or beneficiary angency .`,
          );
          // here to implement sending email logic
        } else {
          // here we have to check if current data is within endNextQuarterlyReportsubmittedDate and startNextQuarterlyReportsubmittedDate or is greater than endNextQuarterlyReportsubmittedDate
          const currentDate = new Date();

          const startNextReportDate = new Date(
            vehicle.startNextQuarterlyReportsubmittedDate,
          );
          const endNextReportDate = new Date(
            vehicle.endNextQuarterlyReportsubmittedDate,
          );
          if (
            currentDate >= startNextReportDate &&
            currentDate <= endNextReportDate
          ) {
            // update vehicle reporting vehicle statustus

            const updateVehicle =
              await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
                .update('RegisteredVehicle') // Provide the table name
                .set({
                  isQuarterlyReportSubmited: false,
                  isoverdueQuarterlyReportSubmition: false,
                })
                .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
                .execute();

            if (updateVehicle) {
              // Implement sending notification logic here
              // this.sendNotification(vehicle);
              // console.log('GGHGDHSGHSGHSGHDGHSDGHDGHGHDGH HDHGGHDGHDDGHDGH');
              const userToSendEmail =
                await this.aquistionApprovalService.getDetailByRole(
                  notifiedinstitutionQuatery,

                  'Institutional logistics', // role name sending email to instution logistics
                  // 'admin',
                );
              console.log('this user to send email');
              // console.log(userToSendEmail);

              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail =
                await this.aquistionApprovalService.sendReminderQuarterlyEmail(
                  email,
                  name,
                );
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          } else if (currentDate > endNextReportDate) {
            console.log(
              `Sending overdue notification for vehicle ID: ${vehicle.id} - Current date is past the reporting period.`,
            );
            const updateVehicle2 =
              await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
                .update('RegisteredVehicle') // Provide the table name
                .set({
                  isQuarterlyReportSubmited: false,
                  isoverdueQuarterlyReportSubmition: true,
                })
                .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
                .execute();
            if (updateVehicle2) {
              const userToSendEmail =
                await this.aquistionApprovalService.getDetailByRole(
                  notifiedinstitutionQuatery,

                  'Institutional logistics', // role name sending email to instution logistics
                  // 'Institutional logistics',
                );

              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail =
                await this.aquistionApprovalService.sendReminderOverdueQuarterlyEmail(
                  email,
                  name,
                );

              if (sentEmail) {
                console.log('========= email sent successfully======');
              }

              // sending email to CBM
              const userToSendEmail2 =
                await this.aquistionApprovalService.getDetailByRole(
                  notifiedinstitutionQuatery,

                  'Institutional CBM', // role name sending email to instution logistics
                  // 'Institutional logistics',
                );

              const email2 = userToSendEmail2[0].email;
              console.log(email);
              const name2 = userToSendEmail2[0].firstName;
              console.log(name);
              const sentEmail2 =
                await this.aquistionApprovalService.sendReminderOverdueQuarterlyEmail(
                  email2,
                  name2,
                );

              if (sentEmail2) {
                console.log('========= email sent successfully======');
              }
            }
          }
        }
      }
    }
  }

  async nitifiyInstitionToReportDates() {
    const vehicles = await this.registeredVehicleRepository.findAll();
    // console.log(vehicles);
    let notifiedinstitutionQuatery;

    for (const vehicle of vehicles) {
      // const vehicleId = vehicle.id;
      notifiedinstitutionQuatery = vehicle.reportingInstitutionId;
      if (!notifiedinstitutionQuatery) {
        notifiedinstitutionQuatery = vehicle.beneficiaryAgencyId;
      }
      if (
        !vehicle.lastQuarterlyReportsubmittedDate ||
        !vehicle.startNextQuarterlyReportsubmittedDate ||
        !vehicle.endNextQuarterlyReportsubmittedDate
      ) {
        // Skip this vehicle if any of the required dates are null
        console.log(
          `Skipping vehicle ID: ${vehicle.id} due to missing of repoting date dates.`,
        );
        continue;
      } else {
        if (
          !vehicle.beneficiaryAgency &&
          !vehicle.beneficiaryAgency &&
          !vehicle.reportingInstitution &&
          !vehicle.reportingInstitution
        ) {
          console.log(
            `Skipping vehicle ID: ${vehicle.id} due to missing of missing repoting institution or beneficiary angency .`,
          );
          // here to implement sending email logic
        } else {
          // here we have to check if current data is within endNextQuarterlyReportsubmittedDate and startNextQuarterlyReportsubmittedDate or is greater than endNextQuarterlyReportsubmittedDate
          const QuarterlyReportSubmited = vehicle.isQuarterlyReportSubmited;
          const overdueQuarterlyReportSubmition =
            vehicle.isoverdueQuarterlyReportSubmition;
          if (!QuarterlyReportSubmited) {
            // Implement sending notification logic here
            // this.sendNotification(vehicle);
            // console.log('GGHGDHSGHSGHSGHDGHSDGHDGHGHDGH HDHGGHDGHDDGHDGH');
            const userToSendEmail =
              await this.aquistionApprovalService.getDetailByRole(
                notifiedinstitutionQuatery,

                'Institutional logistics', // role name sending email to instution logistics
                // 'admin',
              );
            console.log('this user to send email');
            // console.log(userToSendEmail);

            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail =
              await this.aquistionApprovalService.sendReminderQuarterlyEmail(
                email,
                name,
              );
            if (sentEmail) {
              console.log('========= email sent successfully======');
            }
          } else if (overdueQuarterlyReportSubmition) {
            console.log(
              `Sending overdue notification for vehicle ID: ${vehicle.id} - Current date is past the reporting period.`,
            );

            const userToSendEmail =
              await this.aquistionApprovalService.getDetailByRole(
                notifiedinstitutionQuatery,

                'Institutional logistics', // role name sending email to instution logistics
                // 'Institutional logistics',
              );

            const email = userToSendEmail[0].email;
            console.log(email);
            const name = userToSendEmail[0].firstName;
            console.log(name);
            const sentEmail =
              await this.aquistionApprovalService.sendReminderOverdueQuarterlyEmail(
                email,
                name,
              );

            if (sentEmail) {
              console.log('========= email sent successfully======');
            }

            // sending email to CBM
            const userToSendEmail2 =
              await this.aquistionApprovalService.getDetailByRole(
                notifiedinstitutionQuatery,

                'Institutional CBM', // role name sending email to instution logistics
                // 'Institutional logistics',
              );

            const email2 = userToSendEmail2[0].email;
            console.log(email);
            const name2 = userToSendEmail2[0].firstName;
            console.log(name);
            const sentEmail2 =
              await this.aquistionApprovalService.sendReminderOverdueQuarterlyEmail(
                email2,
                name2,
              );

            if (sentEmail2) {
              console.log('========= email sent successfully======');
            }
          }
        }
      }
    }
  }

  // =======================   requesting project extension logic =======
  async submitProjectExtensionRequest(
    submitProjectExtensionDto: SubmitProjectExtensionDto,
  ) {
    let statusdata;
    let approvalData;
    // let vehicleStatusData;
    let vehicleData2;
    const vehicleId = submitProjectExtensionDto.vehicleId;
    const vehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    if (!vehicleData) {
      return {
        message: 'Vehicle not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    if (vehicleData.ownershipType.code !== 'GP') {
      return {
        message: 'This is not Project Vehicle',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    // console.log(vehicleData.projectExtensionDate);

    if (vehicleData.projectExtensionDate == null) {
      return {
        message: 'Project end date is required',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }

    try {
      statusdata = await this.statusTypeRepository.findOne({
        name: 'PENDING',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      approvalData = await this.approvalLevelRepository.findOne({
        code: 'ICBM2',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      vehicleData2 = await this.getVehicleById(vehicleId);
      if (!vehicleData) {
        throw new NotFoundException(`Vehicle with ID '${vehicleId}' not found`);
      }
      // return vehicleData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Handle NotFoundException
        return {
          message: 'Vehicle not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        // Handle other errors
        throw new Error(`Failed to fetch vehicle data: ${error.message}`);
      }
    }
    const projectExtensionReportDTO = new ProjectExtension({
      ...submitProjectExtensionDto,
      vehicle: vehicleData2,
      description: submitProjectExtensionDto.description,
      newProjectEndDate: submitProjectExtensionDto.newProjectEndDate,
      requested_date: new Date(),
      // isVehicleActive: submitQuateryReportDto.isVehicleActive,
      requestStatus: statusdata,
      approvalLevel: approvalData,
      // vehicleStatus: vehicleStatusData,
      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });

    const projectExtension = await this.projectExtensionRepository.create(
      projectExtensionReportDTO,
    );
    if (projectExtension) {
      // now updating vehicle status is required
      // const quarterObject = await this.getQuarterDates(new Date());
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          // isVehicleActive: submitQuateryReportDto.isVehicleActive,
          isProjectOnGoing: true,
          isTimeToReturnProjectVehicle: false,
          projectExtensionDate: submitProjectExtensionDto.newProjectEndDate,
        })

        .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }

    return projectExtension;
    // return submitProjectExtensionDto;
  }

  // ======================== project extension approval ========================
  async getAllProjectExtension() {
    return await this.projectExtensionManagerReportRepository
      .createQueryBuilder('ProjectExtension')
      .leftJoinAndSelect('ProjectExtension.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('ProjectExtension.requestStatus', 'requestStatus')
      .leftJoinAndSelect('ProjectExtension.vehicle', 'vehicle')
      .getMany();
  }

  async getProjectExtensionByVehicleID(vehicleId: string) {
    return await this.projectExtensionManagerReportRepository
      .createQueryBuilder('ProjectExtension')
      .where('ProjectExtension.vehicle = :vehicleId', {
        vehicleId,
      })
      .leftJoinAndSelect('ProjectExtension.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('ProjectExtension.requestStatus', 'requestStatus')
      .leftJoinAndSelect('ProjectExtension.vehicle', 'vehicle')
      .getMany();
  }

  async getProjectExtensionByReportID(id: string) {
    return await this.projectExtensionManagerReportRepository
      .createQueryBuilder('ProjectExtension')
      .where('ProjectExtension.id = :id', {
        id,
      })

      .leftJoinAndSelect('ProjectExtension.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('ProjectExtension.requestStatus', 'requestStatus')
      .leftJoinAndSelect('ProjectExtension.vehicle', 'vehicle')
      .getOne();
  }

  // updating vehical extensitnon
  async updateProjectExtension(
    id: string,
    submitProjectExtensionDto: SubmitProjectExtensionDto,
  ) {
    let statusdata;
    let approvalData;
    // let vehicleStatusData;
    let vehicleData2;
    const vehicleId = submitProjectExtensionDto.vehicleId;

    const vehicleData =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )

        .where('registeredVehicle.id = :vehicleId', {
          vehicleId,
        })
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .getOne();

    if (!vehicleData) {
      return {
        message: 'Vehicle not found',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    // check if project extension exist
    const peojectExtesionData =
      await this.projectExtensionManagerReportRepository
        .createQueryBuilder('ProjectExtension')
        .where('ProjectExtension.id = :id', {
          id,
        })

        .leftJoinAndSelect('ProjectExtension.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect('ProjectExtension.vehicle', 'vehicle')
        .getOne();

    if (!peojectExtesionData) {
      return {
        message: 'project extension data not find',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    if (vehicleData.ownershipType.code !== 'GP') {
      return {
        message: 'This is not Project Vehicle',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    if (vehicleData.projectExtensionDate == null) {
      return {
        message: 'Project end date is required',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    try {
      statusdata = await this.statusTypeRepository.findOne({
        name: 'PROGRESS',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      approvalData = await this.approvalLevelRepository.findOne({
        code: 'FMSE',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      vehicleData2 = await this.getVehicleById(vehicleId);
      if (!vehicleData) {
        throw new NotFoundException(`Vehicle with ID '${vehicleId}' not found`);
      }
      // return vehicleData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Handle NotFoundException
        return {
          message: 'Vehicle not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        // Handle other errors
        throw new Error(`Failed to fetch vehicle data: ${error.message}`);
      }
    }

    // updating project extension
    const projectExtension = await this.projectExtensionManagerReportRepository
      .createQueryBuilder() // Use query builder without alias
      .update('ProjectExtension') // Provide the table name
      .set({
        // isVehicleActive: submitQuateryReportDto.isVehicleActive,
        vehicle: vehicleData2,
        description: submitProjectExtensionDto.description,
        newProjectEndDate: submitProjectExtensionDto.newProjectEndDate,
        requested_date: new Date(),
        // isVehicleActive: submitQuateryReportDto.isVehicleActive,
        requestStatus: statusdata,
        approvalLevel: approvalData,
        isProjectOnGoing: true,
      })

      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();
    // return 'this is logistiocds';
    if (projectExtension) {
      // now updating vehicle status is required
      // const quarterObject = await this.getQuarterDates(new Date());
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          // isVehicleActive: submitQuateryReportDto.isVehicleActive,
          isProjectOnGoing: true,
          isTimeToReturnProjectVehicle: false,
          projectExtensionDate: submitProjectExtensionDto.newProjectEndDate,
        })

        .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }

    const projectExtensiondata =
      await this.projectExtensionManagerReportRepository
        .createQueryBuilder('ProjectExtension')
        .where('ProjectExtension.id = :id', {
          id,
        })

        .leftJoinAndSelect('ProjectExtension.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect('ProjectExtension.vehicle', 'vehicle')
        .getOne();

    return projectExtensiondata;
  }

  // checking project end date
  async checkProjectEndDate() {
    const vehicles =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder(
        'registeredVehicle',
      )
        .leftJoinAndSelect(
          'registeredVehicle.vehicleRegRequest',
          'vehicleRegRequest',
        )
        .leftJoinAndSelect(
          'vehicleRegRequest.VehicleAcquisition',
          'VehicleAcquisition',
        )
        .leftJoinAndSelect('registeredVehicle.ownershipType', 'ownershipType')
        .where('ownershipType.code = :code', { code: 'GP' })
        .andWhere(
          '(registeredVehicle.reportingInstitutionId IS NOT NULL OR registeredVehicle.beneficiaryAgencyId IS NOT NULL)',
        )
        .andWhere('registeredVehicle.projectExtensionDate IS NOT NULL')
        .leftJoinAndSelect('registeredVehicle.vehicleType', 'vehicleType')
        .leftJoinAndSelect(
          'registeredVehicle.vehicleManufacture',
          'vehicleManufacture',
        )
        .leftJoinAndSelect('registeredVehicle.vehicleModel', 'vehicleModel')
        .leftJoinAndSelect('registeredVehicle.vehicleStatus', 'vehicleStatus')
        .leftJoinAndSelect('registeredVehicle.approvalLevel', 'approvalLevel')
        .leftJoinAndSelect(
          'registeredVehicle.registrationStatus',
          'registrationStatus',
        )
        .where('registrationStatus.name = :name', { name: 'APPROVED' })
        .getMany();
    let notifiedinstitution;
    const currentDate = new Date();
    if (vehicles) {
      for (const vehicle of vehicles) {
        const vehicleId = vehicle.id;
        notifiedinstitution = vehicle.reportingInstitutionId;
        if (!notifiedinstitution) {
          notifiedinstitution = vehicle.beneficiaryAgencyId;
        }
        if (vehicle.projectExtensionDate) {
          const projectExtensionDate = new Date(vehicle.projectExtensionDate);
          const timeDifference =
            projectExtensionDate.getTime() - currentDate.getTime();
          const daysDifference = timeDifference / (1000 * 3600 * 24);

          if (daysDifference <= 14 && daysDifference > 0) {
            console.log(
              `Time to return vehicle with ID ${vehicle.id} is reached.`,
            );
            // update vehicle
            const updateVehicle1 =
              await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
                .update('RegisteredVehicle') // Provide the table name
                .set({
                  // isVehicleActive: submitQuateryReportDto.isVehicleActive,
                  isProjectOnGoing: true,
                  isTimeToReturnProjectVehicle: true,
                })

                .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
                .execute();
            if (updateVehicle1) {
              // sending email for notification
              const userToSendEmail =
                await this.aquistionApprovalService.getDetailByRole(
                  notifiedinstitution,
                  'Institutional logistics', // role name sending email to instution logistics
                  // 'admin',
                );
              console.log('this user to send email');
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail =
                await this.aquistionApprovalService.sendReminderReturnProjectVehicleEmail(
                  email,
                  name,
                );
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
            }
          } else if (daysDifference <= 0) {
            console.log(
              `Time to return vehicle with ID ${vehicle.id} is overdue.`,
            );
            const updateVehicle2 =
              await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
                .update('RegisteredVehicle') // Provide the table name
                .set({
                  // isVehicleActive: submitQuateryReportDto.isVehicleActive,
                  isProjectOnGoing: false,
                  isTimeToReturnProjectVehicle: true,
                })

                .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
                .execute();
            if (updateVehicle2) {
              // sending email for notification
              const userToSendEmail =
                await this.aquistionApprovalService.getDetailByRole(
                  notifiedinstitution,
                  'Institutional logistics', // role name sending email to instution logistics
                  // 'Institutional logistics',
                );
              const email = userToSendEmail[0].email;
              console.log(email);
              const name = userToSendEmail[0].firstName;
              console.log(name);
              const sentEmail =
                await this.aquistionApprovalService.sendReminderReturnProjectOverdueVehicleEmail(
                  email,
                  name,
                );
              if (sentEmail) {
                console.log('========= email sent successfully======');
              }
              const userToSendEmail2 =
                await this.aquistionApprovalService.getDetailByRole(
                  notifiedinstitution,
                  'Institutional CBM', // role name sending email to instution logistics
                  // 'Institutional logistics',
                );
              const email2 = userToSendEmail2[0].email;
              console.log(email);
              const name2 = userToSendEmail2[0].firstName;
              console.log(name);
              const sentEmail2 =
                await this.aquistionApprovalService.sendReminderReturnProjectOverdueVehicleEmail(
                  email2,
                  name2,
                );
              if (sentEmail2) {
                console.log('========= email sent successfully======');
              }
            }
          }
        }
      }
    }

    return vehicles;
  }
  // Acquistion vehicle relavance logic
  async createCostBenefit(costBenefitDto: CostBenefitDto) {
    let dataAcquisition;
    let dataregVehicale;

    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: costBenefitDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: costBenefitDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const newCreateCostBenefitDto = new CostBenefit({
      ...costBenefitDto,
      VehicleAcquisition: dataAcquisition, // Assign the StatusType object
      RequestedVehicle: dataregVehicale, // Assign the ApprovalLevel object
      // Assign the RequestType object
    });

    return await this.costBenefitRepository.create(newCreateCostBenefitDto);
  }

  async findCostBenefitByAcquisitionById(id: string) {
    return await this.costBenefitManagerRepository
      .createQueryBuilder('costBenefit')
      .leftJoinAndSelect('costBenefit.VehicleAcquisition', 'VehicleAcquisition')
      .where('VehicleAcquisition.id = :id', {
        id,
      })
      .leftJoinAndSelect('costBenefit.RequestedVehicle', 'RequestedVehicle')
      .getMany();
  }
  async findCostBenefitByReqVehicleById(id: string) {
    return await this.costBenefitManagerRepository
      .createQueryBuilder('costBenefit')
      .leftJoinAndSelect('costBenefit.RequestedVehicle', 'RequestedVehicle')
      .where('RequestedVehicle.id = :id', {
        id,
      })
      .leftJoinAndSelect('costBenefit.VehicleAcquisition', 'VehicleAcquisition')
      .getMany();
  }

  // update cost Benefit by Id
  async updateCostBenefit(id: string, costBenefitDto: CostBenefitDto) {
    const costBenefit = await this.costBenefitRepository.findOne({ id });
    if (!costBenefit) {
      throw new NotFoundException('Cost Benefit not found');
    }
    let dataAcquisition;
    let dataregVehicale;

    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: costBenefitDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: costBenefitDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const updatedCostBenefit = await this.costBenefitManagerRepository
      .createQueryBuilder() // Use query builder without alias
      .update('CostBenefit') // Provide the table name
      .set({
        VehicleAcquisition: dataAcquisition, // Assign the StatusType object
        RequestedVehicle: dataregVehicale, // Assign the ApprovalLevel object
        acquisitionCost: costBenefitDto.acquisitionCost,
        maintenanceCost: costBenefitDto.maintenanceCost,
        fuelCost: costBenefitDto.fuelCost,
        oilRefillingCost: costBenefitDto.oilRefillingCost,
        insuranceCost: costBenefitDto.insuranceCost,
        driveCost: costBenefitDto.driveCost,
        depreciationCost: costBenefitDto.depreciationCost,
        // Assign the RequestType object
      })
      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();
    // return 'this is logistiocds';

    if (!updatedCostBenefit) {
      return {
        message: 'Cost benefit not updated well ',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    return await this.costBenefitManagerRepository
      .createQueryBuilder('costBenefit')
      .where('costBenefit.id = :id', {
        id,
      })
      .leftJoinAndSelect('costBenefit.RequestedVehicle', 'RequestedVehicle')

      .leftJoinAndSelect('costBenefit.VehicleAcquisition', 'VehicleAcquisition')
      .getMany();
  }

  /// staff relavance logic
  async createStaffRelevance(staffRelevanceDto: StaffRelevanceDto) {
    let dataAcquisition;
    let dataregVehicale;

    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: staffRelevanceDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: staffRelevanceDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const staffRelavanceDto = new StaffRelavance({
      ...staffRelevanceDto,
      VehicleAcquisition: dataAcquisition, // Assign the StatusType object
      RequestedVehicle: dataregVehicale, // Assign the ApprovalLevel object
      // Assign the RequestType object
    });
    return await this.staffRelavanceRepository.create(staffRelavanceDto);
  }

  // staff relavance by acqisition id
  async findStaffRelavcanceByAcquisitionById(id: string) {
    return await this.StaffRelavanceManagerRepository.createQueryBuilder(
      'staffRelavance',
    )
      .leftJoinAndSelect(
        'staffRelavance.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .where('VehicleAcquisition.id = :id', {
        id,
      })
      .leftJoinAndSelect('staffRelavance.RequestedVehicle', 'RequestedVehicle')
      .getMany();
  }

  // getting staff relevance by reg request ID
  async findStaffRelevanceByReqVehicleById(id: string) {
    return await this.StaffRelavanceManagerRepository.createQueryBuilder(
      'staffRelavance',
    )
      .leftJoinAndSelect('staffRelavance.RequestedVehicle', 'RequestedVehicle')
      .where('RequestedVehicle.id = :id', {
        id,
      })
      .leftJoinAndSelect(
        'staffRelavance.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .getMany();
  }
  // updating  staff relavance
  async updateStaffRelevance(id: string, staffRelevanceDto: StaffRelevanceDto) {
    const staffBenefit = await this.staffRelavanceRepository.findOne({ id });
    if (!staffBenefit) {
      throw new NotFoundException('Cost Benefit not found');
    }
    let dataAcquisition;
    let dataregVehicale;

    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: staffRelevanceDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: staffRelevanceDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const updatedStaffRelevance =
      await this.StaffRelavanceManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('StaffRelavance') // Provide the table name
        .set({
          VehicleAcquisition: dataAcquisition, // Assign the StatusType object
          RequestedVehicle: dataregVehicale, // Assign the ApprovalLevel object
          staffPosition: staffRelevanceDto.staffPosition,
          staffLevel: staffRelevanceDto.staffLevel,
          workLocation: staffRelevanceDto.dailyWorkFrequency,
          officeLocation: staffRelevanceDto.officeLocation,
          dailyWorkFrequency: staffRelevanceDto.dailyWorkFrequency,
          OthersDescription: staffRelevanceDto.OthersDescription,
          staffCategory: staffRelevanceDto.staffCategory,
          staffsDepartments: staffRelevanceDto.staffsDepartments,
          numberOfStaff: staffRelevanceDto.numberOfStaff,
          numberOfMonthPerYear: staffRelevanceDto.numberOfMonthPerYear,

          // Assign the RequestType object
        })
        .where('id = :id', { id }) // Assuming id is the primary key column
        .execute();
    // return 'this is logistiocds';

    if (!updatedStaffRelevance) {
      return {
        message: 'Staff Relevance not updated well ',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    return await this.StaffRelavanceManagerRepository.createQueryBuilder(
      'staffRelevance',
    )
      .where('staffRelevance.id = :id', {
        id,
      })
      .leftJoinAndSelect('staffRelevance.RequestedVehicle', 'RequestedVehicle')

      .leftJoinAndSelect(
        'staffRelevance.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .getMany();
  }
  // create drive details
  async createDriveDetails(driveDetailsDto: DriverDetailsDto) {
    let dataAcquisition;
    let dataregVehicale;

    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: driveDetailsDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: driveDetailsDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const driverDetailsDto = new DriverDetails({
      ...driveDetailsDto,
      VehicleAcquisition: dataAcquisition, // Assign the StatusType object
      RequestedVehicle: dataregVehicale, // Assign the ApprovalLevel object
      // Assign the RequestType object
    });
    return await this.driverDetailsRepository.create(driverDetailsDto);
  }
  // implement findDriveDetailsByVehicleId
  async findDriveDetailsByVehicleId(id: string) {
    return await this.driverDetailsManagerRepository
      .createQueryBuilder('driverDetails')
      .leftJoinAndSelect(
        'driverDetails.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .where('VehicleAcquisition.id = :id', {
        id,
      })
      .leftJoinAndSelect('driverDetails.RequestedVehicle', 'RequestedVehicle')
      .getMany();
  }
  // implement findDriveDetailsByReqVehicleId
  // getting staff relevance by reg request ID
  async findDriverDetailsByReqVehicleById(id: string) {
    return await this.driverDetailsManagerRepository
      .createQueryBuilder('driverDetails')
      .leftJoinAndSelect('driverDetails.RequestedVehicle', 'RequestedVehicle')
      .where('RequestedVehicle.id = :id', {
        id,
      })
      .leftJoinAndSelect(
        'driverDetails.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .getMany();
  }
  // implement  updateDriverDetails
  // updating  staff relavance
  async updateDriverDetails(id: string, driverDetailsDto: DriverDetailsDto) {
    const staffDriveDetails = await this.driverDetailsRepository.findOne({
      id,
    });
    if (!staffDriveDetails) {
      throw new NotFoundException('Cost Benefit not found');
    }
    let dataAcquisition;
    let dataregVehicale;

    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: driverDetailsDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: driverDetailsDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const updatedDriverDetails = await this.driverDetailsManagerRepository
      .createQueryBuilder() // Use query builder without alias
      .update('driverDetails') // Provide the table name
      .set({
        VehicleAcquisition: dataAcquisition, // Assign the StatusType object
        RequestedVehicle: dataregVehicale, // Assign the ApprovalLevel object
        isDriveAvailable: driverDetailsDto.isDriveAvailable,
        palanToRecruitDrive: driverDetailsDto.palanToRecruitDrive,
        driverQualification: driverDetailsDto.driverQualification,
        numberOfDriver: driverDetailsDto.numberOfDriver,
        // Assign the RequestType object
      })
      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();
    // return 'this is logistiocds';

    if (!updatedDriverDetails) {
      return {
        message: 'DriverDetails not updated well ',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    return await this.driverDetailsManagerRepository
      .createQueryBuilder('driverDetails')
      .where('driverDetails.id = :id', {
        id,
      })
      .leftJoinAndSelect('driverDetails.RequestedVehicle', 'RequestedVehicle')

      .leftJoinAndSelect(
        'driverDetails.VehicleAcquisition',
        'VehicleAcquisition',
      )
      .getMany();
  }
  // create hiring details
  async createHiringCost(hiringCostDto: HiringCostDto) {
    let dataAcquisition;
    let dataregVehicale;
    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: hiringCostDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: hiringCostDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const hiringCostNewDto = new HiringCost({
      ...hiringCostDto,
      VehicleAcquisition: dataAcquisition, // Assign the StatusType object
      RequestedVehicle: dataregVehicale,
      // Assign the ApprovalLevel object
      // Assign the RequestType object
    });
    return await this.hiringCostRepository.create(hiringCostNewDto);
  }
  async findHiringDriverByVehicleId(id: string) {
    return await this.HiringCostManagerRepository.createQueryBuilder(
      'hiringCost',
    )
      .leftJoinAndSelect('hiringCost.VehicleAcquisition', 'VehicleAcquisition')
      .where('VehicleAcquisition.id = :id', {
        id,
      })
      .leftJoinAndSelect('hiringCost.RequestedVehicle', 'RequestedVehicle')
      .getMany();
  }

  // getting hiring cost by vehicle reg request
  async findHiringCostByReqVehicleById(id: string) {
    return await this.HiringCostManagerRepository.createQueryBuilder(
      'hiringCost',
    )
      .leftJoinAndSelect('hiringCost.RequestedVehicle', 'RequestedVehicle')
      .where('RequestedVehicle.id = :id', {
        id,
      })
      .leftJoinAndSelect('hiringCost.VehicleAcquisition', 'VehicleAcquisition')
      .getMany();
  }

  async updateHiringCost(id: string, hiringCostDto: HiringCostDto) {
    const hiringCost = await this.hiringCostRepository.findOne({
      id,
    });
    if (!hiringCost) {
      throw new NotFoundException('Hiring Cost not found');
    }
    let dataAcquisition;
    let dataregVehicale;

    try {
      dataAcquisition = await this.vehicleAcquisitionRepository.findOne({
        id: hiringCostDto.VehicleAcquisitionID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'acquisition data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for acquisition .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    // checking

    try {
      dataregVehicale = await this.requestedVehicleRepository.findOne({
        id: hiringCostDto.RequestedVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'requested vehicle  data not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for requested vehicle  .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const updatedHiringCost =
      await this.HiringCostManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('hiringCost')
        // Provide the table name
        .set({
          VehicleAcquisition: dataAcquisition, // Assign the StatusType object
          RequestedVehicle: dataregVehicale, // Assign the ApprovalLevel object
          hiringCost: hiringCost.hiringCost,
          // Assign the RequestType object
        })
        .where('id = :id', { id }) // Assuming id is the primary key column
        .execute();
    // return 'this is logistiocds';

    if (!updatedHiringCost) {
      return {
        message: 'hiring Cost not updated well ',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    return await this.HiringCostManagerRepository.createQueryBuilder(
      'hiringCost',
    )
      .where('hiringCost.id = :id', {
        id,
      })
      .leftJoinAndSelect('hiringCost.RequestedVehicle', 'RequestedVehicle')

      .leftJoinAndSelect('hiringCost.VehicleAcquisition', 'VehicleAcquisition')
      .getMany();
  }
  async isDateWithinRange(
    specificDateInput: Date | string,
    startDateInput: string,
    endDateInput: string,
  ) {
    // Convert the dates to Date objects
    const specificDate = new Date(specificDateInput);
    const startDate = new Date(startDateInput);
    const endDate = new Date(endDateInput);

    // Normalize the time for accurate date comparison
    specificDate.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(0, 0, 0, 0);

    // Check if the specific date is within the range
    return specificDate >= startDate && specificDate <= endDate;
  }

  async createActivityPerformance(maintenanceActivitiesDTO: any) {
    let cost: number | null = null;
    let mileage: number | null = null;
    let activityDate: Date | null = null;
    let vehicleGoodCondtionID;
    // handuling date of activities
    // handlong other missing values here
    if (!('activityOnVehicleID' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.activityOnVehicleID = null; //
    }
    if (!('oilServiceCategoryID' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceCategoryID = null; //
    }
    if (!('fuelQuantity' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelQuantity = null; //
    }
    if (!('fuelQuantity' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelQuantity = null; //
    }
    if (!('fuelMileage' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelMileage = null; //
    }
    if (!('oilServiceMileage' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceMileage = null; //
    }

    if (!('driveName' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.driveName = null; //
    }
    if (!('isVehicleActive' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.isVehicleActive = true; //
    }
    try {
      vehicleGoodCondtionID = await this.vehicleStatusRepository.findOne({
        name: 'GOOD CONDITION',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with MOS as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    if (!('vehicleStatusId' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.vehicleStatusId = vehicleGoodCondtionID.id; //
    }
    if (!('maintenanceActivityObeservation' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.maintenanceActivityObeservation = null; //
    }
    if (!('insurancePeriod' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.insurancePeriod = null; //
    }
    if (!('fuelCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelCost = null; //
    }
    if (!('driveCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.driveCost = null; //
    }
    if (!('fuelConsumptionDate' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelConsumptionDate = null; //
    }
    if (!('maintenanceCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.maintenanceCost = null; //
    }
    if (!('maintenanceActivityDate' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.maintenanceActivityDate = null; //
    }

    if (!('oilServiceCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceCost = null; //
    }
    if (!('oilServiceDate' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceDate = null; //
    }
    if (!('sparePartsRepaired' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.sparePartsRepaired = null; //
    }
    if (!('activityDescription' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.activityDescription = null; //
    }
    if (!('insuranceType' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.insuranceType = null; //
    }
    console.log(maintenanceActivitiesDTO);

    if (
      maintenanceActivitiesDTO.fuelConsumptionDate !== null &&
      maintenanceActivitiesDTO.fuelConsumptionDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.fuelConsumptionDate;
    } else if (
      maintenanceActivitiesDTO.insuranceAcquisitionDate !== null &&
      maintenanceActivitiesDTO.insuranceAcquisitionDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.insuranceAcquisitionDate;
    } else if (
      maintenanceActivitiesDTO.maintenanceActivityDate !== null &&
      maintenanceActivitiesDTO.maintenanceActivityDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.maintenanceActivityDate;
    } else if (
      maintenanceActivitiesDTO.oilServiceDate !== null &&
      maintenanceActivitiesDTO.oilServiceDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.oilServiceDate;
    } else if (
      (maintenanceActivitiesDTO.fuelConsumptionDate === null &&
        maintenanceActivitiesDTO.insuranceAcquisitionDate === null &&
        maintenanceActivitiesDTO.oilServiceDate === null &&
        maintenanceActivitiesDTO.maintenanceActivityDate === null) ||
      (!('fuelConsumptionDate' in maintenanceActivitiesDTO) &&
        !('insuranceAcquisitionDate' in maintenanceActivitiesDTO) &&
        !('oilServiceDate' in maintenanceActivitiesDTO) &&
        !('maintenanceActivityDate' in maintenanceActivitiesDTO))
    ) {
      activityDate = null; // Set cost to null if all are null
    } else {
      return {
        message: 'Invalid activity date input',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }

    // Check if cost filed contail null value or not is not null or an empty string

    if (
      maintenanceActivitiesDTO.oilServiceCost !== null &&
      maintenanceActivitiesDTO.oilServiceCost !== undefined &&
      typeof maintenanceActivitiesDTO.oilServiceCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.oilServiceCost;
    } else if (
      maintenanceActivitiesDTO.fuelCost !== null &&
      maintenanceActivitiesDTO.fuelCost !== undefined &&
      typeof maintenanceActivitiesDTO.fuelCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.fuelCost;
    } else if (
      maintenanceActivitiesDTO.driveCost !== null &&
      maintenanceActivitiesDTO.driveCost !== undefined &&
      typeof maintenanceActivitiesDTO.driveCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.driveCost;
    } else if (
      maintenanceActivitiesDTO.insuranceCost !== null &&
      maintenanceActivitiesDTO.insuranceCost !== undefined &&
      typeof maintenanceActivitiesDTO.insuranceCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.insuranceCost;
    } else if (
      maintenanceActivitiesDTO.maintenanceCost !== null &&
      maintenanceActivitiesDTO.maintenanceCost !== undefined &&
      typeof maintenanceActivitiesDTO.maintenanceCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.maintenanceCost;
    } else if (
      maintenanceActivitiesDTO.oilServiceCost === null &&
      maintenanceActivitiesDTO.fuelCost === null &&
      maintenanceActivitiesDTO.insuranceCost === null &&
      maintenanceActivitiesDTO.driveCost === null &&
      (maintenanceActivitiesDTO.maintenanceCost === null ||
        (!('oilServiceCost' in maintenanceActivitiesDTO) &&
          !('fuelCost' in maintenanceActivitiesDTO) &&
          !('insuranceCost' in maintenanceActivitiesDTO) &&
          !('maintenanceCost' in maintenanceActivitiesDTO) &&
          !('driveCost' in maintenanceActivitiesDTO)))
    ) {
      cost = null; // Set cost to null if all are null or maintenanceCost is not present
    } else {
      return {
        message: 'Invalid cost input',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }
    // cheking mealege

    if (
      maintenanceActivitiesDTO.fuelMileage !== null &&
      maintenanceActivitiesDTO.fuelMileage !== undefined &&
      typeof maintenanceActivitiesDTO.fuelMileage === 'number'
    ) {
      mileage = maintenanceActivitiesDTO.fuelMileage;
    } else if (
      maintenanceActivitiesDTO.oilServiceMileage !== null &&
      maintenanceActivitiesDTO.oilServiceMileage !== undefined &&
      typeof maintenanceActivitiesDTO.oilServiceMileage === 'number'
    ) {
      mileage = maintenanceActivitiesDTO.oilServiceMileage;
    } else if (
      (maintenanceActivitiesDTO.fuelMileage === null &&
        maintenanceActivitiesDTO.oilServiceMileage === null) ||
      (!('fuelMileage' in maintenanceActivitiesDTO) &&
        !('oilServiceMileage' in maintenanceActivitiesDTO))
    ) {
      mileage = null; // Set cost to null if all are null
    } else {
      return {
        message: 'Invalid mileage input',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }

    // console.log(maintenanceActivitiesDTO.activityOnVehicleID);
    let activityOnVehicleData;
    let serviceOilType;
    let registredVehicle;
    let vehicleStatusData;

    const currentDate = new Date();
    const activityDAte = new Date(activityDate);
    const quatersObject = this.getQuarterDates(activityDate);
    const activityQuaterSatrtDate = (await quatersObject).currentQuarter
      .startDate;
    const activityQuaterEndDateDate = (await quatersObject).currentQuarter
      .endDate;
    // check if activity dates reached
    if (activityDAte > currentDate) {
      return {
        message: 'You are reporting a date that has not been reached',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }
    // assigning value on isActivityDAteInCurrentQuater
    let isActivityDAteInCurrentQuater = true;
    // checking if currrent date is within calculated quater
    const isCurrentDateInQuater = await this.isDateWithinRange(
      currentDate,
      activityQuaterSatrtDate,
      activityQuaterEndDateDate,
    );
    if (!isCurrentDateInQuater) {
      isActivityDAteInCurrentQuater = false;
    }

    try {
      activityOnVehicleData = await this.activityOnVehicleRepository.findOne({
        id: maintenanceActivitiesDTO.activityOnVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Activity on vehicle not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for activity Vehicle.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      serviceOilType = await this.oilServiceCategoryRepository.findOne({
        id: maintenanceActivitiesDTO.oilServiceCategoryID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'service oil service not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for service oil type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      registredVehicle = await this.registeredVehicleRepository.findOne({
        id: maintenanceActivitiesDTO.vehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicle ID not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message: error.message || 'Activty on vehicle not fund .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      vehicleStatusData = await this.vehicleStatusRepository.findOne({
        id: maintenanceActivitiesDTO.vehicleStatusId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with MOS as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // updating vehicle status
    const vehicleID = maintenanceActivitiesDTO.vehicleID;

    const updateVehicleStatus =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          isVehicleActive: maintenanceActivitiesDTO.isVehicleActive,
          vehicleStatus: vehicleStatusData,
        })
        .where('id = :id', { id: vehicleID }) // Use the correct parameter name
        .execute();
    if (updateVehicleStatus) {
      const maintenanceActivitiesNewDto = new MaintenanceActivities({
        ...maintenanceActivitiesDTO,
        vehicle: registredVehicle, // Assign the StatusType object
        oilServiceCategory: serviceOilType,
        activityOnVehicle: activityOnVehicleData,
        isActivityDAteInCurrentQuater: isActivityDAteInCurrentQuater,
        cost: cost,
        activityDate: activityDAte,
        activityQuaterSatrtDate: new Date(activityQuaterSatrtDate),
        activityQuaterEndDateDate: new Date(activityQuaterEndDateDate),
        mileage: mileage,

        vehicleStatus: vehicleStatusData, // Assign the ApprovalLevel object
        // Assign the ApprovalLevel object
        // Assign the RequestType object
      });
      return await this.maintenanceActivitiesRepository.create(
        maintenanceActivitiesNewDto,
      );
    } else {
      return {
        message: 'An error occurred while  Updating vehicle status.',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }

    // return activityOnvehicle;
  }
  async maitenamceActivityByID(id: string) {
    return await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')
      .where('maintenanceActivities.id = :id', {
        id,
      })
      .leftJoinAndSelect('maintenanceActivities.vehicle', 'vehicle')

      .leftJoinAndSelect(
        'maintenanceActivities.oilServiceCategory',
        'oilServiceCategory',
      )
      .leftJoinAndSelect(
        'maintenanceActivities.activityOnVehicle',
        'activityOnVehicle',
      )
      .getMany();
  }

  async maitenamceActivityByVehicleID(id: string) {
    return await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')

      .leftJoinAndSelect('maintenanceActivities.vehicle', 'vehicle')
      .where('vehicle.id = :id', {
        id,
      })

      .leftJoinAndSelect(
        'maintenanceActivities.oilServiceCategory',
        'oilServiceCategory',
      )
      .leftJoinAndSelect(
        'maintenanceActivities.activityOnVehicle',
        'activityOnVehicle',
      )
      .getMany();
  }

  async getMaintenanceActivityByVehicleIDAndQuarter(
    vehicleId: string,
    date: Date,
  ) {
    // Calculate the start and end dates of the current quarter
    // const dateToserch = new Date(date).setHours(0, 0, 0, 0);
    const quatersObject = await this.getQuarterDates(date);
    console.log(quatersObject);
    const currentQuarterStartDate = (await quatersObject).currentQuarter
      .startDate;

    const currentQuarterEndDate = (await quatersObject).currentQuarter.endDate;

    // Generate an array of dates within the current quarter range
    const dateList = eachDayOfInterval({
      start: currentQuarterStartDate,
      end: currentQuarterEndDate,
    });
    console.log(dateList);
    // Format each date in the desired format
    const formattedDateList = [];
    for (let i = 0; i < dateList.length; i++) {
      const formattedDate1 = format(dateList[i], 'yyyy-MM-dd');
      formattedDateList.push(formattedDate1);
    }
    console.log(formattedDateList);

    // Query the database to get maintenance activities for the given vehicle ID within the date range
    const maintenanceActivities =
      await this.maintenanceActivitiesManagerRepository
        .createQueryBuilder('maintenanceActivities')
        .leftJoinAndSelect('maintenanceActivities.vehicle', 'vehicle')
        .where('vehicle.id = :vehicleId', { vehicleId })
        .andWhere('maintenanceActivities.activityDate IN (:...dateList)', {
          dateList,
        })
        .leftJoinAndSelect(
          'maintenanceActivities.oilServiceCategory',
          'oilServiceCategory',
        )
        .leftJoinAndSelect(
          'maintenanceActivities.activityOnVehicle',
          'activityOnVehicle',
        )
        .getMany();

    return maintenanceActivities;
  }
  async updateActivityPerformed(id: string, maintenanceActivitiesDTO: any) {
    // console.log(maintenanceActivitiesDTO.activityOnVehicleID);
    let cost: number | null = null;
    let mileage: number | null = null;
    let activityDate: Date | null = null;
    let vehicleGoodCondtionID;
    // handuling date of activities

    // handlong other missing values here
    if (!('activityOnVehicleID' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.activityOnVehicleID = null; //
    }
    if (!('oilServiceCategoryID' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceCategoryID = null; //
    }
    if (!('fuelQuantity' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelQuantity = null; //
    }
    if (!('fuelQuantity' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelQuantity = null; //
    }
    if (!('fuelMileage' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelMileage = null; //
    }
    if (!('oilServiceMileage' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceMileage = null; //
    }

    if (!('driveName' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.driveName = null; //
    }
    if (!('isVehicleActive' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.isVehicleActive = true; //
    }

    try {
      vehicleGoodCondtionID = await this.vehicleStatusRepository.findOne({
        name: 'GOOD CONDITION',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with MOS as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    if (!('vehicleStatusId' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.vehicleStatusId = vehicleGoodCondtionID.id; //
    }
    if (!('maintenanceActivityObeservation' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.maintenanceActivityObeservation = null; //
    }
    if (!('insurancePeriod' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.insurancePeriod = null; //
    }
    if (!('fuelCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelCost = null; //
    }
    if (!('driveCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.driveCost = null; //
    }
    if (!('fuelConsumptionDate' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.fuelConsumptionDate = null; //
    }
    if (!('maintenanceCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.maintenanceCost = null; //
    }
    if (!('maintenanceActivityDate' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.maintenanceActivityDate = null; //
    }

    if (!('oilServiceCost' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceCost = null; //
    }
    if (!('oilServiceDate' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.oilServiceDate = null; //
    }
    if (!('sparePartsRepaired' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.sparePartsRepaired = null; //
    }
    if (!('activityDescription' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.activityDescription = null; //
    }
    if (!('insuranceType' in maintenanceActivitiesDTO)) {
      maintenanceActivitiesDTO.insuranceType = null; //
    }
    // console.log(maintenanceActivitiesDTO);

    if (
      maintenanceActivitiesDTO.fuelConsumptionDate !== null &&
      maintenanceActivitiesDTO.fuelConsumptionDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.fuelConsumptionDate;
    } else if (
      maintenanceActivitiesDTO.insuranceAcquisitionDate !== null &&
      maintenanceActivitiesDTO.insuranceAcquisitionDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.insuranceAcquisitionDate;
    } else if (
      maintenanceActivitiesDTO.maintenanceActivityDate !== null &&
      maintenanceActivitiesDTO.maintenanceActivityDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.maintenanceActivityDate;
    } else if (
      maintenanceActivitiesDTO.oilServiceDate !== null &&
      maintenanceActivitiesDTO.oilServiceDate !== undefined
    ) {
      activityDate = maintenanceActivitiesDTO.oilServiceDate;
    } else if (
      (maintenanceActivitiesDTO.fuelConsumptionDate === null &&
        maintenanceActivitiesDTO.insuranceAcquisitionDate === null &&
        maintenanceActivitiesDTO.oilServiceDate === null &&
        maintenanceActivitiesDTO.maintenanceActivityDate === null) ||
      (!('fuelConsumptionDate' in maintenanceActivitiesDTO) &&
        !('insuranceAcquisitionDate' in maintenanceActivitiesDTO) &&
        !('oilServiceDate' in maintenanceActivitiesDTO) &&
        !('maintenanceActivityDate' in maintenanceActivitiesDTO))
    ) {
      activityDate = null; // Set cost to null if all are null
    } else {
      return {
        message: 'Invalid activity date input',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }

    // Check if cost filed contail null value or not is not null or an empty string

    if (
      maintenanceActivitiesDTO.oilServiceCost !== null &&
      maintenanceActivitiesDTO.oilServiceCost !== undefined &&
      typeof maintenanceActivitiesDTO.oilServiceCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.oilServiceCost;
    } else if (
      maintenanceActivitiesDTO.fuelCost !== null &&
      maintenanceActivitiesDTO.fuelCost !== undefined &&
      typeof maintenanceActivitiesDTO.fuelCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.fuelCost;
    } else if (
      maintenanceActivitiesDTO.driveCost !== null &&
      maintenanceActivitiesDTO.driveCost !== undefined &&
      typeof maintenanceActivitiesDTO.driveCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.driveCost;
    } else if (
      maintenanceActivitiesDTO.insuranceCost !== null &&
      maintenanceActivitiesDTO.insuranceCost !== undefined &&
      typeof maintenanceActivitiesDTO.insuranceCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.insuranceCost;
    } else if (
      maintenanceActivitiesDTO.maintenanceCost !== null &&
      maintenanceActivitiesDTO.maintenanceCost !== undefined &&
      typeof maintenanceActivitiesDTO.maintenanceCost === 'number'
    ) {
      cost = maintenanceActivitiesDTO.maintenanceCost;
    } else if (
      maintenanceActivitiesDTO.oilServiceCost === null &&
      maintenanceActivitiesDTO.fuelCost === null &&
      maintenanceActivitiesDTO.insuranceCost === null &&
      maintenanceActivitiesDTO.driveCost === null &&
      (maintenanceActivitiesDTO.maintenanceCost === null ||
        (!('oilServiceCost' in maintenanceActivitiesDTO) &&
          !('fuelCost' in maintenanceActivitiesDTO) &&
          !('insuranceCost' in maintenanceActivitiesDTO) &&
          !('maintenanceCost' in maintenanceActivitiesDTO) &&
          !('driveCost' in maintenanceActivitiesDTO)))
    ) {
      cost = null; // Set cost to null if all are null or maintenanceCost is not present
    } else {
      return {
        message: 'Invalid cost input',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }
    // cheking mealege

    if (
      maintenanceActivitiesDTO.fuelMileage !== null &&
      maintenanceActivitiesDTO.fuelMileage !== undefined &&
      typeof maintenanceActivitiesDTO.fuelMileage === 'number'
    ) {
      mileage = maintenanceActivitiesDTO.fuelMileage;
    } else if (
      maintenanceActivitiesDTO.oilServiceMileage !== null &&
      maintenanceActivitiesDTO.oilServiceMileage !== undefined &&
      typeof maintenanceActivitiesDTO.oilServiceMileage === 'number'
    ) {
      mileage = maintenanceActivitiesDTO.oilServiceMileage;
    } else if (
      (maintenanceActivitiesDTO.fuelMileage === null &&
        maintenanceActivitiesDTO.oilServiceMileage === null) ||
      (!('fuelMileage' in maintenanceActivitiesDTO) &&
        !('oilServiceMileage' in maintenanceActivitiesDTO))
    ) {
      mileage = null; // Set cost to null if all are null
    } else {
      return {
        message: 'Invalid mileage input',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }

    const maintenanceActivitie =
      await this.maintenanceActivitiesRepository.findOne({
        id,
      });
    if (!maintenanceActivitie) {
      throw new NotFoundException('maitenance activity  found');
    }
    // let activityOnvehicle;
    let serviceOilType;
    let registredVehicle;
    let activityOnvehicle;
    let vehicleStatusData;
    const currentDate = new Date();
    const activityDAte = new Date(activityDate);
    const quatersObject = this.getQuarterDates(activityDate);
    const activityQuaterSatrtDate = (await quatersObject).currentQuarter
      .startDate;
    const activityQuaterEndDateDate = (await quatersObject).currentQuarter
      .endDate;
    // check if activity dates reached
    if (activityDAte > currentDate) {
      return {
        message: 'You are reporting a date that has not been reached',
        statusCode: HttpStatus.NOT_FOUND,
        // Handle invalid case here, like throwing an error or returning a specific response
      };
    }
    // assigning value on isActivityDAteInCurrentQuater
    let isActivityDAteInCurrentQuater = true;
    // checking if currrent date is within calculated quater
    const isCurrentDateInQuater = await this.isDateWithinRange(
      currentDate,
      activityQuaterSatrtDate,
      activityQuaterEndDateDate,
    );
    if (!isCurrentDateInQuater) {
      isActivityDAteInCurrentQuater = false;
    }

    try {
      activityOnvehicle = await this.activityOnVehicleRepository.findOne({
        id: maintenanceActivitiesDTO.activityOnVehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Activity on vehicle not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for activity Vehicle.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      serviceOilType = await this.oilServiceCategoryRepository.findOne({
        id: maintenanceActivitiesDTO.oilServiceCategoryID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'service oil service not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for service oil type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      registredVehicle = await this.registeredVehicleRepository.findOne({
        id: maintenanceActivitiesDTO.vehicleID,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'vehicle ID not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message: error.message || 'Activty on vehicle not fund .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      vehicleStatusData = await this.vehicleStatusRepository.findOne({
        id: maintenanceActivitiesDTO.vehicleStatusId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with MOS as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const vehicleID = maintenanceActivitiesDTO.vehicleID;
    const updateVehicleStatus =
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          isVehicleActive: maintenanceActivitiesDTO.isVehicleActive,
          vehicleStatus: vehicleStatusData,
        })
        .where('id = :id', { id: vehicleID }) // Use the correct parameter name
        .execute();
    if (updateVehicleStatus) {
      const updatedMatenanceActivity =
        await this.maintenanceActivitiesManagerRepository
          .createQueryBuilder() // Use query builder without alias
          .update('MaintenanceActivities')
          // Provide the table name
          .set({
            vehicle: registredVehicle, // Assign the StatusType object
            activityOnVehicle: activityOnvehicle, // Assign the ApprovalLevel object
            oilServiceCategory: serviceOilType,
            fuelQuantity: maintenanceActivitiesDTO.fuelQuantity,
            sparePartsRepaired: maintenanceActivitiesDTO.sparePartsRepaired,
            activityDescription: maintenanceActivitiesDTO.activityDescription,
            insuranceType: maintenanceActivitiesDTO.insuranceType,
            isActivityDAteInCurrentQuater: isActivityDAteInCurrentQuater,
            cost: cost,
            activityDate: activityDAte,
            activityQuaterSatrtDate: new Date(activityQuaterSatrtDate),
            activityQuaterEndDateDate: new Date(activityQuaterEndDateDate),
            mileage: mileage,
            vehicleStatus: vehicleStatusData,
            maintenanceActivityObeservation:
              maintenanceActivitiesDTO.maintenanceActivityObeservation,
            insurancePeriod: maintenanceActivitiesDTO.insurancePeriod,
            // Assign the RequestType object
          })

          .where('id = :id', { id }) // Assuming id is the primary key column
          .execute();
      if (!updatedMatenanceActivity) {
        return {
          message: 'performed maitenace activities not updated well ',
          statusCode: HttpStatus.NOT_FOUND,
        };
      }
      return await this.maintenanceActivitiesManagerRepository
        .createQueryBuilder('maintenanceActivities')
        .where('maintenanceActivities.id = :id', {
          id,
        })
        .leftJoinAndSelect('maintenanceActivities.vehicle', 'vehicle')

        .leftJoinAndSelect(
          'maintenanceActivities.oilServiceCategory',
          'oilServiceCategory',
        )
        .leftJoinAndSelect(
          'maintenanceActivities.activityOnVehicle',
          'activityOnVehicle',
        )
        .getMany();
    } else {
      return {
        message: 'An error occurred while  Updating vehicle status.',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  // Operation Cost Report
  async allOperationalCost() {
    const currentYear = new Date().getFullYear();

    const results = await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')
      .select('vehicle.beneficiaryAgencyId', 'beneficiaryAgencyId')
      .addSelect('vehicle.beneficiaryAgency', 'institutionName')
      .addSelect('COUNT(DISTINCT vehicle.id)', 'totalVehicles')
      .addSelect('SUM(maintenanceActivities.cost)', 'totalCost')
      .addSelect('activityOnVehicle.name', 'activityName')
      .where(
        'EXTRACT(YEAR FROM maintenanceActivities.createddate) = :currentYear',
        { currentYear },
      )
      .leftJoin('maintenanceActivities.vehicle', 'vehicle')
      .leftJoin('maintenanceActivities.activityOnVehicle', 'activityOnVehicle')
      .groupBy('vehicle.beneficiaryAgencyId')
      .addGroupBy('vehicle.beneficiaryAgency')
      .addGroupBy('activityOnVehicle.name')
      .getRawMany();

    // Transform the result to match the desired format
    const transformedResult = results.reduce((acc, curr) => {
      let institution = acc.find(
        (inst) => inst.beneficiaryAgencyId === curr.beneficiaryAgencyId,
      );

      if (!institution) {
        institution = {
          beneficiaryAgencyId: curr.beneficiaryAgencyId,
          institutionName: curr.institutionName,
          totalVehicles: parseInt(curr.totalVehicles, 10),
          totalCost: parseFloat(curr.totalCost),
          activities: [],
        };
        acc.push(institution);
      } else {
        institution.totalCost += parseFloat(curr.totalCost);
        institution.totalVehicles += parseInt(curr.totalVehicles, 10);
      }

      institution.activities.push({
        activityName: curr.activityName,
      });

      return acc;
    }, []);
    // Calculate overall total cost
    const overallTotalCost = transformedResult.reduce(
      (sum, institution) => sum + institution.totalCost,
      0,
    );

    // return transformedResult;
    return {
      overallTotalCost: overallTotalCost,
      TotalCostPerInstitution: transformedResult,
    };
  }

  async allOperationalCostByDateRange(startDate: Date, endDate: Date) {
    const results = await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')
      .select('vehicle.beneficiaryAgencyId', 'beneficiaryAgencyId')
      .addSelect('vehicle.beneficiaryAgency', 'institutionName')
      .addSelect('COUNT(DISTINCT vehicle.id)', 'totalVehicles')
      .addSelect('SUM(maintenanceActivities.cost)', 'totalCost')
      .addSelect('activityOnVehicle.name', 'activityName')
      .where(
        'maintenanceActivities.createddate BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      )
      .leftJoin('maintenanceActivities.vehicle', 'vehicle')
      .leftJoin('maintenanceActivities.activityOnVehicle', 'activityOnVehicle')
      .groupBy('vehicle.beneficiaryAgencyId')
      .addGroupBy('vehicle.beneficiaryAgency')
      .addGroupBy('activityOnVehicle.name')
      .getRawMany();

    // Transform the result to match the desired format
    const transformedResult = results.reduce((acc, curr) => {
      let institution = acc.find(
        (inst) => inst.beneficiaryAgencyId === curr.beneficiaryAgencyId,
      );

      if (!institution) {
        institution = {
          beneficiaryAgencyId: curr.beneficiaryAgencyId,
          institutionName: curr.institutionName,
          totalVehicles: parseInt(curr.totalVehicles, 10),
          totalCost: parseFloat(curr.totalCost),
          activities: [],
        };
        acc.push(institution);
      } else {
        institution.totalCost += parseFloat(curr.totalCost);
        institution.totalVehicles += parseInt(curr.totalVehicles, 10);
      }

      institution.activities.push({
        activityName: curr.activityName,
      });

      return acc;
    }, []);

    const overallTotalCost = transformedResult.reduce(
      (sum, institution) => sum + institution.totalCost,
      0,
    );

    // return transformedResult;
    return {
      overallTotalCost: overallTotalCost,
      TotalCostPerInstitution: transformedResult,
    };
  }

  async allOperationalCostByBeneficiaryAngencyId(beneficiaryAgencyId: string) {
    const currentYear = new Date().getFullYear();

    const results = await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')
      .select('vehicle.beneficiaryAgencyId', 'beneficiaryAgencyId')
      .addSelect('vehicle.plateNumber', 'plateNumber')
      .addSelect('vehicle.id', 'vehicleId')
      .addSelect('SUM(maintenanceActivities.cost)', 'totalCost')
      .addSelect('activityOnVehicle.name', 'activityName')
      .where(
        'EXTRACT(YEAR FROM maintenanceActivities.createddate) = :currentYear',
        { currentYear },
      )
      .andWhere('vehicle.beneficiaryAgencyId = :beneficiaryAgencyId', {
        beneficiaryAgencyId,
      })
      .leftJoin('maintenanceActivities.vehicle', 'vehicle')
      .leftJoin('maintenanceActivities.activityOnVehicle', 'activityOnVehicle')
      .groupBy('vehicle.id')
      .addGroupBy('vehicle.beneficiaryAgencyId')
      .addGroupBy('vehicle.plateNumber')
      .addGroupBy('activityOnVehicle.name')
      .getRawMany();

    // Transform the result to match the desired format
    const transformedResult = results.reduce((acc, curr) => {
      let vehicle = acc.find((v) => v.vehicleId === curr.vehicleId);

      if (!vehicle) {
        vehicle = {
          PlateNumber: curr.plateNumber,
          VehiclesID: curr.vehicleId,
          totalCost: parseFloat(curr.totalCost),
          activities: [],
        };
        acc.push(vehicle);
      } else {
        vehicle.totalCost += parseFloat(curr.totalCost);
      }

      vehicle.activities.push({
        activityName: curr.activityName,
      });

      return acc;
    }, []);

    // Calculate overall total cost
    const overallTotalCost = transformedResult.reduce(
      (sum, vehicle) => sum + vehicle.totalCost,
      0,
    );

    return {
      beneficiaryAgencyId: beneficiaryAgencyId,
      overallTotalCost: overallTotalCost,
      TotalCostVehicle: transformedResult,
    };
  }

  // all cost operation by instution id and Date range
  async allOperationalCostByBeneficiaryAngencyIdAndDateRange(
    beneficiaryAgencyId: string,
    startDate: Date,
    endDate: Date,
  ) {
    const results = await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')
      .select('vehicle.beneficiaryAgencyId', 'beneficiaryAgencyId')
      .addSelect('vehicle.plateNumber', 'plateNumber')
      .addSelect('vehicle.id', 'vehicleId')
      .addSelect('SUM(maintenanceActivities.cost)', 'totalCost')
      .addSelect('activityOnVehicle.name', 'activityName')
      .where(
        'maintenanceActivities.createddate BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      )
      .andWhere('vehicle.beneficiaryAgencyId = :beneficiaryAgencyId', {
        beneficiaryAgencyId,
      })
      .leftJoin('maintenanceActivities.vehicle', 'vehicle')
      .leftJoin('maintenanceActivities.activityOnVehicle', 'activityOnVehicle')
      .groupBy('vehicle.id')
      .addGroupBy('vehicle.beneficiaryAgencyId')
      .addGroupBy('vehicle.plateNumber')
      .addGroupBy('activityOnVehicle.name')
      .getRawMany();

    // Transform the result to match the desired format
    const transformedResult = results.reduce((acc, curr) => {
      let vehicle = acc.find((v) => v.vehicleId === curr.vehicleId);

      if (!vehicle) {
        vehicle = {
          PlateNumber: curr.plateNumber,
          VehiclesID: curr.vehicleId,
          totalCost: parseFloat(curr.totalCost),
          activities: [],
        };
        acc.push(vehicle);
      } else {
        vehicle.totalCost += parseFloat(curr.totalCost);
      }

      vehicle.activities.push({
        activityName: curr.activityName,
      });

      return acc;
    }, []);

    // Calculate overall total cost
    const overallTotalCost = transformedResult.reduce(
      (sum, vehicle) => sum + vehicle.totalCost,
      0,
    );

    return {
      beneficiaryAgencyId: beneficiaryAgencyId,
      overallTotalCost: overallTotalCost,
      TotalCostVehicle: transformedResult,
    };
  }

  async allOperationalCostByVehicleId(vehicleId: string) {
    const currentYear = new Date().getFullYear();

    const results = await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')
      .select('vehicle.beneficiaryAgencyId', 'beneficiaryAgencyId')
      .addSelect('vehicle.id', 'vehicleId')
      .addSelect('activityOnVehicle.id', 'activityOnVehicleId')
      .addSelect('activityOnVehicle.name', 'activityName')
      .addSelect('SUM(maintenanceActivities.cost)', 'totalCost')
      .where(
        'EXTRACT(YEAR FROM maintenanceActivities.createddate) = :currentYear',
        { currentYear },
      )
      .andWhere('vehicle.id = :vehicleId', { vehicleId })
      .leftJoin('maintenanceActivities.vehicle', 'vehicle')
      .leftJoin('maintenanceActivities.activityOnVehicle', 'activityOnVehicle')
      .groupBy('vehicle.beneficiaryAgencyId')
      .addGroupBy('vehicle.id')
      .addGroupBy('activityOnVehicle.id')
      .addGroupBy('activityOnVehicle.name')
      .getRawMany();

    // Transform the result to match the desired format
    const transformedResult = results.map((curr) => ({
      activityOnVehicleId: curr.activityOnVehicleId,
      activityName: curr.activityName,
      totalCost: parseFloat(curr.totalCost),
    }));

    // Calculate overall total cost
    const overallTotalCost = transformedResult.reduce(
      (sum, activity) => sum + activity.totalCost,
      0,
    );

    return {
      beneficiaryAgencyId: results[0]?.beneficiaryAgencyId || null,
      vehicleId: vehicleId,
      overallTotalCost: overallTotalCost,
      TotalCostPerActivity: transformedResult,
    };
  }

  // ================================

  async allOperationalCostByVehicleIdAndDateRange(
    vehicleId: string,
    startDate: Date,
    endDate: Date,
  ) {
    const results = await this.maintenanceActivitiesManagerRepository
      .createQueryBuilder('maintenanceActivities')
      .select('vehicle.beneficiaryAgencyId', 'beneficiaryAgencyId')
      .addSelect('vehicle.id', 'vehicleId')
      .addSelect('activityOnVehicle.id', 'activityOnVehicleId')
      .addSelect('activityOnVehicle.name', 'activityName')
      .addSelect('SUM(maintenanceActivities.cost)', 'totalCost')
      .where(
        'maintenanceActivities.createddate BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      )
      .andWhere('vehicle.id = :vehicleId', { vehicleId })
      .leftJoin('maintenanceActivities.vehicle', 'vehicle')
      .leftJoin('maintenanceActivities.activityOnVehicle', 'activityOnVehicle')
      .groupBy('vehicle.beneficiaryAgencyId')
      .addGroupBy('vehicle.id')
      .addGroupBy('activityOnVehicle.id')
      .addGroupBy('activityOnVehicle.name')
      .getRawMany();

    // Transform the result to match the desired format
    const transformedResult = results.map((curr) => ({
      activityOnVehicleId: curr.activityOnVehicleId,
      activityName: curr.activityName,
      totalCost: parseFloat(curr.totalCost),
    }));

    // Calculate overall total cost
    const overallTotalCost = transformedResult.reduce(
      (sum, activity) => sum + activity.totalCost,
      0,
    );

    return {
      beneficiaryAgencyId: results[0]?.beneficiaryAgencyId || null,
      vehicleId: vehicleId,
      overallTotalCost: overallTotalCost,
      TotalCostPerActivity: transformedResult,
    };
  }
}
