import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import {
  CreateVehicleDisposalDto,
  disposalSearchAndFilterDto,
  UpdateVehicleDisposalDto,
} from './dtos/vehicle-disposal/vehicle-disposal.dto';
import { VehicleDisposal } from './entities/vehicle-disposal/vehicle-disposal.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import {
  RegisteredVehicleRepository,
  VehicleDisposalRepository,
} from './fleet-mgmnt.repository';
import { RegisteredVehicle } from './entities/vehicles-registration /registred-vehicle';
import { FleetMgmntService } from './fleet-mgmnt.service';
import {
  ApprovalLevelRepository,
  DisposalReasonsRepository,
  DisposalTypesRepository,
  StatusTypeRepository,
} from './vehicle-management/vehicle-management.repository';
import { AquistionApprovalService } from './approval.service';
import { AuctionReport } from './entities/auction-report/auction-report.entity';
import { AuctionReportRepository } from './fleet-mgmnt.repository';
import {
  CreateAuctionReportDto,
  GetDisposalReportByFilterDto,
  UpdateAuctionReportDto,
} from './dtos/auction-report/auction-report.dto';
@Injectable()
export class DisposalManagementService {
  constructor(
    @InjectRepository(VehicleDisposal)
    private vehicleDisposalManagerRepository: Repository<VehicleDisposal>,
    private readonly vehicleDisposalRepository: VehicleDisposalRepository,
    @InjectRepository(RegisteredVehicle)
    private RegisteredVehicleentityManagerRepository: Repository<RegisteredVehicle>,
    private readonly registeredVehicleRepository: RegisteredVehicleRepository,
    private entityManager: EntityManager,
    private readonly fleetMgmntService: FleetMgmntService,
    private readonly disposalTypesRepository: DisposalTypesRepository,
    private readonly disposalReasonsRepository: DisposalReasonsRepository,
    private readonly statusTypeRepository: StatusTypeRepository,
    private readonly approvalLevelRepository: ApprovalLevelRepository,
    private readonly aquistionApprovalService: AquistionApprovalService,
    @InjectRepository(AuctionReport)
    private auctionReportManagerRepository: Repository<AuctionReport>,
    private readonly auctionReportRepository: AuctionReportRepository,
  ) {} // all code goes gere
  // Getting all user detail in case is needed
  // create vehicle disposals request
  async VehicleDisposal(createVehicleDisposalDto: CreateVehicleDisposalDto) {
    const vehicleId = createVehicleDisposalDto.vehicleId;
    // Golobal variables
    //

    const previousData = await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .where('vehicleDisposal.vehicle = :vehicleId', {
        vehicleId,
      })
      .getOne();
    if (previousData) {
      return {
        message: 'Disposal request for this vehicle has already been submitted',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }

    let vehicleData;
    let disposalTypedata;
    let disposalReasondata;
    let statusdata;
    let approvalData;

    try {
      vehicleData = await this.fleetMgmntService.getVehicleById(vehicleId);
      if (!vehicleData) {
        throw new NotFoundException(`Vehicle with ID '${vehicleId}' not found`);
      }
      // return vehicleData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Handle NotFoundException
        return {
          message: 'Vehicle not found',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        // Handle other errors
        throw new Error(`Failed to fetch vehicle data: ${error.message}`);
      }
    }
    // getting disposal type

    try {
      disposalTypedata = await this.disposalTypesRepository.findOne({
        id: createVehicleDisposalDto.disposalTypeId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'disposal type data   not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for disposal type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // getting disposal reason data
    try {
      disposalReasondata = await this.disposalReasonsRepository.findOne({
        id: createVehicleDisposalDto.disposalReasonId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'disposal reasons data   not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for disposal reasons.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // statutaus leval

    try {
      statusdata = await this.statusTypeRepository.findOne({
        name: 'PENDING',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }

    try {
      approvalData = await this.approvalLevelRepository.findOne({
        code: 'ICBM2',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const vehicleRegStatus = vehicleData.registrationStatus.name;
    const vehicleApproveleval = vehicleData.approvalLevel.code;
    // console.log(vehicleApproveleval);
    // creating disposal request
    if (vehicleRegStatus != 'APPROVED' && vehicleApproveleval != 'MOS') {
      return {
        message: 'Vehicle is not approved',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }

    const newVehicleDisposalDTO = new VehicleDisposal({
      ...createVehicleDisposalDto,
      vehicle: vehicleData,
      disposalTypes: disposalTypedata,
      disposalReasons: disposalReasondata,
      description: createVehicleDisposalDto.description,
      requestStatus: statusdata,
      approvalLevel: approvalData,
      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });

    const vehicleDisposal = await this.vehicleDisposalRepository.create(
      newVehicleDisposalDTO,
    );
    if (vehicleDisposal) {
      await this.RegisteredVehicleentityManagerRepository.createQueryBuilder() // Use query builder without alias
        .update('RegisteredVehicle') // Provide the table name
        .set({
          isDisposalRequestSubmitted: true,
        })
        .where('id = :vehicleId', { vehicleId }) // Assuming id is the primary key column
        .execute();
    }

    return vehicleDisposal;
  }

  // getting all disposal request
  async getAllDisposal() {
    return await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicleDisposal.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.vehicleRegRequest', 'vehicleRegRequest')
      // .leftJoinAndSelect('vehicle.VehicleAcquisition', 'VehicleAcquisition')
      .leftJoinAndSelect('vehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect('vehicle.vehicleManufacture', 'vehicleManufacture')
      .leftJoinAndSelect('vehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('vehicle.vehicleStatus', 'vehicleStatus')
      // .leftJoinAndSelect('vehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicle.registrationStatus', 'registrationStatus')
      .getMany();
  }

  // getting disposal by filter and search string
  async getAllDisposalByfilterAndSort(filters: disposalSearchAndFilterDto) {
    const query = this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicleDisposal.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.vehicleRegRequest', 'vehicleRegRequest')
      // .leftJoinAndSelect('vehicle.VehicleAcquisition', 'VehicleAcquisition')
      .leftJoinAndSelect('vehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect('vehicle.vehicleManufacture', 'vehicleManufacture')
      .leftJoinAndSelect('vehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('vehicle.vehicleStatus', 'vehicleStatus')
      // .leftJoinAndSelect('vehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicle.registrationStatus', 'registrationStatus');

    if (filters.vehicleId) {
      query.andWhere('vehicleDisposal.vehicleId = :vehicleId', {
        vehicleId: filters.vehicleId,
      });
    }
    if (filters.vehicleId) {
      query.andWhere('disposalType.name = :disposalType', {
        disposalType: filters.disposalType,
      });
    }
    if (filters.vehicleId) {
      query.andWhere('disposalReasonId.name = :disposalReasonId', {
        disposalReasonId: filters.disposalReasonId,
      });
    }
    if (filters.vehicleId) {
      query.andWhere('requestStatus.name = :requestStatus', {
        requestStatus: filters.requestStatus,
      });
    }

    if (filters.approvalLeval) {
      query.andWhere('approvalLeval.name = :approvalLeval', {
        approvalLeval: filters.approvalLeval,
      });
    }
    if (filters.approvalLeval) {
      query.andWhere('approvalLeval.code = :approvalLeval', {
        approvalLeval: filters.approvalLeval,
      });
    }
    if (filters.isActionReportsubmitted) {
      query.andWhere(
        'vehicleDisposal.isActionReportsubmitted = :isActionReportsubmitted',
        {
          isActionReportsubmitted: filters.isActionReportsubmitted,
        },
      );
    }
    // date related staff
    if (filters.createdDateStart && filters.createdDateEnd) {
      query.andWhere(
        'vehicleDisposal.createddate BETWEEN :createdDateStart AND :createdDateEnd',
        {
          createdDateStart: filters.createdDateStart,
          createdDateEnd: filters.createdDateEnd,
        },
      );
    } else if (filters.createdDateStart) {
      query.andWhere('vehicleDisposal.createddate >= :createdDateStart', {
        createdDateStart: filters.createdDateStart,
      });
    } else if (filters.createdDateEnd) {
      query.andWhere('vehicleDisposal.createddate <= :createdDateEnd', {
        createdDateEnd: filters.createdDateEnd,
      });
    }

    if (filters.submitedDateStart && filters.submitedDateEnd) {
      query.andWhere(
        'vehicleDisposal.submittedDate BETWEEN :submitedDateStart AND :submitedDateStart',
        {
          submitedDateStart: filters.submitedDateStart,
          submitedDateEnd: filters.submitedDateEnd,
        },
      );
    } else if (filters.submitedDateStart) {
      query.andWhere('vehicleDisposal.submittedDate >= :submitedDateStart', {
        submitedDateStart: filters.submitedDateStart,
      });
    } else if (filters.submitedDateEnd) {
      query.andWhere('vehicleDisposal.submittedDate <= :submitedDateEnd', {
        submitedDateEnd: filters.submitedDateEnd,
      });
    }

    if (filters.approvalDateStart && filters.approvedDateEnd) {
      query.andWhere(
        'vehicleDisposal.approveddate BETWEEN :approvalDateStart AND :approvedDateEnd',
        {
          approvalDateStart: filters.approvalDateStart,
          approvedDateEnd: filters.approvedDateEnd,
        },
      );
    } else if (filters.approvalDateStart) {
      query.andWhere('vehicleDisposal.approveddate >= :approvalDateStart', {
        approvalDateStart: filters.approvalDateStart,
      });
    } else if (filters.approvedDateEnd) {
      query.andWhere('vehicleDisposal.approveddate <= :approvedDateEnd', {
        approvedDateEnd: filters.approvedDateEnd,
      });
    }

    // searching by string handoling logic
    if (filters.searchString) {
      const trimmedSearchString = filters.searchString.trim();
      const lowerCaseSearchString = `%${trimmedSearchString.toLowerCase()}%`;

      query
        // .where('vehicleAcquisition.id LIKE :searchString', {
        //   searchString: lowerCaseSearchString,
        // })
        .orWhere('LOWER(disposalType.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(disposalReason.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(requestStatus.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(approvalLevel.name) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(approvalLevel.code) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        });
    }

    const [vehiclalDisposal, count] = await query.getManyAndCount();
    return { vehiclalDisposal, count };
  }

  // getting vehicle disposal by id.
  async getDisposalById(id: string) {
    return await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .where('vehicleDisposal.id = :id', {
        id,
      })
      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicleDisposal.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.vehicleRegRequest', 'vehicleRegRequest')
      // .leftJoinAndSelect('vehicle.VehicleAcquisition', 'VehicleAcquisition')
      .leftJoinAndSelect('vehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect('vehicle.vehicleManufacture', 'vehicleManufacture')
      .leftJoinAndSelect('vehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('vehicle.vehicleStatus', 'vehicleStatus')
      // .leftJoinAndSelect('vehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicle.registrationStatus', 'registrationStatus')
      .getOne();
  }

  // getting vehicle disposal by disposal type

  async getDisposalByTypeId(disposalTypeId: string) {
    console.log(disposalTypeId);
    return await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .where('disposalType.id= :disposalTypeId', {
        disposalTypeId,
      })
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicleDisposal.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.vehicleRegRequest', 'vehicleRegRequest')
      // .leftJoinAndSelect('vehicle.VehicleAcquisition', 'VehicleAcquisition')
      .leftJoinAndSelect('vehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect('vehicle.vehicleManufacture', 'vehicleManufacture')
      .leftJoinAndSelect('vehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('vehicle.vehicleStatus', 'vehicleStatus')
      // .leftJoinAndSelect('vehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicle.registrationStatus', 'registrationStatus')
      .getMany();
  }

  // updating vehicle disposal
  async updateDisposal(
    id: string,
    updateVehicleDisposalDto: UpdateVehicleDisposalDto,
  ) {
    // let disposaldata;
    let disposalTypedata;
    let disposalReasondata;
    let datastatusType;
    // console.log(id);

    const disposaldata = await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .where('vehicleDisposal.id = :id', {
        id,
      })
      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .getOne();
    if (!disposaldata) {
      return {
        message: 'disposal data not found in database',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    try {
      disposalTypedata = await this.disposalTypesRepository.findOne({
        id: updateVehicleDisposalDto.disposalTypeId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'disposal type data   not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for disposal type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    // getting disposal reason data
    try {
      disposalReasondata = await this.disposalReasonsRepository.findOne({
        id: updateVehicleDisposalDto.disposalReasonId,
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'disposal reasons data   not find in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching data for disposal reasons.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const approvalLevelcode = disposaldata.approvalLevel.code;
    const approvalLevelIndex =
      this.aquistionApprovalService.findIndexByApprovalLevel(approvalLevelcode);
    const nextapprovalLevelIndex = (await approvalLevelIndex) + 1;

    const nextapprovalLevelcode =
      await this.aquistionApprovalService.findApprovalLevelByIndex(
        nextapprovalLevelIndex,
      );
    const nextapprovalLevelData = await this.approvalLevelRepository.findOne({
      code: nextapprovalLevelcode,
    });
    if (nextapprovalLevelcode === 'IL1') {
      // geting status data

      try {
        datastatusType = await this.statusTypeRepository.findOne({
          name: 'PENDING',
        });
      } catch (error) {
        if (error.message === 'Entity not found.') {
          return {
            message: 'PENDING status type not found in database',
            statusCode: HttpStatus.NOT_FOUND,
          };
        } else {
          return {
            message:
              error.message ||
              'An error occurred while fetching status data type.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }

      await this.vehicleDisposalManagerRepository
        .createQueryBuilder() // Use query builder without alias
        .update('VehicleDisposal') // Provide the table name
        .set({
          disposalTypes: disposalTypedata,
          disposalReasons: disposalReasondata,
          description: updateVehicleDisposalDto.description,
          requestStatus: datastatusType,
          approvalLevel: nextapprovalLevelData,
        })
        .where('id = :id', { id }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }

    try {
      datastatusType = await this.statusTypeRepository.findOne({
        name: 'PROGRESS',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    await this.vehicleDisposalManagerRepository
      .createQueryBuilder() // Use query builder without alias
      .update('VehicleDisposal') // Provide the table name
      .set({
        disposalTypes: disposalTypedata,
        disposalReasons: disposalReasondata,
        description: updateVehicleDisposalDto.description,
        requestStatus: datastatusType,
        approvalLevel: nextapprovalLevelData,
      })
      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();
    // return 'this is logistiocds';
    const updateVehicle = await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .where('vehicleDisposal.id = :id', {
        id,
      })

      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicleDisposal.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.vehicleRegRequest', 'vehicleRegRequest')
      // .leftJoinAndSelect('vehicle.VehicleAcquisition', 'VehicleAcquisition')
      .leftJoinAndSelect('vehicle.ownershipType', 'ownershipType')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .leftJoinAndSelect('vehicle.vehicleManufacture', 'vehicleManufacture')
      .leftJoinAndSelect('vehicle.vehicleModel', 'vehicleModel')
      .leftJoinAndSelect('vehicle.vehicleStatus', 'vehicleStatus')
      // .leftJoinAndSelect('vehicle.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('vehicle.registrationStatus', 'registrationStatus')
      .getOne();

    return updateVehicle;
  }

  // deleting vehicle disposal
  async deleteDisposal(id: string) {
    return await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .where('vehicleDisposal.id = :id', {
        id,
      })
      .delete()
      .execute();
  }

  // submitting disposal report
  // Creating auction report
  async AuctionReport(createAuctionReportDto: CreateAuctionReportDto) {
    const disposalId = createAuctionReportDto.disposalId;
    const previousData = await this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      .where('auctionReport.disposal = :disposalId', {
        disposalId,
      })
      // .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel')
      .getOne();
    if (previousData) {
      return {
        message:
          'Auction report request for this disposal has already been submitted',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    // let vehicleData;
    // let disposalTypedata;
    // let disposalReasondata;
    let statusdata;
    let approvalData;

    const disposaldata = await this.vehicleDisposalManagerRepository
      .createQueryBuilder('vehicleDisposal')
      .where('vehicleDisposal.id = :disposalId', {
        disposalId,
      })
      .leftJoinAndSelect('vehicleDisposal.disposalTypes', 'disposalType')
      .leftJoinAndSelect('vehicleDisposal.disposalReasons', 'disposalReason')
      .leftJoinAndSelect('vehicleDisposal.requestStatus', 'requestStatus')
      .leftJoinAndSelect('vehicleDisposal.approvalLevel', 'approvalLevel')
      .getOne();
    if (!disposaldata) {
      return {
        message: 'disposal data not found in database',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    const disposalStatus = disposaldata.requestStatus.name;
    if (disposalStatus != 'APPROVED') {
      return {
        message: 'disposal Request not Approved',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    // console.log(disposaldata);

    // statutaus leval
    try {
      statusdata = await this.statusTypeRepository.findOne({
        name: 'PENDING',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    try {
      approvalData = await this.approvalLevelRepository.findOne({
        code: 'ICBM2',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'Approval level with ICBM2 as code not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching approval leval data .',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    const auctionReportDisposalDTO = new AuctionReport({
      ...createAuctionReportDto,
      disposal: disposaldata,

      buyer_idNumber: createAuctionReportDto.buyer_idNumber,
      buyer_FirstName: createAuctionReportDto.buyer_FirstName,
      buyer_LastName: createAuctionReportDto.buyer_LastName,
      buyer_tinNumber: createAuctionReportDto.buyer_tinNumber,
      sale_amount: createAuctionReportDto.sale_amount,
      valuation_amount: createAuctionReportDto.valuation_amount,
      description: createAuctionReportDto.description,
      requestStatus: statusdata,
      approvalLevel: approvalData,
      // projectExtensionDate: vehicleRegistrationDto.projectExtensionDate,
    });
    const submittedReport = await this.auctionReportRepository.create(
      auctionReportDisposalDTO,
    );
    if (submittedReport) {
      await this.vehicleDisposalManagerRepository
        .createQueryBuilder() // Use query builder without alias
        .update('VehicleDisposal') // Provide the table name
        .set({
          isActionReportsubmitted: true,
        })
        .where('id = :disposalId', { disposalId }) // Assuming id is the primary key column
        .execute();
    }
    return submittedReport;
  }
  // getting all auction reports
  async getAuctionReports() {
    return await this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      // .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel')
      .getMany();
  }
  // getting reports by filter and search
  async getAuctionBySearchAndFilter(filters: GetDisposalReportByFilterDto) {
    console.log(filters);
    const query = this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      // .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel');

    if (filters.buyer_idNumber) {
      query.andWhere('auctionReport.buyer_idNumber = :buyer_idNumber', {
        buyer_idNumber: filters.buyer_idNumber,
      });
    }
    if (filters.buyer_FirstName) {
      query.andWhere('auctionReport.buyer_FirstName = :buyer_FirstName', {
        buyer_FirstName: filters.buyer_FirstName,
      });
    }

    if (filters.buyer_LastName) {
      query.andWhere('auctionReport.buyer_LastName = :buyer_LastName', {
        buyer_LastName: filters.buyer_LastName,
      });
    }
    if (filters.buyer_tinNumber) {
      query.andWhere('auctionReport.buyer_tinNumber = :buyer_tinNumber', {
        buyer_tinNumber: filters.buyer_tinNumber,
      });
    }
    if (filters.requestStatus) {
      query.andWhere('requestStatus.name = :requestStatus', {
        requestStatus: filters.requestStatus,
      });
    }
    if (filters.approvalLeval) {
      query.andWhere('approvalLevel.code = :approvalLeval', {
        approvalLeval: filters.approvalLeval,
      });
    }
    if (filters.createdDateStart && filters.createdDateEnd) {
      query.andWhere(
        'auctionReport.createddate BETWEEN :createdDateStart AND :createdDateEnd',
        {
          createdDateStart: filters.createdDateStart,
        },
      );
    } else if (filters.createdDateStart) {
      query.andWhere('auctionReport.createddate >= :createdDateStart', {
        createdDateStart: filters.createdDateStart,
      });
    } else if (filters.createdDateEnd) {
      query.andWhere('auctionReport.createddate <= :createdDateEnd', {
        createdDateEnd: filters.createdDateEnd,
      });
    }

    if (filters.submitedDateStart && filters.submitedDateEnd) {
      query.andWhere(
        'auctionReport.submittedDate BETWEEN :submitedDateStart AND :submitedDateStart',
        {
          submitedDateStart: filters.submitedDateStart,
          submitedDateEnd: filters.submitedDateEnd,
        },
      );
    } else if (filters.submitedDateStart) {
      query.andWhere('auctionReport.submittedDate >= :submitedDateStart', {
        submitedDateStart: filters.submitedDateStart,
      });
    } else if (filters.submitedDateEnd) {
      query.andWhere('auctionReport.submittedDate <= :submitedDateEnd', {
        submitedDateEnd: filters.submitedDateEnd,
      });
    }

    if (filters.approvalDateStart && filters.approvedDateEnd) {
      query.andWhere(
        'auctionReport.approveddate BETWEEN :approvalDateStart AND :approvedDateEnd',
        {
          approvalDateStart: filters.approvalDateStart,
          approvedDateEnd: filters.approvedDateEnd,
        },
      );
    } else if (filters.approvalDateStart) {
      query.andWhere('auctionReport.approveddate >= :approvalDateStart', {
        approvalDateStart: filters.approvalDateStart,
      });
    } else if (filters.approvedDateEnd) {
      query.andWhere('auctionReport.approveddate <= :approvedDateEnd', {
        approvedDateEnd: filters.approvedDateEnd,
      });
    }
    // search buy string
    if (filters.searchString) {
      const trimmedSearchString = filters.searchString.trim();
      const lowerCaseSearchString = `%${trimmedSearchString.toLowerCase()}%`;

      query
        // .where('vehicleAcquisition.id LIKE :searchString', {
        //   searchString: lowerCaseSearchString,
        // })
        // .orWhere('auctionReport.buyer_idNumber LIKE :searchString', {
        //   searchString: lowerCaseSearchString,
        // })
        .orWhere('LOWER(auctionReport.buyer_FirstName) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        .orWhere('LOWER(auctionReport.buyer_LastName) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        })
        // .orWhere('auctionReport.buyer_tinNumber LIKE :searchString', {
        //   searchString: lowerCaseSearchString,
        // })
        .orWhere('LOWER(requestStatus.name ) LIKE :searchString', {
          searchString: lowerCaseSearchString,
        });
    }

    const [auctionReport, count] = await query.getManyAndCount();
    return { auctionReport, count };
  }
  // getting auction report by id
  async getAuctionReportById(id: string) {
    return await this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      .where('auctionReport.id = :id', {
        id,
      })
      // .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel')
      .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('disposal.vehicle', 'vehicle')
      .getOne();
  }
  // getting auction report by disposal filter and search
  // VehicleDisposal
  //
  async updateAuctionReport(
    id: string,
    updateAuctionReportDto: UpdateAuctionReportDto,
  ) {
    let datastatusType;
    const auctionReportData = await this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      .where('auctionReport.id = :id', {
        id,
      })
      // .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel')
      .getOne();
    if (!auctionReportData) {
      return {
        message: 'report  data not found in database',
        statusCode: HttpStatus.NOT_FOUND,
      };
    }
    // console.log(auctionReportData);
    const approvalLevelcode = auctionReportData.approvalLevel.code;
    const approvalLevelIndex =
      this.aquistionApprovalService.findIndexByApprovalLevel(approvalLevelcode);
    const nextapprovalLevelIndex = (await approvalLevelIndex) + 1;

    const nextapprovalLevelcode =
      await this.aquistionApprovalService.findApprovalLevelByIndex(
        nextapprovalLevelIndex,
      );
    if (nextapprovalLevelcode === 'IL1') {
      // geting status data

      try {
        datastatusType = await this.statusTypeRepository.findOne({
          name: 'PENDING',
        });
      } catch (error) {
        if (error.message === 'Entity not found.') {
          return {
            message: 'PENDING status type not found in database',
            statusCode: HttpStatus.NOT_FOUND,
          };
        } else {
          return {
            message:
              error.message ||
              'An error occurred while fetching status data type.',
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          };
        }
      }

      await this.auctionReportManagerRepository
        .createQueryBuilder() // Use query builder without alias
        .update('auctionReport') // Provide the table name
        .set({
          approvalLevel: nextapprovalLevelcode,
          requestStatus: datastatusType,
          buyer_idNumber: updateAuctionReportDto.buyer_idNumber,
          buyer_FirstName: updateAuctionReportDto.buyer_FirstName,
          buyer_LastName: updateAuctionReportDto.buyer_LastName,
          buyer_tinNumber: updateAuctionReportDto.buyer_tinNumber,
          sale_amount: updateAuctionReportDto.sale_amount,
          valuation_amount: updateAuctionReportDto.valuation_amount,
          description: updateAuctionReportDto.description,
        })
        .where('id = :id', { id }) // Assuming id is the primary key column
        .execute();
      // return 'this is logistiocds';
    }

    try {
      datastatusType = await this.statusTypeRepository.findOne({
        name: 'PROGRESS',
      });
    } catch (error) {
      if (error.message === 'Entity not found.') {
        return {
          message: 'PENDING status type not found in database',
          statusCode: HttpStatus.NOT_FOUND,
        };
      } else {
        return {
          message:
            error.message ||
            'An error occurred while fetching status data type.',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    }
    await this.auctionReportManagerRepository
      .createQueryBuilder() // Use query builder without alias
      .update('auctionReport') // Provide the table name
      .set({
        approvalLevel: nextapprovalLevelcode,
        requestStatus: datastatusType,
        buyer_idNumber: updateAuctionReportDto.buyer_idNumber,
        buyer_FirstName: updateAuctionReportDto.buyer_FirstName,
        buyer_LastName: updateAuctionReportDto.buyer_LastName,
        buyer_tinNumber: updateAuctionReportDto.buyer_tinNumber,
        sale_amount: updateAuctionReportDto.sale_amount,
        valuation_amount: updateAuctionReportDto.valuation_amount,
        description: updateAuctionReportDto.description,
      })
      .where('id = :id', { id }) // Assuming id is the primary key column
      .execute();

    const auctionReport = await this.auctionReportManagerRepository
      .createQueryBuilder('auctionReport')
      .where('auctionReport.id = :id', {
        id,
      })
      .leftJoinAndSelect('auctionReport.disposal', 'disposal')
      .leftJoinAndSelect('auctionReport.requestStatus', 'requestStatus')
      .leftJoinAndSelect('auctionReport.approvalLevel', 'approvalLevel')
      .getOne();

    return auctionReport;
  }
}
