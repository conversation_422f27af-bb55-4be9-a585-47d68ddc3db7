// import { NestFactory } from '@nestjs/core';
// import { FleetMgmntModule } from './fleet-mgmnt.module';
// import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

// async function bootstrap() {
//   const app = await NestFactory.create(FleetMgmntModule);
//   await app.listen(4003);
// }
// bootstrap();

import { NestFactory } from '@nestjs/core';
import { FleetMgmntModule } from './fleet-mgmnt.module';
// import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(FleetMgmntModule);
  // const configService = app.get(ConfigService);
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      // port: 4003,
      // port: configService.get('TCP_PORT'),
    },
  });
  await app.startAllMicroservices();

  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

  // -- Cors setup
  app.enableCors({
    origin: '*',
  });
  // -- Swagger Documentation
  const config = new DocumentBuilder()
    .setTitle('API vehicle management  to manage Fleet MIS.')
    .setDescription(
      'API to perform all activities related to vehicle management',
    )
    .addBasicAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('fleet-mgmnt/docs', app, document);

  // -- Port listening
  // await app.listen(configService.get('HTTP_PORT'));
  await app.listen(4003);
}

bootstrap();
