import { AbstractEntity } from '@app/common';
import {
  BeforeInsert,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { VehicleRegRequest } from './vehicle-reg-request';
import { VehicleType } from '../vehicle-management /vehicle-types.entity';
import { OwnershipType } from '../vehicle-management /vehicle-ownership.entity';
import { VehicleManufacture } from '../vehicle-management /vehicles-manifacturer.entity';
import { VehicleModel } from '../vehicle-management /vehicle-model.entity';
import { StatusType } from '../vehicle-management /status.entity';
import { ApprovalLevel } from '../vehicle-management /approvel-level.entity';
import { VehicleStatus } from '../vehicle-management /vehicle-status.entity';

// import { User } from './User';

@Entity()
export class RegisteredVehicle extends AbstractEntity<RegisteredVehicle> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @ManyToOne(
    () => VehicleRegRequest,
    (vehicleRegRequest) => vehicleRegRequest.registeredVehicle, // Add this line
  )
  vehicleRegRequest: VehicleRegRequest;
  @Column({ nullable: true })
  beneficiaryAgencyId: string;
  @Column({ nullable: true })
  beneficiaryAgency: string;
  // @ManyToOne(() => OwnershipType)
  @Column({ nullable: true })
  reportingInstitution: string;

  @Column({ nullable: true })
  reportingInstitutionId: string;

  @ManyToOne(() => OwnershipType)
  ownershipType: OwnershipType;
  @Column({ nullable: true })
  fuelType: string | null;

  @ManyToOne(() => VehicleType)
  vehicleType: VehicleType;
  @ManyToOne(() => VehicleManufacture)
  vehicleManufacture: VehicleManufacture;
  @ManyToOne(() => VehicleModel, { nullable: true })
  vehicleModel: VehicleModel;
  @Column({ unique: true, nullable: true })
  chassisNumber: string;
  @Column({ nullable: true })
  engineNumber: string;
  //Manufacturing Year
  @Column({ nullable: true })
  transmissionType: string;
  //Manufacturing Year
  @Column({ type: 'date' })
  manufacturingYear: Date;
  //Odometer Reading
  @Column({ nullable: true })
  odometerReading: string;
  //Acquisition details
  @Column({ type: 'date', nullable: true })
  acquisitionDate: Date;
  //Invoice number of the distributor
  @Column({ nullable: true })
  invoiceNumber: string;
  //Invoice Date
  @Column({ type: 'date', nullable: true })
  invoiceDate: Date;
  //Customs declaration number for MVF
  @Column({ nullable: true })
  customsDeclarationNumber: string;
  //Declaration Date
  @Column({ type: 'date', nullable: true })
  customsDeclarationDate: Date;
  // Declared amount
  @Column({ nullable: true })
  declaredAmount: number;

  // project detail in case is GP
  @Column({ nullable: true })
  projectName: string;
  @Column({ type: 'date', nullable: true })
  projectStartDate: Date;

  @Column({ type: 'date', nullable: true })
  projectEndDate: Date;

  @Column({ nullable: true })
  projectDescription: string;

  @Column({ type: 'date', nullable: true })
  projectExtensionDate: Date;
  // pickcard number
  @Column({ nullable: true, unique: true })
  pickCardNumber: string;
  // plate number
  @Column({ nullable: true, unique: true })
  plateNumber: string;
  // registration status ,is vehicle registered
  @ManyToOne(() => StatusType)
  registrationStatus: StatusType;
  @ManyToOne(() => ApprovalLevel)
  approvalLevel: ApprovalLevel;
  // vehicle status (actioned,returned,Good condition,Bad condition)
  @ManyToOne(() => VehicleStatus)
  vehicleStatus: VehicleStatus;
  @Column({ default: true })
  isVehicleActive: boolean;
  @Column({ default: false })
  isDisposalRequestSubmitted: boolean;
  @Column({ default: true })
  isProjectOnGoing: boolean;
  @Column({ default: false })
  isTimeToReturnProjectVehicle: boolean;
  @Column({ type: 'date', nullable: true })
  createddate: Date | null;

  @Column({ type: 'date', nullable: true })
  submittedDate: Date | null;
  @Column({ type: 'date', nullable: true })
  approveddate: Date | null;
  @BeforeInsert()
  updateSubmittedDate() {
    this.createddate = new Date();
  }
  @BeforeInsert()
  setDefaultExtensionDate() {
    // Set projectExtensionDate to projectEndDate by default
    this.projectExtensionDate = this.projectEndDate;
  }
  // field related to quotely  report
  @Column({ default: true })
  isQuarterlyReportSubmited: boolean;
  @Column({ default: false })
  isoverdueQuarterlyReportSubmition: boolean;
  @Column({ type: 'date', nullable: true })
  lastQuarterlyReportsubmittedDate: Date | null;
  @BeforeInsert()
  updatelastQuarterlyReportsubmittedDate() {
    this.lastQuarterlyReportsubmittedDate = new Date();
  }

  @Column({ type: 'date', nullable: true })
  startNextQuarterlyReportsubmittedDate: Date | null;
  @BeforeInsert()
  updatestartNextQuarterlyReportsubmittedDate() {
    this.startNextQuarterlyReportsubmittedDate = new Date();
  }
  @Column({ type: 'date', nullable: true })
  endNextQuarterlyReportsubmittedDate: Date | null;
  @BeforeInsert()
  updateendNextQuarterlyReportsubmittedDate() {
    this.endNextQuarterlyReportsubmittedDate = new Date();
  }
  @Column({ default: false })
  isrecordedFromOldVehicle: boolean;
}
