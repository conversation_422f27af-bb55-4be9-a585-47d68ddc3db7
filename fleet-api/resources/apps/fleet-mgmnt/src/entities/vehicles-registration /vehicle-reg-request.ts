import { AbstractEntity } from '@app/common';

import {
  BeforeInsert,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { RegisteredVehicle } from './registred-vehicle';
import { VehicleAcquisition } from '../vehicle-acquisition/vehicle-acquisition.entity';
@Entity()
export class VehicleRegRequest extends AbstractEntity<VehicleRegRequest> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column()
  userId: string;
  @Column()
  institutionId: string;
  @Column()
  institution: string;
  @ManyToOne(() => VehicleAcquisition, { nullable: true })
  VehicleAcquisition: VehicleAcquisition | null;

  @OneToMany(
    () => RegisteredVehicle,
    (registeredVehicle) => registeredVehicle.vehicleRegRequest,
  )
  registeredVehicle: RegisteredVehicle[];
  @Column({ type: 'date', nullable: true })
  createddate: Date | null;
  @BeforeInsert()
  setCreationDate() {
    this.createddate = new Date(); // Set createddate to current date
  }
}
