import { AbstractEntity } from '@app/common';

import {
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  BeforeInsert,
  PrimaryGeneratedColumn,
} from 'typeorm';

// import { User } from 'apps/auth/src/entities/user-management/user.entity';

import { RequestedVehicle } from './requested-vehicle.entity';
import { RequestType } from '../vehicle-management /requestType.entity';
import { OwnershipType } from '../vehicle-management /vehicle-ownership.entity';

import { StatusType } from '../vehicle-management /status.entity';
import { ApprovalLevel } from '../vehicle-management /approvel-level.entity';

@Entity()
export class VehicleAcquisition extends AbstractEntity<VehicleAcquisition> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column()
  userId: string;
  @ManyToOne(() => RequestType)
  requestType: RequestType;

  @ManyToOne(() => OwnershipType)
  ownershipType: OwnershipType;

  @Column()
  institutionId: string;
  @Column()
  institution: string;

  @ManyToOne(() => StatusType)
  statusType: StatusType;

  @ManyToOne(() => ApprovalLevel)
  approvalLevel: ApprovalLevel;

  @Column({ nullable: true })
  description?: string;

  @OneToMany(
    () => RequestedVehicle,
    (requestedVehicle) => requestedVehicle.acquisition,
  )
  requestedVehicles: RequestedVehicle[];
  @Column({ type: 'date', nullable: true })
  createddate: Date | null;

  @Column({ type: 'date', nullable: true })
  submittedDate: Date | null;
  @Column({ type: 'date', nullable: true })
  approveddate: Date | null;
  @Column({ default: false })
  isAllVehicleregrequestSubmitted: boolean;
  @BeforeInsert()
  updateSubmittedDate() {
    this.createddate = new Date();
  }
}
