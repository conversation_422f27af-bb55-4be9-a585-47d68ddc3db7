import { AbstractEntity } from '@app/common';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { VehicleAcquisition } from './vehicle-acquisition.entity';
// import { VehicleType } from '../vehicle-management /vehicle-types.entity';

// import { User } from './User';

@Entity()
export class VehilceNumberPerAccquisition extends AbstractEntity<VehilceNumberPerAccquisition> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @ManyToOne(
    () => VehicleAcquisition,
    (acquisition) => acquisition.requestedVehicles,
  )
  acquisition: VehicleAcquisition;

  @Column({ default: 0 })
  numberOfRequestedVehicle: number;
  @Column({ default: 0 })
  numberOfRegistredVehicle: number;
}
