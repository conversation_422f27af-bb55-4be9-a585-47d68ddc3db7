import { AbstractEntity } from '@app/common';

import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { VehicleAcquisition } from '../vehicle-acquisition/vehicle-acquisition.entity';
import { RequestedVehicle } from '../vehicle-acquisition/requested-vehicle.entity';
@Entity()
export class HiringCost extends AbstractEntity<HiringCost> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column()
  hiringCost: number;
  @ManyToOne(() => VehicleAcquisition, { nullable: true })
  VehicleAcquisition: VehicleAcquisition | null;
  @ManyToOne(() => RequestedVehicle, { nullable: true })
  RequestedVehicle: RequestedVehicle | null;
}
