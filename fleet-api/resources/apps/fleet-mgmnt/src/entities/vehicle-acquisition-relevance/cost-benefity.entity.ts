import { AbstractEntity } from '@app/common';

import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { VehicleAcquisition } from '../vehicle-acquisition/vehicle-acquisition.entity';
import { RequestedVehicle } from '../vehicle-acquisition/requested-vehicle.entity';
@Entity()
export class CostBenefit extends AbstractEntity<CostBenefit> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column({ nullable: false })
  acquisitionCost: number;
  @Column()
  maintenanceCost: number;
  @Column()
  fuelCost: number;
  @Column()
  oilRefillingCost: number;
  @Column()
  insuranceCost: number;
  @Column()
  driveCost: number;
  @Column({ nullable: true, default: null })
  depreciationCost: number;
  @ManyToOne(() => VehicleAcquisition, { nullable: true })
  VehicleAcquisition: VehicleAcquisition | null;

  @ManyToOne(() => RequestedVehicle, { nullable: true })
  RequestedVehicle: RequestedVehicle | null;
}
