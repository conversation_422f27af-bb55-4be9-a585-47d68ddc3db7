import { AbstractEntity } from '@app/common';
import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

@Entity()
@Unique(['name']) // Ensure 'name' is unique
export class ActivityOnVehicle extends AbstractEntity<ActivityOnVehicle> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
  @Column({ nullable: false }) // Ensure 'name' is not nullable
  name: string;
  @Column({ nullable: true }) // Ensure 'name' is not nullable
  code: string;
}
