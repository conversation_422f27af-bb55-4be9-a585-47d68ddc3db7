import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { MinioService } from 'nestjs-minio-client';
import { BufferedFile } from './file.model';
// import * as crypto from 'crypto';
@Injectable()
export class MinioClientService {
  private readonly logger: Logger;
  private readonly baseBucket = process.env.MINIO_BUCKET_NAME;
  private readonly minioPubicIP = process.env.MINIO_ENDPOINT;

  public get client() {
    return this.minio.client;
  }

  constructor(private readonly minio: MinioService) {
    this.logger = new Logger('MinioStorageService');
  }

  public async upload(
    file: BufferedFile,
    baseBucket: string = this.baseBucket,
  ) {
    if (
      !file.mimetype ||
      !['image/png', 'image/jpeg', 'application/pdf'].includes(file.mimetype)
    ) {
      throw new HttpException('Error uploading file', HttpStatus.BAD_REQUEST);
    }

    const addtional_string = Date.now().toString();
    console.log(addtional_string);

    // let hashedFileName = crypto.createHash('md5').update(temp_filename).digest("hex");
    const ext = file.originalname.substring(
      file.originalname.lastIndexOf('.'),
      file.originalname.length,
    );
    const root_filename = file.originalname.replace(ext, '');
    const metaData = {
      'Content-Type': file.mimetype,
    };
    // let filename = hashedFileName + ext
    const filename = root_filename + '_' + addtional_string + ext;
    const fileName: string = `${filename}`;
    // console.log(fileName);

    const fileBuffer = file.buffer;
    try {
      this.client.putObject(
        baseBucket,
        fileName,
        fileBuffer,
        undefined,
        metaData,
      );
    } catch (error) {
      console.error(`Error uploading file '${fileName}':`, error);
      throw new HttpException('Error uploading file', HttpStatus.BAD_REQUEST);
    }

    // const urtext: string = `${process.env.MINIO_ENDPOINT}:${process.env.MINIO_PORT}/${process.env.MINIO_BUCKET}/${filename}`;
    // console.log(filename);

    return {
      fileName: `${filename}`,
      url: `${process.env.MINIO_ENDPOINT}/${process.env.MINIO_PORT}/${process.env.MINIO_BUCKET_NAME}/${filename}`,
      // url: `${config.MINIO_ENDPOINT}:${config.MINIO_PORT}/${config.GET_FILE_ENDPOINT}/${filename}`
    };
  }
  public getFileUrl(
    fileName: string,
    baseBucket: string = this.baseBucket,
  ): string {
    const endpoint = this.minioPubicIP; // Replace with your MinIO server's endpoint
    const url = `${endpoint}/${baseBucket}/${fileName}`;
    return url;
  }

  public async deleteFile(
    fileName: string,
    baseBucket: string = this.baseBucket,
  ) {
    try {
      await this.client.removeObject(baseBucket, fileName);
      console.log(`File '${fileName}' deleted successfully.`);
    } catch (error) {
      console.error(`Error deleting file '${fileName}':`, error);
      throw new HttpException('Error deleting file', HttpStatus.BAD_REQUEST);
    }
  }
}
