import { Modu<PERSON> } from '@nestjs/common';
import { DocumentsController } from './documents.controller';
import { DocumentsService } from './documents.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DocumentsManagementModule } from './documents-management/documents-management.module';
import { MinioClientModule } from './minio-client/minio-client.module';
import { DatabaseModule } from '@app/common';

import { FLEETMGMNT_SERVICE } from '@app/common/constants';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { DocumentUpload } from '../entities/documentUpload.entity';

@Module({
  imports: [
    DocumentsManagementModule,
    DatabaseModule.forFeature([DocumentUpload]),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MinioClientModule,

    ClientsModule.registerAsync([
      {
        name: FLEETMGMNT_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('APPLICATIONS_HOST'),
            port: configService.get('APPLICATIONS_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [DocumentsController],
  providers: [DocumentsService],
})
export class DocumentsModule {}
