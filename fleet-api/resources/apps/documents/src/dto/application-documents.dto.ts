import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateApplicationDocumentDto {
  @IsNotEmpty({ message: 'The field Url cannot be empty' })
  fileUrl: string;

  @IsNotEmpty({ message: 'application ID is required' })
  applicationid: string;

  @IsOptional() // Make documentsDescription optional
  documentsDescription?: string;

  @IsNotEmpty({ message: 'required document ID is required' })
  // RequiredDocumentid: string;

  // @IsNotEmpty({ message: 'file name is required' })
  fileName: string;

  @IsNotEmpty({ message: 'created at is required' })
  createdAt: Date;
}
