import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { ApplicationDocument } from '../entities/application-documents.entity';
import { AbstractRepository } from '@app/common';

@Injectable()
export class ApplicationDocumentRepository extends AbstractRepository<ApplicationDocument> {
  constructor(
    @InjectRepository(ApplicationDocument)
    applicationDocumentRepository: Repository<ApplicationDocument>,
    entityManager: EntityManager,
  ) {
    super(entityManager, applicationDocumentRepository);
  }
}
