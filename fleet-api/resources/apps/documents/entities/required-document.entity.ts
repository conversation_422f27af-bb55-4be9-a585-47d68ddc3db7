import { AbstractEntity } from '@app/common';
import { Column, Entity } from 'typeorm';
import { IsNotEmpty, IsOptional } from 'class-validator';

@Entity()
export class RequiredDocument extends AbstractEntity<RequiredDocument> {
  /**
   * this decorator will help to auto generate id for the table.
   */
  @Column({ nullable: false })
  @IsNotEmpty()
  name: string;

  @Column({ nullable: true })
  @IsOptional()
  permitTypeId?: string; // will be foreign key for PermitType  entity
  @Column({ nullable: true })
  @IsOptional()
  documentTypeId?: string; // will be foreign key for DocumentType entity
  @Column({ nullable: true })
  @IsOptional()
  categoryId?: string; // will be foreign key for CategoryType entity
}
