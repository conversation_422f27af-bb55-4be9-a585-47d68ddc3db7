import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity()
export class DocumentUpload {
  /**
   * this decorator will help to auto generate id for the table.
   */
  @PrimaryGeneratedColumn('uuid')
  public id!: string;

  @Column({ type: 'varchar', nullable: true })
  documentDescription: string;

  @Column()
  applicationId: string;

  @Column({ default: true })
  isValid: boolean;

  // @Column({ nullable: true })
  // documentStatusId: string;

  @Column({ default: '0' })
  documentStatusId: string;

  @Column()
  fileUrl: string;

  @Column()
  fileName: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
}
