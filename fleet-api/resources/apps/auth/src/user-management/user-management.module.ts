import { Module } from '@nestjs/common';
import { UserManagementController } from './user-management.controller';
import { UserManagementService } from './user-management.service';
import { DatabaseModule } from '@app/common';

import { Role } from 'apps/auth/src/entities/user-management/role.entity';
import { UserType } from 'apps/auth/src/entities/user-management/userType.entity';
// import { UserActivity } from 'apps/auth/src/entities/user-management/userActivity.entity';
// import { CivilStatus } from 'apps/auth/src/entities/user-management/civilStatus.entity';
import { Permission } from 'apps/auth/src/entities/user-management/permission.entity';
// import { Country } from 'apps/auth/src/entities/delimitation/country.entity';
import { Province } from 'apps/auth/src/entities/delimitation/province.entity';
import { District } from 'apps/auth/src/entities/delimitation/district.entity';
import { Sector } from 'apps/auth/src/entities/delimitation/sector.entity';
import { Cell } from 'apps/auth/src/entities/delimitation/cell.entity';
import { Village } from 'apps/auth/src/entities/delimitation/village.entity';
import { Institution } from 'apps/auth/src/entities/institution/institution.entity';
import { InstitutionLocation } from 'apps/auth/src/entities/institution/institutionLocation.entity';
import {
  InstitutionLocationRepository,
  InstitutionRepository,
  CellRepository,
  // CivilStatusRepository,
  // CountryRepository,
  DistrictRepository,
  ProvinceRepository,
  RoleRepository,
  SectorRepository,
  // UserActivityRepository,
  UserTypeRepository,
  UserRepository,
  VillageRepository,
  NonBudgetedAgenciesRepository,
} from './user-management.repository';
import { User } from '../entities/user-management/user.entity';
import { NonBudgetedAgencies } from '../entities/nonBudget-agencies/nonBudget-agencies.entity';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      User,

      Role,
      UserType,
      // UserActivity,
      // CivilStatus,
      Permission,

      // Country,
      Province,
      District,
      Sector,
      Cell,
      Village,

      Institution,
      InstitutionLocation,
      NonBudgetedAgencies,
    ]),
  ],
  controllers: [UserManagementController],
  providers: [
    UserManagementService,

    UserRepository,

    RoleRepository,
    // UserActivityRepository,
    UserTypeRepository,
    // CivilStatusRepository,
    // PermissionRepository,

    // CountryRepository,
    ProvinceRepository,
    DistrictRepository,
    SectorRepository,
    CellRepository,
    VillageRepository,

    InstitutionLocationRepository,
    InstitutionRepository,
    NonBudgetedAgenciesRepository,
  ],
  exports: [UserManagementService],
})
export class UserManagementModule {}
