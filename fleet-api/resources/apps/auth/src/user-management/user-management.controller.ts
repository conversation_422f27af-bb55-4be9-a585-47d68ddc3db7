import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  // UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { UserManagementService } from './user-management.service';
import {
  // CivilStatusDto,
  // PermissionDto,
  RoleDto,
  // UpdateCivilStatusDto,
  // UpdatePermissionDto,
  UpdateRoleDto,
  // UpdateUserActivityDto,
  UpdateUserTypeDto,
  // UserActivityDto,
  UserTypeDto,
} from '../dto/user-management.dto';
import {
  CellDto,
  // CountryDto,
  DistrictDto,
  ProvinceDto,
  SectorDto,
  VillageDto,
} from '../dto/delimitation.dto';
import {
  InstitutionLocationDto,
  InstitutionDto,
  NonBudgetedAgenciesDto,
} from '../dto/institution.dto';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ResponseError, ResponseSuccess } from '@app/common/dto/response.dto';
import { IResponse } from '@app/common/interfaces';
import { UserProfileDto } from '../dto/user.dto';

@ApiTags('user-management')
@Controller('user-management')
export class UserManagementController {
  constructor(private readonly userManagementService: UserManagementService) {}

  @Get('users')
  async findAllUsers() {
    return this.userManagementService.findAllUsers();
  }

  @Get('user/:email')
  async findById(@Param() params): Promise<IResponse> {
    try {
      const user = await this.userManagementService.findUserByEmail(
        params.email,
      );
      return new ResponseSuccess('COMMON.SUCCESS', user);
    } catch (error) {
      return new ResponseError('COMMON.ERROR.GENERIC_ERROR', error);
    }
  }
  // getting all users by institution id
  // @Get('institution-users/:institutionId')
  // async findAllUsersByInstitutionId(@Param() params): Promise<IResponse> {
  //   try {
  //     const users =
  //       await this.userManagementService.findAllUsersByInstitutionId(
  //         params.institutionId,
  //       );
  //     return new ResponseSuccess('COMMON.SUCCESS', users);
  //   } catch (error) {
  //     return new ResponseError('COMMON.ERROR.GENERIC_ERROR', error);
  //   }
  // }
  @Get('institution-users/:institutionId')
  async findAllUsersByInstitutionId(
    @Param('institutionId') institutionId: string,
  ): Promise<IResponse> {
    try {
      const users =
        await this.userManagementService.findAllUsersByInstitutionId(
          institutionId,
        );
      return new ResponseSuccess('COMMON.SUCCESS', users);
    } catch (error) {
      return new ResponseError('COMMON.ERROR.GENERIC_ERROR', error);
    }
  }

  @Post('profile/update')
  async updateProfile(@Body() profileDto: UserProfileDto): Promise<IResponse> {
    try {
      const user = await this.userManagementService.updateProfile(profileDto);
      return new ResponseSuccess('PROFILE.UPDATE_SUCCESS', user);
    } catch (error) {
      return new ResponseError('PROFILE.UPDATE_ERROR', error);
    }
  }

  @ApiTags('user-management')
  // Roles
  @Post('role')
  async createRole(@Body() roleDto: RoleDto) {
    return this.userManagementService.createRole(roleDto);
  }

  @ApiTags('user-management')
  @Get('roles')
  async findAll() {
    return this.userManagementService.findAllRole();
  }

  @ApiTags('user-management')
  @Get('role/:id')
  async findOne(@Param('id') id: string) {
    return this.userManagementService.findOneRole(id);
  }

  @ApiTags('user-management')
  @Patch('role/:id')
  async update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.userManagementService.updateRole(id, updateRoleDto);
  }

  @ApiTags('user-management')
  @Delete('role/:id')
  async remove(@Param('id') id: string) {
    return this.userManagementService.removeRole(id);
  }

  // @ApiTags('user-management')
  // // User Activities
  // @Post('userActivity')
  // async CreateUserActivity(@Body() userActivityDto: UserActivityDto) {
  //   return this.userManagementService.createUserActivity(userActivityDto);
  // }

  // @ApiTags('user-management')
  // @Get('userActivities')
  // async findAllActivities() {
  //   return this.userManagementService.findAllUserActivities();
  // }

  // @Get('userActivity/:id')
  // async findOneUserActivity(@Param('id') id: string) {
  //   return this.userManagementService.findOneUserActivity(id);
  // }

  // @Patch('userActivity/:id')
  // async updateUserActivity(
  //   @Param('id') id: string,
  //   @Body() updateUserActivityDto: UpdateUserActivityDto,
  // ) {
  //   return this.userManagementService.updateUserActivity(
  //     id,
  //     updateUserActivityDto,
  //   );
  // }

  // @Delete('userActivity/:id')
  // async removeUserActivity(@Param('id') id: string) {
  //   return this.userManagementService.removeUserActivity(id);
  // }

  // User Type
  @ApiTags('user-management')
  @Post('userTypes')
  async CreateUserType(@Body() userTypeDto: UserTypeDto) {
    return this.userManagementService.createUserType(userTypeDto);
  }

  @ApiTags('user-management')
  @Get('userType/:id')
  async findOneUserType(@Param('id') id: string) {
    return this.userManagementService.findOneUserType(id);
  }

  @ApiTags('user-management')
  @Patch('userType/:id')
  async updateUserType(
    @Param('id') id: string,
    @Body() updateUserTypeDto: UpdateUserTypeDto,
  ) {
    return this.userManagementService.updateUserType(id, updateUserTypeDto);
  }

  @ApiTags('user-management')
  @Delete('userType/:id')
  async removeUserType(@Param('id') id: string) {
    return this.userManagementService.removeUserType(id);
  }

  // @ApiTags('user-management')
  // // Civil Status
  // @Post('civilStatus')
  // async CreateCivilStatus(@Body() civilStatusDto: CivilStatusDto) {
  //   return this.userManagementService.createCivilStatus(civilStatusDto);
  // }

  // @ApiTags('user-management')
  // @Get('civilStatus')
  // async findAllCivilStatus() {
  //   return this.userManagementService.findAllCivilStatus();
  // }

  // @ApiTags('user-management')
  // @Get('civilStatus/:id')
  // async findOneCivilStatus(@Param('id') id: string) {
  //   return this.userManagementService.findOneCivilStatus(id);
  // }

  // @ApiTags('user-management')
  // @Patch('civilStatus/:id')
  // async updateCivilStatus(
  //   @Param('id') id: string,
  //   @Body() updateCivilStatusDto: UpdateCivilStatusDto,
  // ) {
  //   return this.userManagementService.updateCivilStatus(
  //     id,
  //     updateCivilStatusDto,
  //   );
  // }

  // @ApiTags('user-management')
  // @Delete('civilStatus/:id')
  // async removeCivilStatus(@Param('id') id: string) {
  //   return this.userManagementService.removeCivilStatus(id);
  // }

  @ApiTags('user-management')
  @Post('createPermission')
  async createUserActivityRole(
    @Body() createUserActivityRole: { userActivityId: string; roleId: string },
  ) {
    await this.userManagementService.createUserActivityRole(
      createUserActivityRole,
    );
  }

  // // Permissions
  // @Post('permission')
  // async CreatePermission(@Body() permissionDto: PermissionDto) {
  //   return this.userManagementService.createPermission(permissionDto);
  // }

  @ApiTags('user-management')
  @UseInterceptors(ClassSerializerInterceptor)
  @Get('permissions')
  async findAllPermissions() {
    return this.userManagementService.findAllPermissions();
  }

  @ApiTags('user-management')
  @Get('permission/:id')
  async findOnePermission(@Param('id') id: string) {
    return this.userManagementService.findPermissionByRoleId(id);
  }
  // @Get('permission/:id')
  // async findOnePermission(@Param('id') id: string) {
  //   return this.userManagementService.findOnePermission(id);
  // }

  @ApiTags('user-management')
  @Get('permission/role/:id')
  async findPermissionByRole(@Param('id') id: string) {
    return this.userManagementService.findPermissionByRoleId(id);
  }

  // @Patch('permission/:id')
  // async updatePermission(
  //   @Param('id') id: string,
  //   @Body() updatePermissionDto: UpdatePermissionDto,
  // ) {
  //   return this.userManagementService.updatePermission(id, updatePermissionDto);
  // }

  @ApiTags('user-management')
  @Patch('permission/deleteAction')
  async updatePermission(
    @Body()
    updateUserActivityRole: {
      userActivityId: string;
      roleId: string;
      isDeleted: boolean;
    },
  ) {
    await this.userManagementService.createUserActivityRole(
      updateUserActivityRole,
    );
  }

  @ApiTags('user-management')
  @Delete('permission/:id')
  async removePermission(@Param('id') id: string) {
    return this.userManagementService.removePermission(id);
  }

  @ApiTags('user-management')
  @Delete('permission/delete/:role/:userActivity')
  async removePermissionCorrect(
    @Param('role') role: string,
    @Param('userActivity') userActivity: string,
  ) {
    return this.userManagementService.removePermissionByRoleAndUserActivity(
      role,
      userActivity,
    );
  }

  // @ApiTags('delimitation')
  // Delimitation
  // Country
  // @Post('country')
  // async CreateCountry(@Body() countryDto: CountryDto) {
  //   return this.userManagementService.createCountry(countryDto);
  // }

  // @ApiTags('delimitation')
  // @Get('countries')
  // async findAllCountries() {
  //   return this.userManagementService.findAllCountries();
  // }

  @ApiTags('delimitation')
  @Get('delimitation/sync')
  async synchronize() {
    await this.userManagementService.synchronize();

    return 'OK';
  }
  @ApiTags('delimitation')
  // Province
  @Post('province')
  async CreateProvince(@Body() provinceDto: ProvinceDto) {
    return this.userManagementService.createProvince(provinceDto);
  }

  @ApiTags('delimitation')
  @Get('provinces')
  async findAllProvinces() {
    return this.userManagementService.findAllProvinces();
  }

  @ApiTags('delimitation')
  @Delete('province/:id')
  async removeProvince(@Param('id') id: string) {
    return this.userManagementService.removeProvince(id);
  }

  @ApiTags('delimitation')
  // District
  @Post('district')
  async CreateDistrict(@Body() districtDto: DistrictDto) {
    return this.userManagementService.createDistrict(districtDto);
  }

  @ApiTags('delimitation')
  @Get('districts')
  async findAllDistricts() {
    return this.userManagementService.findAllDistricts();
  }
  // retive ditrict by province id

  @ApiTags('delimitation')
  @Get('districts/:provinceId')
  async findAllDistrictsByProvinceId(@Param('provinceId') provinceId: string) {
    try {
      const districts =
        await this.userManagementService.findAllDistrictsByProvinceId(
          provinceId,
        );
      return districts;
    } catch (error) {
      throw new Error(`Failed to find districts: ${error.message}`);
    }
  }
  // retriving sector by district is

  @ApiTags('delimitation')
  // Sector
  @Post('sector')
  async CreateSector(@Body() sectorDto: SectorDto) {
    return this.userManagementService.createSector(sectorDto);
  }

  @ApiTags('delimitation')
  @Get('sectors')
  async findAllSectors() {
    return this.userManagementService.findAllSectors();
  }

  @ApiTags('delimitation')
  @Get('sectors/:districtId')
  async findAllSectorsByDistrictId(@Param('districtId') districtId: string) {
    try {
      const sectors =
        await this.userManagementService.findAllSectorsByDistrictId(districtId);
      return sectors;
    } catch (error) {
      throw new Error(`Failed to find sectors: ${error.message}`);
    }
  }

  @ApiTags('delimitation')
  // Cell
  @Post('cell')
  async CreateCell(@Body() cellDto: CellDto) {
    return this.userManagementService.createCell(cellDto);
  }

  @ApiTags('delimitation')
  @Get('cells')
  async findAllCells() {
    return this.userManagementService.findAllCells();
  }

  // retrive cells buy sector id
  @ApiTags('delimitation')
  @Get('cells/:sectorId')
  async findAllCellsBySectorId(@Param('sectorId') sectorId: string) {
    try {
      const cells =
        await this.userManagementService.findAllCellsBySectorId(sectorId);
      return cells;
    } catch (error) {
      throw new Error(`Failed to find cells: ${error.message}`);
    }
  }

  @ApiTags('delimitation')
  // Villages
  @Post('village')
  async CreateVillage(@Body() villageDto: VillageDto) {
    return this.userManagementService.createVillage(villageDto);
  }

  @ApiTags('delimitation')
  @Get('villages')
  async findAllVillages() {
    return this.userManagementService.findAllVillages();
  }

  // retrive villages by cell id
  @ApiTags('delimitation')
  @Get('villages/:cellId')
  async findAllVillagesByCellId(@Param('cellId') cellId: string) {
    try {
      const villages =
        await this.userManagementService.findAllVillagesByCellId(cellId);
      return villages;
    } catch (error) {
      throw new Error(`Failed to find villages: ${error.message}`);
    }
  }
  @ApiTags('institution')
  // Agency
  // Agency
  @Post('institution')
  async CreateAgency(@Body() institutionDto: InstitutionDto) {
    return this.userManagementService.createInstitution(institutionDto);
  }

  @ApiTags('institution')
  @Get('institutions')
  async findAllAgencies() {
    return this.userManagementService.findAllAgencies();
  }
  // find instutions but id
  @ApiTags('institution')
  @Get('institution/:id')
  async findAgencyById(@Param('id') id: string) {
    try {
      const institution =
        await this.userManagementService.findInstitutionById(id);
      return institution;
    } catch (error) {
      throw new Error(`Failed to find institutions: ${error.message}`);
    }
  }
  // update instutions
  // @ApiTags('institution')
  // @Put('institution/:id')
  // async updateInstitution(
  //   @Param('id') id: string,
  //   @Body() institutionDto: InstitutionDto,
  // ) {
  //   return this.userManagementService.updateInstitution(id, institutionDto);
  // }
  @ApiTags('institution')
  @Delete('institution/:id')
  async removeAgency(@Param('id') id: string) {
    return this.userManagementService.removeProvince(id);
  }

  // create beneficary angecy
  @ApiTags('Non-Budgeted Agencies')
  // Agency
  // Agency
  @Post('nonBudgetedAgencies')
  async CreateNonBudgetedAgencies(
    @Body() nonBudgetedAgenciesDto: NonBudgetedAgenciesDto,
  ) {
    return this.userManagementService.createBeneficiaryAgencies(
      nonBudgetedAgenciesDto,
    );
  }
  @ApiTags('Non-Budgeted Agencies')
  @Get('nonBudgetedAgencies')
  async findAllBeneficiaryAgencies() {
    return this.userManagementService.findAllBeneficiaryAgencies();
  }

  @ApiTags('Non-Budgeted Agencies')
  @Get('nonBudgetedAgencies/:id')
  async findBeneficiaryAgenciesById(@Param('id') id: string) {
    try {
      const beneficiaryAgencies =
        await this.userManagementService.findBeneficiaryAgenciesById(id);
      return beneficiaryAgencies;
    } catch (error) {
      throw new Error(
        `Failed to find findBeneficiaryAgenciesById: ${error.message}`,
      );
    }
  }
  // update Beneficiary Agencies

  // deleting beneficiary agency

  @ApiTags('Non-Budgeted Agencies')
  @Delete('nonBudgetedAgencies/:id')
  async removeBeneficiaryAgencies(@Param('id') id: string) {
    return this.userManagementService.removeBeneficiaryAgencies(id);
  }
  @ApiOperation({
    summary: 'Updating institution profile ',
    description: 'sample data of institution location',
  })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        institution: {
          type: 'string',
          title: 'This is example of institution ID',
          example: '75e3eee3-110e-463e-be05-aaa499efb583',
        },
        province: {
          type: 'string',
          title: 'province ID ',
          example: 'c00f17b4-647d-4889-8d2d-e080ae562b47',
        },
        district: {
          type: 'string',
          title: 'sample of district ID',
          example: '99d6c888-78f8-411c-bb1e-5e076ebf3cd8',
        },
        sector: {
          type: 'string',
          title: 'sample of sector ID',
          example: '0a17ea0b-8738-4f1f-ac9e-aeacff447d30',
        },
        cell: {
          type: 'string',
          title: 'cell ID ',
          example: '07e2682b-aef7-4801-9be9-15fad3a90e26',
        },
        village: {
          type: 'string',
          title: 'village Id',
          example: 'eb15343f-01a7-42d9-9cef-39df4085396c',
        },
      },
    },
  })
  @ApiTags('Update Institution Location Profile')
  // agency with district
  @Post('institutionDistrict')
  async updateInstitutionProfileLocation(
    @Body() institutionLocationDto: InstitutionLocationDto,
  ) {
    return this.userManagementService.createInstitutionLocation(
      institutionLocationDto,
    );
  }

  // @ApiTags('institution')
  // @Get('institutionDistricts')
  // async findAllAgencyDistricts() {
  //   return this.userManagementService.findAllAgencyDistricts();
  // }

  @ApiTags('institution')
  @Delete('institutionDistrict/:id')
  async removeAgencyDistrict(@Param('id') id: string) {
    return this.userManagementService.removeAgency(id);
  }
}
