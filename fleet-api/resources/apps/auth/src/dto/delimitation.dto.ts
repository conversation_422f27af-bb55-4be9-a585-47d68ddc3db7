import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class ProvinceDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field name code be empty' })
  code: string;
}

export class DistrictDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field province id is required' })
  @IsNotEmpty({ message: 'The field province activity cannot be empty' })
  @Type(() => ProvinceDto)
  @Expose()
  province: ProvinceDto;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field name code be empty' })
  code: string;
}

export class SectorDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field district id is required' })
  @IsNotEmpty({ message: 'The field country activity cannot be empty' })
  @Type(() => DistrictDto)
  @Expose()
  district: DistrictDto;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field name code be empty' })
  code: string;
}

export class CellDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field sector id is required' })
  @IsNotEmpty({ message: 'The field country activity cannot be empty' })
  @Type(() => SectorDto)
  @Expose()
  sector: SectorDto;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field name code be empty' })
  code: string;
}

export class VillageDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field cell id is required' })
  @IsNotEmpty({ message: 'The field country activity cannot be empty' })
  @Type(() => CellDto)
  @Expose()
  cell: CellDto;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field name code be empty' })
  code: string;
}
