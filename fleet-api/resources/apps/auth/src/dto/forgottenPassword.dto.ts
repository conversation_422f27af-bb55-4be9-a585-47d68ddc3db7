import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class ForgottenPasswordDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty({ message: 'The field email cannot be empty' })
  email: string;

  @ApiProperty({ description: 'The field newPasswordToken is required' })
  @IsNotEmpty({ message: 'The field newPasswordToken cannot be empty' })
  newPasswordToken: string;
}
