// import { ActivityTpe } from 'apps/auth/src/entities/user-management/userActivity.entity';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';
// import { Role } from '../entities/user-management/role.entity';
import { ApiProperty } from '@nestjs/swagger';

export class RoleDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
}
export class UpdateRoleDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The name agency is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
}

// export class UserActivityDto {
//   @IsOptional()
//   id: string;

//   @ApiProperty({ description: 'The field name is required' })
//   @IsNotEmpty({ message: 'The field name cannot be empty' })
//   name: string;

//   @ApiProperty({ description: 'The field activity type is required' })
//   @IsEnum(ActivityTpe)
//   @IsIn([
//     ActivityTpe.READ,
//     ActivityTpe.CREATE,
//     ActivityTpe.UPDATE,
//     ActivityTpe.DELETE,
//   ])
//   public activityTpe: ActivityTpe;

//   @IsOptional()
//   roles?: Role[];
// }

// export class UpdateUserActivityDto {
//   @IsOptional({ message: 'The field id cannot be empty' })
//   id: string;

//   @ApiProperty({ description: 'The field name is required' })
//   @IsNotEmpty({ message: 'The field name cannot be empty' })
//   name: string;

//   @ApiProperty({ description: 'The field activity type is required' })
//   @IsEnum(ActivityTpe)
//   @IsIn([
//     ActivityTpe.READ,
//     ActivityTpe.CREATE,
//     ActivityTpe.UPDATE,
//     ActivityTpe.DELETE,
//   ])
//   public activityTpe: ActivityTpe;
// }

export class UserTypeDto {
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
}
export class UpdateUserTypeDto {
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;
}

// export class CivilStatusDto {
//   @ApiProperty({ description: 'The field name is required' })
//   @IsNotEmpty({ message: 'The field name cannot be empty' })
//   name: string;
// }
// export class UpdateCivilStatusDto {
//   @ApiProperty({ description: 'The field name is required' })
//   @IsNotEmpty({ message: 'The field name cannot be empty' })
//   name: string;
// }

export class PermissionDto {
  @ApiProperty({ description: 'The field role id is required' })
  @IsNotEmpty({ message: 'The field role cannot be empty' })
  @Type(() => RoleDto)
  @Expose()
  role: RoleDto;

  // @ApiProperty({ description: 'The field user activity id is required' })
  // @IsNotEmpty({ message: 'The field user activity cannot be empty' })
  // @Type(() => UserActivityDto)
  // @Expose()
  // userActivity: UserActivityDto;
}
export class UpdatePermissionDto {
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  @Type(() => RoleDto)
  @Expose()
  role: RoleDto;

  // @IsNotEmpty({ message: 'The field name cannot be empty' })
  // @Type(() => UserActivityDto)
  // @Expose()
  // userActivity: UserActivityDto;
}
