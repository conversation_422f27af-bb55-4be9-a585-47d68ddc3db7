import axios from 'axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { User } from './entities/user-management/user.entity';
import * as bcrypt from 'bcryptjs';
import { JWTService } from './jwt.service';
import { ForgottenPassword } from './entities/user-management/forgottenPassword.entity';
import config from '../config';
// import * as nodemailer from 'nodemailer';
import { InjectRepository } from '@nestjs/typeorm';
import { EmailVerification } from './entities/user-management/emailVerification.entity';
import { Repository } from 'typeorm';
// import { GetUserDto } from './dto/user.dto';
import { EventPattern } from '@nestjs/microservices';
import { InstitutionLocation } from './entities/institution/institutionLocation.entity';

// import { InstitutionRepository } from './user-management/user-management.repository';
const saltRounds = 10;

// import { MessagePattern } from '@nestjs/microservices';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JWTService,

    @InjectRepository(User)
    private userRepository: Repository<User>,
    // private readonly institutionRepository: InstitutionRepository,

    @InjectRepository(EmailVerification)
    private emailVerificationRepository: Repository<EmailVerification>,

    @InjectRepository(ForgottenPassword)
    private forgottenPasswordRepository: Repository<ForgottenPassword>,
    @InjectRepository(InstitutionLocation)
    private institutionLocationRepository: Repository<InstitutionLocation>,
  ) {}

  @EventPattern('checkUser')
  // @MessagePattern('checkUser')
  async checkUser(userId: string): Promise<boolean> {
    console.log(userId);
    // Check if the user exists in the database
    const user = await this.userRepository.findOne({ where: { id: userId } });
    return !!user; // Return true if the user exists, false otherwise
  }
  @EventPattern('checkUserData')
  async checkUserData(userId: string) {
    return await this.userRepository.findOne({ where: { id: userId } });
  }
  async userExists(userId: string): Promise<boolean> {
    // Your logic to check if the user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });

    return !!user; // Return true if the user exists, false otherwise
  }
  async userData(userId: string) {
    return await this.userRepository.findOne({ where: { id: userId } });
    // }
    // async InstitutionExists(institution: string) {
    //   return await this.institutionRepository.findOne({
    //     name: institution,
    //   });
  }
  // grtting user detalis
  async userDetails(userId: string) {
    return await this.userRepository
      .createQueryBuilder('user')
      // .leftJoinAndSelect('user.userType', 'userType')
      .leftJoinAndSelect('user.institution', 'institution')
      .leftJoinAndSelect('user.role', 'role')
      // .leftJoinAndSelect('role.userActivities', 'userActivities')
      .where('user.id = :userId', { userId })
      .getOne();
  }

  async validateLogin(email: string, password: string) {
    const userFromDb = await this.userRepository
      .createQueryBuilder('user')
      // .leftJoinAndSelect('user.userType', 'userType')
      .leftJoinAndSelect('user.institution', 'institution')
      .leftJoinAndSelect('user.role', 'role')
      // .leftJoinAndSelect('role.userActivities', 'userActivities')
      .where('user.email = :email', { email })
      .getOne();

    // const passwordIsValid = bcrypt.compare(password, userFromDb.password);

    // const userFromDb = await this.userRepository.findOne({
    //   where: { email: email },
    //   relations: { role: true },
    // });
    // console.log(userFromDb);
    if (!userFromDb) {
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }
    // throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);
    if (userFromDb.isEmailValid === false) {
      throw new HttpException('Email not verified', HttpStatus.FORBIDDEN);
    }
    // throw new HttpException('LOGIN.EMAIL_NOT_VERIFIED', HttpStatus.FORBIDDEN);

    const passwordIsValid = await bcrypt.compare(password, userFromDb.password);

    if (passwordIsValid) {
      // const accessToken = await this.jwtService.createToken(userFromDb);
      const accessToken = await this.jwtService.createToken(
        email,
        userFromDb.id,
        userFromDb.firstName,
        userFromDb.lastName,
        userFromDb.institution,
        // userFromDb.userActivities,
      );
      // const newUserData = delete userFromDb.password;
      userFromDb.password = null;
      // userFromDb.institution.isInstitutionLocationFilled = false;
      // check if institution profile is fieled
      const institutionId = userFromDb.institution.id;
      const instutitioLocation = await this.institutionLocationRepository
        .createQueryBuilder('institutionLocation')
        .leftJoinAndSelect('institutionLocation.institution', 'institution')
        .leftJoinAndSelect('institutionLocation.province', 'province')
        .leftJoinAndSelect('institutionLocation.district', 'district')
        .leftJoinAndSelect('institutionLocation.sector', 'sector')
        .leftJoinAndSelect('institutionLocation.cell', 'cell')
        .leftJoinAndSelect('institutionLocation.village', 'village')
        .where('institutionLocation.institution = :institutionId', {
          institutionId,
        })
        .getOne();
      console.log(instutitioLocation);
      console.log('====== Good test ==========================');
      return { token: accessToken, user: userFromDb };
    } else {
      throw new HttpException('Login error', HttpStatus.UNAUTHORIZED);
    }
  }

  async createEmailToken(email: string) {
    console.log(email);
    const createdEmailVerification = {
      email: email,
      emailToken: (Math.floor(Math.random() * 9000000) + 1000000).toString(), //Generate 7 digits number
      timestamp: new Date(),
    };

    console.log(createdEmailVerification);

    const emailVerification = this.emailVerificationRepository.create(
      createdEmailVerification,
    );
    return await emailVerification.save();
  }

  async createForgottenPasswordToken(
    email: string,
  ): Promise<ForgottenPassword> {
    const forgottenPassword = await this.forgottenPasswordRepository.findOne({
      where: { email: email },
    });
    console.log(forgottenPassword);

    if (
      forgottenPassword &&
      (new Date().getTime() - forgottenPassword.timestamp.getTime()) / 60000 <
        15
    ) {
      throw new HttpException(
        'RESET_PASSWORD.EMAIL_SENDED_RECENTLY',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } else {
      console.log(forgottenPassword);
      const forgottenPasswordModel = {
        email: email,
        newPasswordToken: (
          Math.floor(Math.random() * 9000000) + 1000000
        ).toString(), //Generate 7 digits number,
        timestamp: new Date(),
      };

      console.log(forgottenPasswordModel);

      const forgottenPasswordSaved = this.forgottenPasswordRepository.create(
        forgottenPasswordModel,
      );
      return await forgottenPasswordSaved.save();
    }
  }

  async verifyEmail(token: string): Promise<boolean> {
    const emailToBeVerified = await this.emailVerificationRepository.findOne({
      where: { emailToken: token },
    });
    if (emailToBeVerified.email) {
      const userFromDb = await this.userRepository.findOne({
        where: { email: emailToBeVerified.email },
      });
      if (userFromDb) {
        userFromDb.isEmailValid = true;
        // const savedUser = await userFromDb.save();
        const savedUser = this.userRepository.create(userFromDb);
        await savedUser.save();
        // await emailVerif.remove();
        await this.emailVerificationRepository.remove(emailToBeVerified);
        return !!savedUser;
      }
    } else {
      throw new HttpException(
        'LOGIN.EMAIL_CODE_NOT_VALID',
        HttpStatus.FORBIDDEN,
      );
    }
  }

  async resetForgotenPassowrd(
    token: string,
    newPassword: string,
  ): Promise<boolean> {
    const emailToBeVerified = await this.forgottenPasswordRepository.findOne({
      where: { newPasswordToken: token },
    });
    if (emailToBeVerified.email) {
      const userFromDb = await this.userRepository.findOne({
        where: { email: emailToBeVerified.email },
      });
      if (userFromDb) {
        const hashedPassowrd = await bcrypt.hash(newPassword, saltRounds);
        userFromDb.password = hashedPassowrd;
        // const savedUser = await userFromDb.save();
        const savedUser = this.userRepository.create(userFromDb);
        await savedUser.save();
        // await emailVerif.remove();
        await this.forgottenPasswordRepository.remove(emailToBeVerified);
        return !!savedUser;
      }
    } else {
      throw new HttpException(
        'LOGIN.EMAIL_CODE_NOT_VALID',
        HttpStatus.FORBIDDEN,
      );
    }
  }

  async getForgottenPassword(
    newPasswordToken: string,
  ): Promise<ForgottenPassword> {
    return await this.forgottenPasswordRepository.findOne({
      where: { newPasswordToken: newPasswordToken },
    });
  }

  async checkPassword(email: string, password: string) {
    const userFromDb = await this.userRepository.findOne({
      where: { email: email },
    });
    if (!userFromDb)
      throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);

    return await bcrypt.compare(password, userFromDb.password);
  }

  async sendEmailVerification(
    email: string,
    phoneNumber: string,
    userNames: string,
    password: string,
  ): Promise<boolean> {
    const model = await this.emailVerificationRepository.findOne({
      where: { email: email },
    });

    if (model && model.emailToken) {
      const requestData = {
        sender_name: 'FLEET MIS',
        sender_email: '<EMAIL>',
        receiver_name: userNames,
        receiver_email: email,
        subject: 'Verify Email',
        message: `
        Hi ${userNames} there, <br>
        Thank you for registering with us! <br><br>
        To activate your account, simply click the link below: <br>
        Your OTP Code is: ${model.emailToken} <br>
        Activate Account Here: ${config.host.url}/auth/verify/${email}/${model.emailToken} <br><br>
        Your default password is : ${password}.
        NB: Default password must be changed 
        Best regards, <br>
        FLEET MIS Team       
        `,
      };

      try {
        const [emailResponse, smsResponse] = await Promise.all([
          // sending email and sms responses
          axios.post(config.notification.email_url, requestData),
          axios.post(config.notification.sms_url, {
            msisdn: phoneNumber,
            message: `Hi ${userNames}there,\nPlease check your email ${model.email} and activate your account,\nThis is your token ${model.emailToken}\n\nBest regards\nFLEET MIS Team <br>
            Activate Account Here: ${config.host.url}/auth/verify/${email}/${model.emailToken} <br>
            Your default password is : ${password}.<br>
            NB: Default password must be changed `,
          }),
        ]);

        console.log('Email sent successfully:', emailResponse);
        console.log('SMS sent successfully:', smsResponse);
        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
    }
  }
  async resendEmailVerification(email: string): Promise<boolean> {
    const model = await this.emailVerificationRepository.findOne({
      where: { email: email },
    });

    if (model && model.emailToken) {
      const requestData = {
        sender_name: 'FLEET MIS',
        sender_email: '<EMAIL>',
        receiver_name: 'FLEET User',
        receiver_email: email,
        subject: 'Verify Email',
        message: `
        Hi there, <br>
        Thank you for registering with us! <br><br>
        To activate your account, simply click the link below: <br>
        Your OTP Code is: ${model.emailToken} <br>
        Activate Account Here: ${config.host.url}/auth/verify/${email}/${model.emailToken} <br><br>
        Best regards, <br>
        FLEET Team       
        `,
      };

      try {
        const [emailResponse] = await Promise.all([
          // sending email and sms responses
          axios.post(config.notification.email_url, requestData),
        ]);

        console.log('Email sent successfully:', emailResponse);
        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
    }
  }

  async sendEmailForgotPassword(email: string): Promise<boolean> {
    const userFromresetdb = await this.forgottenPasswordRepository.findOne({
      where: { email: email },
    });
    if (userFromresetdb) {
      await this.forgottenPasswordRepository.delete(userFromresetdb.id);
    }
    const userFromDb = await this.userRepository.findOne({
      where: { email: email },
    });
    if (!userFromDb)
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);

    const tokenModel = await this.createForgottenPasswordToken(email);

    if (tokenModel && tokenModel.newPasswordToken) {
      const requestData = {
        sender_name: 'FLEET MIS',
        sender_email: '<EMAIL>',
        receiver_name: 'Fleet User',
        receiver_email: email,
        subject: 'Forgot Password',
        message: `Hi there, <br>
            If you have requested to reset your password, <br>
            please follow the link below: <br>
            <a href="${config.host.url}/auth/reset-password/${email}">Click here to reset password</a> <br>
            token  ${tokenModel.newPasswordToken}
            Best regards, <br>
            FLEET Team`,
      };

      try {
        const response = await axios.post(
          config.notification.email_url,
          requestData,
        );
        console.log('Email sent successfully:', response.data);
        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
    }
  }
}
