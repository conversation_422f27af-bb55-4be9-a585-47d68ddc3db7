import { NestFactory } from '@nestjs/core';
import { AuthModule } from './auth.module';
import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';
// import helmet from 'helmet';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
// import { join } from 'path';
// import * as express from 'express';
import { rateLimit } from 'express-rate-limit';
// import { AllExceptionsFilter } from '@app/common/filters/all-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AuthModule, { cors: true });
  const configService = app.get(ConfigService);
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: configService.get('TCP_PORT'),
    },
  });
  await app.startAllMicroservices();

  // -- Helmet
  // app.use(helmet());

  // -- Cors setup
  app.enableCors({
    origin: '*',
  });
  // app.enableCors({
  //   origin: '*',
  //   methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  //   preflightContinue: false,
  //   optionsSuccessStatus: 204,
  // });
  // app.enableCors({
  //   origin: 'http://localhost:4200', // Specify your Angular app URL
  //   methods: ['GET', 'POST', 'PATCH', 'PUT', 'DELETE'],
  //   allowedHeaders: '*',
  // });

  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

  // -- Swagger Documentation
  const config = new DocumentBuilder()
    .setTitle('Fleet MIS API')
    .setDescription('API Auth to manage Fleet MIS.')
    .addBasicAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  await app.listen(configService.get('HTTP_PORT'), '0.0.0.0');
  console.log(configService.get('HTTP_PORT'));
  // await app.listen(5000);

  // app.use('/public', express.static(join(__dirname, '../public')));
  // // eslint-disable-next-line @typescript-eslint/no-var-requires
  // const bodyParser = require('body-parser');
  // app.use(bodyParser.json({ limit: '5mb' }));
  // app.use(bodyParser.urlencoded({ limit: '5mb', extended: true }));
  // app.useGlobalFilters(new AllExceptionsFilter());

  /* SECURITY */

  app.use(
    rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later',
    }),
  );
  const createAccountLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour window
    max: 3, // start blocking after 3 requests
    message:
      'Too many accounts created from this IP, please try again after an hour',
  });
  app.use('/auth/email/register', createAccountLimiter);
}
bootstrap();
