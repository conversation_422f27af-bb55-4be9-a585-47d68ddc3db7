import { AbstractEntity } from '@app/common';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
// import { Institution } from '../institution/institution.entity';

@Entity()
export class NonBudgetedAgencies extends AbstractEntity<NonBudgetedAgencies> {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  // This specifies the join table for the many-to-many relationship
  @Column({ unique: false })
  institution: string;

  @Column({ unique: true })
  agency: string;
}
