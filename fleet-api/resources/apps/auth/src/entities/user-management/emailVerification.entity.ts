// import { AbstractEntity } from '@app/common';
import { BaseEntity, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class EmailVerification extends BaseEntity {
  // export class EmailVerification extends AbstractEntity<EmailVerification> {
  /**
   * this decorator will help to auto generate id for the table.
   */
  @PrimaryGeneratedColumn('uuid')
  public id!: string;

  @Column({ type: 'varchar' })
  email: string;

  @Column({ type: 'varchar' })
  emailToken: string;

  @Column({ type: 'varchar' })
  timestamp: Date;
}
