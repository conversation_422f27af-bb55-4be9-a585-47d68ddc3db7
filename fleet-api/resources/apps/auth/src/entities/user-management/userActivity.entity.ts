// // import { AbstractEntity } from '@app/common';
// // import { Column, Entity, JoinTable, ManyToMany } from 'typeorm';
// // import { Role } from './role.entity';

// // export enum ActivityTpe {
// //   CREATE = 'create',
// //   READ = 'read',
// //   UPDATE = 'update',
// //   DELETE = 'delete',
// // }
// // @Entity()
// // export class UserActivity extends AbstractEntity<UserActivity> {
// //   /**
// //    * this decorator will help to auto generate id for the table.
// //    */

// //   @Column({ type: 'varchar' })
// //   name: string;

// //   @Column({
// //     type: 'enum',
// //     enum: ActivityTpe,
// //     default: ActivityTpe.READ,
// //   })
// //   activityTpe: ActivityTpe;

// //   @ManyToMany(
// //     () => Role,
// //     role => role.userActivities, //optional
// //     {onDelete: 'NO ACTION', onUpdate: 'NO ACTION'})
// //   @JoinTable({
// //     name: 'userActivity_role',
// //     joinColumn: {
// //       name: 'userActivity_id',
// //       referencedColumnName: 'id',
// //     },
// //     inverseJoinColumn: {
// //       name: 'role_id',
// //       referencedColumnName: 'id',
// //     },
// //   })
// //   roles?: Role[];
// // }

// // import { AbstractEntity } from '@app/common';
// import {
//   Column,
//   Entity,
//   JoinTable,
//   ManyToMany,
//   PrimaryGeneratedColumn,
// } from 'typeorm';
// import { Role } from './role.entity';

// export enum ActivityTpe {
//   CREATE = 'create',
//   READ = 'read',
//   UPDATE = 'update',
//   DELETE = 'delete',
// }
// @Entity()
// export class UserActivity {
//   /**
//    * this decorator will help to auto generate id for the table.
//    */

//   @PrimaryGeneratedColumn('uuid', { name: 'id' })
//   id: string;

//   @Column({ type: 'varchar' })
//   name: string;

//   @Column({
//     type: 'enum',
//     enum: ActivityTpe,
//     default: ActivityTpe.READ,
//   })
//   activityTpe: ActivityTpe;

//   @ManyToMany(
//     () => Role,
//     (role) => role.userActivities, //optional
//     { onDelete: 'NO ACTION', onUpdate: 'NO ACTION' },
//   )
//   @JoinTable({
//     name: 'userActivity_role',
//     joinColumn: {
//       name: 'userActivity_id',
//       referencedColumnName: 'id',
//     },
//     inverseJoinColumn: {
//       name: 'role_id',
//       referencedColumnName: 'id',
//     },
//   })
//   roles?: Role[];
// }
