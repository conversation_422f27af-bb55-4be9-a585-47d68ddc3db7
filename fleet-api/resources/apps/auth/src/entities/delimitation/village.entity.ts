import { Column, Entity, JoinTable, ManyToOne } from 'typeorm';
import { Cell } from './cell.entity';
import { AbstractEntity } from '@app/common';

@Entity()
export class Village extends AbstractEntity<Village> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;
  @Column()
  code: string;

  @ManyToOne(() => Cell, { cascade: true })
  @JoinTable()
  cell: Cell;
}
