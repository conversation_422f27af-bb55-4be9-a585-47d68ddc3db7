import { Column, Entity, JoinTable, ManyToOne } from 'typeorm';
import { Sector } from './sector.entity';
import { AbstractEntity } from '@app/common';

@Entity()
export class Cell extends AbstractEntity<Cell> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;
  @Column()
  code: string;

  @ManyToOne(() => Sector, { cascade: true })
  @JoinTable()
  sector: Sector;
}
