import { Column, Entity } from 'typeorm';
// import { Country } from './country.entity';
import { AbstractEntity } from '@app/common';

@Entity()
export class Province extends AbstractEntity<Province> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;
  @Column()
  code: string;

  // @ManyToOne(() => Country, { cascade: true })
  // @JoinTable()
  // country: Country;
}
