import { Column, Entity, JoinTable, ManyToOne } from 'typeorm';
import { District } from './district.entity';
import { AbstractEntity } from '@app/common';

@Entity()
export class Sector extends AbstractEntity<Sector> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;
  @Column()
  code: string;

  @ManyToOne(() => District, { cascade: true })
  @JoinTable()
  district: District;
}
