import { Test, TestingModule } from '@nestjs/testing';
import { FleetApiGatwayController } from './fleet-api-gatway.controller';
import { FleetApiGatwayService } from './fleet-api-gatway.service';

describe('FleetApiGatwayController', () => {
  let fleetApiGatwayController: FleetApiGatwayController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [FleetApiGatwayController],
      providers: [FleetApiGatwayService],
    }).compile();

    fleetApiGatwayController = app.get<FleetApiGatwayController>(FleetApiGatwayController);
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(fleetApiGatwayController.getHello()).toBe('Hello World!');
    });
  });
});
